const extend = require('../extend');
const standardConfig = require('../standard-config.js');

module.exports = extend(standardConfig, {
	db: 'default',
	table: 'sys_relationship_type',
	entity: {
		base: 'sys',
		name: 'relationship_type',
	},
	caption: 'Record Link Type',
	captionPlural: 'Record Link Types',
	addCaption: 'Add Record Link Type',
	newCaption: 'New Record Link Type',
	acl: require('./acl.js'),
	aclHierarchy: require('./acl-hierarchy.js'),
	validation: require('./validation.js'),
	historyNav: require('./history-nav.js'),
	audit: require('./audit.js'),
	configurationExport: true,
	configurationImport: true,
	importTransformer: 'relationship_type',
	grids: require('./grids.js'),
	model() {
		return require('../../public/models/relationship-type-model.js');
	},
	collection() {
		return require('../../public/collections/relationship-types-collection.js');
	},
	view() {
		return require('../../public/views/settings/data/relationship-types/relationship-type-details-view.js');
	},
	fields: [
		{
			field: 'primaryVerb',
			caption: 'Record Link Type',
			type: 'relationshipTypeVerb',
			kind: 'editable',
		},
		{
			field: 'inverseVerb',
			caption: 'Bi-Directional Record Link Type',
			type: 'relationshipTypeVerb',
			kind: 'editable',
		},
		{
			field: 'rank',
			caption: 'Sequence',
			type: 'number',
			kind: 'editable',
		},
		{
			field: 'locked',
			caption: 'Locked',
			type: 'yesno',
			kind: 'editable',
		},
	],
});
