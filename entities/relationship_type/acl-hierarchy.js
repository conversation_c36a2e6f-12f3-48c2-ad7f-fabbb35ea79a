const permHelper = require('../../lib/core/permission-helper.js');

module.exports = [{
	type: 'group',
	caption: 'Record Link Types',
	permission: 'view_relationship_type_settings',
	parentPermission: 'view_data_settings',
	options: [
		{
			caption: 'View',
			tooltip: 'View record link types',
			sequence: 1,
			permission: 'view_relationship_type',
			disabled: true,
		},
		{
			caption: 'Create',
			tooltip: 'Create record link types',
			sequence: 2,
			permission: 'create_relationship_type',
		},
		{
			type: 'group',
			caption: 'Edit',
			tooltip: 'Edit record link types',
			sequence: 3,
			permission: permHelper.getEditGroupPermissionCode('relationship_type'),
			options: [{
				caption: 'Save',
				tooltip: 'Edit record link types',
				permission: 'edit_relationship_type',
			}],
		},
		{
			caption: 'Remove',
			tooltip: 'Delete record link types',
			sequence: 4,
			permission: 'remove_relationship_type',
		},
	],
}];
