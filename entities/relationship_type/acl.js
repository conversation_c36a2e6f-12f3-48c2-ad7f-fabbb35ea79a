const permHelper = require('../../lib/core/permission-helper.js');

module.exports = permHelper.initialize()
	.filter({
		name: 'Edit Record Link Type Restricted Fields',
		roles: ['edit_relationship_type_restricted_fields'],
		actions: ['save_existing'],
		conditions: [{
			attributes: {
				'!id': null,
			},
		}],
		filters: {
			primaryVerb: false,
			inverseVerb: false,
			locked: false,
		},
	})
	.required({
		name: 'View Record Link Type',
		roles: ['view_relationship_type'],
		actions: ['load', 'list'],
		conditions: [],
	})
	.required({
		name: 'Create Record Link Type',
		roles: ['create_relationship_type'],
		actions: ['save_new'],
		conditions: [{
			attributes: {
				id: null,
			},
		}],
	})
	.required({
		name: 'Edit Record Link Type',
		roles: ['edit_relationship_type'],
		actions: ['save_existing'],
		conditions: [{
			attributes: {
				'!id': null,
			},
		}],
	})
	.required({
		name: 'Edit Locked Record Link Type',
		roles: ['edit_locked_relationship_type'],
		actions: ['save_existing'],
		conditions: [{
			attributes: {
				'!id': null,
				locked: true,
			},
		}],
	})
	.required({
		name: 'Remove Record Link Type',
		roles: ['remove_relationship_type'],
		actions: ['remove'],
		conditions: [],
	})
	.required({
		name: 'Remove Locked Record Link Type',
		roles: ['remove_locked_relationship_type'],
		actions: ['remove'],
		conditions: [{
			attributes: {
				locked: true,
			},
		}],
	})
	.value();
