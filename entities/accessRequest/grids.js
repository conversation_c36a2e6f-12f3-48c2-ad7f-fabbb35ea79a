module.exports = {
	'pending-access-requests': {
		sortColumn: 'requestUser',
		sortOrder: 'desc',
		excludeColumns: ['caseId', 'status', 'responseUser', 'responseDate'],
		columns: [
			{ field: 'approve' },
			{ field: 'deny' },
			{ field: 'requestUser' },
			{ field: 'requestUserNick' },
			{ field: 'requestUserEmail' },
			{ field: 'requestReason' },
		],
		defaultDynamicDataFilters: ['requestUser'],
	},
};
