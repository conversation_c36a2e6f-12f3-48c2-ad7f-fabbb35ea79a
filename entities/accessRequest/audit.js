const requestReference = {
	displayFields: ['caseId', 'requestUser', 'createdDate'],
};

const responseDiff = {
	displayFields: ['status', 'responseDate'],
};

module.exports = {
	child: true,
	parentType: {
		base: 'sys',
		name: 'case',
	},
	parentFieldId: 'caseId',
	allowNavigateTo: false,
	cmd: {
		load: {
			viewed: {
				options: {
					reference: {
						displayFields: ['requestUser', 'status'],
					},
				},
			},
		},
		save: {
			access_request_created: {
				condition: {
					composite: 'all',
					rules: [
						{
							path: 'diff.id.originalValue',
							comparator: 'nexists',
						},
					],
				},
				options: {
					reference: {
						displayFields: ['caseId', 'createdDate'],
					},
				},
			},
			access_request_approved: {
				condition: {
					composite: 'all',
					rules: [
						{
							path: 'diff.status.updatedValue',
							comparator: 'eq',
							value: 'approved',
						},
					],
				},
				options: {
					changes: responseDiff,
					reference: requestReference,
				},
			},
			access_request_denied: {
				condition: {
					composite: 'all',
					rules: [
						{
							path: 'diff.status.updatedValue',
							comparator: 'eq',
							value: 'denied',
						},
					],
				},
				options: {
					changes: responseDiff,
					reference: requestReference,
				},
			},
			updated: {
				options: {
					changes: {
						displayFields: ['caseId'],
					},
					reference: {
						displayFields: ['caseId'],
					},
				},
			},
		},
		remove: {
			deleted: {
				options: {
					reference: {
						displayFields: ['requestUser', 'status'],
					},
				},
			},
		},
	},
};
