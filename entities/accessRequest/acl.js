const permHelper = require('../../lib/core/permission-helper.js');

module.exports = permHelper.initialize()
	.required({
		name: 'View Access Requests',
		roles: ['view_access_request'],
		actions: ['load', 'list', 'save_existing', 'remove'],
	})
	.required({
		name: 'Create Access Requests',
		roles: ['create_access_request'],
		actions: ['save_new'],
		conditions: [{
			attributes: {
				id: null,
			},
		}],
	})
	.required({
		name: 'Remove Access Request',
		roles: ['remove_access_request'],
		actions: ['remove'],
	})
	.required({
		name: 'Inherit Case Load ACL',
		roles: ['bypass_inherited_acl'],
		actions: ['load', 'list', 'save_existing', 'remove'],
		conditions: [{
			// Do not request inherited case view when attempting viewing
			// requests the user made.
			attributes: {
				'!requestUser': '{user.id}',
			},
		}, 'sys/case::{caseId}::load'],
	})
	.required({
		name: 'Edit Access Requests',
		roles: ['edit_access_request'],
		actions: ['save_existing'],
		conditions: [{
			attributes: {
				'!id': null,
			},
		}],
	})
	.value();
