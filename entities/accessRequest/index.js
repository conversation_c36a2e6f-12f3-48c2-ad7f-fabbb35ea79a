const extend = require('../extend.js');
const standardConfig = require('../standard-config.js');

module.exports = extend(standardConfig, {
	db: 'default',
	table: 'sys_access_request',
	entity: {
		base: 'sys',
		name: 'access_request',
	},
	caption: 'Access Request',
	captionPlural: 'Access Requests',
	addCaption: 'Add Access Request',
	newCaption: 'Request Access',
	acl: require('./acl.js'),
	validation: require('./validation.js'),
	dataExport: false,
	allowPurge: true,
	report: false,
	excludeFromAggregation: true,
	grids: require('./grids.js'),
	features: ['requestCaseAccess'],
	audit: require('./audit.js'),
	model() {
		return require('../../public/models/access-request-model.js');
	},
	collection() {
		return require('../../public/collections/access-request-collection.js');
	},
	parents: [
		{
			entity: {
				base: 'sys',
				name: 'case',
			},
			field: 'caseId',
		},
	],
	fields: [
		{
			field: 'caseId',
			caption: 'Case',
			type: 'case',
			kind: 'editable',
		},
		{
			field: 'requestUser',
			caption: 'Requestor',
			type: 'user',
			kind: 'system',
		},
		{
			field: 'requestReason',
			caption: 'Reason for Request',
			type: 'textarea',
			kind: 'editable',
		},
		{
			field: 'status',
			caption: 'Status',
			type: 'picklist',
			kind: 'system',
			typeOptions: {
				picklistName: 'access_request_statuses',
			},
		},
		{
			field: 'responseUser',
			caption: 'Response User',
			type: 'user',
			kind: 'system',
		},
		{
			field: 'responseDate',
			caption: 'Response Date',
			type: 'datetime',
			kind: 'system',
		},
	],
	joins: [
		{
			referenceField: 'requestUser',
			table: 'sys_user',
			fields: ['nick'],
		},
	],
});
