const _ = require('lodash');
const permHelper = require('../../lib/core/permission-helper.js');

module.exports = permHelper.initialize()
	.filterMarkForPurge()
	.filterPurge()
	.filter({
		name: 'Filter fields on existing rule',
		roles: ['edit_rules_entity_and_event'],
		actions: ['save_new', 'save_existing'],
		conditions: [{
			attributes: {
				'!id': null,
				sysSubmitted: true,
			},
		}],
		filters: {
			entityId: false,
			recordEvent: false,
		},
	})
	.filter({
		name: 'Filter fields on draft rule',
		roles: ['edit_draft_rules_entity'],
		actions: ['save_new', 'save_existing'],
		conditions: [{
			attributes: {
				'!id': null,
				sysSubmitted: false,
			},
		}],
		filters: {
			entityId: false,
		},
	})
	.required({
		name: 'View Rules',
		roles: ['view_rule'],
		actions: ['load', 'list'],
		conditions: [],
	})
	.required({
		name: 'Create Rule',
		roles: ['create_rule'],
		actions: ['save_new'],
		conditions: [{
			attributes: {
				id: null,
			},
		}],
	})
	.required({
		name: 'Create Rule',
		roles: ['create_rule'],
		actions: ['save_existing'],
		conditions: [{
			attributes: {
				'!id': null,
				sysSubmitted: false,
			},
		}],
	})
	.required({
		name: 'Edit Rule',
		roles: ['edit_rule'],
		actions: ['save_existing'],
		conditions: [{
			attributes: {
				'!id': null,
				sysSubmitted: true,
			},
		}],
	})
	.required({
		name: 'Remove Rule',
		roles: ['remove_rule'],
		actions: ['remove'],
		conditions: [],
	})
	.required({
		name: 'Save/Delete Hard Locked Rule',
		roles: ['save_delete_hard_locked_rule'],
		actions: ['save_new', 'save_existing', 'remove'],
		conditions: [{
			attributes: {
				hardLocked: true,
			},
		}],
	})
	.required({
		name: 'Inherit workflow acl',
		roles: ['bypass_inherited_acl'],
		control: 'required',
		actions: ['load', 'list', 'save_new', 'save_existing', 'remove'],
		conditions: [{
			attributes: {
				'!workflowId': null,
			},
		}, 'sys/workflow::{workflowId}::load'],
	})
	.required({
		name: 'Access disabled schedule process rule',
		roles: ['access_disabled_schedule_process_rule'],
		actions: ['load', 'list', 'save_new', 'save_existing'],
		conditions: [{
			attributes: {
				type: 'Schedule Process',
			},
		}, {
			fn(obj, context) {
				if (obj.type === 'Schedule Process') {
					const sftpFeature = _.includes(context.enabledFeatures, 'dataImport');
					if (!sftpFeature) return { ok: true};
				}
				return { ok: false };
			},
			esFilter(context, attributePrefix = '') {
				const sftpFeature = _.includes(context.enabledFeatures, 'dataImport');
				if (!sftpFeature) {
					return {
						bool: {
							must: [{
								term: {
									type: 'Schedule Process',
								},
							}],
						},
					};
				}
				return { bool: { must_not: [{ exists: { field: 'id' } }] } };
			},
			selectedFields(opts, callback) {
				return callback(null, ['id', 'type']);
			},
		}],
	})
	.value();
