module.exports = {
	mandatory$: [
		'name',
		'type',
	],
	dependentMandatory$: [
		{
			condition: 'isRecordEvent',
			fields: [
				'entityId',
				'recordEvent',
			],
		},
		{
			condition: 'isWorkflowStep',
			fields: [
				'workflowId',
				'transitionId',
			],
		},
		{
			condition: 'isSchedule',
			fields: [
				'entityId',
				'scheduleField',
				'scheduleDelay',
				'scheduleOccurrence',
			],
		},
		{
			condition: 'isScheduleEvent',
			fields: [
				'frequency',
			],
		},
		{
			condition: 'isTriggerTimeRequired',
			fields: [
				'triggerTime',
			],
		},
		{
			condition: 'isWeekly',
			fields: [
				'weekDays',
			],
		},
		{
			condition: 'isMonthly',
			fields: [
				'monthDays',
			],
		},
	],
};
