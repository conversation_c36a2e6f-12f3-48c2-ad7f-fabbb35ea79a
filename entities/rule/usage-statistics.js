const statHelper = require('../../shared/stat-helper.js')('sys_rule');

module.exports = [
	{
		category: 'workflows',
		key: 'totalAllRules',
		query: statHelper.countActiveEntity(),
	},
	{
		category: 'workflows',
		key: 'totalWorkflowRules',
		query: statHelper.countActiveEntity({
			where() {
				this.where('type', 'Workflow');
			},
		}),
	},
	{
		category: 'workflows',
		key: 'totalStandaloneRules',
		query: statHelper.countActiveEntity({
			where() {
				this.whereNot('type', 'Workflow');
			},
		}),
	},
];
