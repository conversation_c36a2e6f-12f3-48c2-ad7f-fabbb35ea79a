# Rules

Rules allow users to automate the workflow immediate/scheduled effects on a record by sending notification, creating To-Dos, or Assigning user fields on cases. These can be triggered by either dynamic workflow’s steps or the events generated by static workflows.

A rule consists of a triggering even, conditional criteria and actions to take.

A user can group multiple actions on a single rule.

Platform ships with default rule functionality.

## Rules Types

Rules support various different "rule types" which determine different kind of sources of triggering events.

### Record Event

Record events are events triggered in the context of specific record updates such as a "Party Save".

More record types can be added by updating `ruleEvents` property on `entityDefinition`.

Record Events can be updated by modifying record_events picklist

### Workflow

Workflow events are fired when the specified Workflow "Step" is triggered by a user and may be created through the Workflow creation wizard.

### Schedule Rules

When the “Rule Type” is set to “Schedule”, the rule can be scheduled to occur a set amount of time before or after the record date field selected. The occurrence of the “Schedule” rule can also be set either to trigger once or on a recurring frequency. Consistent with the existing rule types (“Record Event” and “Workflow”), both Criteria and Actions can be added to the rule. When the Rule executes, the system validates the Criteria and, if they evaluate as true, triggers the Actions.

#### Delay

The user can choose a Delay that’s either before or after the date specified by the Date Field. Since some date fields can be set in advance (e.g. a to-do’s Due Date), while others are timestamps that are set as they occur (e.g. a case’s Created Date or Last Updated Date), the user needs to be conscious of this fact when setting up the rule so that it makes sense. For example, a Schedule rule that executes 1 day before a to-do is due makes sense, but a Schedule rule that executes 1 day before a case is created does not.

If a Schedule rule executes for a date that is in the past, the rule’s Criteria are immediately validated and, if true, Actions are triggered. Going back to the example of the Schedule rule set to execute 1 day before the case is created, it means that this rule behaves the same as one that is set to execute 0 days after the case is created--the rule will execute as soon as a case is created.

#### Occurrence

Schedule rules can be configured to execute once after the date field has changed, or execute continuously as long as the Criteria continue to evaluate as true. As soon as the Criteria evaluate as false for a “continuous” Schedule rule, the rule no longer applies until the next time the record’s date field changes. Updating a record so that the Criteria to pass will not resume the Schedule rule.

If a “continuous” Schedule rule executes for a date that is in the past, the rule’s Criteria are immediately validated and, if true, Actions are triggered. However, the following instance of the rule to be scheduled will be based on the Delay value calculated from the current date. For example, if a “continuous” Schedule rule is set to execute 1 day before a case is created, the rule will execute as soon as a case is created. Then, the rule will be rescheduled to run every 1 day, starting 1 day after the created. The rule will continue to reschedule until the Criteria no longer evaluate as true.

Updating a Schedule rule will update any existing scheduled tasks and deleting a Schedule rule will delete any existing scheduled tasks.

#### Blacklisted Fields

The following Date Fields are blacklisted from Schedule rules since they do not provide any benefit to the user:

- `pendingPurgeDate`
- `datePurged`
- `deletedDate`
- `lastSnapshotGenerationDate`

## Extending

Extends `standard-config`.

> :warning: **WARNING**: This is a standardized platform entity that drives out of box functionality and it is not recommended to extend this entity.
