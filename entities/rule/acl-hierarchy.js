const permHelper = require('../../lib/core/permission-helper.js');

module.exports = [{
	type: 'group',
	caption: 'Rule',
	permission: 'rule',
	parentPermission: 'view_workflow_settings',
	sequence: 3,
	options: [
		{
			permission: 'view_rule',
			caption: 'View',
			tooltip: 'View Rule',
			sequence: 1,
		},
		{
			permission: 'create_rule',
			caption: 'Create',
			tooltip: 'Create Rule',
			sequence: 2,
			dependencies: ['view_rule'],
		},
		{
			type: 'group',
			permission: permHelper.getEditGroupPermissionCode('rule'),
			caption: 'Edit',
			sequence: 3,
			dependencies: ['view_rule'],
			options: [{
				permission: 'edit_rule',
				caption: 'Save',
				tooltip: 'Edit Rule',
				dependencies: ['view_rule'],
			}],
		},
		{
			permission: 'remove_rule',
			caption: 'Remove',
			tooltip: 'Delete Rule',
			sequence: 4,
			dependencies: ['view_rule'],
		},
	],
}];
