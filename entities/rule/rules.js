module.exports = {
	isRecordEvent(data) { return data.type === 'Record Event'; },
	isWorkflowStep(data) { return data.type === 'Workflow'; },
	isSchedule(data) { return data.type === 'Schedule'; },
	isScheduleEvent(data) { return data.type === 'Schedule Process'; },
	isTriggerTimeRequired(data) { return (data.type === 'Schedule Process' && data.frequency !== 'calendar quarterly' && data.frequency !== 'fiscal quarterly'); },
	isWeekly(data) { return data.frequency === 'weekly'; },
	isMonthly(data) { return data.frequency === 'monthly'; },
	isNotCalendarQuarterly(data) { return data.frequency !== 'calendar quarterly'; },
	isNotFiscalQuarterly(data) { return data.frequency !== 'fiscal quarterly'; },
};
