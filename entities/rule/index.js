const extend = require('../extend.js');
const standardWorkflowChildConfig = require('../standard-workflow-child-config.js');

module.exports = extend(standardWorkflowChildConfig, {
	table: 'sys_rule',
	entity: {
		base: 'sys',
		name: 'rule',
	},
	bypassValidationOnDraft: true,
	caption: 'Rule',
	captionPlural: 'Rules',
	addCaption: 'Add Rule',
	newCaption: 'New Rule',
	acl: require('./acl.js'),
	aclHierarchy: require('./acl-hierarchy.js'),
	validation: require('./validation.js'),
	grids: require('./grids.js'),
	rules: require('./rules.js'),
	audit: require('./audit.js'),
	usageStatistics: require('./usage-statistics.js'),
	copyPostAction: 'rule',
	copyTransformer: 'rule',
	historyNav: [
		{ from: [], to: '/settings/workflow/custom-workflow/{workflowId}' },
		{ from: [], to: '/settings/workflow/rules' },
	],
	configurationExport: true,
	exportTransformer: 'rule',
	importTransformer: 'rule',
	customDependencies: ['sys/transition', 'isight/dynamic_entity'],
	model() { return require('../../public/models/workflow-rule-model.js'); },
	collection() { return require('./collection.js'); },
	fields: [
		{
			field: 'type',
			type: 'picklist',
			typeOptions: {
				picklistName: 'rule_types',
			},
			caption: 'Rule Type',
			kind: 'editable',
		},
		{
			field: 'entityId',
			type: 'entityPicklist',
			caption: 'Record Type',
			kind: 'editable',
			typeOptions: {
				entityCategory: 2,
				filter: 'ruleEntitiesFilter',
				canCreate: false,
			},
		},
		{
			field: 'workflowId',
			type: 'workflowPicklist',
			caption: 'Workflow',
			kind: 'editable',
			typeOptions: {
				entity: 'sys/workflow',
				textField: 'name',
				// Show Pending Changes workflows in place of the Active workflows if both exist
				filter: {
					operator: 'and',
					values: [
						{
							field: 'copiedToId',
							type: 'id',
							operator: 'is_empty',
							values: [],
						},
						{
							field: 'status',
							type: 'picklist',
							operator: 'is_not',
							values: ['draft'],
						},
						{
							field: 'hardLocked',
							type: 'yesno',
							operator: 'is_not',
							values: [true],
						},
					],
				},
			},
		},
		{
			field: 'recordEvent',
			type: 'picklist',
			caption: 'Record Event',
			kind: 'editable',
			typeOptions: {
				picklistName: 'record_events',
				ignoreParents: true,
				picklistDependencies: ['entityId'],
			},
		},
		{
			field: 'name',
			type: 'textbox',
			kind: 'editable',
			caption: 'Name',
		},
		{
			field: 'transitionId',
			type: 'workflowTransition',
			caption: 'Step',
			kind: 'editable',
			typeOptions: {
				entity: 'sys/transition',
				textField: 'name',
				filterBy: 'workflowId',
			},
		},
		{
			field: 'conditionId',
			type: 'id',
			kind: 'hidden-editable',
			caption: 'Condition',
			excludeFromSaveAndCopy: true,
		},
		{
			field: 'scheduleField',
			type: 'fieldPicklist',
			typeOptions: {
				entityNameField: 'entityId__canon',
				filterType: ['date', 'datetime'],
				refreshOnFields: ['entityId'],
				excludedFields: [
					'pendingPurgeDate',
					'datePurged',
					'deletedDate',
					'lastSnapshotGenerationDate',
				],
				excludeDynamicKind: ['aggregate'],
			},
			kind: 'editable',
			caption: 'Date Field',
		},
		{
			field: 'scheduleDelay',
			type: 'delay',
			typeOptions: {
				showBeforeAfter: true,
				maxDuration: {
					years: 10,
				},
			},
			kind: 'editable',
			caption: 'Delay',
		},
		{
			field: 'scheduleOccurrence',
			type: 'radio',
			typeOptions: {
				picklistName: 'schedule_occurrence',
				orientation: 'horizontal',
			},
			kind: 'editable',
			caption: 'Occurrence',
		},
		{
			field: 'frequency',
			type: 'picklist',
			typeOptions: {
				picklistName: 'rule_frequency',
			},
			caption: 'Frequency',
			kind: 'editable',
		},
		{
			field: 'weekDays',
			type: 'picklist[]',
			typeOptions: {
				picklistName: 'rule_week_days',
			},
			kind: 'editable',
			caption: 'Weekdays',
		},
		{
			field: 'monthDays',
			type: 'picklist[]',
			typeOptions: {
				picklistName: 'rule_month_days',
			},
			kind: 'editable',
			caption: 'Days of the Month',
		},
		{
			field: 'triggerTime',
			type: 'time',
			caption: 'Trigger time',
			kind: 'editable',
		},
		{
			field: 'jobId',
			type: 'textbox',
			kind: 'hidden',
			caption: 'Job Id',
		},
		{
			field: 'active',
			type: 'yesno',
			kind: 'editable',
			caption: 'Active',
		},
	],
	parents: [
		{
			entity: {
				base: 'sys',
				name: 'workflow',
			},
			field: 'workflowId',
		},
	],
});
