const reference = {
	displayFields: [
		'name',
	],
};

module.exports = {
	child: true,
	parentType: {
		base: 'sys',
		name: 'workflow',
	},
	parentFieldId: 'workflowId',
	allowNavigateTo: true,
	cmd: {
		load: {
			viewed: {
				options: {
					reference,
				},
			},
		},
		save: {
			created: {
				options: {
					reference,
				},
			},
			updated: {
				options: {
					changes: {
						excludeFields: ['id'],
					},
					reference,
				},
			},
		},
		remove: {
			deleted: {
				options: {
					reference,
				},
			},
		},
	},
};
