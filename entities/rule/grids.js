module.exports = {
	'main-rules-grid': {
		sortColumn: 'name',
		sortOrder: 'desc',
		excludeColumns: ['datePurged', 'pendingPurgeDate', 'purgeReason', 'excludeFromSuggestedLinks'],
		columns: [
			{ field: 'name' },
			{field: 'type'},
			{ field: 'recordEvent'},
			{ field: 'transitionId'},
			{ field: 'active' },
		],
		defaultDynamicDataFilters: ['createdDate', 'lastUpdatedDate'],
	},
	'rules-step-grid': {
		sortColumn: 'createdDate',
		sortOrder: 'desc',
		excludeColumns: ['datePurged', 'pendingPurgeDate', 'purgeReason', 'excludeFromSuggestedLinks'],
		columns: [
			{ field: 'name' },
			{ field: 'transitionId' },
		],
	},
};
