var extend = require('../extend.js');
var standardConfig = require('../standard-config.js');
const parentField = 'caseId';
const parentEntity = {
	base: 'sys',
	name: 'case',
};

module.exports = extend(standardConfig, {
	db: 'default',
	table: 'sys_link_snapshot',
	entity: {
		base: 'sys',
		name: 'link_snapshot',
	},
	search: false,
	caption: 'Link Snapshot',
	captionPlural: 'Link Snapshots',
	addCaption: 'Add Link Snapshot',
	newCaption: 'New Link Snapshot',
	validation: require('./validation.js'),
	acl: require('./acl.js'),
	excludeFromAggregation: true,
	fields: [
		{
			field: parentField,
			type: 'case',
			kind: 'system',
			caption: 'Linked From',
		},
		{
			field: 'status',
			type: 'picklist',
			caption: 'Status',
			kind: 'system',
			typeOptions: {
				picklistName: 'link_snapshot_status',
			},
			dbIndex: true,
		},
		{
			field: 'userId',
			type: 'user',
			kind: 'system',
			caption: 'User',
		},
	],
	parents: [
		{
			entity: parentEntity,
			field: parentField,
		},
	],
	audit: {
		child: true,
		parentType: parentEntity,
		parentFieldId: parentField,
		allowNavigateTo: true,
		cmd: {
			load: {
				'viewed': true,
			},
			save: {
				'created': true,
				'updated': {
					options: {
						changes: {
							excludeFields: ['id'],
						},
					},
				},
			},
			remove: {
				'deleted': true,
			},
		},
	},
});
