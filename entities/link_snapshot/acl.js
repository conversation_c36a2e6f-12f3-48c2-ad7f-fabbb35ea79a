var permHelper = require('../../lib/core/permission-helper.js');

module.exports = permHelper.initialize()
	.requireCaseLoadInheritance()
	.requireCaseSaveInheritance()
	.required({
		name: 'Create Link Snapshot',
		roles: ['create_link_snapshot'],
		actions: ['save_new', 'save_existing'],
		conditions: [{
			attributes: {
				'id': null,
			},
		}],
	})
	.required({
		name: 'Edit Link Snapshot',
		roles: ['edit_link_snapshot'],
		actions: ['save_new', 'save_existing'],
		conditions: [{
			attributes: {
				'!id': null,
			},
		}],
	})
	.required({
		name: 'Remove Link Snapshot',
		roles: ['remove_link_snapshot'],
		actions: ['remove'],
		conditions: [],
	})

	.required({
		name: 'Remove Link Snapshot',
		roles: ['view_link_snapshot'],
		actions: ['load', 'list'],
		conditions: [],
	})
	.value();
