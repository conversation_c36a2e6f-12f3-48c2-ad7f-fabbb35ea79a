const extend = require('../extend.js');
const standardConfig = require('../standard-config.js');

module.exports = extend(standardConfig, {
	db: 'default',
	table: 'sys_theme',
	entity: {
		base: 'sys',
		name: 'theme',
	},
	api: {
		useGenericApi: true,
	},
	caption: 'Theme',
	captionPlural: 'Themes',
	addCaption: 'Add Theme',
	newCaption: 'New Theme',
	gridDescriptorField: 'name',
	acl: require('./acl.js'),
	aclHierarchy: require('./acl-hierarchy.js'),
	grids: require('./grids.js'),
	validation: require('./validation.js'),
	importTransformer: 'theme',
	configurationExport: true,
	exportTransformer: 'theme',
	historyNav: require('./history-nav.js'),
	usageStatistics: require('./usage-statistics.js'),
	audit: require('./audit.js'),
	model() {
		return require('../../public/models/theme-model.js');
	},
	collection() {
		return require('../../public/collections/themes-collection.js');
	},
	view() {
		return require('../../public/views/settings/system/themes/theme-details-view.js');
	},
	primaryConfig: require('./primary-config.js'),
	fields: [
		{
			field: 'name',
			type: 'textbox',
			caption: 'Name',
			kind: 'editable',
		},
		{
			field: 'description',
			type: 'textbox',
			caption: 'Description',
			kind: 'editable',
		},
		{
			field: 'default',
			type: 'checkbox',
			caption: 'Default',
			kind: 'editable',
		},
		{
			field: 'logo',
			type: 'imageCropper',
			kind: 'editable',
			search: false,
			caption: 'Logo',
		},
		{
			field: 'primary',
			type: 'color',
			kind: 'editable',
			caption: 'Primary Color',
			cellTemplate(fs) {
				return fs.readFileSync(
					`${__dirname}/cell-templates/theme-color-cell-tmpl.dust`,
					'utf8',
				);
			},
		},
		{
			field: 'accent',
			type: 'color',
			kind: 'editable',
			caption: 'Accent Color',
			cellTemplate(fs) {
				return fs.readFileSync(
					`${__dirname}/cell-templates/theme-color-cell-tmpl.dust`,
					'utf8',
				);
			},
		},
		{
			field: 'complementary1',
			type: 'color',
			kind: 'editable',
			caption: 'Complementary Color 1',
			cellTemplate(fs) {
				return fs.readFileSync(
					`${__dirname}/cell-templates/theme-color-cell-tmpl.dust`,
					'utf8',
				);
			},
		},
		{
			field: 'complementary2',
			type: 'color',
			kind: 'editable',
			caption: 'Complementary Color 2',
			cellTemplate(fs) {
				return fs.readFileSync(
					`${__dirname}/cell-templates/theme-color-cell-tmpl.dust`,
					'utf8',
				);
			},
		},
		{
			field: 'readOnly',
			type: 'checkbox',
			caption: 'Read Only',
			kind: 'editable',
		},
		{
			field: 'additionalCss',
			type: 'textbox',
			caption: 'Additional CSS',
			kind: 'hidden',
		},
	],
});
