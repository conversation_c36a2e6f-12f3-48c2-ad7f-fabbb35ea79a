const permHelper = require('../../lib/core/permission-helper.js');

module.exports = [{
	type: 'group',
	caption: 'Themes',
	parentPermission: 'view_system_settings',
	permission: 'view_theme_settings',
	options: [
		{
			caption: 'Create',
			permission: 'create_theme',
			tooltip: 'Create themes',
			sequence: 1,
		},
		{
			caption: 'View',
			permission: 'view_theme',
			tooltip: 'View themes',
			sequence: 2,
			disabled: true,
		},
		{
			type: 'group',
			caption: 'Edit',
			permission: permHelper.getEditGroupPermissionCode('themes'),
			sequence: 3,
			options: [{
				caption: 'Save',
				permission: 'edit_theme',
				tooltip: 'Edit themes',
			}],
		},
		{
			caption: 'Remove',
			permission: 'remove_theme',
			tooltip: 'Delete themes',
			sequence: 4,
		},
	],
}];
