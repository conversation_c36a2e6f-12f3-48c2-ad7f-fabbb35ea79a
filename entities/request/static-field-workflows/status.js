module.exports = {
	name: 'request-status-workflow',
	field: 'status',
	strict: true,
	entity: {
		zone: undefined,
		base: 'sys',
		name: 'request',
	},
	values: [
		{
			value: 'sent',
			indicator: 'warning',
		},
		{
			value: 'complete',
			indicator: 'success',
		},
		{
			value: 'failed_delivery',
			indicator: 'danger',
		},
		{
			value: 'expired',
			indicator: 'danger',
		},
		{
			value: 'cancelled',
			indicator: 'danger',
		},
		{
			value: 'declined',
			indicator: 'danger',
		},
	],
	transitions: [
		{
			id: 'request-status-initial',
			from: [null, undefined],
			to: 'sent',
		},
		{
			id: 'request-status-sent',
			from: 'sent',
			to: ['failed_delivery', 'complete', 'declined', 'cancelled', 'expired'],
		},
		{
			id: 'request-status-failed_delivery',
			from: 'failed_delivery',
			to: 'sent',
		},
		{
			id: 'request-status-expired',
			from: 'expired',
			to: 'sent',
		},
		{
			id: 'request-status-cancelled',
			from: 'cancelled',
			to: 'sent',
		},
		{
			id: 'request-status-declined',
			from: 'declined',
			to: 'sent',
		},
	],
	conditions: [],
};
