module.exports = [{
	type: 'group',
	features: ['nonUserCollaboration'],
	caption: 'Requests',
	permission: 'request',
	parentPermission: 'case',
	options: [
		{
			caption: 'Send',
			tooltip: 'Send requests',
			sequence: 1,
			permission: 'create_request',
		},
		{
			caption: 'View',
			tooltip: 'View requests',
			sequence: 2,
			permission: 'view_request',
		},
		{
			caption: 'Remove',
			tooltip: 'Delete requests',
			sequence: 3,
			permission: 'remove_request',
			dependencies: ['view_request'],
		},
		{
			caption: 'Edit',
			tooltip: 'Edit requests',
			sequence: 4,
			permission: 'edit_request',
			dependencies: ['view_request'],
		},
		{
			caption: 'Cancel',
			tooltip: 'Cancel requests',
			sequence: 5,
			permission: 'cancel_request',
			dependencies: ['view_request'],
		},
	],
}];
