const _ = require('lodash');
const permHelper = require('../../lib/core/permission-helper.js');

function hasViewResponseFormPermission(entityId, roles) {
	return roles.some(role => role === `responseForm-view:${entityId}`);
}

function buildFormConditionFn() {
	return (obj, context) => {
		const { recordEntityId } = obj;
		if (!recordEntityId) return { ok: true };
		const { user } = context;
		const roles = user?.perm?.roles;
		if (!roles || roles.length === 0) return { ok: true };
		if (hasViewResponseFormPermission(recordEntityId, roles)) {
			return { ok: false };
		}
		return { ok: true };
	};
}

module.exports = permHelper.initialize()
	.filterMarkForPurge()
	.filterPurge()
	.required({
		name: 'View Request',
		roles: ['view_request'],
		actions: ['load', 'list'],
		conditions: [
			{ attributes: { '!id': null }},
		],
	})
	.required({
		name: 'Create Request',
		roles: ['create_request'],
		actions: ['save_new'],
		conditions: [
			{ attributes: { id: null }},
		],
	})
	.filter({
		/**
		 * During any save, if the user does not have the cancel_request permission
		 * they can NOT update the status field to cancelled
		 */
		name: 'Cancel Request Filter',
		roles: ['cancel_request'],
		actions: ['save_new'],
		conditions: [
			{ attributes: { '!id': null } },
		],
		filters: {
			// DONT Allow status to be set if status is being changed to cancelled
			fn(obj) {
				if (obj.status === 'cancelled') return {status: false};
				return {};
			},
		},
	})
	.filter({
		/**
		 * During any save, if the user does not have the edit_request permission
		 * they can update the status field to ONLY cancelled
		 */
		name: 'Edit Request Filter',
		roles: ['edit_request'],
		actions: ['save_new'],
		conditions: [
			{ attributes: { '!id': null } },
		],
		filters: {
			// DON'T Allow status to be set if status is NOT being changed to cancelled
			fn(obj) {
				if (obj.status !== 'cancelled') return {status: false};
				return {};
			},
		},
	})
	.required({
		name: 'Edit Request',
		roles: ['edit_request', 'cancel_request'],
		actions: ['save_new'],
		conditions: [
			{ attributes: { '!id': null } },
		],
	})
	.required({
		name: 'Remove Request',
		roles: ['remove_request'],
		actions: ['remove'],
		conditions: [],
	})
	.filter({
		name: 'View Request Verification Code',
		roles: ['view_request_verification_code'],
		actions: ['load', 'list'],
		conditions: [],
		filters: {
			verificationCode: false,
		},
	})
	.required({
		name: 'Require Response Form View ACL',
		roles: ['bypass_inherited_acl'],
		actions: ['load', 'list', 'save_new', 'save_existing', 'remove'],
		conditions: [{
			fn: buildFormConditionFn(),
			knexFilter(knex, knexion) {
				const permissionPrefix = 'responseForm-view:';
				const accessibleEntityIds = knexion
					.select(knexion.raw(`(REGEXP_MATCHES(code, '${permissionPrefix}(.+)'))[1]`))
					.from('vw_user_permissions')
					.where('user_id', knexion.raw('u.id'))
					.whereRaw(`code SIMILAR TO '${permissionPrefix}%'`);
				knex.whereNotIn('record_entity_id', accessibleEntityIds);
			},
			esFilter(context, attributePrefix = '') {
				const { user } = context;
				const roles = user?.perm?.roles;
				const accessibleEntityIds = [];
				roles.forEach((permissionCode) => {
					if (permissionCode?.includes('responseForm-view:')) {
						accessibleEntityIds.push(permissionCode.split(':')[1]);
					}
				});
				if (accessibleEntityIds.length === 0) {
					return {
						bool: {
							must: [
								{
									exists: {
										field: 'id',
									},
								},
							],
						},
					};
				}
				const esFilter = { bool: { must_not: [] }};
				_.each(accessibleEntityIds, (entityId) => {
					const filterTerm = { term: {[`${attributePrefix}recordEntityId._exact`]: entityId }};
					esFilter.bool.must_not.push(filterTerm);
				});
				return esFilter;
			},
			selectedFields(opts, callback) {
				return callback(null, ['id', 'recordEntityId']);
			},
		}],
	})
	.requireCaseLoadInheritance()
	.requireCaseSaveInheritance()
	.value();
