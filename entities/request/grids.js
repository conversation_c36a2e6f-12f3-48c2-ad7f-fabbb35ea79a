module.exports = {
	'case-requests-grid': {
		sortColumn: 'lastSentDate',
		sortOrder: 'desc',
		columns: [
			{ field: 'recordEntityId' },
			{ field: 'recipient' },
			{ field: 'dueDate' },
			{ field: 'lastSentDate' },
			{ field: 'verified' },
			{ field: 'status' },
		],
		defaultDynamicDataFilters: ['status'],
	},
	'advanced-search-result-request': {
		sortColumn: 'lastSentDate',
		sortOrder: 'desc',
		columns: [
			{ field: 'recordEntityId' },
			{ field: 'recipient' },
			{ field: 'dueDate' },
			{ field: 'lastSentDate' },
			{ field: 'verified' },
			{ field: 'status' },
		],
		defaultDynamicDataFilters: ['status'],
	},
	'search-result-request-purge': {
		sortColumn: 'lastSentDate',
		sortOrder: 'desc',
		columns: [
			{ field: 'recordEntityId' },
			{ field: 'recipient' },
			{ field: 'dueDate' },
			{ field: 'lastSentDate' },
			{ field: 'verified' },
			{ field: 'status' },
		],
		defaultDynamicDataFilters: ['status'],
	},
	'search-result-request-schedule-purge': {
		sortColumn: 'lastSentDate',
		sortOrder: 'desc',
		columns: [
			{ field: 'recordEntityId' },
			{ field: 'recipient' },
			{ field: 'dueDate' },
			{ field: 'lastSentDate' },
			{ field: 'verified' },
			{ field: 'status' },
		],
		defaultDynamicDataFilters: ['status'],
	},
};
