const extend = require('../extend.js');
const standardChildConfig = require('../standard-child-config.js');

module.exports = extend(standardChildConfig, {
	db: 'default',
	table: 'sys_request',
	entity: {
		base: 'sys',
		name: 'request',
	},
	features: ['nonUserCollaboration'],
	customForm: false,
	ruleEvents: true,
	// report: true, TODO :: ITPL-37233
	caption: 'Request',
	captionPlural: 'Requests',
	addCaption: 'Add Request',
	newCaption: 'New Request',
	acl: require('./acl.js'),
	aclHierarchy: require('./acl-hierarchy.js'),
	icon: 'fa-envelope-square',
	staticFieldWorkflows: [
		require('./static-field-workflows/status.js'),
	],
	model(){
		return require('../../public/models/request-model.js');
	},
	collection() {
		return require('../../public/collections/request-collection.js');
	},
	validation: require('./validation.js'),
	grids: require('./grids.js'),
	historyNav: require('./history-nav.js'),
	fields: [
		{
			field: 'status',
			type: 'picklist',
			kind: 'system',
			caption: 'Status',
			typeOptions: {
				picklistName: 'request_statuses',
			},
			cellTemplate(fs) {
				return fs.readFileSync(`${__dirname}/cell-templates/status-cell-tmpl.dust`, 'utf8');
			},
		},
		{
			field: 'sendTo',
			type: 'emailLookup[]',
			kind: 'virtual',
			caption: 'Send To',
			typeOptions: {
				linkWithSystemUser: false,
			},
		},
		{
			field: 'recipient',
			type: 'email',
			kind: 'system',
			caption: 'Sent To',
		},
		{
			field: 'subject',
			type: 'textbox',
			caption: 'Subject',
			kind: 'editable',
			gridWidth: 150,
		},
		{
			field: 'dueDate',
			type: 'date',
			kind: 'editable',
			caption: 'Due Date',
			typeOptions: {
				datePickerEndDate: '+1y',
				datePickerStartDate: '0d',
			},
		},
		{
			field: 'emailReminder',
			type: 'numberRangePicklist',
			caption: 'Send email reminder',
			kind: 'editable',
			typeOptions: {
				startValue: 0,
				endValue: 30,
			},
		},
		{
			field: 'message',
			type: 'texteditor',
			caption: 'Request Message',
			kind: 'editable',
		},
		{
			field: 'record',
			type: 'id',
			caption: 'Response Form',
			kind: 'editable',
			cellTemplate(fs) {
				return fs.readFileSync(
					`${__dirname}/cell-templates/record-cell-tmpl.dust`,
					'utf8',
				);
			},
		},
		{
			field: 'recordEntityCanon',
			type: 'entityPicklist',
			caption: 'Response Form Canon',
			kind: 'virtual',
			typeOptions: {
				valueField: 'canon',
				canCreate: false,
				filter: 'recordEntityTypeFilter',
				entityCategory: 1,
			},
		},
		{
			field: 'recordEntityId',
			type: 'entityPicklist',
			caption: 'Response Form Type',
			kind: 'submit-only',
			typeOptions: {
				valueField: 'id',
				canCreate: false,
				filter: 'recordEntityTypeFilter',
				entityCategory: 1,
			},
		},
		{
			field: 'verificationCode',
			type: 'textbox',
			caption: 'Verification Code',
			kind: 'hidden',
		},
		{
			field: 'verified',
			type: 'yesno',
			caption: 'Verified',
			kind: 'system',
		},
		{
			field: 'lastSentDate',
			type: 'datetime',
			caption: 'Last Sent Date',
			kind: 'system',
		},
		{
			field: 'timesSent',
			type: 'number',
			caption: 'Times Sent',
			kind: 'system',
		},
		{
			field: 'expiryJobId',
			type: 'textbox',
			caption: 'Expiry Job Id',
			kind: 'hidden',
			excludeFromSaveAndCopy: true,
		},
	],
	joins: [
		{
			referenceField: 'record',
			table: 'sys_dynamic_entity_data',
			fields: [
				'number',
			],
		},
	],
});
