const permHelper = require('../../lib/core/permission-helper.js');

module.exports = permHelper.initialize()
	.required({
		name: 'View Packets',
		roles: ['view_packet'],
		actions: ['load', 'list', 'save_existing', 'remove'],
		conditions: [],
	})
	.required({
		name: 'Create Packets',
		roles: ['create_packet'],
		actions: ['save_new'],
		conditions: [{
			attributes: {
				id: null,
			},
		}],
	})
	.required({
		name: 'Create Packets',
		roles: ['create_packet'],
		actions: ['save_existing'],
		conditions: [{
			attributes: {
				'!id': null,
				sysSubmitted: false,
			},
		}],
	})
	.required({
		name: 'Edit Packets',
		roles: ['edit_packet'],
		actions: ['save_existing'],
		conditions: [{
			attributes: {
				'!id': null,
				sysSubmitted: true,
			},
		}],
	})
	.required({
		name: 'Remove Packets',
		roles: ['remove_packet'],
		actions: ['remove'],
		conditions: [],
	})
	.value();
