const permHelper = require('../../lib/core/permission-helper.js');

module.exports = [{
	type: 'group',
	caption: 'Packets',
	parentPermission: 'view_data_settings',
	permission: 'packet_settings',
	options: [
		{
			caption: 'View',
			sequence: 2,
			permission: 'view_packet_settings',
			tooltip: 'View packets',
		},
		{
			permission: 'create_packet',
			caption: 'Create',
			tooltip: 'Create packets',
			sequence: 1,
			dependencies: ['view_packet_settings'],
		},
		{
			type: 'group',
			permission: permHelper.getEditGroupPermissionCode('packet'),
			caption: 'Edit',
			sequence: 3,
			dependencies: ['view_packet_settings'],
			options: [{
				permission: 'edit_packet',
				caption: 'Save',
				tooltip: 'Edit packets',
			}],
		},
		{
			permission: 'remove_packet',
			caption: 'Remove',
			tooltip: 'Delete packets',
			sequence: 4,
			dependencies: ['view_packet_settings'],
		},
	],
}];
