const extend = require('../extend.js');
const standardConfig = require('../standard-config.js');
const packetConfig = require('../standard-packet-config.js');

module.exports = extend(extend(standardConfig, packetConfig), {
	db: 'default',
	table: 'sys_packet',
	entity: {
		base: 'sys',
		name: 'packet',
	},
	api: {
		useGenericApi: true,
	},
	caption: 'Packet',
	captionPlural: 'Packets',
	addCaption: 'Add Packet',
	newCaption: 'New Packet',
	gridDescriptorField: 'name',
	acl: require('./acl.js'),
	aclHierarchy: require('./acl-hierarchy.js'),
	grids: require('./grids.js'),
	validation: require('./validation.js'),
	historyNav: require('./history-nav.js'),
	configurationExport(ctx = {}){
		return ctx.pdftronEnabled === true;
	},
	configurationImport(ctx = {}){
		return ctx.pdftronEnabled === true;
	},
	exportTransformer: 'packet',
	audit: require('./audit.js'),
	bypassValidationOnDraft: true,
	rules: require('./rules.js'),
	usageStatistics: require('./usage-statistics.js'),
	model() {
		return require('../../public/models/packet-model.js');
	},
	collection() {
		return require('../../public/collections/packets-collection.js');
	},
	view() {
		return require('../../public/views/settings/data/packets/packet-details-view.js');
	},
	fields: [
	],
});
