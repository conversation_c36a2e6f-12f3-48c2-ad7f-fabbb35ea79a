# Packets

Packets, as defined by this entity, are administration definitions of how to generate collections of documents.

For Case end users Packets are these documents themselves. Specifically a "Packet" is a PDF file that consists of several types of Files merged together into a large, aggregate file that is suitable for describing a collection of Case data or distribution.

This administration entity refered to as "Packets" consists of a Name, a series of Templates (see `sys/packet_template`) and a series of options that alter behaviour upon generation.

A user generates a Packet by adding a "File" (`sys/attachment`) to a Case of "Kind" "Packet", selecting a specific, named Packet and submitting (saving).

See the Packets service/plugin for behaviour details (`plugins/packets`).

## Templates

When a user chooses to generate a Packet it will automatically generate a series of Templates if defined to be included in the Packet. These documents will NOT be individually attached to the Case as separate Files. Only the aggregate Packet File will be.

The relationship (join table) of Packets to selected Templates to be included is defined by the `sys/packet_template` entity.
