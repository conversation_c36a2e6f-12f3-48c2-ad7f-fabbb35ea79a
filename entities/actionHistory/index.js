const extend = require('../extend.js');
const standardAuditConfig = require('../standard-audit-config.js');

module.exports = extend(standardAuditConfig, {
	db: 'default',
	table: 'sys_action_history',
	entity: {
		base: 'sys',
		name: 'action_history',
	},
	caption: 'Action History',
	captionPlural: 'Action History Items',
	addCaption: 'Add Action History',
	newCaption: 'New Action History',
	gridDescriptorField: 'triggeredOn',
	allowPurge: true,
	audit: false,
	acl: require('./acl.js'),
	aclHierarchy: require('./acl-hierarchy.js'),
	grids: require('./grids.js'),
	model() {
		return require('../../public/models/action-history-model.js');
	},
	collection() {
		return require('../../public/collections/action-history-collection.js');
	},
	joins: [
		{
			referenceField: 'ruleId',
			table: 'sys_rule',
			fields: [
				'name',
			],
		},
	],
	gridFilterExcludeFields: ['actionType'],
	fields: [
		{
			field: 'actionType',
			type: 'entityName',
			kind: 'system',
			caption: 'Action',
		},
		{
			field: 'ruleId',
			type: 'id',
			kind: 'system',
			caption: 'Rule',
			cellTemplate(fs) {
				return fs.readFileSync(`${__dirname}/cell-templates/rule-id-cell-tmpl.dust`, 'utf8');
			},
		},
		{
			field: 'triggeredOn',
			type: 'datetime',
			kind: 'system',
			excludeFromRedact: true,
			caption: 'Triggered On',
		},
		{
			field: 'triggeredBy',
			type: 'user',
			kind: 'system',
			caption: 'Triggered By',
		},
		{
			field: 'eventName',
			type: 'code',
			kind: 'system',
			caption: 'Event',
		},
		{
			field: 'summary',
			type: 'textarea',
			caption: 'Summary',
			kind: 'custom',
			kindOptions: {
				flags: {
					audit: false,
					schema: false,
					search: false,
					apiWritable: false,
					apiExternalWritable: false,
					computedOnSave: false,
					computedOnRead: false,
					formVisible: false,
					gridVisible: true,
					gridSortable: false,
					searchVisible: false,
					formattedData: false,
					gridExportable: false,
					reportable: false,
				},
			},
			cellTemplate(fs) {
				return fs.readFileSync(`${__dirname}/cell-templates/summary-cell-tmpl.dust`, 'utf8');
			},
		},
		{
			field: 'flag',
			type: 'textbox',
			kind: 'system',
			caption: 'Flag',
		},
		{
			field: 'targetUser',
			type: 'user',
			kind: 'hidden',
			caption: 'Target User',
		},
		{
			field: 'distribution',
			type: 'textbox[]',
			kind: 'system',
			caption: 'Distribution',
		},
		{
			field: 'recordContext',
			type: 'json',
			kind: 'hidden',
			caption: 'Record Context',
			alwaysInApiSelectedFields: true,
		},
		{
			field: 'triggerType',
			type: 'picklist',
			kind: 'system',
			caption: 'Trigger Type',
			default: 'Immediate',
			typeOptions: {
				picklistName: 'trigger_types',
			},
		},
		{
			field: 'targetUser',
			type: 'user',
			kind: 'system',
			caption: 'Target User',
		},
	],
});
