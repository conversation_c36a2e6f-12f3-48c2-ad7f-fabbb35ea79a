const permHelper = require('../../lib/core/permission-helper.js');

module.exports = [{
	type: 'group',
	caption: 'Action History',
	permission: 'action_history',
	disabled: true,
	options: [
		{
			permission: 'view_action_history',
			caption: 'View',
			tooltip: 'View Action History',
			disabled: true,
			sequence: 2,
		},
		{
			permission: 'create_action_history',
			caption: 'Create',
			tooltip: 'Add Action History',
			disabled: true,
			sequence: 1,
		},
		{
			type: 'group',
			permission: permHelper.getEditGroupPermissionCode('action_history'),
			caption: 'Edit',
			sequence: 3,
			dependencies: ['view_action_history'],
			options: [{
				permission: 'edit_action_history',
				caption: 'Save',
				tooltip: 'Edit Action History',
				disabled: true,
				dependencies: ['view_action_history'],
			}],
		},
		{
			permission: 'remove_action_history',
			caption: 'Remove',
			tooltip: 'Delete Action History',
			sequence: 4,
			disabled: true,
			dependencies: ['view_action_history'],
		},
	],
}];
