const permHelper = require('../../lib/core/permission-helper.js');

module.exports = permHelper.initialize()
	.filterMarkForPurge()
	.filterPurge()
	.required({
		name: 'View Action History',
		roles: ['view_action_history'],
		actions: ['load', 'list', 'save_existing', 'remove'],
		conditions: [],
	})
	.required({
		name: 'Create Action History',
		roles: ['create_action_history'],
		actions: ['save_new'],
		conditions: [{
			attributes: {
				id: null,
			},
		}],
	})
	.required({
		name: 'Edit Action History',
		roles: ['edit_action_history'],
		actions: ['save_new'],
		conditions: [{
			attributes: {
				'!id': null,
			},
		}],
	})
	.required({
		name: 'Remove Action History',
		roles: ['remove_action_history'],
		actions: ['remove'],
		conditions: [],
	})
	.required({
		name: 'View Case History',
		roles: ['view_case_history'],
		actions: ['load', 'list', 'save_new', 'save_existing', 'remove'],
		conditions: [{
			attributes: {
				objectType: 'sys/case',
			},
		}],
	})
	.required({
		name: 'Inherit ACL of Case to which the action was done',
		roles: ['bypass_inherited_acl'],
		actions: ['load', 'list', 'save_new', 'save_existing', 'remove'],
		conditions: [{
			attributes: {
				objectType: 'sys/case',
			},
		}, 'sys/case::{objectId}::load'],
	})
	.required({
		name: 'Access Action History of actions done to non-Case entities',
		roles: ['view_non_case_action_history'],
		actions: ['load', 'list', 'save_new', 'save_existing', 'remove'],
		conditions: [{
			attributes: {
				'!objectType': 'sys/case',
			},
		}],
	})
	.value();
