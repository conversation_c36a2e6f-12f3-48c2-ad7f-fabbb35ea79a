const permHelper = require('../../lib/core/permission-helper.js');

module.exports = [{
	type: 'group',
	parentPermission: 'view_system_settings',
	caption: 'Integration Logs',
	permission: 'event',
	options: [
		{
			permission: 'view_event',
			caption: 'View',
			tooltip: 'View integration logs',
			sequence: 2,
		},
		{
			caption: 'Create',
			sequence: 1,
			disabled: true,
			permission: 'create_event',
		},
		{
			type: 'group',
			caption: 'Edit',
			sequence: 3,
			disabled: true,
			permission: permHelper.getEditGroupPermissionCode('event'),
			options: [{
				caption: 'Edit',
				disabled: true,
				permission: 'edit_event',
			}],
		},
		{
			caption: 'Remove',
			sequence: 4,
			disabled: true,
			permission: 'remove_event',
		},
	],
}];
