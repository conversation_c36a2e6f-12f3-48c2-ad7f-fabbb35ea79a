module.exports = {
	'main-events': {
		sortColumn: 'createdDate',
		sortOrder: 'desc',
		columns: [
			{ field: 'createdDate' },
			{ field: 'eventType' },
			{ field: 'name' },
			{ field: 'status' },
			{ field: 'errorCount' },
		],
		defaultDynamicDataFilters: ['eventType', 'createdDate', 'lastUpdatedDate'],
	},
	'event-details': {
		sortColumn: 'createdDate',
		sortOrder: 'asc',
		columns: [
			{ field: 'createdDate' },
			{ field: 'eventDetails' },
			{ field: 'status' },
		],
	},
};
