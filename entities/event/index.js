var extend = require('../extend.js');
var standardConfig = require('../standard-config.js');

module.exports = extend(standardConfig, {
	db: 'default',
	table: 'sys_event',
	entity: {
		base: 'sys',
		name: 'event',
	},
	rules: require('./rules.js'),
	caption: 'Event',
	captionPlural: 'Events',
	addCaption: 'Add Event',
	newCaption: 'New Event',
	acl: require('./acl.js'),
	aclHierarchy: require('./acl-hierarchy.js'),
	grids: require('./grids.js'),
	usageStatistics: require('./usage-statistics.js'),
	model: function(){
		return require('../../public/models/event-model.js');
	},
	collection: function(){
		return require('../../public/collections/event-collection.js');
	},
	view: function(){
		return require('../../public/views/settings/system/integration-log/integration-log-details-view.js');
	},
	fields: [
		{
			field: 'jobId',
			type: 'textbox',
			kind: 'system',
			caption: 'Job Id',
			dbIndex: true,
			alwaysInApiSelectedFields: true,
		},
		{
			field: 'child',
			type: 'checkbox',
			typeOptions: {
				allowNull: true,
			},
			kind: 'system',
			caption: 'Child',
		},
		{
			field: 'eventType',
			type: 'picklist',
			kind: 'system',
			caption: 'Type',
			typeOptions: {
				allowGridFilter: true,
				picklistName: 'event_types',
			},
			dbIndex: true,
		},
		{
			field: 'source',
			type: 'code',
			kind: 'system',
			caption: 'Source',
		},
		{
			field: 'status',
			type: 'code',
			kind: 'system',
			caption: 'Status',
		},
		{
			field: 'errorCount',
			type: 'number',
			kind: 'system',
			caption: 'Errors',
		},
		{
			field: 'errorDetails',
			type: 'textarea',
			kind: 'system',
			caption: 'Error Details',
		},
		{
			field: 'eventDetails',
			type: 'textarea',
			kind: 'system',
			caption: 'Details',
		},
		{
			field: 'name',
			type: 'textbox',
			kind: 'system',
			caption: 'Name',
		},
		{
			field: 'file',
			type: 'file[]',
			kind: 'editable',
			caption: 'Events Log',
			typeOptions: { vectorize: true },
			excludeFromSaveAndCopy: true,
		},
		{
			field: 'progressPercentage',
			type: 'textbox',
			kind: 'system',
			caption: 'Progress Percentage',
		},
	],
});
