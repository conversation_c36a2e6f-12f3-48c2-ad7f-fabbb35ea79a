# Integration Log

Integration Logs allow users to view the progress of any "jobs" such as integrations, migrations, imports, exports, etc,.

These logs can be viewed in the Integration Log section, under Settings.

There are two screens, the Integration Log screen, and a Integration Log Details screen.

The Integration Log screen displays jobs, and the Integration Log Details screen display the job's details.

For example, in a case import that consists of multiple excel files with many cases in them each, the import as a whole would be the job, and the individual files being imported would be the details.

## Integration Log Search

Logs in the grid can be filtered by typing in the search box located above the grid. The search operates by bringing all results that match the search input, followed by anything, for example:

The following columns, at least, support searching:

- Type
- Status

## Integration Log Grid

This grid contains logs of jobs that have completed (integrations/migrations/etc). It is sorted by the Date Occurred, showing the most recent jobs that happened at the top.

The grid has available, at least, the following columns:

- **Type**
  - Type/name of the job that happened, for example, 'Peoplesoft Import' being the type for Corning's weekly import of employee data. 
- **Status**
  - Status of the job. There are two statuses that are possible for the job, In-progress and Completed
    - **In-progress** is the status a job has when it beings, and it keeps this status until the whole job finishes.
    - **Completed** is the status a job has when it ends (with or without errors), and it is the final status for a job.
- **Date Occurred**
  - Date of when the job began/finished, depending on the status of the job. For example, for an In-progress job, the Date Occurred would represent the date it began.
- **Errors**
  - Number of errors that occurred during the job.

## Integration Log Details

This view displays the details of a job; the individual processes that happened in the job.

For example, in an import of cases, multiple excel files might be imported, and each file might contain many cases. So, it is important to track what happened with each individual file import; and this essentially makes up the details of the job.

There is a header above the grid, and this header displays a summary of the job; the type of the job, when it began/completed (dependent on the status) and its current status.
