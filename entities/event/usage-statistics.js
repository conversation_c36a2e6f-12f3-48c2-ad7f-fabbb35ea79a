const statHelper = require('../../shared/stat-helper.js')('sys_event');
const loggerOptions = require('../../config/options.event-logger.js');

module.exports = [
	{
		category: 'event',
		key: 'integrationLogEntries',
		query: statHelper.countActiveEntity(),
	},
	{
		category: 'event',
		key: 'uniqueIntegrationLogThreads',
		query: statHelper.countActiveDistinctField({
			where: {
				child: false,
			},
		}),
		options: {
			field: 'event_type',
		},
	},
	{
		category: 'event',
		key: 'integrationLogEntriesByJobType',
		query: statHelper.countActiveEntityByPicklistGroup(),
		options: {
			groupingField: 'event_type',
			picklistName: 'event_types',
			datasetTranslation(dataset, translate) {
				return statHelper.getPicklistDatasetTrans(this, dataset, translate);
			},
		},
	},
	{
		category: 'event',
		key: 'integrationLogEntriesWithoutError',
		query: statHelper.countActiveEntity({
			where() {
				this.whereNot('status', loggerOptions.errorStatus);
			},
		}),
	},
	{
		category: 'event',
		key: 'integrationLogEntriesWithError',
		query: statHelper.countActiveEntity({
			where() {
				this.where('status', loggerOptions.errorStatus);
			},
		}),
	},
	{
		category: 'event',
		key: 'totalConfigurationExportWithErrors',
		query: statHelper.countActiveEntity({
			where() {
				this.where('event_type', 'Configuration Export')
					.whereNotNull('error_count')
					.whereNot('error_count', 0);
			},
		}),
	},
	{
		category: 'event',
		key: 'totalConfigurationExportWithoutErrors',
		query: statHelper.countActiveEntity({
			where() {
				this.where('event_type', 'Configuration Export')
					.whereNotNull('error_count')
					.andWhere('error_count', 0);
			},
		}),
	},
	{
		category: 'event',
		key: 'totalConfigurationImportWithErrors',
		query: statHelper.countActiveEntity({
			where() {
				this.where('event_type', 'Configuration Import')
					.whereNotNull('error_count')
					.whereNot('error_count', 0);
			},
		}),
	},
	{
		category: 'event',
		key: 'totalConfigurationImportWithoutErrors',
		query: statHelper.countActiveEntity({
			where() {
				this.where('event_type', 'Configuration Import')
					.whereNotNull('error_count')
					.andWhere('error_count', 0);
			},
		}),
	},
];
