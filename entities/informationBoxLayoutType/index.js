const _ = require('lodash');
const extend = require('../extend.js');
const layoutEnt = require('../layout');

module.exports = extend(layoutEnt, {
	createTable: false,
	reuseEsIndex: {
		index: 'sys_layout',
		typeField: 'layoutType',
	},
	includeInElementCount: true,
	layoutType: 'information-box',
	entity: {
		base: 'sys',
		name: 'information_box_layout_type',
	},
	caption: 'Information Box',
	captionPlural: 'Information Boxes',
	addCaption: 'Add Information Box',
	newCaption: 'New Information Box',
	importTransformer: 'information_box_layout_type',
	exportTransformer: 'information_box_layout_type',
	customDependencies: entityService => _.map(entityService.getRootLayoutEntities(), 'entityCanon'),
	validation: require('./validation.js'),
	fields: [
		{
			field: 'caption',
			type: 'textarea',
			caption: 'Caption',
			kind: 'editable',
			typeOptions: {
				placeholder: false,
			},
		},
	],
});
