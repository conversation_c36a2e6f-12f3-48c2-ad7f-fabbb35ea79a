var extend = require('../extend.js');
var standardConfig = require('../standard-config.js');
const parentField = 'linkId';
const parentEntity = {
	base: 'sys',
	name: 'link',
};

module.exports = extend(standardConfig, {
	db: 'default',
	table: 'sys_link_match',
	entity: {
		base: 'sys',
		name: 'link_match',
	},
	caption: 'Link Match',
	captionPlural: 'Link Matches',
	addCaption: 'Add Link Match',
	newCaption: 'New Link Match',
	validation: require('./validation.js'),
	acl: require('./acl.js'),
	search: false,
	dataExport: true,
	includeInDataDictionary: true,
	usageStatistics: require('./usage-statistics.js'),
	fields: [
		{
			field: 'latestSnapshotId',
			type: 'id',
			kind: 'hidden',
			caption: 'Snapshot',
			dbIndex: true,
		},
		{
			field: parentField,
			type: 'id',
			kind: 'system',
			caption: 'Link',
			dbIndex: true,
		},
		{
			field: 'case1EntityId',
			type: 'id',
			kind: 'system',
			caption: 'Case 1 Record Link Id',
			dbIndex: true,
		},
		{
			field: 'case1Entity',
			type: 'json',
			kind: 'system',
			caption: 'Case 1 Record',
			alwaysInApiSelectedFields: true,
		},
		{
			field: 'case1EntityType',
			type: 'code',
			kind: 'system',
			caption: 'Case 1 Record Link Type',
			dbIndex: true,
			alwaysInApiSelectedFields: true,
		},
		{
			field: 'case1MatchHighlight',
			type: 'json',
			kind: 'system',
			caption: 'Case 1 Match Highlight',
		},
		{
			field: 'case2EntityId',
			type: 'id',
			kind: 'system',
			caption: 'Case 2 Record Link Id',
			dbIndex: true,
		},
		{
			field: 'case2Entity',
			type: 'json',
			kind: 'system',
			caption: 'Case 2 Record',
			alwaysInApiSelectedFields: true,
		},
		{
			field: 'case2EntityType',
			type: 'code',
			kind: 'system',
			caption: 'Case 2 Record Link Type',
			dbIndex: true,
			alwaysInApiSelectedFields: true,
		},
		{
			field: 'case2MatchHighlight',
			type: 'json',
			kind: 'system',
			caption: 'Case 2 Match Highlight',
		},
		{
			field: 'status',
			type: 'picklist',
			kind: 'editable',
			caption: 'Status',
			typeOptions: {
				picklistName: 'link_match_statuses',
			},
			dbIndex: true,
		},
		{
			field: 'relevance',
			type: 'decimal',
			kind: 'system',
			caption: 'Relevance',
			typeOptions: {
				format: '0%',
			},
		},
		{
			field: 'weightedHitFieldCount',
			type: 'decimal',
			kind: 'system',
			caption: 'Weighted Hit Field Count',
		},
	],
	parents: [
		{
			entity: parentEntity,
			field: parentField,
		},
	],
	audit: {
		child: true,
		parentType: parentEntity,
		parentFieldId: parentField,
		allowNavigateTo: true,
		cmd: {
			load: {
				'viewed': true,
			},
			save: {
				'created': true,
				'updated': {
					options: {
						changes: {
							excludeFields: ['id'],
						},
					},
				},
			},
			remove: {
				'deleted': true,
			},
		},
	},
});