const _ = require('lodash');
const statHelper = require('../../shared/stat-helper.js')('sys_link_match');

module.exports = [
	{
		category: 'link_match',
		key: 'suggestedMatches',
		query(knex, options, callback) {
			knex('sys_link_match')
				.leftJoin('sys_link', 'sys_link.id', 'sys_link_match.link_id')
				.count('* as count')
				.where('sys_link.type', 'Suggested')
				.where('sys_link_match.sys_active', true)
				.asCallback((err, rows) => {
					if (err) return callback(err);
					return callback(null, parseInt(_.first(rows).count, 10));
				});
		},
	},
	{
		category: 'link_match',
		key: 'suggestedMatchesByStatus',
		query(knex, options, callback) {
			knex('sys_link_match')
				.leftJoin('sys_link', 'sys_link.id', 'sys_link_match.link_id')
				.rightJoin('sys_listitem', 'sys_link_match.status', 'sys_listitem.value')
				.count('sys_link_match.id as count')
				.select('sys_listitem.value as groupName')
				.where('sys_link.type', 'Suggested')
				.orWhereNull('sys_link.type')
				.andWhere((queryBuilder) => {
					queryBuilder
						.where('sys_link_match.sys_active', true)
						.orWhereNull('sys_link_match.sys_active');
				})
				.andWhere((queryBuilder) => {
					queryBuilder
						.where('sys_listitem.sys_active', true)
						.orWhereNull('sys_listitem.sys_active');
				})
				.andWhere('sys_listitem.name', 'link_match_statuses')
				.groupBy('sys_listitem.value')
				.orderBy('sys_listitem.value')
				.asCallback((err, rows) => {
					if (err) return callback(err);
					const cleanedCounts = {};
					_.each(rows, (row) => {
						const group = row.groupName || 'notDefined';
						cleanedCounts[group] = parseInt(row.count, 10);
					});
					return callback(null, cleanedCounts);
				});
		},
		options: {
			picklistName: 'link_match_statuses',
			datasetTranslation(dataset, translate) {
				return statHelper.getPicklistDatasetTrans(this, dataset, translate);
			},
		},
	},
];
