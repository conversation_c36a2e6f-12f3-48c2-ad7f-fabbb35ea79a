var permHelper = require('../../lib/core/permission-helper.js');

module.exports = permHelper.initialize()
	.required({
		name: 'Inherit link',
		roles: ['bypass_inherited_acl'],
		control: 'required',
		actions: ['load', 'list', 'save_new', 'save_existing', 'remove'],
		conditions: ['sys/link::{linkId}'],
	})
	.required({
		name: 'Inherit linked case acl',
		roles: ['bypass_inherited_acl'],
		control: 'required',
		actions: ['load', 'list', 'save_new', 'save_existing', 'remove'],
		conditions: ['{case1EntityType}::{case1EntityId}::load'],
	})
	.required({
		name: 'Inherit linked case acl',
		roles: ['bypass_inherited_acl'],
		control: 'required',
		actions: ['load', 'list', 'save_new', 'save_existing', 'remove'],
		conditions: ['{case2EntityType}::{case2EntityId}::load'],
	})
	.filter({
		name: 'Can modify entity on existing link match',
		roles: ['link_match_modify_entity'],
		actions: ['save_new', 'save_existing'],
		filters: {
			'case1EntityId': false,
			'case2EntityId': false,
			'case1EntityType': false,
			'case2EntityType': false,
			'linkId': false,
		},
		conditions: [{
			attributes: {
				'!id': null,
			},
		}],
	})
	.entity({
		name: 'Clean link match entity fields',
		actions: ['load', 'list'],
		conditions: [],
		entityFields: [{
			name: 'case1Entity',
			type: 'case1EntityType',
		}, {
			name: 'case2Entity',
			type: 'case2EntityType',
		}],
	})
	.value();