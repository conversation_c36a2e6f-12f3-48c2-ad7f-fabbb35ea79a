const extend = require('./extend.js');
const standardConfig = require('./standard-config.js');

module.exports = extend(standardConfig, {
	enableTranslation: true,
	fields: [
		{
			field: 'submittedLanguage',
			type: 'picklist',
			typeOptions: {
				picklistName: 'supported_languages',
				filter: 'existingLanguagesFilter',
			},
			caption: 'Submitted Language',
			kind: 'system',
			showOnPortal: true,
			showOnHotline: true,
		},
		{
			field: 'intakeTranslationStatus',
			type: 'picklist',
			caption: 'Translation Status',
			typeOptions: {
				picklistName: 'intake_translation_statuses',
			},
			kind: 'system',
			features: ['intakeTranslation'],
			showOnPortal: true,
			showOnHotline: true,
		},
		{
			field: 'translationData',
			type: 'jsonb',
			caption: 'Translation Data',
			kind: 'hidden',
			showOnPortal: true,
			showOnHotline: true,
		},
		{
			field: 'translationLanguageByField',
			type: 'jsonb',
			caption: 'Translation Language By Field',
			kind: 'virtual',
			features: ['intakeTranslation'],
			showOnPortal: true,
			showOnHotline: true,
		},
	],
});
