
const validationUtil = require('../../plugins/entity-validator/utils');
// Limits for text based fields
const charTypeLimits = {
	textBoxMax: 255,
	textAreaMax: 10000,
	textFieldMin: 0,
};
/* eslint-disable max-len */
module.exports = {
	mandatory$: [
		'type',
		'caption',
		'kind',
	],
	dependentPattern$: [
		{
			fields: ['caption'],
			pattern: '^[a-zA-Z].*$',
			code: 'field_caption_pattern$',
			allowNull: true,
			condition: 'isKindDynamic',
		},
	],
	dependentMandatory$: [
		{
			condition: 'isKindDynamic',
			fields: [
				'entityId',
				'search',
				'dynamicKind',
			],
		},
		{
			condition: 'isRadio',
			fields: [
				'options',
			],
		},
		{
			condition: 'isNumberRangePicklist',
			fields: [
				'startValue',
				'endValue',
			],
		},
		{
			condition: 'isIselComputed',
			fields: ['iselExpression'],
		},
		{
			condition: 'isAggregateComputed',
			fields: [
				'childRecordType',
				'aggregationMethod',
			],
		},
		{
			condition: 'isAggregateComputed && !isAggregateExpression && aggregationMethodRequiresTarget',
			fields: ['targetField'],
		},
		{
			condition: 'isAggregateComputed && isAggregateExpression',
			fields: ['aggregateExpression'],
		},
		{
			condition: 'isCopilotType',
			fields: ['copilotType'],
		},
		{
			condition: 'isCopilotType && isRecommendationCopilotType',
			fields: ['copilotDocuments'],
		},
	],
	maxLength$: [
		{
			name: 'caption',
			value: 60,
		},
		{
			name: 'default',
			value: validationUtil.getCharMaxLength,
		},
	],
	minLength$: [
		{
			name: 'default',
			value: validationUtil.getCharMinLength,
		},
	],
	numberRange$: [
		{
			number: 'max',
			minimumNumber: {
				number: 'min',
			},
		},
		{
			number: 'max',
			maximumNumber: {
				// 15 digits
				number: 999999999999999,
			},
		},
		{
			number: 'charMaxTextbox',
			minimumNumber: {
				number: ['charMin', 0],
			},
			maximumNumber: {
				number: charTypeLimits.textBoxMax,
			},
		},
		{
			number: 'charMaxTextArea',
			minimumNumber: {
				number: ['charMin', 0],
			},
			maximumNumber: {
				number: charTypeLimits.textAreaMax,
			},
		},
		{
			number: 'charMin',
			maximumNumber: {
				number: [
					'charMaxTextArea',
					'charMaxTextbox',
					{
						textbox: charTypeLimits.textBoxMax,
						textarea: charTypeLimits.textAreaMax,
					},
				],
			},
			minimumNumber: {
				number: [charTypeLimits.textFieldMin, 0],
			},
		},
		{
			number: 'endValue',
			minimumNumber: {
				number: 'startValue',
			},
		},
	],
	dependentPhoneNumber$: [{
		condition: 'isKindDynamic && isPhoneNumber',
		fields: ['default'],
		allowNull: true,
	}],
	dependentNumberRange$: [
		{
			number: 'maxItems',
			minimumNumber: {
				number: 1,
			},
			customTranslationKey: 'max_email_number',
			condition: 'isEmailMultiple',
		},
		{
			number: 'precision',
			minimumNumber: {
				number: ['scale', 2],
			},
			maximumNumber: {
				number: 13,
			},
			condition: 'isMoney',
		},
		{
			number: 'precision',
			minimumNumber: {
				number: ['scale', 2],
			},
			maximumNumber: {
				number: 15,
			},
			condition: '!isMoney',
		},
		{
			number: 'scale',
			maximumNumber: {
				// money based dynamic fields must have max scale of 2 for db type restrictions
				number: ['precision', 2],
			},
			minimumNumber: {
				number: 0,
			},
			condition: 'isMoney',
		},
		{
			number: 'scale',
			maximumNumber: {
				// decimal based dynamic fields must have max scale of 6 for db type restrictions
				number: ['precision', 6],
			},
			minimumNumber: {
				number: 0,
			},
			condition: '!isMoney',
		},
	],
	expression$: [
		{
			field: 'iselExpression',
			ignoreIdentifiers: true,
		},
		{
			field: 'aggregateExpression',
			ignoreIdentifiers: true,
		},
	],
};
