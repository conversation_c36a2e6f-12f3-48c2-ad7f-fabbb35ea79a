const fieldTypes = require('../../field-types');

module.exports = {
	hasDependentMandatoryRule: function(data){
		return !!data.mandatoryWhen;
	},
	hasAlwaysMandatoryRule: function(data){
		return !!data.mandatory;
	},
	hasType: function(data){
		return !!data.type;
	},
	isBoolean: function(data){
		return data.type === 'boolean';
	},
	isCheckbox: function(data){
		return data.type === 'checkbox';
	},
	isDate: function(data){
		return data.type === 'date';
	},
	isDatetime: function(data){
		return data.type === 'datetime';
	},
	isDecimal: function(data){
		return data.type === 'decimal';
	},
	isEmail: function(data){
		return data.type === 'email';
	},
	isEmailMultiple: function(data){
		return data.type === 'email[]';
	},
	isFieldPicklist: function(data){
		return data.type === 'fieldPicklist';
	},
	isFieldPicklistMultiple: function(data){
		return data.type === 'fieldPicklist[]';
	},
	isInteger: function(data){
		return data.type === 'integer';
	},
	isMoney: function(data){
		return data.type === 'money';
	},
	isNumber: function(data){
		return data.type === 'number';
	},
	isNumberRangePicklist: function(data){
		return data.type === 'numberRangePicklist';
	},
	isPicklist: function(data){
		return data.type === 'picklist';
	},
	isPicklistApi: function(data){
		return data.type === 'picklistApi';
	},
	isPicklistSelectize: function(data){
		return data.type === 'picklistSelectize';
	},
	isPicklistSelectizeApi: function(data){
		return data.type === 'picklistSelectizeApi';
	},
	isPicklistApiMultiple: function(data){
		return data.type === 'picklistApi[]';
	},
	isPicklistMultiple: function(data){
		return data.type === 'picklist[]';
	},
	isCountry: function(data){
		return data.type === 'country';
	},
	isRadio: function(data){
		return data.type === 'radio';
	},
	isTime: function(data){
		return data.type === 'time';
	},
	istName: function(data){
		return data.type === 'tName';
	},
	isUser: function(data){
		return data.type === 'user';
	},
	isUserMultiple: function(data){
		return data.type === 'user[]';
	},
	isYesNo: function(data){
		return data.type === 'yesno';
	},
	isKindDynamic: function(data){
		return data.kind === 'dynamic';
	},
	isPhoneNumber: function(data) {
		return data.type === 'phone-number' || data.type === 'phone-number[]';
	},
	isIselComputed: function(data) {
		return data.dynamicKind === 'isel-computed';
	},
	isAggregateComputed: function(data) {
		return data.dynamicKind === 'aggregate';
	},
	isAggregateExpression: function(data) {
		return data.aggregationMethod === 'expression';
	},
	isEntityNameCase: function(data){
		return data.entityName === 'sys/case';
	},
	isEntityNameParty: function(data){
		return data.entityName === 'sys/party';
	},
	isEntityNamePerson: function(data){
		return data.entityName === 'sys/person';
	},
	isAutoLinkCase: function(data){
		return !!data.automaticallyCreateCaseLink;
	},
	displayLinkWithSystemParties: function(data) {
		return !['sys/user', 'sys/person'].includes(data.entityName) && data.dynamicKind !== 'isel-computed';
	},
	aggregationMethodRequiresTarget: function(data) {
		return !['atLeastOne', 'count'].includes(data.aggregationMethod);
	},
	isDataFormField(data) {
		return !!data.dataForm;
	},
	isDataLookupField(data) {
		return !!data.dataLookup;
	},
	isNew(data) {
		return !data.id;
	},
	isSubmitOnly(data) {
		return data.dynamicKind === 'submit-only';
	},
	isTranslatableFieldType(data) {
		return !!fieldTypes[data.type]?.enableTranslation;
	},
	isCopilotType(data) {
		return data.type === 'copilot';
	},
	isRecommendationCopilotType(data) {
		return data.copilotType === 'recommendation';
	},
};
