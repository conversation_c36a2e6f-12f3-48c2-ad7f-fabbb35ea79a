var permHelper = require('../../lib/core/permission-helper.js');

module.exports = permHelper.initialize()
	.required({
		name: 'View Fields',
		roles: ['view_field'],
		actions: ['load', 'list', 'save_existing', 'remove'],
		conditions: [],
	})
	.required({
		name: 'Create Fields',
		roles: ['create_field'],
		actions: ['save_new'],
		conditions: [
			{
				attributes: {
					id: null,
					'!kind': 'dynamic',
				},
			},
		],
	})
	.required({
		name: 'Create Dynamic Fields',
		roles: ['form_builder'],
		actions: ['save_new'],
		conditions: [
			{
				attributes: {
					id: null,
					kind: 'dynamic',
				},
			},
		],
	})
	.required({
		name: 'Edit Fields',
		roles: ['edit_field'],
		actions: ['save_existing'],
		conditions: [
			{
				attributes: {
					'!id': null,
					'!kind': 'dynamic',
				},
			}],
	})
	.required({
		name: 'Edit Dynamic Fields',
		roles: ['form_builder'],
		actions: ['save_existing', 'save_new'],
		conditions: [
			{
				attributes: {
					'!id': null,
					kind: 'dynamic',
				},
			},
		],
	})
	.required({
		name: '<PERSON>mo<PERSON>',
		roles: ['remove_field'],
		actions: ['remove'],
		conditions: [
			{
				attributes: {
					'!kind': 'dynamic',
				},
			},
		],
	})
	.required({
		name: 'Remove Dynamic Fields',
		roles: ['form_builder'],
		actions: ['remove'],
		conditions: [
			{
				attributes: {
					kind: 'dynamic',
				},
			},
		],
	})
	.required({
		name: 'Inherit caseiq entity ACL',
		roles: ['bypass_inherited_acl'],
		actions: ['load', 'list', 'save_new', 'save_existing', 'remove'],
		conditions: ['isight/entity::{entityId}::load'],
	})
	.value();
