const statHelper = require('../../shared/stat-helper.js')('isight_field');

module.exports = [
	{
		category: 'staticForms',
		key: 'caseDynamicFields',
		query: statHelper.countActiveEntity({
			where() {
				this
					.where('kind', 'dynamic')
					.whereNot('dynamic_kind', 'custom')
					.andWhere('entity_name', 'sys/case');
			},
		}),
	},
	{
		category: 'staticForms',
		key: 'profileDynamicFields',
		query: statHelper.countActiveEntity({
			where() {
				this
					.where('kind', 'dynamic')
					.andWhere('entity_name', 'sys/person');
			},
		}),
	},
	{
		category: 'staticForms',
		key: 'partyDynamicFields',
		query: statHelper.countActiveEntity({
			where() {
				this
					.where('kind', 'dynamic')
					.andWhere('entity_name', 'sys/party');
			},
		}),
	},
	{
		category: 'staticForms',
		key: 'computedFieldsPerForm',
		query: statHelper.countActiveEntity({
			where() {
				this.where('dynamic_kind', 'isel-computed');
			},
		}),
	},
	{
		category: 'staticForms',
		key: 'aggregatedFieldsPerForm',
		query: statHelper.countActiveEntity({
			where() {
				this.where('dynamic_kind', 'aggregate');
			},
		}),
	},
	{
		category: 'staticForms',
		key: 'coordinatesFieldsPerForm',
		query: statHelper.countActiveEntity({
			where() {
				this.where('type', 'coordinate')
					.whereNotIn('entity_name', ['isight/field', 'isight/dynamic_entity_field']);
			},
		}),
	},
	{
		category: 'staticForms',
		key: 'partyRestrictEditFields',
		query: statHelper.countActiveEntity({
			where() {
				this
					.where('kind', 'dynamic')
					.andWhere('entity_name', 'sys/party')
					.andWhere('restrict_edit', true);
			},
		}),
	},
	{
		category: 'staticForms',
		key: 'personRestrictEditFields',
		query: statHelper.countActiveEntity({
			where() {
				this
					.where('kind', 'dynamic')
					.andWhere('entity_name', 'sys/person')
					.andWhere('restrict_edit', true);
			},
		}),
	},
	{
		category: 'staticForms',
		key: 'caseRestrictEditFields',
		query: statHelper.countActiveEntity({
			where() {
				this
					.where('kind', 'dynamic')
					.andWhere('entity_name', 'sys/case')
					.andWhere('restrict_edit', true);
			},
		}),
	},
];
