var _ = require('lodash');
var extend = require('../extend.js');
var standardConfig = require('../standard-config.js');
const parentField = 'entityId';
const parentEntity = {
	base: 'isight',
	name: 'entity',
};
const reference = {
	displayFields: ['caption'],
};

module.exports = extend(standardConfig, {
	db: 'default',
	search: true,
	table: 'isight_field',
	entity: {
		base: 'isight',
		name: 'field',
	},
	caption: 'Field',
	captionPlural: 'Fields',
	addCaption: 'Add Field',
	newCaption: 'New Field',
	importTransformer: 'field',
	exportTransformer: 'field',
	customExportColumns: ['entityId__originalId', 'userRoleIdFilter__name'],
	copyTransformer: 'field',
	copyPostAction: 'field',
	customDependencies: (entityService) => {
		// Since we don't export/import "isight/entity", we'll include the root layout dependency here
		const rootLayoutTypes = _.map(entityService.getRootLayoutEntities(), 'entityCanon');
		return ['isight/entity', 'sys/user_role', ...rootLayoutTypes];
	},
	acl: require('./acl.js'),
	grids: require('./grids.js'),
	rules: require('./rules.js'),
	validation: require('./validation.js'),
	usageStatistics: require('./usage-statistics.js'),
	parents: [
		{
			entity: parentEntity,
			field: parentField,
		},
	],
	fields: [
		{
			field: 'source',
			type: 'code',
			caption: 'Source',
			kind: 'system',
		},
		{
			field: parentField,
			type: 'code',
			caption: 'Entity Id',
			kind: 'editable',
			dbIndex: true,
		},
		{
			field: 'field',
			type: 'code',
			caption: 'Field System Name',
			kind: 'system',
			dbIndex: true,
		},
		{
			field: 'caption',
			type: 'textbox',
			caption: 'Name',
			kind: 'editable',
			translateData: true,
			typeOptions: {
				// postgres column name char limit is 63
				charMaxTextbox: 60,
			},
		},
		{
			field: 'formCaption',
			type: 'textbox',
			caption: 'Caption',
			kind: 'editable',
			translateData: true,
		},
		{
			field: 'default',
			type: 'textbox',
			caption: 'Default Value',
			kind: 'editable',
			typeOptions: {
				/*
					This field is not of type textbox most of the time,
					Thus we bypass the default validation and add custom one in ./validation.js
				*/
				bypassDefaultTypeValidation: true,
			},
		},
		{
			field: 'type',
			type: 'code',
			caption: 'Field Type',
			kind: 'editable',
			dbIndex: true,
		},
		{
			field: 'typeOptions',
			type: 'json',
			caption: 'Type Options',
			kind: 'editable',
		},
		{
			field: 'cellTemplate',
			type: 'textarea',
			caption: 'Cell Template',
			kind: 'editable',
		},
		{
			field: 'kind',
			type: 'code',
			caption: 'Field Kind',
			kind: 'editable',
		},
		{
			field: 'kindOptions',
			type: 'json',
			caption: 'Kind Options',
			kind: 'system',
		},
		{
			field: 'customKindFlags',
			type: 'json',
			caption: 'Custom Kind Flags',
			kind: 'system',
		},
		{
			field: 'search',
			type: 'toggle',
			caption: 'Searchable',
			kind: 'editable',
		},
		{
			field: 'dbIndex',
			type: 'checkbox',
			caption: 'Database Index',
			kind: 'editable',
		},
		{
			field: 'dbIndexType',
			type: 'textbox',
			caption: 'Database Index Type',
			kind: 'editable',
		},
		{
			field: 'yfSourceFilter',
			type: 'checkbox',
			caption: 'Yellowfin Source Filter',
			kind: 'editable',
		},
		{
			field: 'createdBy',
			type: 'id',
			kind: 'system',
			caption: 'Created By',
		},
		{
			field: 'lastUpdatedBy',
			type: 'user',
			kind: 'system',
			caption: 'Last Updated By',
		},
		{
			field: 'deletedBy',
			type: 'id',
			kind: 'system',
			caption: 'Deleted By',
		},
		{
			field: 'excludeFromSaveAndCopy',
			type: 'checkbox',
			kind: 'editable',
			caption: 'Exclude From Save and Copy',
		},
		{
			field: 'excludeFromAutofill',
			type: 'checkbox',
			kind: 'editable',
			caption: 'Exclude From Autofill',
		},
		{
			field: 'gridWidth',
			type: 'number',
			kind: 'system',
			caption: 'Grid Width',
		},
		{
			field: 'minGridWidth',
			type: 'number',
			kind: 'system',
			caption: 'Min Grid Width',
		},
		{
			field: 'esSort',
			type: 'code[]',
			kind: 'system',
			caption: 'ES Sort',
		},
		{
			field: 'esBoost',
			type: 'number',
			kind: 'system',
			caption: 'ES Boost',
		},
		{
			field: 'mandatory',
			caption: 'Required',
			type: 'toggle',
			kind: 'editable',
		},
		{
			field: 'mandatoryWhen',
			caption: 'Required When',
			type: 'textarea',
			kind: 'editable',
		}, {
			field: 'specialNotes',
			caption: 'Special Notes',
			type: 'texteditor',
			kind: 'editable',
		},
		/* typeOptions */
		{
			field: 'picklistName',
			type: 'picklistName',
			caption: 'Picklist Name',
			kind: 'editable',
			typeOptions: {
				picklistName: 'picklist_types',
			},
		},
		{
			field: 'picklistDependencies',
			type: 'fieldPicklist[]',
			caption: 'Picklist Dependencies',
			kind: 'editable',
			typeOptions: {
				entityNameField: 'entityName',
			},
		},
		{
			field: 'directPicklistDependency',
			type: 'fieldPicklist[]',
			caption: 'Dependent Picklist',
			kind: 'editable',
			typeOptions: {
				translationGroup: 'picklist_type',
			},
		},
		{
			field: 'linkWithSystemUser',
			type: 'toggle',
			caption: 'Link With System User',
			typeOptions: {
				allowNull: true,
			},
			kind: 'editable',
		},
		{
			field: 'allowNull',
			caption: 'Allow Null',
			type: 'checkbox',
			kind: 'editable',
		},
		{
			field: 'hideCurrent',
			caption: 'Hide Current',
			type: 'checkbox',
			kind: 'editable',
		},
		{
			field: 'format',
			caption: 'Format',
			type: 'textbox',
			kind: 'editable',
		},
		{
			field: 'orientation',
			caption: 'Orientation',
			type: 'picklist',
			kind: 'editable',
			typeOptions: {
				allowNull: false,
				picklistName: 'radio_button_orientations',
			},
		},
		{
			//TODO: create a caption for this
			field: 'blankText',
			caption: 'Blank Text',
			type: 'textbox',
			kind: 'editable',
		},
		{
			field: 'ignoreParents',
			caption: 'Ignore Parents',
			type: 'checkbox',
			kind: 'editable',
		},
		{
			field: 'mask',
			caption: 'Mask',
			type: 'textbox',
			kind: 'editable',
		},
		{
			field: 'startValue',
			caption: 'Start Value',
			type: 'number',
			typeOptions: {
				max: 9999,
				min: -9999,
			},
			kind: 'editable',
		},
		{
			field: 'endValue',
			caption: 'End Value',
			type: 'number',
			typeOptions: {
				max: 9999,
				min: -9999,
			},
			kind: 'editable',
		},
		{
			field: 'entityNameField',
			caption: 'Entity Name Field',
			type: 'textbox',
			kind: 'editable',
		},
		{
			field: 'entityName',
			caption: 'Entity Name',
			type: 'textbox',
			kind: 'editable',
		},
		{
			field: 'refreshOnFields',
			caption: 'Refresh On Fields',
			type: 'fieldType[]',
			kind: 'editable',
		},
		{
			field: 'hiddenValues',
			caption: 'Hidden Values',
			type: 'picklist[]',
			kind: 'editable',
		},
		{
			field: 'excludedFields',
			caption: 'Excluded Fields',
			type: 'picklist[]',
			kind: 'editable',
		},
		{
			field: 'excludeKind',
			caption: 'Excluded Field Kinds',
			type: 'picklist[]',
			kind: 'editable',
		},
		{
			field: 'showBeforeAfter',
			caption: 'Show Before or After',
			type: 'yesno',
			kind: 'editable',
		},
		{
			field: 'filterFlag',
			caption: 'Filter Field Flag',
			type: 'picklist',
			kind: 'editable',
			typeOptions: {
				picklistName: 'field_kind_flags',
			},
		},
		{
			field: 'filterType',
			caption: 'Filter Field Type',
			type: 'fieldType[]',
			kind: 'editable',
		},
		{
			field: 'filterKind',
			caption: 'Filter Field Kinds',
			type: 'picklist[]',
			kind: 'editable',
		},
		{
			field: 'searchableFields',
			caption: 'Searchable Fields',
			type: 'checkbox',
			kind: 'editable',
		},
		{
			field: 'precision',
			caption: 'Precision',
			type: 'number',
			kind: 'editable',
		},
		{
			field: 'scale',
			caption: 'Decimal Places',
			type: 'number',
			kind: 'editable',
		},
		{
			field: 'max',
			caption: 'Maximum Value',
			type: 'number',
			kind: 'editable',
		},
		{
			field: 'min',
			caption: 'Minimum Value',
			type: 'number',
			kind: 'editable',
		},
		{
			field: 'maxItems',
			caption: 'Maximum Items',
			type: 'number',
			kind: 'editable',
		},
		{
			field: 'disableTimezoneTracking',
			caption: 'Disable Timezone Tracking',
			type: 'toggle',
			kind: 'editable',
		},
		{
			field: 'datePickerStartDate',
			caption: 'Date Picker Start Date',
			type: 'textbox',
			kind: 'editable',
		},
		{
			field: 'roleFilter',
			caption: 'Role Filter',
			type: 'textbox',
			kind: 'editable',
		},
		{
			field: 'userRoleIdFilter',
			caption: 'User Role Filter',
			type: 'picklistSelectizeApi',
			typeOptions: {
				picklistName: 'user_roles',
			},
			kind: 'editable',
		},
		{
			field: 'relationshipWeight',
			caption: 'Relationship Weight',
			type: 'decimal',
			kind: 'system',
		},
		{
			field: 'excludeFromRedact',
			caption: 'Exclude from Redact',
			type: 'checkbox',
			kind: 'system',
		},
		{
			field: 'allowCreate',
			caption: 'Allow Create',
			type: 'yesno',
			kind: 'editable',
		},
		// file[]
		{
			field: 'maxFileCount',
			caption: 'Max File Count',
			type: 'number',
			kind: 'editable',
		},
		{
			field: 'maxTotalAttachmentSize',
			caption: 'Max Total Attachment Size',
			type: 'number',
			kind: 'editable',
		},
		{
			field: 'maxAttachmentSize',
			caption: 'Max Attachment Size',
			type: 'number',
			kind: 'editable',
		},
		{
			field: 'dragAndDrop',
			caption: 'Drag And Drop',
			type: 'yesno',
			typeOptions: {
				allowNull: true,
			},
			kind: 'editable',
		},
		{
			field: 'manualAttach',
			caption: 'Manual Attach',
			type: 'yesno',
			typeOptions: {
				allowNull: true,
			},
			kind: 'editable',
		},
		{
			field: 'caseIdField',
			caption: 'Case Id Field',
			type: 'textbox',
			kind: 'editable',
		},
		{
			field: 'supportedFormats',
			caption: 'Supported Formats',
			type: 'picklist[]',
			kind: 'editable',
		},
		{
			field: 'canDelete',
			caption: 'Can Delete',
			type: 'yesno',
			typeOptions: {
				allowNull: true,
			},
			kind: 'editable',
		},
		{
			field: 'hideCaption',
			caption: 'Hide caption',
			type: 'yesno',
			kind: 'editable',
		},
		{
			field: 'removeSeconds',
			caption: 'Remove Seconds',
			type: 'yesno',
			kind: 'editable',
		},
		{
			field: 'entity',
			caption: 'Entity',
			type: 'textbox',
			kind: 'editable',
		},
		{
			field: 'textField',
			caption: 'Text Field',
			type: 'textbox',
			kind: 'editable',
		},
		{
			field: 'showInactiveRecords',
			caption: 'Show Inactive Records',
			type: 'textbox',
			kind: 'editable',
		},
		{
			field: 'filterBy',
			caption: 'Filter By',
			type: 'textbox',
			kind: 'editable',
		},
		{
			field: 'filter',
			caption: 'Filter',
			type: 'textbox',
			kind: 'editable',
		},
		{
			field: 'showOnCapture',
			caption: 'Show On Capture',
			type: 'toggle',
			kind: 'editable',
		},
		{
			field: 'charMaxTextbox',
			caption: 'Max Character Count',
			type: 'number',
			kind: 'editable',
		},
		{
			field: 'charMaxTextArea',
			caption: 'Max Character Count',
			type: 'number',
			kind: 'editable',
		},
		{
			field: 'charMin',
			caption: 'Min Character Count',
			type: 'number',
			kind: 'editable',
		},
		{
			field: 'linkWithSystemParties',
			caption: 'Link with System Parties',
			type: 'toggle',
			typeOptions: {
				allowNull: true,
			},
			kind: 'editable',
		},
		{
			field: 'formatInternational',
			caption: 'Format International',
			type: 'toggle',
			kind: 'editable',
		},
		{
			field: 'postalCodeCountry',
			caption: 'Select Country',
			type: 'picklist',
			kind: 'editable',
			typeOptions: {
				picklistName: 'postal_code_countries',
			},
		},
		{
			field: 'revealOnHoverAndFocus',
			caption: 'Reveal on Hover and Focus',
			type: 'toggle',
			kind: 'editable',
		},
		{
			field: 'timeFormat',
			caption: 'Format',
			type: 'picklist',
			kind: 'editable',
			typeOptions: {
				picklistName: 'time_formats',
			},
		},
		{
			field: 'dateConstraint',
			caption: 'Date Constraint',
			type: 'picklist',
			kind: 'editable',
			typeOptions: {
				picklistName: 'date_constraints',
			},
		},
		{
			field: 'dateRange',
			caption: 'Date Range',
			type: 'number',
			kind: 'editable',
		},
		{
			field: 'fixedCountry',
			caption: 'Fixed Country Code',
			type: 'picklist',
			typeOptions: {
				picklistName: 'phone_number_country_codes',
				addSelectedValueToTop: true,
				transformSelectedValueFn: 'fixedCountryTransform',
			},
			kind: 'editable',
		},
		{
			field: 'hideOnIntake',
			caption: 'Show on Intake',
			type: 'toggle-inverted',
			kind: 'editable',
			default: true,
		},
		{
			field: 'options',
			caption: 'Options',
			type: 'textbox[]',
			kind: 'editable',
			translateData: true,
		},
		{
			field: 'excludeType',
			caption: 'Excluded Field Types',
			type: 'fieldType[]',
			kind: 'editable',
		},
		{
			field: 'restorable',
			caption: 'Restorable Field',
			type: 'yesno',
			kind: 'editable',
		},
		{
			field: 'originalId',
			type: 'id',
			caption: 'Original Id',
			kind: 'hidden',
		},
		{
			field: 'subText',
			caption: 'Sub Text',
			type: 'textbox',
			translateData: true,
			kind: 'editable',
		},
		{
			field: 'placeholderText',
			caption: 'Placeholder Text',
			type: 'textbox',
			translateData: true,
			kind: 'editable',
		},
		{
			field: 'helpText',
			caption: 'Help Text',
			type: 'textbox',
			translateData: true,
			kind: 'editable',
		},
		{
			field: 'translateData',
			caption: 'Translate Data',
			type: 'yesno',
			kind: 'hidden-searchable',
		},
		{
			field: 'alwaysInApiSelectedFields',
			type: 'checkbox',
			caption: 'Always in Api Selected Fields',
			kind: 'system',
		},
		{
			field: 'dynamicKind',
			caption: 'Dynamic Kind',
			type: 'picklistSelectize',
			typeOptions: {
				picklistName: 'dynamic_kinds',
			},
			kind: 'editable',
		},
		{
			field: 'iselExpression',
			caption: 'ISEL Expression',
			type: 'expression',
			kind: 'editable',
		},
		{
			field: 'showOnPortal',
			caption: 'Show on Portal',
			type: 'toggle',
			kind: 'editable',
		},
		{
			field: 'initialMapCenter',
			caption: 'Initial Map Center',
			type: 'coordinate',
			kind: 'editable',
		},
		{
			field: 'initialMapZoom',
			caption: 'Initial Map Zoom',
			type: 'numberRangePicklist',
			typeOptions: {
				startValue: 0,
				endValue: 28,
			},
			kind: 'editable',
		},
		{
			field: 'defaultMapZoom',
			caption: 'Default Map Zoom',
			type: 'numberRangePicklist',
			typeOptions: {
				startValue: 0,
				endValue: 28,
			},
			kind: 'editable',
		},
		{
			field: 'defaultMapLayer',
			caption: 'Default Map Layer',
			type: 'dropdown',
			typeOptions: {
				staticData: 'geomapping.layerNames',
				useAppData: true,
			},
			kind: 'editable',
		},
		{
			field: 'dropMapPinOnSearch',
			caption: 'Drop Map Pin on Search',
			type: 'toggle',
			kind: 'editable',
		},
		{
			field: 'childRecordType',
			caption: 'Child Record Type',
			type: 'entityPicklist',
			kind: 'editable',
			typeOptions: {
				filter: 'aggregableChildEntitiesFilter',
				canCreate: false,
				valueField: 'canon',
				entityCategory: 2,
			},
		},
		{
			field: 'aggregationMethod',
			caption: 'Aggregation Method',
			type: 'picklistSelectize',
			kind: 'editable',
			typeOptions: {
				picklistName: 'aggregation_methods',
				canCreate: false,
				filter: 'compatibleAggregationMethodFilter',
			},
		},
		{
			field: 'targetField',
			caption: 'Target Field',
			type: 'fieldPicklist',
			kind: 'editable',
			typeOptions: {
				entityNameField: 'childRecordType',
				refreshOnFields: ['aggregationMethod'],
				filter: 'compatibleTargetFieldFilter',
			},
		},
		{
			field: 'aggregateExpression',
			caption: 'ISEL Expression',
			type: 'expression',
			kind: 'editable',
			typeOptions: {
				entityField: 'childRecordType',
				refreshOnFields: ['childRecordType'],
			},
		},
		{
			field: 'aggregateFilterGroupId',
			caption: 'Aggregate Filter Group ID',
			type: 'code',
			kind: 'custom',
			kindOptions: {
				flags: {
					audit: false,
					schema: true,
					search: true,
					readable: true,
					apiWritable: true,
					apiExternalWritable: false,
					computedOnSave: false,
					computedOnRead: false,
					iselComputedOnSave: false,
					aggregateField: false,
					formVisible: true,
					gridVisible: true,
					gridSortable: true,
					searchVisible: true,
					formattedData: true,
					gridExportable: true,
					reportable: true,
				},
			},
		},
		{
			field: 'esReverseJoinPriority',
			caption: 'ES Reverse Join Priority',
			type: 'textbox',
			kind: 'hidden',
		},
		{
			field: 'availableForCaseFiltering',
			caption: 'Available For Case Filtering',
			type: 'toggle',
			kind: 'editable',
		},
		{
			field: 'automaticallyCreateCaseLink',
			type: 'checkbox',
			caption: 'Case Linking',
			typeOptions: {
				allowNull: true,
			},
			kind: 'editable',
		},
		{
			field: 'autoCaseLinkReason',
			type: 'textbox',
			caption: 'Automatic Link Reason',
			kind: 'editable',
		},
		{
			field: 'showOnHotline',
			caption: 'Show on Hotline',
			type: 'toggle',
			kind: 'editable',
		},
		{
			field: 'dataForm',
			type: 'entityPicklist',
			caption: 'Data Form',
			kind: 'editable',
			typeOptions: {
				filter: 'dataFormFilter',
				valueField: 'canon',
				entityCategory: 1,
				canCreate: false,
				inactiveCanon: 'unknown_data_form',
			},
		},
		{
			field: 'sourceField',
			type: 'textbox',
			caption: 'Source',
			kind: 'editable',
			features: ['dataLookupSection'],
		},
		{
			field: 'dataLookup',
			type: 'yesno',
			caption: 'Data Lookup',
			kind: 'hidden-editable',
			features: ['dataLookupSection'],
		},
		{
			field: 'mappedField',
			caption: 'Mapped Field',
			type: 'fieldPicklist',
			kind: 'editable',
			typeOptions: {
				entityNameField: 'dataForm',
				filter: 'compatibleDataFormFieldFilter',
				ignoreAttributesNotInList: true,
			},
		},
		{
			field: 'restrictEdit',
			caption: 'Restrict Edit',
			type: 'toggle',
			kind: 'editable',
		},
		{
			field: 'readOnly',
			caption: 'Read Only',
			type: 'toggle',
			kind: 'editable',
		},
		{
			field: 'enableTranslation',
			caption: 'Automatic Translation',
			type: 'toggle',
			kind: 'editable',
			features: ['intakeTranslation'],
		},
		{
			field: 'maxLengthCharacterCount',
			caption: 'Max length character count',
			type: 'toggle',
			kind: 'editable',
		},
		{
			field: 'features',
			type: 'textbox[]',
			caption: 'Features',
			kind: 'hidden-searchable',
		},
		{
			field: 'customFieldDefField',
			type: 'textbox',
			caption: 'Custom Field Definition Field',
			kind: 'editable',
		},
		{
			field: 'preExistingDynamicField',
			type: 'yesno',
			caption: 'Pre-Existing Dynamic Field',
			kind: 'hidden',
			default: false,
		},
		{
			field: 'gridEntity',
			type: 'entityPicklist',
			caption: 'Entity',
			kind: 'editable',
			typeOptions: {
				valueField: 'canon',
				entityCategory: 2,
				canCreate: false,
			},
		},
		{
			field: 'fieldAttributes',
			type: 'code[]',
			caption: 'Attributes',
			kind: 'editable',
		},
		{
			field: 'copilotType',
			type: 'picklist',
			caption: 'Copilot Type',
			kind: 'editable',
			typeOptions: {
				picklistName: 'copilot_types',
			},
			features: ['outcomeAssistant'],
		},
		{
			field: 'copilotDocuments',
			type: 'document[]',
			caption: 'Copilot Documents',
			kind: 'editable',
			features: ['outcomeAssistant'],
		},
		{
			field: 'fieldGroup',
			type: 'code',
			caption: 'Field Group',
			kind: 'editable',
		},
		{
			field: 'sourceGroupField',
			type: 'code',
			caption: 'Source Group Field',
			kind: 'editable',
		},
		{
			field: 'fieldGroupSection',
			type: 'id',
			caption: 'Field Group Section',
			kind: 'hidden-editable',
		},
		{
			field: 'localName',
			type: 'code',
			caption: 'Field System Local Name',
			kind: 'system',
			dbIndex: true,
			dbIndexType: 'hash',
		},
		{
			field: 'path',
			type: 'code',
			caption: 'Field System Path',
			kind: 'system',
			dbIndex: true,
			dbIndexType: 'hash',
		},
		{
			field: 'dataImportMappableOverride',
			type: 'yesno',
			caption: 'Data Import Mappable Override?',
			kind: 'editable',
			typeOptions: {
				allowNull: true,
			},
		},
		{
			field: 'dataImportDefaultValue',
			type: 'textbox',
			caption: 'Data Import Default Value',
			kind: 'system',
		},
		{
			field: 'skipNullifyIfDeleted',
			type: 'yesno',
			caption: 'Skip Nullify If Deleted?',
			kind: 'system',
		},
	],
	joins: [
		{
			referenceField: parentField,
			table: 'isight_entity',
			fields: ['base', 'name'],
		},
	],
	audit: {
		child: true,
		parentType: parentEntity,
		parentFieldId: parentField,
		allowNavigateTo: true,
		cmd: {
			load: {
				'viewed': true,
			},
			save: {
				'created': {
					options: {
						reference,
					},
				},
				'updated': {
					options: {
						changes: {
							excludeFields: ['id'],
						},
					},
				},
			},
			remove: {
				'deleted': {
					options: {
						reference,
					},
				},
			},
		},
	},
});
