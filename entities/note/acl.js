var permHelper = require('../../lib/core/permission-helper.js');

module.exports = permHelper.initialize()
	.filterMarkForPurge()
	.filterPurge()
	.filterChildPortalFields()
	.filter({
		name: 'Mark Notes External',
		roles: ['mark_note_external'],
		actions: ['save_new', 'save_existing'],
		conditions: [],
		filters: {
			externalRecord: false,
		},
	})
	.filterViewSourceData()
	.required({
		name: 'View Notes',
		roles: ['view_note'],
		actions: ['load', 'list', 'save_existing', 'remove'],
		conditions: [permHelper.conditionForInternalRecord, {
			attributes: {
				sysActive: true,
			},
		}],
		generatePermissions: ['external'],
	})
	.required({
		name: 'Create Notes',
		roles: ['create_note'],
		actions: ['save_new'],
		conditions: [permHelper.conditionForInternalRecord, {
			attributes: {
				id: null,
			},
		}],
		generatePermissions: ['external', 'external_case_not_created_by'],
	})
	.required({
		name: 'Edit Notes',
		roles: ['edit_note'],
		actions: ['save_existing'],
		conditions: [{
			attributes: {
				'!id': null,
				sysSubmitted: true,
				sysActive: true,
			},
		}],
	})
	.required({
		name: 'Remove Notes',
		roles: ['remove_note'],
		actions: ['remove'],
		conditions: [{
			attributes: {
				sysActive: true,
			},
		}],
	})
	.required({
		name: 'Access Draft Notes',
		roles: ['access_others_draft_children'],
		actions: ['load', 'list', 'save_existing', 'remove'],
		conditions: [{
			attributes: {
				'!id': null,
				sysActive: false,
				createdBy: '{!user.id}',
			},
		}],
	})
	.requireCaseLoadInheritance()
	.requireCaseSaveInheritance()
	.value();
