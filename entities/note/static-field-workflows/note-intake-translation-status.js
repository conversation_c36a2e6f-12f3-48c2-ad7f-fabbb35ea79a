module.exports = {
	name: 'note-intake-translation-status',
	field: 'intakeTranslationStatus',
	entity: {
		zone: undefined,
		base: 'sys',
		name: 'note',
	},
	values: [
		{
			value: 'complete',
			indicator: 'success',
		},
		{
			value: 'pending',
			indicator: 'warning',
		},
		{
			value: 'error',
			indicator: 'danger',
		},
		{
			value: null,
		},
	],
	strict: true,
	transitions: [
		{
			id: 'note-intake-translation-status-blank',
			from: [null, undefined],
			to: [null, undefined],
		},
		{
			id: 'note-intake-translation-status-initial',
			from: [null, undefined],
			to: 'pending',
		},
		{
			id: 'note-intake-translation-status-complete',
			from: 'pending',
			to: 'complete',
		},
		{
			id: 'note-intake-translation-status-error',
			from: 'pending',
			to: 'error',
		},
		{
			id: 'note-intake-translation-status-pending',
			from: ['complete', 'error'],
			to: 'pending',
		},
	],
	conditions: [],
	displayRule: 'shouldShowTranslationStatusWorkflow',
};
