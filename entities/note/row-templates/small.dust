<div class="card-small{?model.datePurged} purged{:else}{?model.pendingPurgeDate} scheduled-purge{/model.pendingPurgeDate}{/model.datePurged}">
	<div class="card-header">
		{@entityIcon entity=entity$/}
		{@entityLink entity=entity$ context=model}<div class="card-title">{formattedData.childNumber|s}</div>{/entityLink}
		<div class="card-label" title="{formattedData.noteType|s}">
			{formattedData.noteType|s}
		</div>
	</div>
	{#highlightedFields}
		{>small-highlight-tmpl entityName="sys/note" ago=model.ago/}
	{/highlightedFields}
</div>