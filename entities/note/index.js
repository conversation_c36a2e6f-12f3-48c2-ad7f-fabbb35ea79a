var extend = require('../extend.js');
var standardChildConfig = require('../standard-child-config.js');
var standardIntakeTranslationConfig = require('../standard-intake-translation-config.js');

var standardIntakeTranslationChildConfig = extend(
	standardIntakeTranslationConfig, standardChildConfig,
);

module.exports = extend(standardIntakeTranslationChildConfig, {
	db: 'default',
	table: 'sys_note',
	entity: {
		base: 'sys',
		name: 'note',
	},
	joins: [
		{
			referenceField: 'sourceJob',
			table: 'sys_event',
			fields: [
				'name',
				'jobId',
			],
		},
	],
	api: {
		useGenericApiExternal: (enabledFeatures, twoWayPortalAccess) => {
			return (enabledFeatures.includes('twoWayPortal') && twoWayPortalAccess);
		},
	},
	normalizeMultiValuePicklistEntries: true,
	allowExternalFlag: true,
	allowExternalSearch: (enabledFeatures, twoWayPortalAccess) => {
		return (enabledFeatures.includes('twoWayPortal') && twoWayPortalAccess);
	},
	customForm: false,
	ruleEvents: true,
	enablePortalUserNotifications: true,
	caption: 'Note',
	captionPlural: 'Notes',
	addCaption: 'Add Note',
	newCaption: 'New Note',
	acl: require('./acl.js'),
	aclHierarchy: require('./acl-hierarchy.js'),
	audit: require('./audit.js'),
	grids: require('./grids.js'),
	validation: require('./validation.js'),
	historyNav: require('./history-nav.js'),
	icon: 'fa-note-sticky',
	includeInDataDictionary: true,
	enableRecordSourceView: true,
	usageStatistics: require('./usage-statistics.js'),
	allowInGridViews: true,
	linkSummaryFields: ['details'],
	staticFieldWorkflows: [
		require('../case/static-field-workflows/external-record.js'),
		require('./static-field-workflows/note-intake-translation-status.js'),
	],
	model: function(){
		return require('../../public/models/note-model.js');
	},
	collection: function(){
		return require('../../public/collections/notes-collection.js');
	},
	view: function(){
		return require('../../public/views/note/note-details-view.js');
	},
	rowTemplates: {
		small: function(){
			return require('./row-templates/small.dust');
		},
		medium: function(){
			return require('./row-templates/medium.dust');
		},
		tiny: function(){
			return require('./row-templates/tiny.dust');
		},
	},
	fields: [
		{
			field: 'details',
			type: 'texteditor',
			caption: 'Details',
			showOnPortal: true,
			enableTranslation: true,
			kind: 'editable-external',
		},
		{
			field: 'noteType',
			type: 'picklist',
			caption: 'Note Type',
			typeOptions: {
				picklistName: 'note_types',
			},
			kind: 'editable-external',
			showOnPortal: true,
		},
		{
			field: 'externalRecord',
			type: 'checkbox',
			caption: 'External',
			kind: 'editable',
			typeOptions: {
				allowNull: false,
			},
			kindOptions: {
				useInDynamicJoins: true,
			},
			showOnPortal: true,
			dataImportMappableOverride: false,
		},
	],
});
