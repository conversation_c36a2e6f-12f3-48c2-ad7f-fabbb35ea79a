const refFields = [
	'details',
];
const reference = {
	displayFields(data, auditModel) {
		return auditModel.hideCaseItemNumberReference() !== true
			? ['childNumber'].concat(refFields)
			: refFields;
	},
};
module.exports = {
	child: true,
	parentType: {
		base: 'sys',
		name: 'case',
	},
	parentFieldId: 'caseId',
	allowNavigateTo: true,
	cmd: {
		load: {
			'viewed': {
				options: {
					reference,
				},
			},
		},
		save: {
			'created': {
				options: {
					reference,
				},
			},
			'updated': {
				options: {
					reference,
					changes: {
						excludeFields: ['id'],
					},
				},
			},
		},
		remove: {
			'deleted': {
				options: {
					reference,
				},
			},
		},
		translate_record: {
			status: {
				condition: {
					composite: 'all',
					rules: [
						{
							path: 'entity.intakeTranslationStatus',
							comparator: 'exists',
						},
					],
				},
				options: {
					changes: {
						displayFields: ['intakeTranslationStatus'],
					},
					reference,
				},
			},
		},
	},
};
