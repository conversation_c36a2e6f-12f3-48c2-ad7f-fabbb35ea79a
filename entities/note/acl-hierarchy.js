const permHelper = require('../../lib/core/permission-helper.js');

module.exports = [{
	type: 'group',
	caption: 'Notes',
	parentPermission: 'case',
	permission: 'note',
	options: [
		{
			permission: 'view_note',
			caption: 'View',
			tooltip: 'View Notes',
			sequence: 2,
		},
		{
			permission: 'create_note',
			caption: 'Create',
			tooltip: 'Add Notes',
			sequence: 1,
		},
		{
			type: 'group',
			permission: permHelper.getEditGroupPermissionCode('note'),
			caption: 'Edit',
			sequence: 3,
			dependencies: ['view_note'],
			options: [
				{
					permission: 'edit_note',
					caption: 'Save',
					tooltip: 'Edit Notes',
					dependencies: ['view_note'],
				},
				{
					caption: 'Set Reporter Access',
					permission: 'mark_note_external',
					tooltip: 'Set "Allow Reporter Access" on Notes belonging to an external Case when the two-way portal is enabled',
					dependencies: ['view_note', 'edit_note'],
				},
			],
		},
		{
			permission: 'remove_note',
			caption: 'Remove',
			tooltip: 'Delete Notes',
			sequence: 4,
			dependencies: ['view_note'],
		},
	],
}];
