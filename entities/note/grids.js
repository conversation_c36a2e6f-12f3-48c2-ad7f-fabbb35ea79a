const defaultDynamicDataFilters = ['noteType', 'createdDate', 'lastUpdatedDate'];

module.exports = {
	'main-notes': {
		sortColumn: 'createdDate',
		sortOrder: 'desc',
		columns: [
			{ field: 'childNumber' },
			{ field: 'noteType' },
			{ field: 'details' },
			{ field: 'createdBy' },
			{ field: 'createdDate' },
		],
		defaultDynamicDataFilters,
	},
	'main-notes-external': {
		sortColumn: 'createdDate',
		sortOrder: 'desc',
		columns: [
			{ field: 'childNumber' },
			{ field: 'noteType' },
			{ field: 'details' },
			{ field: 'createdDate' },
		],
	},
	'case-notes': {
		sortColumn: 'createdDate',
		sortOrder: 'desc',
		columns: [
			{ field: 'number' },
			{ field: 'noteType' },
			{ field: 'details' },
			{ field: 'createdBy' },
			{ field: 'createdDate' },
		],
		defaultDynamicDataFilters,
		default: true,
	},
	'case-notes-external': {
		sortColumn: 'createdDate',
		sortOrder: 'desc',
		columns: [
			{ field: 'number' },
			{ field: 'noteType' },
			{ field: 'details' },
			{ field: 'createdDate' },
		],
	},
	'advanced-search-result-notes': {
		sortColumn: 'createdDate',
		sortOrder: 'desc',
		columns: [
			{ field: 'childNumber' },
			{ field: 'noteType' },
			{ field: 'details' },
			{ field: 'createdBy' },
			{ field: 'createdDate' },
		],
	},
	'search-result-notes-schedule-purge': {
		sortColumn: 'createdDate',
		sortOrder: 'desc',
		columns: [
			{ field: 'childNumber' },
			{ field: 'noteType' },
			{ field: 'details' },
			{ field: 'createdBy' },
			{ field: 'createdDate' },
		],
	},
	'search-result-notes-purge': {
		sortColumn: 'createdDate',
		sortOrder: 'desc',
		columns: [
			{ field: 'childNumber' },
			{ field: 'noteType' },
			{ field: 'details' },
			{ field: 'createdBy' },
			{ field: 'createdDate' },
			{ field: 'pendingPurgeDate' },
		],
	},
};
