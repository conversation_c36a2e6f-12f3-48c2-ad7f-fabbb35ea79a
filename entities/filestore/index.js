var extend = require('../extend.js');
var standardConfig = require('../standard-config.js');

module.exports = extend(standardConfig, {
	db: 'filestore',
	table: 'file_uploads',
	entity: {
		base: 'sys',
		name: 'filestore',
	},
	search: false,
	caption: 'File Upload',
	captionPlural: 'File Uploads',
	addCaption: 'Add File Upload',
	newCaption: 'New File Upload',
	acl: require('./acl.js'),
	fields: [
		{
			field: 'id',
			type: 'primary',
			kind: 'system',
			caption: 'ID',
		},
		{
			field: 'createdBy',
			type: 'id',
			kind: 'system',
			caption: 'Created By',
		},
		{
			field: 'createdDate',
			type: 'datetime',
			kind: 'system',
			caption: 'Created Date',
		},
		{
			field: 'lastUpdatedBy',
			type: 'id',
			kind: 'system',
			caption: 'Last Updated By',
		},
		{
			field: 'lastUpdatedDate',
			type: 'datetime',
			kind: 'system',
			caption: 'Last Updated Date',
		},
		{
			field: 'fileName',
			type: 'textbox',
			kind: 'system',
			caption: 'File Name',
		},
		{
			field: 'relativePath',
			type: 'textarea',
			kind: 'system',
			caption: 'Relative Path',
		},
		{
			field: 'md5',
			type: 'textbox',
			kind: 'system',
			caption: 'MD5',
		},
		{
			field: 'size',
			type: 'number',
			kind: 'system',
			caption: 'Size',
		},
		{
			field: 'deletedBy',
			type: 'id',
			kind: 'system',
			caption: 'Deleted By',
		},
		{
			field: 'metaData',
			type: 'json',
			kind: 'system',
			caption: 'Meta Data',
		},
	],
});
