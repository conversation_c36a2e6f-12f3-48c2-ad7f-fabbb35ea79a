const _ = require('lodash');
const async = require('async');
const statHelper = require('../../shared/stat-helper.js')('sys_role_filter');

module.exports = [
	{
		category: 'role_filter',
		key: 'totalRoleFilters',
		query: statHelper.countActiveEntity(),
	},
	{
		category: 'role_filter',
		key: 'filtersPerRole',
		query(knex, options, callback) {
			async.parallel({
				userRoles(asyncCb) {
					knex('sys_user_role')
						.select('sys_user_role.id', 'sys_user_role.name')
						.whereNot('sys_user_role.name', 'Anonymous')
						.andWhere('sys_user_role.sys_active', true)
						.asCallback(asyncCb);
				},
				roleFilters(asyncCb) {
					knex('sys_role_filter')
						.select('sys_role_filter.user_role_id')
						.count('*')
						.where('sys_role_filter.sys_active', true)
						.groupBy('sys_role_filter.user_role_id')
						.orderBy('sys_role_filter.user_role_id')
						.asCallback(asyncCb);
				},
			}, (err, results) => {
				if (err) return callback(err);
				const {userRoles, roleFilters} = results;
				const cleanedCounts = {};
				_.each(userRoles, (userRole) => {
					const roleFilter = _.find(roleFilters, {user_role_id: userRole.id});
					cleanedCounts[userRole.name] = roleFilter ? roleFilter.count : 0;
				});
				return callback(null, cleanedCounts);
			});
		},
		options: {
			datasetTranslation(dataset, translate) {
				const filter = statHelper.getTranslationFilter(this.key);
				return translate(filter, true, {dataset}, dataset);
			},
		},
	},
];
