const extend = require('../extend.js');
const standardConfig = require('../standard-config.js');

const parentField = 'userRoleId';
const parentEntity = {
	base: 'sys',
	name: 'user_role',
};
const reference = {
	displayFields: [
		'userRoleId',
	],
};

module.exports = extend(standardConfig, {
	db: 'default',
	table: 'sys_role_filter',
	entity: {
		base: 'sys',
		name: 'role_filter',
	},
	search: false,
	validation: require('./validation.js'),
	importTransformer: 'role_filter',
	caption: 'Role Filter',
	captionPlural: 'Role Filters',
	addCaption: 'Add Role Filter',
	newCaption: 'New Role Filter',
	acl: require('./acl.js'),
	usageStatistics: require('./usage-statistics.js'),
	parents: [
		{
			entity: parentEntity,
			field: parentField,
		},
	],
	fields: [
		{
			field: parentField,
			type: 'picklistApi',
			caption: 'Role',
			kind: 'editable',
			typeOptions: {
				picklistName: 'user_roles',
			},
			dbIndex: true,
		},
		{
			field: 'userField',
			type: 'fieldPicklist',
			caption: 'User Field',
			kind: 'editable',
			typeOptions: {
				entityNameField: 'userFieldEntity',
			},
			dbIndex: true,
		},
		{
			field: 'caseField',
			type: 'fieldPicklist',
			caption: 'Case Field',
			kind: 'editable',
			typeOptions: {
				entityNameField: 'caseFieldEntity',
			},
			dbIndex: true,
		},
		// We have to duplicate the "caseField" & "userField" fields so that we can store both
		// the snake and camel case versions of the field name. The app uses camel case, the db acl
		// views use the snake cased versions.
		{
			field: 'userFieldYf',
			type: 'fieldPicklist',
			caption: 'User Field',
			kind: 'editable',
			typeOptions: {
				entityNameField: 'userFieldEntity',
			},
			dbIndex: true,
		},
		{
			field: 'caseFieldYf',
			type: 'fieldPicklist',
			caption: 'Case Field',
			kind: 'editable',
			typeOptions: {
				entityNameField: 'caseFieldEntity',
			},
			dbIndex: true,
		},
		{
			field: 'operator',
			type: 'code',
			caption: 'Operator',
			kind: 'editable',
			dbIndex: true,
		},
	],
	joins: [
		{
			referenceField: parentField,
			table: 'sys_user_role',
			fields: [
				'name',
			],
		},
	],
	audit: {
		child: true,
		parentType: parentEntity,
		parentFieldId: parentField,
		allowNavigateTo: true,
		cmd: {
			load: {
				viewed: {
					options: {
						reference,
					},
				},
			},
			save: {
				created: {
					options: {
						reference,
					},
				},
				updated: {
					options: {
						changes: {
							excludeFields: ['id'],
						},
						reference,
					},
				},
			},
			remove: {
				deleted: {
					options: {
						reference,
					},
				},
			},
		},
	},
});
