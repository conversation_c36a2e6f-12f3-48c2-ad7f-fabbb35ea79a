const permHelper = require('../../lib/core/permission-helper.js');

module.exports = permHelper
	.initialize()
	.required({
		name: 'Inherit user role ACL',
		roles: ['bypass_inherited_acl'],
		actions: ['load', 'list', 'save_new', 'save_existing'],
		conditions: ['sys/user_role::{userRoleId}'],
	})
	.required({
		name: 'Remove role filter if user has edit access on user role',
		roles: ['edit_user_role'],
		actions: ['remove'],
	})
	.value();
