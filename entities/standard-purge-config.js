const extend = require('./extend.js');
const standardCoreConfig = require('./standard-core-config.js');

module.exports = extend(standardCoreConfig, {
	allowPurge: true,
	fields: [
		{
			caption: 'Pending Purge Date',
			field: 'pendingPurgeDate',
			type: 'datetime',
			kind: 'system',
			excludeFromAutofill: true,
			alwaysInApiSelectedFields: true,
			typeOptions: {
				excludeFromAggregates: true,
			},
			showOnPortal: true,
		},
		{
			caption: 'Date Purged',
			field: 'datePurged',
			type: 'datetime',
			kind: 'system',
			excludeFromRedact: true,
			excludeFromAutofill: true,
			alwaysInApiSelectedFields: true,
			typeOptions: {
				excludeFromAggregates: true,
			},
			showOnPortal: true,
		},
		{
			field: 'purgeReason',
			type: 'picklist',
			caption: 'Purge Reason',
			typeOptions: {
				picklistName: 'purge_reasons',
				excludeFromAggregates: true,
			},
			kind: 'editable',
			excludeFromRedact: true,
			excludeFromAutofill: true,
			dataImportMappableOverride: false,
		},
		{
			caption: 'Do Not Purge',
			field: 'excludeFromPurge',
			type: 'yesno',
			kind: 'system',
			excludeFromAutofill: true,
			showOnHotline: true,
			dataImportMappableOverride: false,
		},
	],
});
