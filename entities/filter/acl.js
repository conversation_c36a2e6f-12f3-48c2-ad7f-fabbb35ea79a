const permHelper = require('../../lib/core/permission-helper.js');

module.exports = permHelper.initialize()
	.filterMarkForPurge()
	.filterPurge()
	.required({
		name: 'Save/Delete Hard Locked Filter',
		roles: ['save_delete_hard_locked_filter'],
		actions: ['save_new', 'save_existing', 'remove'],
		conditions: [{
			attributes: {
				hardLocked: true,
			},
		}],
	})
	.required({
		name: 'Inherit Filter Group ACL when filterGroupId is not null',
		roles: ['bypass_inherited_acl'],
		control: 'required',
		actions: ['load', 'list', 'save_new', 'save_existing', 'remove'],
		conditions: [{
			attributes: {
				'!filterGroupId': null,
			},
		}, 'sys/filter_group::{filterGroupId}::load'],
	})
	.value();
