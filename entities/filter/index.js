const extend = require('../extend.js');
const standardFilterConfig = require('../standard-filter-config.js');

module.exports = extend(standardFilterConfig, {
	table: 'sys_filter',
	entity: {
		base: 'sys',
		name: 'filter',
	},
	caption: 'Filter',
	captionPlural: 'Filters',
	addCaption: 'Add Filter',
	newCaption: 'New Filter',
	acl: require('./acl.js'),
	validation: require('./validation'),
	importTransformer: 'filter',
	copyTransformer: 'filter',
	copyPostAction: 'filter',
	customDependencies: ['sys/filter_group', 'sys/conditionType'],
	api: {
		writableFields: [
			'filterData',
			'ruleData',
			'entityName',
		],
	},
	fields: [
		{
			field: 'type',
			type: 'textbox',
			caption: 'Condition Type',
			kind: 'editable',
		},
		{
			field: 'filterGroupId',
			type: 'id',
			caption: 'Filter Group',
			kind: 'hidden-editable',
		},
	],
});
