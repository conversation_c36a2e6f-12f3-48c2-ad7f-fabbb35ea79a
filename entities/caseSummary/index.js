const extend = require('../extend.js');
const standardChildConfig = require('../standard-child-config.js');

module.exports = extend(standardChildConfig, {
	db: 'default',
	table: 'sys_case_summary',
	entity: {
		base: 'sys',
		name: 'caseSummary',
	},
	allowRecordLinking: false,
	audit: {
		allowNavigateTo: false,
		cmd: {
			load: {
				viewed: {
					options: {
						reference: {
							displayFields: ['createdDate'],
						},
					},
				},
			},
			save: {
				created: {
					options: {
						reference: {
							displayFields: ['createdDate'],
						},
					},
				},
				updated: {
					options: {
						changes: {
							excludeFields: ['id'],
						},
						reference: {
							displayFields: ['createdDate'],
						},
					},
				},
			},
			remove: {
				deleted: {
					options: {
						reference: {
							displayFields: ['createdDate'],
						},
					},
				},
			},
		},
	},
	validation: {
		mandatory$: ['content'],
	},
	search: false,
	allowAdvancedSearch: false,
	allowQuickSearch: false,
	dataExport: false,
	customForm: false,
	report: false,
	excludeFromAggregation: true,
	caption: 'Case Summary',
	captionPlural: 'Case Summaries',
	addCaption: 'Add Case Summary',
	newCaption: 'New Case Summary',
	model: () => require('../../public/models/case-summary-model.js'),
	fields: [
		{
			field: 'type',
			caption: 'Summary Type',
			type: 'textbox',
			kind: 'system',
		},
		{
			field: 'content',
			caption: 'Summary Content',
			type: 'texteditor',
			kind: 'system',
		},
		{
			field: 'publishedDate',
			caption: 'Published Date',
			type: 'datetime',
			kind: 'system',
		},
		{
			field: 'liked',
			caption: 'Is Liked?',
			type: 'yesno',
			kind: 'editable',
			typeOptions: { allowNull: true },
		},
		{
			field: 'tokens',
			caption: 'Token Usage',
			type: 'number',
			kind: 'system',
		},
	],
});
