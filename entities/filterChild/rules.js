module.exports = {
	// TODO: This should ultimately be reading from the fields.
	// Later on perhaps when rules are also passed the entitiy's fields,
	// we can use that to figure out if we should be mandatory or not
	// vs just for the is_empty/is_not_empty operators
	shouldHaveFieldValues(data) {
		return data.operator !== 'is_empty' && data.operator !== 'is_not_empty';
	},
	isOperatorOfTypeBetween(data) {
		return data.operator === 'between';
	},
};
