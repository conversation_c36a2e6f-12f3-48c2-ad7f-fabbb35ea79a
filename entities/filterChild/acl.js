const permHelper = require('../../lib/core/permission-helper.js');

module.exports = permHelper.initialize()
	.filterMarkForPurge()
	.filterPurge()
	.required({
		name: 'Save/Delete Hard Locked Filter Child',
		roles: ['save_delete_hard_locked_filter_child'],
		actions: ['save_new', 'save_existing', 'remove'],
		conditions: [{
			attributes: {
				hardLocked: true,
			},
		}],
	})
	.required({
		name: 'Inherit Filter acl',
		roles: ['bypass_inherited_acl'],
		control: 'required',
		actions: ['load', 'list', 'save_new', 'save_existing', 'remove'],
		conditions: ['sys/filter::{parentId}'],
	})
	.value();
