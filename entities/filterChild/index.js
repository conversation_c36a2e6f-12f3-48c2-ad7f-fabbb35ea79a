const extend = require('../extend.js');
const standardFilterChildConfig = require('../standard-filter-child-config');

module.exports = extend(standardFilterChildConfig, {
	table: 'sys_filter_child',
	entity: {
		base: 'sys',
		name: 'filter_child',
	},
	parents: [
		{
			entity: {
				base: 'sys',
				name: 'filter',
			},
			field: 'parentId',
		},
	],
	caption: 'Filter Item',
	captionPlural: 'Filter Items',
	addCaption: 'Add Filter Item',
	newCaption: 'New Filter Item',
	acl: require('./acl.js'),
	rules: require('./rules.js'),
	validation: require('./validation'),
	importTransformer: 'filter_child',
	exportTransformer: 'filter_child',
	customExportColumns: ['parentId__originalId'],
	customDependencies: ['sys/flag', 'sys/packet', 'sys/packet_template'],
	copyTransformer: 'filter_child',
});
