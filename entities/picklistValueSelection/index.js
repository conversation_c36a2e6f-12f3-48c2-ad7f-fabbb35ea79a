const extend = require('../extend.js');
const standardConfig = require('../standard-config.js');

/**
 * DESCRIPTION:
 *
 * This entity is used to save values selected by users for multi-value picklists in the app.
 *
 * At the time of writing this comment, this entity does not serve as the source of truth
 * for picklist value selections in the app, although that might change in the future. Rather,
 * the source of truth remains in the dynamic_data columns of their respective  entties.
 */

module.exports = extend(standardConfig, {
	report: false,
	db: 'default',
	table: 'sys_picklist_value_selection',
	caption: 'Picklist Value Selection',
	captionPlural: 'Picklist Value Selections',
	addCaption: 'Add Picklist Value Selection',
	newCaption: 'New Picklist Value Selection',
	allowPurge: true,
	search: false,
	excludeFromAggregation: true,
	entity: {
		base: 'sys',
		name: 'picklistValueSelection',
	},
	fields: [
		{
			// This field stores the ID of the record for which the multi-picklist field is for.
			field: 'parentId',
			type: 'id',
			caption: 'Parent Id',
			dbIndex: true,
			kind: 'system',
			excludeFromRedact: true,
		},
		{
			// This field stores the canon of the entity for which the parentId is for. E.g.,
			// if it's sys/case, then parentId is for a record in the sys_case table.
			//
			// Please note that for dynamic entities this will always be sys/dynamic_entity_data,
			// that's because dynamic entities' data is always stored in the sys_dynamic_entity_data
			// table.
			//
			// NOTE: This field is required in order for the purge feature to work for multi-value
			// picklist fields.
			field: 'parentCanon',
			type: 'code',
			caption: 'Parent Canon',
			kind: 'system',
			excludeFromRedact: true,
		},
		{
			// For static entities, this field will always be equal to the parentCanon field.
			// For dynamic entities, this field will store the actual dynamic entity name,
			// e.g. sys/my_precious_custom_form
			//
			// NOTE: This field is primarily for the purpose of internal visibility.
			field: 'entityCanon',
			type: 'code',
			caption: 'Entity Canon',
			kind: 'system',
			excludeFromRedact: true,
		},
		{
			// This field stores the system name of the multi-picklist field that the value was
			// selected for.
			//
			// NOTE: We are using system field names as opposed to the isight_field.id column -
			// to avoid the dangling/wild FK situation. Such situation would arise because the
			// field ID changes whenever a user re-publishes the form in the Form Builder. As
			// a result this field_id column would end up referring to a non-existing field.
			field: 'field',
			type: 'code',
			caption: 'Field System Name',
			kind: 'system',
			dbIndex: true,
			excludeFromRedact: true,
		},
		{
			// This field stores the value selected in the app for the multi-picklist field.
			field: 'value',
			type: 'textbox',
			caption: 'Value',
			kind: 'system',
		},
	],
	parents: [
		{
			entity: {
				base: 'sys',
				name: 'dynamic_entity_data',
			},
			field: 'parentId',
			filter: {
				parentCanon: 'sys/dynamic_entity_data',
			},
		},
		{
			entity: {
				base: 'sys',
				name: 'case',
			},
			field: 'parentId',
			filter: {
				parentCanon: 'sys/case',
			},
		},
		{
			entity: {
				base: 'sys',
				name: 'party',
			},
			field: 'parentId',
			filter: {
				parentCanon: 'sys/party',
			},
		},
		{
			entity: {
				base: 'sys',
				name: 'todo',
			},
			field: 'parentId',
			filter: {
				parentCanon: 'sys/todo',
			},
		},
		{
			entity: {
				base: 'sys',
				name: 'note',
			},
			field: 'parentId',
			filter: {
				parentCanon: 'sys/note',
			},
		},
	],
});
