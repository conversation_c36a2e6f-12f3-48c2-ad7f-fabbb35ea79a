const externalNotificationCodes = [
	'portal-case-submission-notify-reporter',
	'portal-case-update-notify-reporter',
	'portal-child-update-notify-reporter',
	'portal-shared-child-notify-reporter',
	'external-password-reset-requested',
	'portal-system-option-change-notify-reporter',
	'external-username-reminder',
	'external-password-generated',
	'external-user-expiring',
	'external-password-expiring',
	'external-password-expired',
	'hotline-case-submission-notify-reporter',
	'hotline-password-generated',
	'request-link',
	'request-verification-code',
	'request-cancelled',
	'external-case-access-granted',
];

module.exports = {
	isDefault(data) {
		return data.userId === '0';
	},
	isNotExternalNotification(data) {
		return !externalNotificationCodes.includes(data.code);
	},
};
