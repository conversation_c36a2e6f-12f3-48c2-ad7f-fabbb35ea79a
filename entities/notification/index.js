const extend = require('../extend.js');
const standardConfig = require('../standard-config.js');

module.exports = extend(standardConfig, {
	db: 'default',
	caption: 'Notification',
	captionPlural: 'System Notifications',
	addCaption: 'Add System Notification',
	newCaption: 'New System Notification',
	gridDescriptorField: 'name', // Added to front-end model
	grids: require('./grids.js'),
	acl: require('./acl.js'),
	aclHierarchy: require('./acl-hierarchy.js'),
	historyNav: require('./history-nav.js'),
	validation: require('./validation.js'),
	rules: require('./rules.js'),
	audit: require('./audit.js'),
	table: 'sys_notification',
	rowTemplates: {
		medium(){
			return require('./row-templates/medium.dust');
		},
	},
	entity: {
		base: 'sys',
		name: 'notification',
	},
	fields: [
		{
			field: 'userId',
			type: 'user',
			caption: 'User Id',
			kind: 'system',
		},
		{
			field: 'code',
			type: 'textbox',
			caption: 'Name',
			kind: 'custom',
			kindOptions: {
				flags: {
					audit: true,
					schema: true,
					search: true,
					apiWritable: false,
					apiExternalWritable: false,
					computedOnSave: false,
					computedOnRead: false,
					formVisible: true,
					gridVisible: true,
					gridSortable: false,
					searchVisible: true,
					formattedData: true,
					gridExportable: true,
					reportable: false,
				},
			},
			cellTemplate(fs) {
				return fs.readFileSync(
					`${__dirname}/cell-templates/notification-name-cell-tmpl.dust`,
					'utf8',
				);
			},
		},
		{
			field: 'subject',
			type: 'textbox',
			caption: 'Subject',
			kind: 'editable',
		},
		{
			field: 'html',
			type: 'texteditor',
			caption: 'HTML Body',
			kind: 'editable',
		},
		{
			field: 'text',
			type: 'textarea',
			caption: 'Text Body',
			kind: 'editable',
		},
		{
			field: 'methods',
			type: 'picklist[]',
			caption: 'Methods',
			kind: 'editable',
			typeOptions: {
				picklistName: 'notify_methods',
			},
		},
		{
			field: 'locked',
			type: 'yesno',
			caption: 'Locked',
			kind: 'editable',
		},
		{
			field: 'disabled',
			type: 'yesno',
			caption: 'Disabled',
			kind: 'editable',
		},
		{
			field: 'saveToInbox',
			type: 'yesno',
			caption: 'Save to Inbox',
			kind: 'editable',
		},
		{
			field: 'features',
			type: 'textbox[]',
			caption: 'Features',
			kind: 'hidden-searchable',
		},
	],
});
