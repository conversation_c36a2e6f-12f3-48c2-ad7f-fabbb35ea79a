const permHelper = require('../../lib/core/permission-helper.js');

const isNewCondition = {
	attributes: {
		id: null,
	},
};
const isEditCondition = {
	attributes: {
		'!id': null,
	},
};
const ownNotificationCondition = {
	attributes: {
		userId: '{user.id}',
	},
};
const defaultNotificationCondition = {
	attributes: {
		userId: '0',
	},
};
const externalNotificationCodes = [
	'portal-case-submission-notify-reporter',
	'portal-case-update-notify-reporter',
	'portal-child-update-notify-reporter',
	'portal-shared-child-notify-reporter',
	'portal-system-option-change-notify-reporter',
	'hotline-case-submission-notify-reporter',
];
const isExternalNotificationCondition = [
	{
		fn(obj, context) {
			return { ok: externalNotificationCodes.includes(obj.code) };
		},
		selectedFields(opts, callback) {
			return callback(null, ['code']);
		},
	},
];

module.exports = permHelper.initialize()
	.filter({
		name: 'View Default Notification Details',
		roles: ['view_default_notification_details'],
		actions: ['load', 'list', 'save_new', 'save_existing', 'remove'],
		conditions: [defaultNotificationCondition],
		filters: {
			html: false,
			subject: false,
			text: false,
			saveToInbox: false,
		},
	})
	.filter({
		name: 'View Own Notification Details',
		roles: ['view_own_notification_details'],
		actions: ['load', 'list', 'save_new', 'save_existing', 'remove'],
		conditions: [ownNotificationCondition],
		filters: {
			html: false,
			subject: false,
			text: false,
			saveToInbox: false,
		},
	})
	.filter({
		name: 'Reassign Notification',
		roles: ['edit_notification_reassign'],
		actions: ['save_new', 'save_existing'],
		conditions: [isEditCondition],
		filters: {
			userId: false,
		},
	})
	.filter({
		name: 'Edit external saveToInbox notifications',
		roles: ['edit_external_notification_save_to_inbox'],
		actions: ['save_new', 'save_existing'],
		conditions: [...isExternalNotificationCondition],
		filters: {
			saveToInbox: false,
		},
	})
	.required({
		name: 'View Notification',
		roles: ['view_notification'],
		actions: ['load', 'list', 'save_existing', 'remove'],
		conditions: [],
	})
	.required({
		name: 'View Default Notification',
		roles: ['view_default_notification'],
		actions: ['load', 'list', 'save_existing', 'remove'],
		conditions: [defaultNotificationCondition],
	})
	.required({
		name: 'View Own Notification',
		roles: ['view_own_notification'],
		actions: ['load', 'list', 'save_existing', 'remove'],
		conditions: [ownNotificationCondition],
	})
	.required({
		name: 'Create Notification',
		roles: ['create_notification'],
		actions: ['save_new'],
		conditions: [isNewCondition],
	})
	.required({
		name: 'Create Default Notification',
		roles: ['create_default_notification'],
		actions: ['save_new'],
		conditions: [defaultNotificationCondition, isNewCondition],
	})
	.required({
		name: 'Create Own Notification',
		roles: ['create_own_notification'],
		actions: ['save_new'],
		conditions: [ownNotificationCondition, isNewCondition],
	})
	.required({
		name: 'Edit Notification',
		roles: ['edit_notification'],
		actions: ['save_new'],
		conditions: [isEditCondition],
	})
	.required({
		name: 'Edit Default Notification',
		roles: ['edit_default_notification'],
		actions: ['save_existing'],
		conditions: [defaultNotificationCondition, isEditCondition],
	})
	.required({
		name: 'Edit Own Notification',
		roles: ['edit_own_notification'],
		actions: ['save_existing'],
		conditions: [ownNotificationCondition, isEditCondition],
	})
	.required({
		name: 'Own Locked Notification',
		roles: ['own_locked_notification'],
		actions: ['save_new', 'save_existing'],
		conditions: [ownNotificationCondition, {
			attributes: {
				locked: true,
			},
		}],
	})
	.required({
		name: 'Others Notificatin',
		roles: ['others_notification'],
		actions: ['load', 'list', 'save_new', 'save_existing', 'remove'],
		conditions: [{
			attributes: {
				'!userId': '{user.id}',
			},
		}, {
			attributes: {
				'!userId': '0',
			},
		}],
	})
	.required({
		name: 'Remove Notification',
		roles: ['remove_notification'],
		actions: ['remove'],
		conditions: [],
	})
	.required({
		name: 'Remove Default Notification',
		roles: ['remove_default_notification'],
		actions: ['remove'],
		conditions: [defaultNotificationCondition],
	})
	.required({
		name: 'Remove Own Notification',
		roles: ['remove_own_notification'],
		actions: ['remove'],
		conditions: [ownNotificationCondition],
	})
	.value();
