const permHelper = require('../../lib/core/permission-helper.js');

module.exports = [{
	type: 'group',
	caption: 'Notifications',
	parentPermission: 'view_system_settings',
	permission: 'view_notification_settings',
	options: [
		{
			permission: 'view_default_notification_details',
			caption: 'View',
			tooltip: 'View system notifications',
			sequence: 2,
		},
		{
			caption: 'Create',
			sequence: 1,
			disabled: true,
			permission: 'create_notification',
		},
		{
			type: 'group',
			caption: 'Edit',
			sequence: 3,
			permission: permHelper.getEditGroupPermissionCode('notification'),
			dependencies: ['view_default_notification_details'],
			options: [{
				caption: 'Save',
				tooltip: 'Edit system notifications',
				permission: 'edit_default_notification',
				dependencies: ['view_default_notification_details'],
			}],
		},
		{
			caption: 'Remove',
			sequence: 4,
			disabled: true,
			permission: 'remove_notification',
			dependencies: ['view_default_notification_details'],
		},
	],
}];
