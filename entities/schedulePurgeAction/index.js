const extend = require('../extend.js');
const standardActionConfig = require('../standard-action-config.js');

module.exports = extend(standardActionConfig, {
	db: 'default',
	table: 'sys_schedule_purge_action',
	entity: {
		base: 'sys',
		name: 'schedule_purge_action',
	},
	caption: 'Schedule Purge',
	captionPlural: 'Schedule Purges',
	addCaption: 'Add Scheduled Purge',
	newCaption: 'New Scheduled Purge',
	workflowActionConfig: {
		formName: 'schedule-purge-action',
		aclRoles: {
			createACLRole: 'agent',
			editACLRole: 'agent',
			deleteACLRole: 'agent',
		},
		unsupportedEvents: {
			'sys/email': ['unassigned-incoming-mail'],
		},
	},
	validation: require('./validation'),
	audit: require('./audit'),
	usageStatistics: require('./usage-statistics.js'),
	model() {
		return require('./model');
	},
	fields: [
		{
			field: 'targetPurgeReason',
			type: 'picklist',
			caption: 'Purge Reason',
			typeOptions: {
				picklistName: 'purge_reasons',
			},
			kind: 'editable',
		},
	],
});
