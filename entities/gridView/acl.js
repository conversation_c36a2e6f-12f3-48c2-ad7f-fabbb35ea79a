const permHelper = require('../../lib/core/permission-helper.js');

module.exports = permHelper.initialize()
	.required({
		name: 'View Grid Views',
		roles: ['view_grid_views'],
		actions: ['load', 'list', 'save_existing', 'save_new', 'remove'],
		conditions: [],
	})
	.required({
		name: 'Access Others Grid Views',
		roles: ['access_others_grid_views'],
		actions: ['load', 'list', 'save_existing', 'remove'],
		conditions: [{
			attributes: {
				createdBy: '{!user.id}',
			},
		}],
	})
	.value();
