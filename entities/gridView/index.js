const extend = require('../extend.js');
const standardConfig = require('../standard-config.js');

module.exports = extend(standardConfig, {
	db: 'default',
	table: 'sys_grid_view',
	gridDescriptorField: 'name',
	entity: {
		base: 'sys',
		name: 'grid_view',
	},
	caption: 'View',
	captionPlural: 'Views',
	addCaption: 'Add Grid View',
	newCaption: 'New Grid View',
	search: true,
	acl: require('./acl.js'),
	aclHierarchy: require('./acl-hierarchy.js'),
	audit: require('./audit.js'),
	validation: require('./validation.js'),
	model() {
		return require('../../public/models/grid-view-model.js');
	},
	collection() {
		return require('../../public/collections/grid-views-collection.js');
	},
	fields: [
		{
			field: 'name',
			type: 'textbox',
			kind: 'editable',
			caption: 'Name',
			typeOptions: {
				charMaxTextbox: 60,
				maxLengthCharacterCount: true,
			},
		},
		{
			field: 'entityName',
			type: 'entityPicklist',
			caption: 'Entity',
			kind: 'editable',
			typeOptions: {
				valueField: 'canon',
			},
		},
		{
			field: 'gridFilterIdDenormalized',
			type: 'id',
			kind: 'hidden',
			caption: 'Grid Filter ID Denormalized',
		},
		{
			field: 'selectedEntityTypes',
			type: 'textbox[]',
			caption: 'Selected Entity Types',
			kind: 'hidden-editable',
			features: ['multiEntityViews'],
		},
		{
			field: 'selectAllEntities',
			type: 'checkbox',
			caption: 'Are "All" entities selected?',
			kind: 'hidden-editable',
			features: ['multiEntityViews'],
		},
	],
});
