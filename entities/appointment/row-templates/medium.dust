<td  class="card-medium{?model.datePurged} purged{:else}{?model.pendingPurgeDate} scheduled-purge{/model.pendingPurgeDate}{/model.datePurged}">
    <div class="card-header">
    	{#model}
		  <div class="card-header">
				{@entityIcon entity=entity$/}
				{@entityLink entity=entity$ context=model}<div class="card-title">{formattedData.childNumber|s}</div>{/entityLink}
		    <div class="card-label">
				{formattedData.subject|s}
		    </div>
		  </div>
    	{/model}
	</div>
	{#highlightedFields}
		{>medium-highlight-tmpl entityName="sys/appointment" ago="{model.ago}"/}
	{/highlightedFields}   
</td>

