/* global module */

/**
 *  Appointment Status Workflow.
 */
module.exports = {
	name: 'appointment-status-workflow',
	field: 'status',
	entity: {
		zone: undefined,
		base: 'sys',
		name: 'appointment',
	},
	values: [
		{
			value: 'sent',
			indicator: 'success',
		},
		{
			value: 'unsentChanges',
			indicator: 'warning',
		},
		{
			value: 'cancelled',
			indicator: 'danger',
			onSet: {
				cancelledDate: '{now}',
				cancelledBy: '{user.id}',
			},
		},
		{
			value: 'purged',
			indicator: 'danger',
		},
	],
	strict: true,
	transitions: [
		{
			id: 'appointment-create',
			from: [null, undefined],
			to: 'sent',
		},
		{
			id: 'appointment-save-and-send',
			from: 'unsentChanges',
			to: 'sent',
		},
		{
			id: 'appointment-save-without-send',
			from: 'sent',
			to: 'unsentChanges',
		},
		{
			id: 'appointment-cancel',
			from: ['sent', 'unsentChanges'],
			to: 'cancelled',
			roles: ['cancel_appointment'],
		},
	],
	conditions: [],
};
