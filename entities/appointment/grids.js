module.exports = {
	'case-appointments': {
		sortColumn: 'createdDate',
		sortOrder: 'desc',
		columns: [
			{ field: 'number' },
			{ field: 'organizer' },
			{ field: 'invitees' },
			{ field: 'subject' },
			{ field: 'location' },
			{ field: 'startDate' },
			{ field: 'endDate' },
			{ field: 'status' },
			{ field: 'createdDate' },
		],
	},
	'advanced-search-result-appointments': {
		sortColumn: 'createdDate',
		sortOrder: 'desc',
		columns: [
			{ field: 'childNumber' },
			{ field: 'organizer' },
			{ field: 'invitees' },
			{ field: 'subject' },
			{ field: 'location' },
			{ field: 'startDate' },
			{ field: 'endDate' },
			{ field: 'status' },
			{ field: 'createdDate' },
		],
	},
	'search-result-appointments-schedule-purge': {
		sortColumn: 'createdDate',
		sortOrder: 'desc',
		columns: [
			{ field: 'childNumber' },
			{ field: 'organizer' },
			{ field: 'invitees' },
			{ field: 'subject' },
			{ field: 'location' },
			{ field: 'startDate' },
			{ field: 'endDate' },
			{ field: 'status' },
			{ field: 'createdDate' },
		],
	},
	'search-result-appointments-purge': {
		sortColumn: 'createdDate',
		sortOrder: 'desc',
		columns: [
			{ field: 'childNumber' },
			{ field: 'organizer' },
			{ field: 'invitees' },
			{ field: 'subject' },
			{ field: 'location' },
			{ field: 'startDate' },
			{ field: 'endDate' },
			{ field: 'status' },
			{ field: 'pendingPurgeDate' },
			{ field: 'createdDate' },
		],
	},
};
