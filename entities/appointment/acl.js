const permHelper = require('../../lib/core/permission-helper.js');

module.exports = permHelper.initialize()
	.filterMarkForPurge()
	.filterPurge()
	.filter({
		name: 'Cancel appointment',
		roles: ['cancel_appointment'],
		actions: ['save_new'],
		conditions: [{
			attributes: {
				status: 'cancelled',
			},
		}],
		filters: {
			status: false,
		},
	})
	.filter({
		name: 'Edit appointment',
		roles: ['edit_appointment'],
		actions: ['save_new'],
		conditions: [{
			attributes: {
				'!id': null,
			},
		}],
		filters: {
			status: true,
		},
	})
	.required({
		name: 'View Appointments',
		roles: ['view_appointment'],
		actions: ['load', 'list', 'save_existing', 'remove'],
		conditions: [],
	})
	.required({
		name: 'Create Appointments',
		roles: ['create_appointment'],
		actions: ['save_new'],
		conditions: [{
			attributes: {
				id: null,
			},
		}],
	})
	.required({
		name: 'Edit Appointments',
		roles: ['edit_appointment', 'cancel_appointment'],
		actions: ['save_new'],
		conditions: [{
			attributes: {
				'!id': null,
			},
		}],
	})
	.required({
		name: 'Edit Cancelled Appointments',
		roles: ['edit_cancelled_appointment'],
		actions: ['save_existing'],
		conditions: [{
			attributes: {
				'!id': null,
				status: 'cancelled',
			},
		}],
	})
	.required({
		name: 'Remove Appointments',
		roles: ['remove_appointment'],
		actions: ['remove'],
		conditions: [],
	})
	.requireCaseLoadInheritance()
	.requireCaseSaveInheritance()
	.value();
