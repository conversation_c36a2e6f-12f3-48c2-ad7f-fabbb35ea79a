const permHelper = require('../../lib/core/permission-helper.js');

module.exports = [{
	type: 'group',
	caption: 'Appointments',
	parentPermission: 'case',
	permission: 'appointment',
	options: [
		{
			permission: 'view_appointment',
			caption: 'View',
			tooltip: 'View Appointments',
			sequence: 2,
		},
		{
			permission: 'create_appointment',
			caption: 'Create',
			tooltip: 'Add Appointments',
			sequence: 1,
		},
		{
			type: 'group',
			permission: permHelper.getEditGroupPermissionCode('appointment'),
			caption: 'Edit',
			sequence: 3,
			dependencies: ['view_appointment'],
			options: [{
				permission: 'edit_appointment',
				caption: 'Save',
				tooltip: 'Edit Appointments',
				dependencies: ['view_appointment'],
			}],
		},
		{
			permission: 'cancel_appointment',
			caption: 'Cancel',
			tooltip: 'Cancel Appointments',
			sequence: 4,
			dependencies: ['view_appointment'],
		},
		{
			permission: 'remove_appointment',
			caption: 'Remove',
			tooltip: 'Delete Appointments',
			sequence: 5,
			dependencies: ['view_appointment', 'cancel_appointment'],
		},
	],
}];
