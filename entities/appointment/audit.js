const refFields = [
	'subject',
	'location',
	'startDate',
	'endDate',
];
const reference = {
	displayFields(data, auditModel) {
		return auditModel.hideCaseItemNumberReference() !== true
			? ['childNumber'].concat(refFields)
			: refFields;
	},
};
module.exports = {
	child: true,
	parentType: {
		base: 'sys',
		name: 'case',
	},
	parentFieldId: 'caseId',
	allowNavigateTo: true,
	cmd: {
		load: {
			viewed: {
				options: {
					reference,
				},
			},
		},
		save: {
			created: {
				options: {
					reference,
				},
			},
			updated: {
				options: {
					reference,
					changes: {
						excludeFields: [
							'id',
							'lastUpdatedBy',
							'lastUpdatedDate',
						],
					},
				},
			},
		},
		remove: {
			deleted: {
				options: {
					reference,
				},
			},
		},
	},
};
