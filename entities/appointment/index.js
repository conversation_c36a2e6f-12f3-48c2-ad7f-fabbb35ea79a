const extend = require('../extend.js');
const standardChildConfig = require('../standard-child-config.js');

module.exports = extend(standardChildConfig, {
	db: 'default',
	table: 'sys_appointment',
	entity: {
		base: 'sys',
		name: 'appointment',
	},
	api: {
		useGenericApi: true,
		writableFields: [
			'sendInvite',
		],
	},
	customForm: false,
	ruleEvents: true,
	caption: 'Appointment',
	captionPlural: 'Appointments',
	addCaption: 'Add Appointment',
	newCaption: 'New Appointment',
	audit: require('./audit.js'),
	validation: require('./validation.js'),
	rules: require('./rules.js'),
	acl: require('./acl.js'),
	aclHierarchy: require('./acl-hierarchy.js'),
	grids: require('./grids.js'),
	historyNav: require('./history-nav.js'),
	computeFunctions: require('./compute-functions.js'),
	icon: 'fa-calendar',
	includeInDataDictionary: true,
	backgroundColor: '#FAFAA5',
	linkSummaryFields: ['subject'],
	usageStatistics: require('./usage-statistics.js'),
	model() {
		return require('../../public/models/appointment-model.js');
	},
	collection() {
		return require('../../public/collections/appointments-collection.js');
	},
	view() {
		return require('../../public/views/appointment/appointment-details-view.js');
	},
	calendarView() {
		return require('../../public/views/appointment/appointment-event-view.js');
	},
	staticFieldWorkflows: [require('./static-field-workflows/appointment-status.js')],
	rowTemplates: {
		small(){
			return require('./row-templates/small.dust');
		},
		medium(){
			return require('./row-templates/medium.dust');
		},
		tiny(){
			return require('./row-templates/tiny.dust');
		},
	},
	calendarMapping: {
		title: 'subject',
		subtitle: 'location',
		description: 'description',
		startDate: 'startDate',
		endDate: 'endDate',
		allDay: 'allDayEvent',
	},
	fields: [
		{
			field: 'organizer',
			type: 'user',
			caption: 'Organizer',
			kind: 'computed',
			kindOptions: {
				computeFunction: 'setOrganizer',
			},
		},
		{
			field: 'invitees',
			type: 'emailLookup[]',
			caption: 'Invitees',
			kind: 'custom',
			kindOptions: {
				flags: {
					audit: true,
					schema: true,
					search: true,
					apiWritable: true,
					apiExternalWritable: false,
					computedOnSave: false,
					computedOnRead: false,
					formVisible: true,
					gridVisible: true,
					gridSortable: false,
					searchVisible: true,
					formattedData: true,
					gridExportable: true,
					reportable: true,
				},
			},
		},
		{
			field: 'subject',
			type: 'textbox',
			caption: 'Subject',
			kind: 'editable',
			gridWidth: 150,
		},
		{
			field: 'location',
			type: 'textbox',
			caption: 'Location',
			kind: 'editable',
		},
		{
			field: 'startDate',
			type: 'datetime',
			caption: 'Start Date',
			kind: 'editable',
			typeOptions: {
				removeSeconds: true,
				clearTimeWhenDisabled: false,
			},
		},
		{
			field: 'endDate',
			type: 'datetime',
			caption: 'End Date',
			kind: 'editable',
			typeOptions: {
				removeSeconds: true,
				clearTimeWhenDisabled: false,
			},
		},
		{
			field: 'allDayEvent',
			type: 'checkbox',
			caption: 'All Day',
			kind: 'editable',
		},
		{
			field: 'duration',
			type: 'delay',
			caption: 'Duration',
			kind: 'editable',
		},
		{
			field: 'description',
			type: 'texteditor',
			caption: 'Description',
			kind: 'editable',
		},
		{
			field: 'descriptionText',
			type: 'textarea',
			kind: 'hidden',
			caption: 'Description Text',
			// HACK: Currently the system ignores the search flag defined in kind options
			//		 Only way to control that is to specify the flag here.
			//		 Can be removed once this ticket is resolved: ITPL-10556
			search: false,
		},
		{
			field: 'failedInvitees',
			type: 'code[]',
			kind: 'system',
			caption: 'Failed Invitees',
		},
		{
			field: 'dateSent',
			type: 'datetime',
			kind: 'system',
			caption: 'Date Sent',
		},
		{
			field: 'sentAfterSave',
			type: 'yesno',
			kind: 'hidden',
			caption: 'Sent After Save',
		},
		{
			field: 'status',
			type: 'picklist',
			caption: 'Status',
			typeOptions: {
				picklistName: 'appointment_statuses',
			},
			kind: 'editable',
			excludeFromRedact: true,
			excludeFromSaveAndCopy: true,
			cellTemplate(fs) {
				return fs.readFileSync(`${__dirname}/cell-templates/status-cell-tmpl.dust`, 'utf8');
			},
		},
		{
			field: 'cancelledDate',
			type: 'datetime',
			caption: 'Cancelled Date',
			kind: 'system',
		},
		{
			field: 'cancelledBy',
			type: 'user',
			caption: 'Cancelled By',
			kind: 'system',
		},
	],
});
