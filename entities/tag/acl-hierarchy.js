const permHelper = require('../../lib/core/permission-helper.js');

module.exports = [{
	type: 'group',
	caption: 'Tags',
	permission: 'view_tag_settings',
	parentPermission: 'view_data_settings',
	features: ['fileEnhancement'],
	options: [
		{
			caption: 'View Tag Settings',
			tooltip: 'View the Tag settings page',
			features: ['fileEnhancement'],
			sequence: 2,
			permission: 'view_tag',
		},
		{
			permission: 'create_tag',
			caption: 'Create',
			tooltip: 'Create a tag',
			features: ['fileEnhancement'],
			dependencies: ['view_tag'],
			sequence: 1,
		},
		{
			type: 'group',
			permission: permHelper.getEditGroupPermissionCode('tag'),
			caption: 'Edit',
			dependencies: ['view_tag'],
			features: ['fileEnhancement'],
			sequence: 3,
			options: [{
				permission: 'edit_tag',
				caption: 'Save',
				tooltip: 'Edit a tag',
				dependencies: ['view_tag'],
				features: ['fileEnhancement'],
			}],
		},
		{
			permission: 'remove_tag',
			caption: 'Remove',
			tooltip: 'Delete a tag',
			dependencies: ['view_tag'],
			features: ['fileEnhancement'],
			sequence: 4,
		},
	],
}];
