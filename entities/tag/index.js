const extend = require('../extend.js');
const standardConfig = require('../standard-config.js');

module.exports = extend(standardConfig, {
	db: 'default',
	table: 'sys_tag',
	entity: {
		base: 'sys',
		name: 'tag',
	},
	caption: 'Tag',
	captionPlural: 'Tags',
	addCaption: 'Add Tag',
	newCaption: 'New Tag',
	validation: require('./validation.js'),
	historyNav: require('./history-nav.js'),
	acl: require('./acl.js'),
	aclHierarchy: require('./acl-hierarchy.js'),
	grids: require('./grids.js'),
	features: ['fileEnhancement'],
	api: {
		useGenericApi: true,
	},
	fields: [
		{
			field: 'tagName',
			type: 'textbox',
			caption: 'Tag Name',
			kind: 'editable',
			features: ['fileEnhancement'],
		},
	],
});
