const permHelper = require('../../lib/core/permission-helper.js');

module.exports = permHelper.initialize()
	.required({
		name: 'View Tag Records',
		features: ['fileEnhancement'],
		roles: ['view_tag_records'],
		actions: ['load', 'list', 'save_existing', 'remove'],
		conditions: [],
	})
	.required({
		name: 'View Tag Settings',
		features: ['fileEnhancement'],
		roles: ['view_tag'],
		actions: ['save_new', 'save_existing', 'remove'],
		conditions: [],
	})
	.required({
		name: 'Create Tag',
		features: ['fileEnhancement'],
		roles: ['create_tag'],
		actions: ['save_new'],
		conditions: [{
			attributes: {
				id: null,
			},
		}],
	})
	.required({
		name: 'Edit Tag',
		features: ['fileEnhancement'],
		roles: ['edit_tag'],
		actions: ['save_new'],
		conditions: [{
			attributes: {
				'!id': null,
			},
		}],
	})
	.required({
		name: 'Remove Tag',
		features: ['fileEnhancement'],
		roles: ['remove_tag'],
		actions: ['remove'],
		conditions: [],
	})
	.value();
