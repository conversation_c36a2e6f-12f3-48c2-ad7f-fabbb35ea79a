const extend = require('../extend.js');
const standardFilterConfig = require('../standard-filter-config.js');

const parentField = 'gridFilterId';
const parentEntity = {
	base: 'sys',
	name: 'gridFilter',
};

module.exports = extend(standardFilterConfig, {
	table: 'sys_grid_data_filter',
	entity: {
		base: 'sys',
		name: 'gridDataFilter',
	},
	search: false,
	caption: 'Grid Data Filter',
	captionPlural: 'Grid Data Filters',
	addCaption: 'Add Grid Data Filter',
	newCaption: 'New Grid Data Filter',
	acl: require('./acl.js'),
	validation: require('./validation'),
	computeFunctions: require('./compute-functions.js'),
	configurationImport: true,
	importTransformer: 'grid_data_filter',
	parents: [
		{
			entity: parentEntity,
			field: parentField,
		},
	],
	fields: [
		{
			field: parentField,
			type: 'id',
			kind: 'editable',
			caption: 'Grid Filter',
			dbIndex: true,
		},
	],
	joins: [
		{
			referenceField: parentField,
			table: 'sys_grid_filter',
			fields: [
				'createdBy',
				'gridName',
				'userId',
			],
		},
	],
});
