const extend = require('../extend.js');
const standardConfig = require('../standard-config.js');

module.exports = extend(standardConfig, {
	db: 'default',
	table: 'sys_user_specific_permission',
	entity: {
		base: 'sys',
		name: 'user_specific_permission',
	},
	search: false,
	validation: require('./validation.js'),
	caption: 'User Specific Permission',
	captionPlural: 'User Specific Permissions',
	addCaption: 'Add User Specific Permission',
	newCaption: 'New Uesr Specific Permission',
	parents: [
		{
			entity: {
				base: 'sys',
				name: 'user',
			},
			field: 'userId',
		},
		{
			entity: {
				base: 'sys',
				name: 'permission',
			},
			field: 'permissionId',
		},
	],
	fields: [
		{
			field: 'userId',
			type: 'user',
			caption: 'User',
			kind: 'editable',
			dbIndex: true,
		},
		{
			field: 'permissionId',
			type: 'id',
			caption: 'Permission Id',
			kind: 'editable',
			dbIndex: true,
		},
	],
	joins: [
		{
			referenceField: 'permissionId',
			table: 'sys_permission',
			fields: [
				'code',
			],
		},
	],
	audit: {
		child: true,
		parentType: {
			base: 'sys',
			name: 'permission',
		},
		parentFieldId: 'permissionId',
		allowNavigateTo: true,
		cmd: {
			load: {
				viewed: true,
			},
			save: {
				created: true,
				updated: {
					options: {
						changes: {
							excludeFields: ['id'],
						},
					},
				},
			},
			remove: {
				deleted: true,
			},
		},
	},
});
