const extend = require('../extend.js');
const standardConfig = require('../standard-config.js');

module.exports = extend(standardConfig, {
	db: 'default',
	table: 'sys_usage_rule',
	entity: {
		base: 'sys',
		name: 'usage_rule',
	},
	caption: 'Usage Rule',
	captionPlural: 'Usage Rules',
	addCaption: 'Add Usage Rule',
	newCaption: 'New Usage Rule',
	validation: require('./validation.js'),
	fields: [
		{
			field: 'startYear',
			type: 'number',
			caption: 'Start Year',
			kind: 'editable',
		},
		{
			field: 'startMonth',
			type: 'number',
			caption: 'Start Month',
			kind: 'editable',
		},
		{
			field: 'endYear',
			type: 'number',
			caption: 'End Year',
			kind: 'editable',
		},
		{
			field: 'endMonth',
			type: 'number',
			caption: 'End Month',
			kind: 'editable',
		},
		{
			field: 'seatLimit',
			type: 'number',
			caption: 'Seat Limit',
			kind: 'editable',
		},
		{
			field: 'storageLimit',
			type: 'number',
			caption: 'Storage Limit',
			kind: 'editable',
		},
		{
			field: 'overagePricePerSeat',
			type: 'decimal',
			caption: 'Overage Price Per Seat',
			kind: 'editable',
		},
		{
			field: 'overagePricePerGB',
			type: 'decimal',
			caption: 'Overage Price Per GB',
			kind: 'editable',
		},
		{
			field: 'reportingOveragePrice',
			type: 'decimal',
			caption: 'Overage Reporting Price Per Seat',
			kind: 'editable',
		},
		{
			field: 'reportingUserLimit',
			type: 'number',
			caption: 'Overage Reporting Seat Limit',
			kind: 'editable',
		},
	],
});
