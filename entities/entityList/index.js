const extend = require('../extend.js');
const standardConfig = require('../standard-config.js');

module.exports = extend(standardConfig, {
	db: 'default',
	table: 'sys_entity_list',
	entity: {
		base: 'sys',
		name: 'entity_list',
	},
	api: {
		useGenericApi: true,
	},
	acl: require('./acl.js'),
	caption: 'Entity List',
	captionPlural: 'Entity Lists',
	addCaption: 'Add Entity List',
	newCaption: 'New Entity List',
	allowMetaDataHardDelete: true,
	parents: [
		{
			entity: {
				base: 'sys',
				name: 'user_list',
			},
			field: 'userListId',
		},
	],
	fields: [
		{
			field: 'userListId',
			type: 'id',
			caption: 'List Id',
			kind: 'editable',
			dbIndex: true,
		},
		{
			field: 'entityId',
			type: 'textbox',
			caption: 'Entity ID',
			kind: 'editable',
		},
		{
			field: 'entityType',
			type: 'textbox',
			caption: 'Entity Type',
			kind: 'editable',
		},
	],
	joins: [
		{
			referenceField: 'userListId',
			table: 'sys_user_list',
			fields: [
				'name',
				'createdBy',
			],
		},
	],
	audit: {
		child: true,
		parentType: {
			base: 'sys',
			name: 'user_list',
		},
		parentFieldId: 'userListId',
		allowNavigateTo: true,
		cmd: {
			load: {
				viewed: true,
			},
			save: {
				created: true,
				updated: {
					options: {
						changes: {
							excludeFields: ['id'],
						},
					},
				},
			},
			remove: {
				deleted: true,
			},
		},
	},
});
