const permHelper = require('../../lib/core/permission-helper.js');

module.exports = permHelper.initialize()
	.required({
		name: 'Inherit User List ACL',
		roles: ['bypass_inherited_acl'],
		actions: ['load', 'list', 'save_new', 'save_existing', 'remove'],
		conditions: ['sys/user_list::{userListId}::load'],
	})
	/**
	 * If list functionality is added to any other entity,
	 * we will need to inherit entity's acl in this file similar to `Inherit Case ACL`
	 *
	 * ES filters are not able to apply ACL correctly for inheritance conditions as
	 * entity_list doesn't have joined fields for all the entities.
	 * So count may be incorrect but we have a backend solution in place for correct counts
	 *
	 * It's only ES where this ACL rule will not work correctly but we should still have it in place
	 * if someone plans to load/list in the workflows
	 */
	.required({
		name: 'Inherit Case ACL',
		roles: ['bypass_inherited_acl'],
		actions: ['load', 'list', 'save_new', 'save_existing', 'remove'],

		conditions: [{
			attributes: {
				entityType: '-/sys/case',
			},
		}, 'sys/case::{entityId}::load'],
	})
	.value();
