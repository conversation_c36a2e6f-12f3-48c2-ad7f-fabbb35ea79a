var extend = require('../extend.js');
var standardConfig = require('../standard-config.js');

module.exports = extend(standardConfig, {
	db: 'default',
	table: 'sys_template',
	entity: {
		base: 'sys',
		name: 'template',
	},
	api: {
		useGenericApi: true,
		mainFileField: 'files',
	},
	caption: 'Template',
	captionPlural: 'Templates',
	addCaption: 'Add Template',
	newCaption: 'New Template',
	gridDescriptorField: 'name',
	acl: require('./acl.js'),
	aclHierarchy: require('./acl-hierarchy.js'),
	grids: require('./grids.js'),
	validation: require('./validation.js'),
	rules: require('./rules.js'),
	historyNav: require('./history-nav.js'),
	exportTransformer: 'template',
	configurationExport: true,
	audit: require('./audit.js'),
	usageStatistics: require('./usage-statistics.js'),
	model: function(){
		return require('../../public/models/template-model.js');
	},
	collection: function(){
		return require('../../public/collections/templates-collection.js');
	},
	view: function(){
		return require('../../public/views/settings/data/templates/template-details-view.js');
	},
	fields: [
		{
			field: 'name',
			type: 'textbox',
			caption: 'Template Name',
			kind: 'editable',
		},
		{
			field: 'files',
			type: 'file[]',
			kind: 'editable',
			caption: 'Add File',
			typeOptions: {
				canDelete: false,
				supportedFormats: ['.docx'],
			},
			excludeFromSaveAndCopy: true,
		},
		{
			field: 'locale',
			type: 'picklist',
			caption: 'Locale',
			typeOptions: {
				picklistName: 'supported_languages',
				filter: 'existingLanguagesFilter',
			},
			kind: 'editable',
			dbIndex: true,
		},
		{
			field: 'fileType',
			type: 'picklist',
			caption: 'File Type',
			typeOptions: {
				picklistName: 'template_file_types',
			},
			kind: 'editable',
		},
		{
			field: 'filesVersionHistory',
			type: 'file[]',
			kind: 'editable',
			caption: 'Files Version History',
			excludeFromSaveAndCopy: true,
		},
	],
});
