const statHelper = require('../../shared/stat-helper.js')('sys_template');
const sharedUtils = require('../../shared/utils.js')();

module.exports = [
	{
		category: 'settings_data',
		key: 'totalTemplates',
		query: statHelper.countActiveEntity(),
	},
	{
		category: 'settings_data',
		key: 'templatesByType',
		query: statHelper.countActiveEntityByPicklistGroup(),
		options: {
			groupingField: 'file_type',
			picklistName: 'template_file_types',
			datasetTranslation(dataset, translate) {
				return statHelper.getPicklistDatasetTrans(this, dataset, translate);
			},
		},
	},
	{
		category: 'storage',
		key: 'sizeOfTemplates',
		query(knex, options, callback) {
			const summary = {
				totalSize: 0,
			};
			const {
				pipeline,
				BulkTransformStream,
				DrainStream,
			} = options;
			const listStream = knex
				.select('sys_template.files_version_history as files')
				.from('sys_template')
				.where('sys_template.sys_active', true)
				.whereNotNull('sys_template.files_version_history')
				.stream();
			const accumulatorStream = sharedUtils.getStreamToCalculateTotalSize(BulkTransformStream, summary, 'files');
			const drainStream = new DrainStream();

			return pipeline(
				listStream,
				accumulatorStream,
				drainStream,
				err => callback(err, summary.totalSize),
			);
		},
	},
];
