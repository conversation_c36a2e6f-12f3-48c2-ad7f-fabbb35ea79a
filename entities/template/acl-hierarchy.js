const permHelper = require('../../lib/core/permission-helper.js');

module.exports = [{
	type: 'group',
	caption: 'Templates',
	parentPermission: 'view_data_settings',
	permission: 'view_template_settings',
	options: [
		{
			caption: 'View',
			sequence: 2,
			disabled: true,
			permission: 'view_template',
		},
		{
			permission: 'create_template',
			caption: 'Create',
			tooltip: 'Create templates',
			sequence: 1,
		},
		{
			type: 'group',
			permission: permHelper.getEditGroupPermissionCode('template'),
			caption: 'Edit',
			sequence: 3,
			options: [{
				permission: 'edit_template',
				caption: 'Save',
				tooltip: 'Edit templates',
			}],
		},
		{
			permission: 'remove_template',
			caption: 'Remove',
			tooltip: 'Delete templates',
			sequence: 4,
		},
	],
}];
