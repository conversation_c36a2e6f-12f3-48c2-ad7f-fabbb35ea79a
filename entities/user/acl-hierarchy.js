module.exports = [{
	type: 'group',
	caption: 'User',
	parentPermission: 'view_access_settings',
	permission: 'view_user_settings',
	options: [
		{
			tooltip: 'View User Details',
			permission: 'view_user_details',
			caption: 'View',
			sequence: 1,
		},
		{
			permission: 'create_user',
			caption: 'Create',
			tooltip: 'Create a user',
			sequence: 2,
			dependencies: ['view_user_details'],
		},
		{
			permission: 'edit_other_users_profile',
			caption: 'Edit',
			tooltip: 'Edit a user',
			sequence: 3,
			dependencies: ['view_user_details'],
		},
		{
			caption: 'Remove',
			disabled: true,
			sequence: 4,
			permission: 'remove_user',
		},
		{
			permission: 'view_user_audit_log',
			caption: 'View History',
			tooltip: 'View actions done to a user\'s profile',
			dependencies: ['view_user_details'],
			sequence: 5,
		},
		{
			permission: 'view_user_activity_log',
			caption: 'View Activity',
			tooltip: 'View user activity for individual profiles under Settings -> Access -> Users',
			dependencies: ['view_user_details'],
			sequence: 6,
		},
		{
			permission: 'view_export_audit_log',
			caption: 'View User Export Activity',
			tooltip: 'View user export activity',
			dependencies: ['view_user_activity_log'],
			sequence: 7,
		},
		{
			permission: 'view_import_audit_log',
			caption: 'View User Import Activity',
			tooltip: 'View user import activity',
			dependencies: ['view_user_activity_log'],
			sequence: 8,
		},
	],
}];
