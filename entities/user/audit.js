const excludedFields = [
	'password',
	'yellowfinHash',
	'yellowfinSalt',
	'roles',
	'rounds',
	'perm',
	'passwordResetSession',
	'passwordResetCode',
	'passwordExpiry',
	'passwordExpiryReminder',
	'lastLoginDate',
	'deactivateJob',
	'accountDeactivationReminderJob',
	'id',
	'when',
	'perm.roles',
	'createdBy',
	'lastUpdatedBy',
	'lastUpdatedDate',
];
const reference = {
	displayFields: [
		'name',
	],
};


module.exports = {
	hiddenLogFilter: {
		field: 'type',
		value: 'guest',
	},
	cmd: {
		load: {
			viewed: {
				options: {
					reference,
				},
			},
		},
		save: {
			created: {
				condition: {
					composite: 'all',
					rules: [
						{
							path: 'diff.id.updatedValue',
							comparator: 'exists',
						}],
				},
				options: {
					changes: {
						excludeFields: excludedFields,
					},
					reference,
				},
			},
			updated: {
				options: {
					changes: {
						excludeFields: excludedFields,
					},
					reference,
				},
			},

			changed_password: {
				condition: {
					composite: 'all',
					rules: [{
						path: 'diff.password',
						comparator: 'exists',
					}],
				},
				options: {
					changes: {
						excludeFields: excludedFields,
						// the pass values are empty anyway, but this is used not to
						// display empty row in changes column
					},
					reference,
				},
			},
		},
	},
};
