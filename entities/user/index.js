const extend = require('../extend.js');
const standardConfig = require('../standard-config.js');

module.exports = extend(standardConfig, {
	db: 'default',
	table: 'sys_user',
	entity: {
		base: 'sys',
		name: 'user',
	},
	normalizeMultiValuePicklistEntries: true,
	caption: 'User Profile',
	captionPlural: 'User Profiles',
	addCaption: 'Add User Profile',
	newCaption: 'New User Profile',
	// custom isel context name as "user" is already being used
	customIselName: 'userProfile',
	partiallyDynamic: true,
	createPivotView: true,
	pivotViewExtraColumns: ['user_role_id'],
	gridDescriptorField: 'name',
	acl: require('./acl.js'),
	aclHierarchy: require('./acl-hierarchy.js'),
	audit: require('./audit.js'),
	rules: require('./rules.js'),
	grids: require('./grids.js'),
	validation: require('./validation.js'),
	computeFunctions: require('./compute-functions.js'),
	icon: 'fa-user',
	includeInDataDictionary: true,
	report: false,
	usageStatistics: require('./usage-statistics.js'),
	esIndexRecordFilter(data) {
		const isStaticServiceAccount = data.type === 'service' && data.static === true;
		const isPortalUser = data.type === 'portal';
		return !isStaticServiceAccount && !isPortalUser;
	},
	model() {
		return require('../../public/models/user-model.js');
	},
	collection() {
		return require('../../public/collections/users-collection.js');
	},
	view() {
		return require('../../public/views/settings/access/user/user-details-view.js');
	},
	staticFieldWorkflows: [
		require('./static-field-workflows/user-status.js'),
		require('./static-field-workflows/user-password-expired-status.js'),
	],
	fields: [
		{
			field: 'ssoUser',
			type: 'checkbox',
			caption: 'SSO User',
			kind: 'editable',
			dataImportMappableOverride: false,
		},
		{
			field: 'nick',
			type: 'textbox',
			caption: 'User ID',
			kind: 'editable',
			excludeFromSaveAndCopy: true,
			dbIndex: true,
			showOnPortal: true,
		},
		{
			field: 'type',
			type: 'picklist',
			caption: 'Type',
			kind: 'editable',
			typeOptions: {
				picklistName: 'user_types',
			},
			dbIndex: true,
			showOnPortal: true,
			dataImportMappableOverride: false,
			dataImportDefaultValue: 'local',
		},
		{
			field: 'email',
			type: 'email',
			typeOptions: {
				disableWhitelist: true,
			},
			caption: 'Email',
			kind: 'editable',
			excludeFromSaveAndCopy: true,
			showOnPortal: true,
			dbIndex: true,
			esSort: [
				'email._exact',
			],
		},
		{
			field: 'phoneNumber',
			type: 'phone-number',
			caption: 'Phone Number',
			kind: 'editable',
			excludeFromSaveAndCopy: true,
		},
		{
			field: 'name',
			type: 'textbox',
			caption: 'Name',
			kind: 'computed',
			kindOptions: {
				computeFunction: 'userName',
			},
			dataImportMappableOverride: true,
			excludeFromSaveAndCopy: true,
		},
		{
			field: 'firstName',
			type: 'textbox',
			caption: 'First Name',
			kind: 'editable',
			excludeFromSaveAndCopy: true,
		},
		{
			field: 'lastName',
			type: 'textbox',
			caption: 'Last Name',
			kind: 'editable',
			excludeFromSaveAndCopy: true,
		},
		{
			field: 'service',
			type: 'json',
			kind: 'hidden',
			excludeFromSaveAndCopy: true,
			caption: 'Service',
		},
		{
			field: 'active',
			type: 'yesno',
			caption: 'Active',
			kind: 'editable',
			dbIndex: true,
			alwaysInApiSelectedFields: true,
			showOnPortal: true,
			dataImportMappableOverride: false,
		},
		{
			field: 'when',
			type: 'datetime',
			kind: 'hidden',
			excludeFromSaveAndCopy: true,
			caption: 'When',
		},
		{
			field: 'confirmed',
			type: 'checkbox',
			typeOptions: {
				allowNull: true,
			},
			kind: 'hidden',
			caption: 'Confirmed',
		},
		{
			field: 'confirmcode',
			type: 'textbox',
			kind: 'hidden',
			caption: 'Confirm Code',
		},
		{
			field: 'salt',
			type: 'textbox',
			kind: 'hidden',
			caption: 'Salt',
		},
		{
			field: 'password',
			type: 'textbox',
			caption: 'Password',
			kind: 'custom',
			kindOptions: {
				flags: {
					audit: false,
					schema: true,
					search: false,
					apiWritable: true,
					apiExternalWritable: false,
					computedOnSave: false,
					computedOnRead: false,
					formVisible: false,
					gridVisible: false,
					gridSortable: false,
					searchVisible: false,
					formattedData: false,
					gridExportable: false,
					reportable: false,
				},
			},
		},
		{
			field: 'dateregistered',
			type: 'datetime',
			kind: 'hidden',
			caption: 'Date Registered',
			excludeFromSaveAndCopy: true,
		},
		{
			field: 'perm',
			type: 'json',
			kind: 'hidden',
			caption: 'Permission',
			excludeFromSaveAndCopy: true,
			showOnPortal: true,
		},
		{
			field: 'locale',
			type: 'picklist',
			caption: 'Language',
			typeOptions: {
				picklistName: 'supported_languages',
				filter: 'existingLanguagesFilter',
			},
			kind: 'editable-external',
			showOnPortal: true,
		},
		{
			field: 'iterations',
			type: 'number',
			caption: 'Password Iterations',
			kind: 'hidden',
		},
		{
			field: 'keyLength',
			type: 'number',
			caption: 'Password Key Length',
			kind: 'hidden',
		},
		{
			field: 'digest',
			type: 'textbox',
			caption: 'Password Digest',
			kind: 'hidden',
		},
		{
			field: 'yellowfinHash',
			type: 'textbox',
			kind: 'hidden',
			excludeFromSaveAndCopy: true,
			caption: 'Yellowfin Hash',
		},
		{
			field: 'yellowfinSalt',
			type: 'textbox',
			kind: 'hidden',
			excludeFromSaveAndCopy: true,
			caption: 'Yellowfin Salt',
		},
		{
			field: 'yellowfinUsername',
			type: 'textbox',
			kind: 'system',
			caption: 'Yellowfin Username',
			excludeFromSaveAndCopy: true,
		},
		{
			field: 'signature',
			type: 'texteditor',
			caption: 'Signature',
			kind: 'editable',
		},
		{
			field: 'userRoleId',
			type: 'picklistSelectizeApi',
			typeOptions: {
				picklistName: 'user_roles',
				missingValueTranslation: 'Unknown User Role',
			},
			caption: 'User Role',
			kind: 'editable',
			dbIndex: true,
			esSort: [
				'userRoleId__name._exactKeywordLowercase',
			],
			showOnPortal: true,
		},
		{
			field: 'passwordResetSession',
			type: 'textbox',
			kind: 'hidden',
			excludeFromSaveAndCopy: true,
			caption: 'Password Reset Session',
		},
		{
			field: 'passwordResetCode',
			type: 'textbox',
			kind: 'hidden',
			excludeFromSaveAndCopy: true,
			caption: 'Password Reset Code',
		},
		{
			field: 'passwordExpiry',
			type: 'datetime',
			kind: 'hidden',
			caption: 'Password Expiry',
			showOnPortal: true,
		},
		{
			field: 'isPasswordExpired',
			caption: 'Password Expired',
			type: 'yesno',
			kind: 'custom',
			kindOptions: {
				flags: {
					audit: false,
					schema: false,
					search: false,
					apiWritable: false,
					apiExternalWritable: false,
					computedOnSave: false,
					computedOnRead: false,
					formVisible: true,
					gridVisible: true,
					gridSortable: false,
					searchVisible: false,
					formattedData: false,
					gridExportable: false,
					reportable: false,
				},
			},
		},
		{
			field: 'passwordExpiryReminder',
			type: 'textbox',
			kind: 'hidden',
			caption: 'Password Expiry Reminder',
		},
		{
			field: 'lastLoginDate',
			type: 'datetime',
			kind: 'system',
			caption: 'Last Login Date',
		},
		{
			field: 'deactivateJob',
			type: 'textbox',
			kind: 'hidden',
			caption: 'Deactivate Job',
		},
		{
			field: 'accountDeactivationReminderJob',
			type: 'textbox',
			kind: 'hidden',
			caption: 'Account Deactivation Reminder',
		},
		{
			field: 'status',
			caption: 'Status',
			type: 'picklist',
			typeOptions: {
				picklistName: 'user_status',
			},
			kind: 'custom',
			kindOptions: {
				flags: {
					audit: false,
					schema: false,
					search: false,
					apiWritable: false,
					apiExternalWritable: false,
					computedOnSave: false,
					computedOnRead: false,
					formVisible: true,
					gridVisible: true,
					gridSortable: false,
					searchVisible: false,
					formattedData: false,
					gridExportable: false,
					reportable: false,
				},
			},
			cellTemplate(fs) {
				return fs.readFileSync(`${__dirname}/cell-templates/status.dust`, 'utf8');
			},
		},
		{
			field: 'gridRowCount',
			type: 'dropdown',
			typeOptions: {
				staticData: 'globalConfig.gridRowCountOptions',
				useAppData: true,
			},
			kind: 'editable',
			caption: 'User Default Grid Row Count',
			excludeFromSaveAndCopy: true,
			dataImportMappableOverride: false,
		},
		{
			field: 'themeId',
			type: 'picklistSelectizeApi',
			typeOptions: {
				picklistName: 'themes',
			},
			caption: 'Theme',
			kind: 'editable-external',
			showOnPortal: true,
			dbIndex: true,
			esSort: [
				'themeId__name._exactKeywordLowercase',
			],
			dataImportMappableOverride: false,
		},
		{
			field: 'caseCaptureRedirect',
			type: 'picklist',
			typeOptions: {
				picklistName: 'case_capture_redirect_options',
			},
			kind: 'editable',
			caption: 'Case Capture Redirect Option',
			dataImportMappableOverride: false,
		},
		{
			field: 'portalUserSubType',
			type: 'picklist',
			caption: 'Sub Type',
			kind: 'editable',
			typeOptions: {
				picklistName: 'portal_user_sub_types',
			},
			dataImportMappableOverride: false,
		},
		{
			field: 'authenticatedBySession',
			type: 'id',
			caption: 'Authenticated By Session',
			kind: 'hidden',
		},
		// Portal form fields
		{
			field: 'isReturningPortalUser',
			type: 'yesno',
			typeOptions: {
				allowNull: true,
			},
			caption: 'Are you a returning user?',
			kind: 'form-only',
		},
		{
			field: 'createPortalUserAccount',
			type: 'yesno',
			typeOptions: {
				allowNull: true,
			},
			caption: 'Would you like to receive updates?',
			kind: 'form-only',
		},
		{
			field: 'portalUserRemainAnonymous',
			type: 'yesno',
			typeOptions: {
				allowNull: true,
			},
			caption: 'Would you like to remain anonymous?',
			kind: 'form-only',
		},
		{
			field: 'portalUserProvideEmail',
			type: 'radio',
			typeOptions: {
				picklistName: 'portal_user_provide_email',
				orientation: 'horizontal',
			},
			caption: 'Would you like to confidentially provide your email address?',
			kind: 'form-only',
		},
		{
			field: 'portalUserEmail',
			type: 'email',
			typeOptions: {
				linkWithSystemUser: false,
			},
			caption: 'Email Address',
			kind: 'form-only',
		},
		{
			field: 'portalUserUsername',
			type: 'textbox',
			caption: 'Username',
			kind: 'form-only',
		},
		{
			field: 'portalUserNewPassword',
			type: 'secret',
			caption: 'Enter Your New Password',
			kind: 'form-only',
			typeOptions: {
				hideShowButton: true,
				revealOnHoverAndFocus: false,
				disableEncrypt: true,
			},
		},
		{
			field: 'portalUserConfirmedPassword',
			type: 'secret',
			caption: 'Confirm Your New Password',
			kind: 'form-only',
			typeOptions: {
				hideShowButton: true,
				revealOnHoverAndFocus: false,
				disableEncrypt: true,
			},
		},
		{
			field: 'portalLoginUsernameEmail',
			type: 'textbox',
			caption: 'Username',
			kind: 'form-only',
			typeOptions: {
				nameAttrValue: 'username',
			},
		},
		{
			field: 'portalLoginPassword',
			type: 'secret',
			caption: 'Password',
			kind: 'form-only',
			typeOptions: {
				hideShowButton: true,
				revealOnHoverAndFocus: false,
				disableEncrypt: true,
				isPassword: true,
				nameAttrValue: 'password',
			},
		},
		{
			field: 'hasReportAccess',
			type: 'yesno',
			caption: 'Reporting Access',
			kind: 'system',
		},
		{
			field: 'static',
			caption: 'Static Service Account',
			type: 'checkbox',
			typeOptions: {
				allowNull: true,
			},
			kind: 'hidden',
			features: ['serviceAccountAuthentication'],
		},
		{
			field: 'apiKey0',
			caption: 'API Key 0',
			type: 'secret',
			typeOptions: {
				disableEncrypt: false,
			},
			kind: 'hidden',
			features: ['serviceAccountAuthentication'],
		},
		{
			field: 'apiKey1',
			caption: 'API Key 1',
			type: 'secret',
			typeOptions: {
				disableEncrypt: false,
			},
			kind: 'hidden',
			features: ['serviceAccountAuthentication'],
		},
		{
			field: 'apiKey0ExpiryDate',
			caption: 'API Key 0 Expiry Date',
			type: 'datetime',
			kind: 'system',
			features: ['serviceAccountAuthentication'],
		},
		{
			field: 'apiKey1ExpiryDate',
			caption: 'API Key 1 Expiry Date',
			type: 'datetime',
			kind: 'system',
			features: ['serviceAccountAuthentication'],
		},
		{
			field: 'apiKey0ExpiryJob',
			caption: 'API Key 0 Expiry Job ID',
			type: 'code',
			kind: 'hidden',
			features: ['serviceAccountAuthentication'],
		},
		{
			field: 'apiKey1ExpiryJob',
			caption: 'API Key 1 Expiry Job ID',
			type: 'code',
			kind: 'hidden',
			features: ['serviceAccountAuthentication'],
		},
		{
			field: 'apiKey0ExpiryNotificationJob',
			caption: 'API Key 0 Expiry Notification Job ID',
			type: 'code',
			kind: 'hidden',
			features: ['serviceAccountAuthentication'],
		},
		{
			field: 'apiKey1ExpiryNotificationJob',
			caption: 'API Key 1 Expiry Notification Job ID',
			type: 'code',
			kind: 'hidden',
			features: ['serviceAccountAuthentication'],
		},
		{
			field: 'apiKeyExpiryDisabled',
			caption: 'API Key Expiry Disabled',
			type: 'checkbox',
			kind: 'hidden',
			typeOptions: {
				allowNull: true,
			},
			features: ['serviceAccountAuthentication'],
		},
	],
	joins: [
		{
			referenceField: 'userRoleId',
			table: 'sys_user_role',
			fields: [
				'name',
				'filterOperator',
				'features',
			],
		},
		{
			referenceField: 'themeId',
			table: 'sys_theme',
			fields: [
				'name',
				'deletedDate',
			],
		},
	],
});
