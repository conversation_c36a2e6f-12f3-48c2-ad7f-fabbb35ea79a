const _ = require('lodash');
const standardConfigFields = require('../standard-config').fields;
const permHelper = require('../../lib/core/permission-helper.js');

const standardFieldsFilterMap = standardConfigFields.reduce((acc, fieldDef) => {
	if (fieldDef.field === 'id') return acc;
	acc[fieldDef.field] = false;
	return acc;
}, {});

function isUserRoleFeatureEnabled(obj, enabledFeatures = []) {
	const userRoleFeatures = obj?.userRoleId__features;
	if (!userRoleFeatures || !Array.isArray(userRoleFeatures) || userRoleFeatures.length === 0) {
		return true;
	}
	// Check if all features in the userRole are present in enabledFeatures
	return _.difference(userRoleFeatures, enabledFeatures).length === 0;
}

function isTwoWayPortalOptionsEnabled(context) {
	return context.options?.enablePortal
		&& context.options?.portalSubmissionAccess === 'Two-Way';
}

module.exports = permHelper.initialize()
	.filterPortalFields({
		joinFieldsWhitelist: [
			'themeId__name',
		],
	})
/* Only users given this role will be able to see those fields on their own user  */
	.filter({
		name: 'view users details',
		roles: ['view_user_details'],
		actions: ['load', 'list'],
		conditions: [{
			attributes: {
				id: '{user.id}',
			},
		}],
		filters: {
			lastLoginDate: false,
			yellowfinUsername: false,
			dateJoined: false,
			service: false,
			status: false,
			userSpecificPermissionCodes: false,
			deactivateJob: false,
			accountDeactivationReminderJob: false,
			userRoleId__filters: false,
			userRoleId__filterOperator: false,
			themeId__deletedDate: false,
			email__name: false,
			isPasswordExpired: false,
			...standardFieldsFilterMap,
		},
	})

	/* Only users given this role will be able to load these fields for other users */
	.filter({
		name: 'view users details',
		roles: ['view_user_details'],
		actions: ['load', 'list'],
		conditions: [{
			attributes: {
				'!id': '{user.id}',
			},
		}],
		filters: {
			locale: false,
			password: false,
			signature: false,
			active: false,
			perm: false,
			type: false,
			portalUserSubType: false,
			authenticatedBySession: false,
			caseCaptureRedirect: false,
			lastLoginDate: false,
			yellowfinUsername: false,
			dateJoined: false,
			service: false,
			status: false,
			userSpecificPermissionCodes: false,
			deactivateJob: false,
			accountDeactivationReminderJob: false,
			gridRowCount: false,
			passwordExpiry: false,
			userRoleId__filters: false,
			userRoleId__filterOperator: false,
			themeId__deletedDate: false,
			email__name: false,
			isPasswordExpired: false,
			static: false,
			apiKey0: false,
			apiKey1: false,
			apiKey0ExpiryDate: false,
			apiKey1ExpiryDate: false,
			apiKey0ExpiryJob: false,
			apiKey1ExpiryJob: false,
			apiKey0ExpiryNotificationJob: false,
			apiKey1ExpiryNotificationJob: false,
			apiKeyExpiryDisabled: false,
			...standardFieldsFilterMap,
		},
	})
	/* Users without this role will only be able to edit these felids on their profile */
	.filter({
		name: 'Edit own non profile fields',
		// TODO: this role should be changed in 9.0 (ITPL-25162)
		roles: ['edit_other_users_profile'],
		actions: ['save_new', 'save_existing'],
		conditions: [{
			attributes: {
				id: '{user.id}',
			},
		}],
		filters: {
			locale: true,
			themeId: true,
			signature: true,
			phoneNumber: true,
			caseCaptureRedirect: true,
			gridRowCount: true,
			// following properties will be blocked from user change by business logic
			// but because they are set by business logic when changing own password,
			// we can't have acl filtering them out
			password: true,
			passwordExpiry: true,
			passwordResetCode: true,
			passwordResetSession: true,
			salt: true,
		},
	})
	.required({
		name: 'Access User With Disabled Feature User Role',
		roles: ['access_user_with_disabled_feature_user_role'],
		actions: ['load', 'list', 'save_new', 'save_existing', 'remove'],
		conditions: [{
			fn(obj, context) {
				if (!isUserRoleFeatureEnabled(obj, context.enabledFeatures)) return { ok: true };
				return { ok: false };
			},
			esFilter(context, attributePrefix = '') {
				return {
					bool: {
						must_not: [
							{ terms: { userRoleId__features: context.enabledFeatures } },
						],
					},
				};
			},
			selectedFields(opts, callback) {
				return callback(null, ['id', 'userRoleId__features']);
			},
		}],
	})
	.required({
		name: 'Access Disabled Hotline User',
		roles: ['access_disabled_hotline_user'],
		actions: ['load', 'list', 'save_existing', 'save_new', 'remove'],
		conditions: [{
			attributes: {
				userRoleId__name: 'Hotline Agent',
			},
		}, {
			fn(obj, context) {
				if (!isTwoWayPortalOptionsEnabled(context)) return { ok: true };
				return { ok: false };
			},
			esFilter(context, attributePrefix = '') {
				if (!isTwoWayPortalOptionsEnabled(context)) {
					return {
						bool: {
							must_not: [{
								term: {
									userRoleId__name: 'Hotline Agent',
								},
							}],
						},
					};
				}
				return { bool: { must_not: [{ exists: { field: 'id' } }] } };
			},
			selectedFields(opts, callback) {
				return callback(null, ['id', 'userRoleId__name']);
			},
		}],
	})
	// View
	.required({
		name: 'View System Users',
		roles: ['access_system_users'],
		actions: ['load', 'list', 'save_new', 'save_existing', 'remove'],
		conditions: [{
			attributes: {
				type: 'system',
			},
		}],
	})
	.required({
		name: 'View Static Service Accounts',
		roles: ['access_static_service_accounts'],
		actions: ['load', 'list', 'save_new', 'save_existing', 'remove'],
		conditions: [{
			attributes: {
				type: 'service',
				static: true,
			},
		}],
	})
	.required({
		name: 'View Service Accounts',
		roles: ['access_service_accounts'],
		actions: ['load', 'list', 'save_new', 'save_existing', 'remove'],
		conditions: [{
			attributes: {
				type: 'service',
				'!static': true,
			},
		}],
	})
	.required({
		name: 'View Guest Users',
		roles: ['view_guest_users'],
		actions: ['load', 'list', 'save_new', 'save_existing', 'remove'],
		conditions: [{
			attributes: {
				type: 'guest',
			},
		}],
	})
	.required({
		name: 'View Non-Portal Users',
		roles: ['view_user'],
		actions: ['load', 'list', 'save_existing', 'remove'],
		conditions: [{
			attributes: {
				'!type': 'portal',
			},
		}],
	})
	.required({
		name: 'View Portal Users',
		roles: ['access_portal_users'],
		actions: ['load', 'list', 'save_new', 'save_existing', 'remove'],
		conditions: [{
			attributes: {
				type: 'portal',
			},
		}],
	})
	.required({
		name: 'View Other Portal User',
		roles: ['view_other_portal_user'],
		actions: ['load', 'list'],
		conditions: [{
			attributes: {
				type: 'portal',
				id: '{!user.id}',
			},
		}],
	})
	// Create
	.required({
		name: 'Create Users',
		roles: ['create_user'],
		actions: ['save_new'],
		conditions: [{
			attributes: {
				id: null,
			},
		}],
	})
	// Edit
	.required({
		name: 'Edit Users',
		roles: ['edit_user'],
		actions: ['save_new'],
		conditions: [{
			attributes: {
				'!id': null,
				'!type': 'portal',
			},
		}],
	})
	.required({
		name: 'Edit Portal Users',
		roles: ['edit_portal_user'],
		actions: ['save_new'],
		conditions: [{
			attributes: {
				'!id': null,
				type: 'portal',
			},
		}],
	})
	.required({
		name: 'Edit Own User Profile',
		roles: ['edit_own_user_profile'],
		actions: ['save_existing'],
		conditions: [{
			attributes: {
				id: '{user.id}',
			},
		}],
	})
	.required({
		name: 'Edit Other Users Profile',
		roles: ['edit_other_users_profile'],
		actions: ['save_existing'],
		conditions: [{
			attributes: {
				'!id': '{user.id}',
			},
		}],
	})
	// Remove
	.required({
		name: 'Remove Users',
		roles: ['remove_user'],
		actions: ['remove'],
		conditions: [],
	})
	.value();
