const utils = require('../../shared/utils')();

module.exports = {
	actionIsChangePassword(data) {
		return data.action === 'change_password';
	},
	isSystemUser(data) {
		return utils.isSystemUserId(data.id);
	},
	isLocalUser(data) {
		return data.type === 'local';
	},
	isConfidentialPortalUser(data) {
		return data.type === 'portal' && data.portalUserSubType === 'confidential';
	},
	isHotlineUser(data) {
		return data.type === 'hotline';
	},
	isActive(data) {
		return data.active === true;
	},
	needsUsernameAndPassword(data){
		/** *
		*	1. If user is not local, NO need to send PW and UN
		*	2. If user is inactive, NO need to send PW and UN (trying to do this gives generalerror)
		*	3. If user password is expired, need to send PW and UN
		*	4. If user has not logged on yet but has not received password, need to send PW and UN
		*	Note: When user is created, password gets generated, but once email is sent, passwordExpiry
		*	is generated
		*/
		return data.type === 'local' && data.active
			&& (data.isPasswordExpired || (data.lastLoginDate === null && !data.passwordExpiry));
	},
	// Portal user form rules
	isReturningPortalUser(data) {
		return data.isReturningPortalUser === true;
	},
	isNotReturningPortalUser(data) {
		return data.isReturningPortalUser === false;
	},
	createPortalUserAccount(data) {
		return data.createPortalUserAccount === true;
	},
	portalUserRemainAnonymous(data) {
		return data.portalUserRemainAnonymous === true;
	},
	portalUserNotRemainAnonymous(data) {
		return data.portalUserRemainAnonymous === false;
	},
	portalUserProvideEmail(data) {
		return data.portalUserProvideEmail === 'yes';
	},
	portalUserNotProvideEmail(data) {
		return data.portalUserProvideEmail === 'no';
	},
	portalUserAuthenticatedDuringSubmission(data) {
		return data.portalUserAuthenticated === true;
	},
};
