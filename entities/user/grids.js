module.exports = {
	'main-users': {
		sortColumn: 'name',
		sortOrder: 'asc',
		columns: [
			{ field: 'name' },
			{ field: 'userRoleId' },
			{ field: 'status' },
			{ field: 'email' },
			{ field: 'nick' },
		],
		defaultDynamicDataFilters: ['active', 'userRoleId', 'createdDate', 'lastUpdatedDate'],
	},
	'investigative-team-members': {
		sortColumn: 'name',
		sortOrder: 'asc',
		columns: [
			{ field: 'name' },
			{ field: 'nick' },
			{ field: 'email' },
		],
		defaultDynamicDataFilters: ['name'],
	},
	'denied-access-users': {
		sortColumn: 'name',
		sortOrder: 'asc',
		columns: [
			{ field: 'name' },
			{ field: 'nick' },
			{ field: 'email' },
		],
		defaultDynamicDataFilters: ['name'],
	},
};
