const statHelper = require('../../shared/stat-helper.js')('sys_user');
const utils = require('../../shared/utils')();

module.exports = [
	{
		category: 'user',
		key: 'activeUsers',
		query(knex, options, callback) {
			utils.countUsers(true, knex, callback);
		},
	},
	{
		category: 'user',
		key: 'inactiveUsers',
		query(knex, options, callback) {
			utils.countUsers(false, knex, callback);
		},
	},
	{
		category: 'user',
		key: 'activeUsersByRole',
		query(knex, options, callback) {
			utils.countActiveUsersByRole(knex, callback);
		},
	},
	{
		category: 'user',
		key: 'reportAccessUsers',
		query: statHelper.countEntity({
			where() {
				this.where('sys_user.has_report_access', true)
					.andWhere('sys_user.active', true);
			},
		}),
	},
];
