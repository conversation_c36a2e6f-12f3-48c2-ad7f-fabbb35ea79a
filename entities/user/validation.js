module.exports = {
	mandatory$: [
		'locale',
		'active',
		'nick',
		'userRoleId',
		'type',
	],

	dependentMandatory$: [{
		condition: 'actionIsChangePassword',
		fields: ['oldPassword', 'password', 'confirmedPassword'],
	}, {
		condition: 'isLocalUser',
		fields: [
			'email',
			'firstName',
			'lastName',
		],
	}, {
		condition: 'isPortalUser',
		fields: [
			'portalUserSubType',
		],
	}, {
		condition: 'isConfidentialPortalUser',
		fields: [
			'email',
		],
	}, {
		condition: 'isHotlineUser',
		fields: [
			'ssoUser',
		],
	}],

	phoneNumber$: ['phoneNumber'],
};
