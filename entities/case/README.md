# Case
## Case Details Tabs

This is found in the case details view, these tabs are displayed when details tab is selected, this allows to configure the set of sub tabs within that. Basically views that group fields of a case (overview, investigation, resolution fields) etc.

The system uses the `caseDetailsTabs` attribute on the entity definition to define the tabs. 

If you would like to overwrite this configuration, follow these steps:

* Create a file in your config project under `(config_project)/entities/case/case-details-tabs.js`
* Reference that file in the case `index.js` under the `caseDetailsTabs` attribute.
* This file follows the same as the default one, returns an array of tab objects. The properties are as following:
	* `id` a unique identifyer for the tab (ex overview) This also helps for testing to be able to click these dynamic tabs.
	* `caption` translation key for the tab display name
	* `view` a function that requires the object definition / class of the view to instantiate and render. By default the case model will be passed to it as the `model` attribute. If you would like to overwrite this behaviour see `model`.
	* `model` (optional) a function that requires the model object definition / class to create within the view if it doens't want the case model as the main model (for example case linking has it's own model). By doing this, the case model will be passed through with the attribute of `caseModel` and it's up to you to handle that in the `onInit` function of your view.
	* `formConfigName` (optional) the name of the form config that will be rendered in the tab's view (ex: `case-overview`). Setting this will allow the form to be populated in the form-builder.
	* `displayRule` (optional) rule defining when the tab should be hidden/shown
	
##Dynamic Case Details Tabs

In order to use dynamic case tabs, `enableDynamicCaseDetailsTab` flag must be set in `config/options.global.js`.

An insertion point must be added to the case details tabs and all dynamic case tabs will be displayed in that location. To move the location of the insertion point within the list make sure to run `make sync-dynamic-forms`. 

```
caseDetailsTabs: [
	...
	{
		id: 'dynamic-tabs-insertion-point',
		caption: 'dynamic_tabs_insertion_point',
	},
	...
]
```

## Case Number Format

This handles case number formats that are assigned in the platform. By default we have: YYYY-***********.

The format of a case number is defined through a global option called `caseNumberFormat`. It contains an array of formatters and static injections.

Looking at the default one

```
['YYYY', '-', 'MM', '-', 'NNNNNNNN']
```
We can see YYYY, MM and NNNNNNN are formatters and - as a static injection. The mapping for this is defined in `isight_main_v5_beta/lib/core/case-number-formatter.js`. In there we see a list of default formatters and an array of static variables.

In order to modify the generation of case numbers, you can overwrite the global option `caseNumberFormat` in the config project's global options, and then create an extension file under `(config_project)/lib/core/case-number-formatter-ex.js` which returns an object of key/value formatters. Each formatter takes 3 arguments, seneca, args, and done. Seneca is the instance from the request, args is the seneca arguments including args.ent if you need to do data-specific formats as well as done which is called with either an error as the first argument or the format result as a string in the second argument.

The system runs through each array element and appends the formatted result to an empty string.

### Examples

##### Ex 1
To generate a format with fiscal years starting in November, your case-number-format-ex.js file would look like this:

```
var moment = require('moment');

var formatters = {
	// Overwrite platform's 'YYYY' definition and use this one instead
	'YYYY': function (seneca, args, done) {
		// Add 2 months to today's date, November 1st becomes January 1st and the year is then +1.
		done(null, moment().add('month', 2).format('YYYY'));
	}
};

module.exports = formatters;
```

##### Ex 2
To have a prefix in your case number, your options.global.js would look like this:

```
caseNumberFormat: ['PREFIX', '-', 'YYYY', '-', 'MM', '-', 'NNNNNNNN']
```

And case-number-format-ex.js would look like:

```
var formatters = {
	// Overwrite platform's 'static' definition and use these static strings instead
	// NOTE: we re-add '-' from platform as we're using it
	'static': ['-', 'PREFIX']
};

module.exports = formatters;
```
## Case Team Fields

Configuration of fields to display under the Manage Team screen. By default there isn't any fields. There is one standard that will always be there and it's the owner field, explaining why it isn't configurable. These fields will appear in order after the owner field.

**Location:**

- Default config: isight_main_v5_beta/config/options.case-team-fields.js
- Overwriting config: (config_project)/config/options.case-team-fields.js

Note that overwriting will overwrite everything from the default config.

## Case Tombstone

The case tombstone is found in the case details view. This allows for displaying fields on the top that don't dissapear when the user changes tabs in the lower section. They are limited to 3 fields per row, the first one is always the case owner with it's capability to reassign, assign etc..

The system uses RequireJS to load the configuration of the case tombstone. By default, the system loads the `isight_main_v5_beta/public/config/options.case-tombstone.js` file which has the standard vanilla version of a case tombstone. 

If you would like to overwrite this configuration, follow these steps:

* Create a file in your config project under `(config_project)/public/config/options.case-tombstone-ex.js`
* This file follows the same as the default one, returns an array of objects that define which `field` and `caption` to display. The `field` attribute is the attribute of the case entity to be displayed (ex caseStatus) and the `caption` attribute is a translation key that will be passed to the translation mechanism and be displayed in the UI.
* Within `(config_project)/public/js/require-config.js` we need to change the reference from `options.case-tombstone.js` to `options.case-tombstone-ex.js`. We need to add the following:

```
{
	paths: {
		// Add new file into RequireJS
		'options.case-tombstone-ex': 'custom/config/options.case-tombstone-ex',
	},
	map: {
		'*': {
			// Overwrite default reference with this one for the whole system.
			'options.case-tombstone': 'options.case-tombstone-ex'
		}
	}
}
```