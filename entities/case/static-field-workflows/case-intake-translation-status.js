module.exports = {
	name: 'case-intake-translation-status',
	field: 'intakeTranslationStatus',
	entity: {
		zone: undefined,
		base: 'sys',
		name: 'case',
	},
	values: [
		{
			value: 'complete',
			indicator: 'success',
		},
		{
			value: 'pending',
			indicator: 'warning',
		},
		{
			value: 'error',
			indicator: 'danger',
		},
		{
			value: null,
		},
	],
	strict: true,
	transitions: [
		{
			id: 'case-intake-translation-status-blank',
			from: [null, undefined],
			to: [null, undefined],
		},
		{
			id: 'case-intake-translation-status-initial',
			from: [null, undefined],
			to: 'pending',
		},
		{
			id: 'case-intake-translation-status-complete',
			from: 'pending',
			to: 'complete',
		},
		{
			id: 'case-intake-translation-status-error',
			from: 'pending',
			to: 'error',
		},
		{
			id: 'case-intake-translation-status-pending',
			from: ['complete', 'error'],
			to: 'pending',
		},
	],
	conditions: [],
	displayRule: 'shouldShowTranslationStatusWorkflow',
};
