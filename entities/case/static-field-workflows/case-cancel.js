module.exports = {
	name: 'case-cancel-static-workflow',
	field: 'canceled',
	entity: {
		zone: undefined,
		base: 'sys',
		name: 'case',
	},
	values: [
		{
			value: true,
			onSet: {
				cancelDate: '{now}',
			},
			onUnset: {
				cancelDate: null,
				cancelReason: null,
			},
			indicator: 'default',
		},
		{
			value: false,
			onUnset: {
				restoreReason: null,
			},
		},
	],
	strict: true,
	transitions: [
		{
			id: 'case-create',
			from: [null, undefined],
			to: [false, true],
		},
		{
			id: 'case-cancelled',
			caption: 'case_canceled',
			from: false,
			to: true,
			roles: [
				'cancel_a_case',
				{
					role: 'cancel_a_case_as_owner',
					condition: {
						owner: '{user.id}',
					},
				},
				{
					role: 'cancel_a_case_as_investigative_team_member',
					condition: {
						investigativeTeamMembers: '{user.id}',
					},
				},
			],
			conditions: ['hasCancelReason', 'isNotScheduled'],
			displayRule: '(!isClosed || canEditWhenClosed) && !caseCanceled',
		},
		{
			id: 'case-restored',
			caption: 'case_restored',
			from: true,
			to: false,
			roles: ['restore_a_case'],
			conditions: ['hasRestoreReason', 'isNotScheduled', 'isNotPurged'],
			displayRule: '!isClosed || canEditWhenClosed',
		},
	],
	conditions: [
		{
			affectsUi: false,
			name: 'hasRestoreReason',
			message: 'restoreReason not supplied',
			attributes: {
				restoreReason: '*!empty',
			},
		},
		{
			affectsUi: false,
			name: 'hasCancelReason',
			message: 'cancelReason not supplied',
			attributes: {
				cancelReason: '*!empty',
			},
		},
		{
			affectsUi: true,
			name: 'isNotPurged',
			message: 'Case is scheduled for purge or purged',
			attributes: {
				datePurged: '*empty',
			},
		},
		{
			affectsUi: true,
			name: 'isNotScheduled',
			message: 'Case is scheduled for purge',
			attributes: {
				pendingPurgeDate: '*empty',
			},
		},
	],
};
