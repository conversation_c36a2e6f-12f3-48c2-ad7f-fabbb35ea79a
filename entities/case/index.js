var extend = require('../extend.js');
var standardFlagConfig = require('../standard-flag-config.js');
var standardIntakeTranslationConfig = require('../standard-intake-translation-config.js');
var standardIntakeTranslationConfigWithFlag = extend(
	standardIntakeTranslationConfig, standardFlagConfig,
);

module.exports = extend(standardIntakeTranslationConfigWithFlag, {
	db: 'default',
	table: 'sys_case',
	entity: {
		base: 'sys',
		name: 'case',
	},
	joins: [
		{
			referenceField: 'sourceJob',
			table: 'sys_event',
			fields: [
				'name',
				'jobId',
			],
		},
	],
	normalizeMultiValuePicklistEntries: true,
	allowExternalSearch: (enabledFeatures, twoWayPortalAccess) => {
		return (enabledFeatures.includes('twoWayPortal') && twoWayPortalAccess);
	},
	allowOnPortal: true,
	bypassValidationOnDraft: true,
	allowOnHotline: true,
	caption: 'Case',
	captionPlural: 'Cases',
	addCaption: 'Add Case',
	newCaption: 'New Case',
	gridDescriptorField: 'caseNumber',
	customWorkflows: true,
	ruleEvents: true,
	allowDataAggregation: true,
	allowAdvancedSearch: true,
	allowQuickSearch: true,
	dataExport: true,
	allowPurge: true,
	report: true,
	partiallyDynamic: true,
	createPivotView: true,
	enablePortalUserNotifications: true,
	excludeFromAggregation: true,
	acl: require('./acl/acl.js'),
	aclHierarchy: require('./acl/acl-hierarchy.js'),
	audit: require('./audit.js'),
	rules: require('./rules.js'),
	grids: require('./grids.js'),
	validation: require('./validation.js'),
	historyNav: require('./history-nav.js'),
	workflowHistoryUrl: '/case/{id}/history/workflowHistory',
	icon: 'fa-folder',
	includeInDataDictionary: true,
	enableRecordSourceView: true,
	usageStatistics: require('./usage-statistics.js'),
	allowInGridViews: true,
	staticFieldWorkflows: [
		require('./static-field-workflows/case-cancel.js'),
		require('./static-field-workflows/date-purged.js'),
		require('./static-field-workflows/case-closed.js'),
		require('./static-field-workflows/external-record.js'),
		require('./static-field-workflows/case-intake-translation-status.js'),
		require('./static-field-workflows/case-confidential.js'),
		require('./static-field-workflows/purge-flag-status.js'),
	],
	entityEvaluators: require('./entity-evaluators.js'),
	model: function () {
		return require('../../public/models/case-model.js');
	},
	collection: function () {
		return require('../../public/collections/cases-collection.js');
	},
	view: function () {
		return require('../../public/views/case/case-details-view.js');
	},
	caseDetailsTabs: require('./case-details-tabs.js'),
	caseDetailsTabsExternal: require('./case-details-tabs-external.js'),
	rowTemplates: {
		small: function () {
			return require('./row-templates/small.dust');
		},
		medium: function () {
			return require('./row-templates/medium.dust');
		},
		tiny: function () {
			return require('./row-templates/tiny.dust');
		},
	},
	esDefaultFilters: function () {
		var query = this.query('es');
		return query.and([
			query.is_not('canceled', true),
			query.is_not('sysActive', false),
		]).toQuery();
	},
	fields: [
		{
			field: 'caseNumber',
			type: 'sequenceNumber',
			caption: 'Case #',
			kind: 'system',
			gridWidth: 160,
			esBoost: 3,
			excludeFromRedact: true,
			dbIndex: true,
			dbIndexType: 'hash',
			cellTemplate: function (fs) {
				return fs.readFileSync(
					__dirname + '/cell-templates/case-number-cell-tmpl.dust',
					'utf8',
				);
			},
			showOnPortal: true,
			dataImportMappableOverride: true,
		},
		{
			field: 'confidential',
			type: 'yesno',
			typeOptions: {
				allowNull: true,
			},
			caption: 'Confidential',
			kind: 'editable',
		},
		{
			field: 'cancelDate',
			type: 'datetime',
			caption: 'Case Cancel Date',
			kind: 'custom',
			kindOptions: {
				flags: {
					audit: true,
					schema: true,
					search: true,
					apiWritable: true,
					apiExternalWritable: false,
					computedOnSave: false,
					computedOnRead: false,
					formVisible: true,
					gridVisible: true,
					gridSortable: true,
					searchVisible: true,
					formattedData: true,
					gridExportable: true,
					reportable: false,
				},
			},
			dataImportMappableOverride: false,
		},
		{
			field: 'cancelReason',
			type: 'picklist',
			caption: 'Case Cancel Reason',
			typeOptions: {
				picklistName: 'cancel_reasons',
			},
			kind: 'custom',
			kindOptions: {
				flags: {
					audit: true,
					schema: true,
					search: true,
					apiWritable: true,
					apiExternalWritable: false,
					computedOnSave: false,
					computedOnRead: false,
					formVisible: true,
					gridVisible: true,
					gridSortable: true,
					searchVisible: true,
					formattedData: true,
					gridExportable: true,
					reportable: false,
				},
			},
			dataImportMappableOverride: false,
		},
		{
			field: 'caseType',
			type: 'picklist',
			caption: 'Case Type',
			typeOptions: {
				picklistName: 'case_types',
				availableForCaseFiltering: true,
			},
			kind: 'editable',
		},
		{
			field: 'parent',
			type: 'id',
			caption: 'Case Parent',
			kind: 'hidden',
		},
		{
			field: 'dateRecorded',
			type: 'datetime',
			caption: 'Date Recorded',
			kind: 'system',
		},
		{
			field: 'dateClosed',
			type: 'datetime',
			caption: 'Date Closed',
			kind: 'system',
			dataImportMappableOverride: true,
		},
		{
			field: 'reportedBy',
			type: 'user',
			caption: 'Reported By',
			kind: 'system',
			dataImportMappableOverride: true,
		},
		{
			field: 'investigativeTeamMembers',
			type: 'user[]',
			caption: 'Investigative Team Members',
			kind: 'editable',
		},
		{
			field: 'userBlacklist',
			type: 'user[]',
			caption: 'User Denied Access',
			kind: 'editable',
		},
		{
			field: 'systemBlacklist',
			type: 'user[]',
			caption: 'System Blacklist',
			kind: 'custom',
			kindOptions: {
				flags: {
					audit: true,
					schema: true,
					search: true,
					apiWritable: false,
					apiExternalWritable: false,
					computedOnSave: false,
					computedOnRead: false,
					gridExportable: false,
					formVisible: false,
					gridVisible: false,
					gridSortable: false,
					searchVisible: false,
					formattedData: true,
					reportable: false,
				},
			},
		},
		{
			field: 'assignedBy',
			type: 'user',
			caption: 'Assigned By',
			kind: 'system',
			dataImportMappableOverride: true,
		},
		{
			field: 'owner',
			type: 'user',
			caption: 'Case Owner',
			kind: 'editable',
			typeOptions: {
				roleFilter: 'case_owner',
			},
			alwaysInApiSelectedFields: true,
			dataImportMappableOverride: true,
		},
		{
			caption: 'Assign Reason',
			field: 'reassignReason',
			type: 'picklist',
			typeOptions: {
				picklistName: 'reassign_reasons',
			},
			kind: 'editable',
			alwaysInApiSelectedFields: true,
		},
		{
			field: 'caseEmail',
			type: 'json',
			kind: 'hidden',
			caption: 'Case Email',
		},
		{
			field: 'dateAssigned',
			type: 'datetime',
			caption: 'Date Assigned',
			kind: 'system',
			dataImportMappableOverride: true,
		},
		{
			field: 'createdBySession',
			type: 'id',
			caption: 'Created By Session',
			kind: 'hidden',
			showOnHotline: true,
		},
		{
			field: 'lastSnapshotGenerationDate',
			type: 'datetime',
			caption: 'Last Snapshot Generation Date',
			kind: 'custom',
			kindOptions: {
				flags: {
					audit: true,
					schema: true,
					search: true,
					apiWritable: false,
					apiExternalWritable: false,
					computedOnSave: false,
					computedOnRead: false,
					formVisible: true,
					gridVisible: true,
					gridSortable: true,
					searchVisible: true,
					formattedData: true,
					gridExportable: true,
					reportable: false,
				},
			},
			excludeFromRedact: true,
			showOnHotline: true,
		},
		// We're making this field writeable for cases
		{
			field: 'excludeFromPurge',
			kind: 'editable',
		},
		{
			field: 'acceptTermsAndConditions',
			caption: 'Terms and Conditions',
			type: 'checkbox',
			kind: 'custom',
			kindOptions: {
				// Editable-external but not visible on grids
				flags: {
					audit: true,
					schema: true,
					search: true,
					readable: true,
					apiWritable: true,
					apiExternalWritable: true,
					computedOnSave: false,
					computedOnRead: false,
					iselComputedOnSave: false,
					aggregateField: false,
					formVisible: true,
					gridVisible: false,
					gridSortable: false,
					searchVisible: true,
					formattedData: true,
					gridExportable: false,
					reportable: true,
				},
			},
			showOnPortal: true,
			dataImportMappableOverride: false,
		},
		{
			field: 'primaryPartyId',
			type: 'primaryParty',
			caption: 'Primary Party',
			kind: 'system',
		},
		{
			field: 'canceled',
			type: 'yesno',
			caption: 'Canceled',
			kind: 'custom',
			kindOptions: {
				flags: {
					audit: true,
					schema: true,
					search: true,
					apiWritable: true,
					apiExternalWritable: false,
					computedOnSave: false,
					computedOnRead: false,
					formVisible: true,
					gridVisible: true,
					gridSortable: true,
					searchVisible: true,
					formattedData: true,
					gridExportable: true,
					reportable: false,
				},
			},
			excludeFromRedact: true,
			showOnHotline: true,
		},
		{
			field: 'restoreReason',
			type: 'picklist',
			caption: 'Case Restore Reason',
			typeOptions: {
				picklistName: 'restore_reasons',
			},
			kind: 'editable',
		},
		{
			field: 'openBusinessDays',
			type: 'number',
			caption: 'Open Business Days',
			kind: 'system',
		},
		{
			field: 'totalBusinessDays',
			type: 'number',
			caption: 'Total Business Days',
			kind: 'system',
		},
		{
			field: 'openCalendarDays',
			type: 'number',
			caption: 'Open Calendar Days',
			kind: 'system',
		},
		{
			field: 'totalCalendarDays',
			type: 'number',
			caption: 'Total Calendar Days',
			kind: 'system',
		},
		{
			field: 'lastCaseAgeSyncDate',
			caption: 'Last Case Age Sync Date',
			type: 'datetime',
			kind: 'hidden',
			showOnHotline: true,
		},
		{
			field: 'createdCalendarYear',
			type: 'number',
			caption: 'Created Calendar Year',
			kind: 'system',
			typeOptions: {
				format: '0000',
			},
		},
		{
			field: 'createdCalendarMonth',
			type: 'number',
			caption: 'Created Calendar Month',
			kind: 'system',
		},
		{
			field: 'createdCalendarWeek',
			type: 'number',
			caption: 'Created Calendar Week',
			kind: 'system',
		},
		{
			field: 'createdCalendarQuarter',
			type: 'number',
			caption: 'Created Calendar Quarter',
			kind: 'system',
		},
		{
			field: 'createdFiscalYear',
			type: 'number',
			caption: 'Created Fiscal Year',
			kind: 'system',
			typeOptions: {
				format: '0000',
			},
		},
		{
			field: 'createdFiscalMonth',
			type: 'number',
			caption: 'Created Fiscal Month',
			kind: 'system',
		},
		{
			field: 'createdFiscalWeek',
			type: 'number',
			caption: 'Created Fiscal Week',
			kind: 'system',
		},
		{
			field: 'createdFiscalQuarter',
			type: 'number',
			caption: 'Created Fiscal Quarter',
			kind: 'system',
		},
		{
			field: 'closedCalendarYear',
			type: 'number',
			caption: 'Closed Calendar Year',
			kind: 'system',
			typeOptions: {
				format: '0000',
			},
		},
		{
			field: 'closedCalendarMonth',
			type: 'number',
			caption: 'Closed Calendar Month',
			kind: 'system',
		},
		{
			field: 'closedCalendarWeek',
			type: 'number',
			caption: 'Closed Calendar Week',
			kind: 'system',
		},
		{
			field: 'closedCalendarQuarter',
			type: 'number',
			caption: 'Closed Calendar Quarter',
			kind: 'system',
		},
		{
			field: 'closedFiscalYear',
			type: 'number',
			caption: 'Closed Fiscal Year',
			kind: 'system',
			typeOptions: {
				format: '0000',
			},
		},
		{
			field: 'closedFiscalMonth',
			type: 'number',
			caption: 'Closed Fiscal Month',
			kind: 'system',
		},
		{
			field: 'closedFiscalWeek',
			type: 'number',
			caption: 'Closed Fiscal Week',
			kind: 'system',
		},
		{
			field: 'closedFiscalQuarter',
			type: 'number',
			caption: 'Closed Fiscal Quarter',
			kind: 'system',
		},
		{
			field: 'externalRecord',
			type: 'checkbox',
			caption: 'External',
			kind: 'editable',
			typeOptions: {
				allowNull: false,
			},
			showOnPortal: true,
			showOnHotline: true,
			dataImportMappableOverride: false,
		},
		{
			field: 'reportedAnonymously',
			type: 'yesno',
			caption: 'Reported Anonymously',
			kind: 'editable-external',
			typeOptions: {
				allowNull: true,
			},
			showOnPortal: true,
			showOnHotline: true,
			dataImportMappableOverride: false,
		},
		{
			field: 'intakeMethod',
			type: 'picklist',
			typeOptions: {
				picklistName: 'intake_methods',
			},
			caption: 'Intake Method',
			kind: 'system',
			showOnPortal: true,
		},
		{
			field: 'reporterAccess',
			type: 'yesno',
			caption: 'Reporter Access',
			kind: 'system',
			showOnPortal: true,
		},
		{
			field: 'caseSummary',
			type: 'texteditor',
			caption: 'Case Summary',
			kind: 'editable',
			features: ['outcomeAssistant'],
		},
		{
			field: 'caseState',
			type: 'picklist',
			caption: 'Case State',
			typeOptions: {
				picklistName: 'case_states',
			},
			kind: 'system',
		},
		{
			field: 'aiBlocksCertified',
			caption: 'AI Blocks Generated Content Certified',
			type: 'checkbox',
			kind: 'editable',
			typeOptions: {
				allowNull: true,
			},
			features: ['outcomeAssistant'],
		},
		{
			field: 'pendingAccessRequests',
			caption: 'Pending Access Requests',
			type: 'checkbox',
			kind: 'system',
			typeOptions: {
				allowNull: false,
			},
			features: ['requestCaseAccess'],
		},
		{
			field: 'dataSource',
			caption: 'Data Source',
			type: 'textbox',
			kind: 'hidden',
		},
	],
	reportOptions: {
		virtualJoinFields: [
			{
				referenceField: 'primaryPartyId',
				joinField: 'partyName',
				canon: 'sys/party',
				caption: 'Primary Party Name',
			},
			{
				referenceField: 'lastWorkflowStateId',
				joinField: 'name',
				canon: 'sys/state',
				caption: 'Last Workflow Status Name',
				bypassAcl: true,
			},
		],
	},
	queryFilter: function ({ query, args }) {
		return query.and([
			args.showInactiveRecords !== true && query.is('sysActive', true),
			args.showCanceledCases$ !== true && query.is_not('canceled', true),
		]);
	},
});
