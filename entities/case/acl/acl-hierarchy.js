const permHelper = require('../../../lib/core/permission-helper.js');

module.exports = [
	{
		type: 'group',
		parentPermission: 'view_system_settings',
		caption: 'Canceled Cases',
		permission: 'case_settings',
		options: [
			{
				permission: 'view_canceled_cases',
				caption: 'View',
				tooltip: 'View canceled Cases',
			},
			{
				permission: 'restore_a_case',
				caption: 'Restore',
				tooltip: 'Restore a Case',
				dependencies: ['view_canceled_cases'],
			},
		],
	},
	{
		type: 'group',
		caption: 'Case',
		permission: 'case',
		sequence: 1,
		options: [
			{
				permission: 'create_case',
				caption: 'Create',
				tooltip: 'Submit a new Case',
				sequence: 1,
			},
			{
				permission: 'create_case_restricted_fields',
				caption: 'Create Restricted Fields',
				tooltip: 'Submit restricted fields',
				dependencies: ['create_case'],
			},
			{
				caption: 'Remove',
				disabled: true,
				permission: 'remove_case',
			},
			{
				caption: 'History',
				permission: 'view_case_history',
				tooltip: 'View Case Audit and Workflow and Action History',
			},
			{
				caption: 'View Source Data',
				permission: 'view_source_data',
				tooltip: 'View Source Data of any Record',
			},
			{
				caption: 'Set Reporter Access',
				permission: 'mark_case_external',
				disabled: true,
			},
			{
				type: 'group',
				caption: 'View',
				sequence: 2,
				tooltip: 'View a Case',
				permission: 'view_case',
				options: [
					{
						permission: 'view_active_cases',
						caption: 'All',
						tooltip: 'View active Case',
						sequence: 1,
					},
					{
						type: 'group',
						caption: 'Flags',
						sequence: 2,
						permission: 'view_case_flags',
						dependencies: ['view_active_cases'],
						options: [
							{
								permission: 'view_confidential_cases',
								caption: 'Confidential',
								tooltip: 'View a confidential Case',
								dependencies: ['view_active_cases'],
							},
							{
								permission: 'view_purged_cases',
								caption: 'Purged',
								tooltip: 'View purged Cases',
								dependencies: ['view_active_cases'],
							},
						],
					},
					{
						type: 'group',
						permission: 'view_case_team',
						caption: 'Team',
						tooltip: 'View the Manage Team section',
						dependencies: ['view_active_cases'],
						options: [
							{
								permission: 'view_investigative_team',
								caption: 'Investigative Team',
								tooltip: 'View the Investigation Team section',
								dependencies: ['view_active_cases'],
							},
							{
								permission: 'view_blacklist',
								caption: 'Access Denied List',
								tooltip: 'View the Access Denied section',
								dependencies: ['view_active_cases'],
							},
						],
					},
				],
			},
			{
				type: 'group',
				permission: permHelper.getEditGroupPermissionCode('case'),
				caption: 'Edit',
				tooltip: 'Update a Case',
				sequence: 3,
				dependencies: ['view_active_cases'],
				options: [
					{
						permission: 'edit_case_form',
						caption: 'Save',
						tooltip: 'Edit unrestricted fields',
						dependencies: ['view_active_cases'],
					},
					{
						permission: 'cancel_a_case',
						caption: 'Cancel a Case',
						tooltip: 'Cancel a Case',
						dependencies: ['view_active_cases'],
					},
					{
						permission: 'assign_a_case',
						caption: 'Assign a Case',
						tooltip: 'Assign a Case to a Case Owner',
						dependencies: ['view_active_cases'],
					},
					{
						permission: 'edit_confidential_case_field',
						caption: 'Mark Case confidential',
						tooltip: 'Set Case confidentiality flag',
						dependencies: ['view_active_cases'],
					},
					{
						permission: 'edit_exclude_from_purge',
						caption: 'Do Not Purge',
						tooltip: 'Mark Case as Do Not Purge',
						dependencies: ['view_active_cases'],
					},
					{
						permission: 'edit_closed_case',
						caption: 'Edit a Closed Case',
						tooltip: 'Edit & action a closed Case, and its children',
						dependencies: ['view_active_cases', 'edit_case_form'],
					},
					{
						permission: 'save_case_restricted_fields',
						caption: 'Save Restricted Fields',
						tooltip: 'Edit restricted fields',
						dependencies: ['view_active_cases', 'edit_case_form'],
					},
					{
						permission: 'submit_case_for_translation',
						caption: 'Submit for Translation',
						tooltip: 'Submit case for translation',
						dependencies: ['view_active_cases'],
						features: ['intakeTranslation'],
					},
					{
						permission: 'generate_ai_content',
						caption: 'Generate AI Content',
						tooltip: 'Can create and edit AI content generated by the Copilots.',
						dependencies: ['view_active_cases'],
						features: ['outcomeAssistant'],
						warningMessage: {
							title: 'Content will consider all case data',
							confirm: 'Grant permission',
							cancel: 'Cancel',
							message: 'Granting access to this permission allows a user role to create and download content which will reference all case information.',
						},
					},
					{
						type: 'group',
						permission: 'manage_case_team',
						caption: 'Team',
						tooltip: 'Update the Manage Team section',
						dependencies: ['view_active_cases'],
						options: [
							{
								permission: 'edit_investigative_team',
								caption: 'Investigative Team',
								tooltip: 'Add users to the Investigative Team',
								dependencies: ['view_investigative_team'],
							},
							{
								permission: 'edit_blacklist',
								caption: 'Access Denied List',
								tooltip: 'Add users to the Denied List',
								dependencies: ['view_blacklist', 'view_investigative_team'],
							},
							{
								permission: 'edit_case_roles',
								caption: 'Case Roles',
								disabled: true,
								dependencies: ['view_active_cases'],
							},
						],
					},
				],
			},
			{
				type: 'group',
				sequence: 4,
				caption: 'Ownership',
				permission: 'case_ownership',
				options: [
					{
						permission: 'case_owner',
						caption: 'Case Owner',
						type: 'group',
						options: [
							{
								caption: 'View',
								sequence: 1,
								permission: 'view_case_owner',
								type: 'group',
								options: [
									{
										permission: 'view_case_as_owner',
										caption: 'All',
										tooltip: 'Case owner can view their Case',
									},
									{
										type: 'group',
										caption: 'Flags',
										sequence: 2,
										permission: 'view_case_flags_as_owner',
										dependencies: ['view_case_as_owner'],
										options: [
											{
												permission: 'view_confidential_cases_as_owner',
												dependencies: ['view_case_as_owner'],
												caption: 'Confidential',
												tooltip: 'Case owner can view their confidential Cases',
											},
										],
									},
									{
										type: 'group',
										permission: 'view_case_team_as_owner',
										dependencies: ['view_case_as_owner'],
										tooltip: 'Case owner can view Manage Team section',
										caption: 'Team',
										options: [
											{
												permission: 'view_investigative_team_as_owner',
												dependencies: ['view_case_as_owner'],
												caption: 'Investigative Team',
												tooltip: 'Case owner can view the Investagtive Team',
											},
											{
												permission: 'view_blacklist_as_owner',
												dependencies: ['view_case_as_owner'],
												caption: 'Access Denied List',
												tooltip: 'Case owner can view the Denied List',
											},
										],
									},
								],
							},
							{
								caption: 'Edit',
								tooltip: 'Case owner can update their Case',
								sequence: 2,
								type: 'group',
								permission: 'edit_case_as_owner',
								dependencies: ['view_case_as_owner'],
								options: [
									{
										permission: 'edit_case_form_as_owner',
										caption: 'Save',
										tooltip: 'Case owner can Edit unrestricted fields',
										dependencies: ['view_case_as_owner'],
									},
									{
										permission: 'cancel_a_case_as_owner',
										caption: 'Cancel A Case',
										tooltip: 'Case owner can cancel their Case',
										dependencies: ['view_case_as_owner'],
									},
									{
										permission: 'assign_a_case_as_owner',
										caption: 'Assign A Case',
										tooltip: 'Case owner can reassign their Case',
										dependencies: ['view_case_as_owner'],
									},
									{
										permission: 'edit_confidential_case_field_as_owner',
										caption: 'Mark Case confidential',
										tooltip: 'Case owner can mark their Case confidential',
										dependencies: ['view_case_as_owner'],
									},
									{
										permission: 'edit_exclude_from_purge_as_owner',
										caption: 'Do Not Purge',
										tooltip: 'Case owner can mark their case as Do Not Purge',
										dependencies: ['view_case_as_owner'],
									},
									{
										permission: 'edit_closed_case_as_owner',
										caption: 'Edit a Closed Case',
										tooltip: 'Case Owner can edit & action a closed Case, and its children',
										dependencies: ['view_case_as_owner', 'edit_case_form_as_owner'],
									},
									{
										permission: 'save_case_restricted_fields_as_owner',
										caption: 'Save Restricted Fields',
										tooltip: 'Case owner can Edit restricted fields',
										dependencies: ['view_case_as_owner', 'edit_case_form_as_owner'],
									},
									{
										permission: 'submit_case_for_translation_as_owner',
										caption: 'Submit for Translation',
										tooltip: 'Case owner can Submit a case for translation',
										dependencies: ['view_case_as_owner'],
										features: ['intakeTranslation'],
									},
									{
										permission: 'generate_ai_content_as_owner',
										caption: 'Generate AI Content',
										tooltip: 'Can create and edit AI content generated by the Copilots.',
										dependencies: ['view_case_as_owner'],
										features: ['outcomeAssistant'],
										warningMessage: {
											title: 'Content will consider all case data',
											confirm: 'Grant permission',
											cancel: 'Cancel',
											message: 'Granting access to this permission allows a user role to create and download content which will reference all case information.',
										},
									},
									{
										type: 'group',
										permission: 'manage_case_team_as_owner',
										caption: 'Team',
										tooltip: 'Case owner can update the Manage Team section',
										dependencies: ['view_case_team_as_owner'],
										options: [
											{
												permission: 'edit_investigative_team_as_owner',
												caption: 'Investigative Team',
												tooltip: 'Case owner can add users to the Investigative Team',
												dependencies: ['view_investigative_team_as_owner'],
											},
											{
												permission: 'edit_blacklist_as_owner',
												caption: 'Access Denied List',
												tooltip: 'Case owner can add users to the Access Denied list',
												dependencies: ['view_blacklist_as_owner', 'view_investigative_team_as_owner'],
											},
											{
												permission: 'edit_case_roles_as_owner',
												caption: 'Case Roles',
												disabled: true,
												dependencies: ['view_case_as_owner'],
											},
										],
									},
								],
							},
						],
					},
					{
						permission: 'investigative_team_member',
						caption: 'Investigative Team Member',
						type: 'group',
						options: [
							{
								caption: 'View',
								sequence: 1,
								permission: 'view_case_investigative_team_member',
								type: 'group',
								options: [
									{
										permission: 'view_case_as_investigative_team_member',
										caption: 'All',
										tooltip: 'Investigative Team members can view their Case',
									},
									{
										type: 'group',
										caption: 'Flags',
										sequence: 2,
										permission: 'view_case_flags_as_investigative_team_member',
										dependencies: ['view_case_as_investigative_team_member'],
										options: [
											{
												permission: 'view_confidential_cases_as_investigative_team_member',
												dependencies: ['view_case_as_investigative_team_member'],
												caption: 'Confidential',
												tooltip: 'Investigative Team members can view their confidential Cases',
											},
										],
									},
									{
										type: 'group',
										permission: 'view_case_team_as_investigative_team_member',
										dependencies: ['view_case_as_investigative_team_member'],
										caption: 'Team',
										tooltip: 'Investigative Team members can view Manage Team section',
										options: [
											{
												permission: 'view_investigative_team_as_investigative_team_member',
												dependencies: ['view_case_as_investigative_team_member'],
												caption: 'Investigative Team',
												tooltip: 'Investigative Team members can view the Investigative Team',
											},
											{
												permission: 'view_blacklist_as_investigative_team_member',
												dependencies: ['view_case_as_investigative_team_member'],
												caption: 'Access Denied List',
												tooltip: 'Investigative Team members can view the Access Denied list',
											},
										],
									},
								],
							},
							{
								caption: 'Edit',
								tooltip: 'Investigative Team members can update their Case',
								sequence: 2,
								type: 'group',
								permission: 'edit_case_as_investigative_team_member',
								dependencies: ['view_case_as_investigative_team_member'],
								options: [
									{
										permission: 'edit_case_form_as_investigative_team_member',
										caption: 'Save',
										tooltip: 'Investigative Team members can Edit unrestricted fields',
										dependencies: ['view_case_as_investigative_team_member'],
									},
									{
										permission: 'cancel_a_case_as_investigative_team_member',
										caption: 'Cancel a Case',
										tooltip: 'Investigative Team members can cancel their Case',
										dependencies: ['view_case_as_investigative_team_member'],
									},
									{
										permission: 'assign_a_case_as_investigative_team_member',
										caption: 'Assign a Case',
										tooltip: 'Investigative Team members can assign their Case',
										dependencies: ['view_case_as_investigative_team_member'],
									},
									{
										permission: 'edit_confidential_case_field_as_investigative_team_member',
										caption: 'Mark Case Confidential',
										tooltip: 'Investigative Team members can mark their Case confidential',
										dependencies: ['view_case_as_investigative_team_member'],
									},
									{
										permission: 'edit_exclude_from_purge_as_investigative_team_member',
										caption: 'Do Not Purge',
										tooltip: 'Investigative Team members can mark their Case as Do Not Purge',
										dependencies: ['view_case_as_investigative_team_member'],
									},
									{
										permission: 'edit_closed_case_as_investigative_team_member',
										caption: 'Edit a Closed Case',
										tooltip: 'Investigative Team members can edit & action a closed Case, and its children',
										dependencies: ['view_case_as_investigative_team_member', 'edit_case_form_as_investigative_team_member'],
									},
									{
										permission: 'save_case_restricted_fields_as_investigative_team_member',
										caption: 'Save Restricted Fields',
										tooltip: 'Investigative Team members can Edit restricted fields',
										dependencies: ['view_case_as_investigative_team_member', 'edit_case_form_as_investigative_team_member'],
									},
									{
										permission: 'submit_case_for_translation_as_investigative_team_member',
										caption: 'Submit for Translation',
										tooltip: 'Investigative Team members can Submit a case for translation',
										dependencies: ['view_case_as_investigative_team_member'],
										features: ['intakeTranslation'],
									},
									{
										permission: 'generate_ai_content_as_investigative_team_member',
										caption: 'Generate AI Content',
										tooltip: 'Can create and edit AI content generated by the Copilots.',
										dependencies: ['view_case_as_investigative_team_member'],
										features: ['outcomeAssistant'],
										warningMessage: {
											title: 'Content will consider all case data',
											confirm: 'Grant permission',
											cancel: 'Cancel',
											message: 'Granting access to this permission allows a user role to create and download content which will reference all case information.',
										},
									},
									{
										type: 'group',
										permission: 'manage_case_team_as_investigative_team_member',
										caption: 'Team',
										tooltip: 'Investigative Team members can update the Manage Team section',
										dependencies: ['view_case_team_as_investigative_team_member'],
										options: [
											{
												permission: 'edit_investigative_team_as_investigative_team_member',
												caption: 'Investigative Team',
												tooltip: 'Investigative Team members can add users to Investigative Team',
												dependencies: ['view_investigative_team_as_investigative_team_member'],
											},
											{
												permission: 'edit_blacklist_as_investigative_team_member',
												caption: 'Access Denied List',
												tooltip: 'Investigative Team members can add users to the Denied List',
												dependencies: ['view_blacklist_as_investigative_team_member', 'view_investigative_team_as_investigative_team_member'],
											},
											{
												permission: 'edit_case_roles_as_investigative_team_member',
												caption: 'Case Roles',
												disabled: true,
												dependencies: ['view_case_as_investigative_team_member'],
											},
										],
									},
								],
							},
						],
					},
				],
			},
		],
	},
];
