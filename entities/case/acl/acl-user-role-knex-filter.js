// Going for practicality over purity here. It's more practical to have each SQL where statement
// on one line
/* eslint-disable max-len */

/**
 * No context, must use database view (vw_sys_case) with "u" referencing the user table.
 *
 * The function cross checks the role filters to see how many filters fail on the case row.
 * Based on the count and the operator set (all vs any), it will require a count of 0 when all
 * conditions must meet and will require <= (count of all filters - 1) when operator any is
 * selected. This means if you have 3 filters, it's ok when 2 filters fail,
 * but not 3 but "any" operator is used.
 */
module.exports = (knex, knexion) => {
	const filterOperatorQuery = knexion
		.select('filter_operator')
		.from('sys_user_role')
		.whereNull('sys_user_role.deleted_date')
		.where('sys_user_role.sys_active', true)
		.whereRaw('?? = ??', ['sys_user_role.id', 'u.user_role_id']);
	const roleFilterCountQuery = knexion
		.count('*')
		.from('sys_role_filter')
		.whereNull('sys_role_filter.deleted_date')
		.where('sys_role_filter.sys_active', true)
		.whereRaw('?? = ??', ['sys_role_filter.user_role_id', 'u.user_role_id']);
	const casePivotView = 'vw_sys_case_pivot';
	const userPivotView = 'vw_sys_user_pivot';
	const countQuery = knexion
		.count('*')
		.from('sys_role_filter')
		.leftJoin(casePivotView, 'sys_role_filter.case_field_yf', `${casePivotView}.column_name`)
		.leftJoin(userPivotView, 'sys_role_filter.user_field_yf', `${userPivotView}.column_name`)
		.whereNull('sys_role_filter.deleted_date')
		.where('sys_role_filter.sys_active', true)
		.where(`${casePivotView}.id`, knexion.raw('sys_case.id'))
		.where(`${userPivotView}.id`, knexion.raw('u.id'))
		.whereRaw('?? = ??', ['sys_role_filter.user_role_id', 'u.user_role_id'])
		.where(knex => {
			knex
				.orWhere(knex => {
					knex.whereRaw('sys_role_filter.operator = ?', ['=']);
					knex.where(knex => {
						knex
							.orWhere(knex => {
								// When operator is =, case field is text, user field is text
								knex
									.where(`${casePivotView}.value_column`, 'text_value')
									.where(`${userPivotView}.value_column`, 'text_value')
									// Inverse logic as we're trying to find role filters that fail
									.whereRaw(`(${casePivotView}.text_value != ${userPivotView}.text_value OR (${casePivotView}.text_value IS NULL AND ${userPivotView}.text_value IS NOT NULL) OR (${casePivotView}.text_value IS NOT NULL AND ${userPivotView}.text_value IS NULL) OR (${casePivotView}.text_value IS NULL AND ${userPivotView}.text_value IS NULL))`);
							})
							.orWhere(knex => {
								// When operator is =, case field is text, user field is text array
								knex
									.where(`${casePivotView}.value_column`, 'text_value')
									.where(`${userPivotView}.value_column`, 'text_array_value')
									// Inverse logic as we're trying to find role filters that fail
									.whereRaw(
										`(NOT ? OR (${casePivotView}.text_value IS NULL AND ${userPivotView}.text_array_value IS NOT NULL) OR (${casePivotView}.text_value IS NOT NULL AND ${userPivotView}.text_array_value IS NULL) OR (${casePivotView}.text_value IS NULL AND ${userPivotView}.text_array_value IS NULL))`,
										[knexion.custom.arrayContainsQuery(`${casePivotView}.text_value`, `${userPivotView}.text_array_value`, true)],
									);
							})
							.orWhere(knex => {
								// When operator is =, case field is text array, user field is text
								knex
									.where(`${casePivotView}.value_column`, 'text_array_value')
									.where(`${userPivotView}.value_column`, 'text_value')
									// Inverse logic as we're trying to find role filters that fail
									.whereRaw(
										`(NOT ? OR (${casePivotView}.text_array_value IS NULL AND ${userPivotView}.text_value IS NOT NULL) OR (${casePivotView}.text_array_value IS NOT NULL AND ${userPivotView}.text_value IS NULL) OR (${casePivotView}.text_array_value IS NULL AND ${userPivotView}.text_value IS NULL))`,
										[knexion.custom.arrayContainsQuery(`${userPivotView}.text_value`, `${casePivotView}.text_array_value`, true)],
									);
							})
							.orWhere(knex => {
								// When operator is =, case field is text array, user field is text array
								knex
									.where(`${casePivotView}.value_column`, 'text_array_value')
									.where(`${userPivotView}.value_column`, 'text_array_value')
									// Inverse logic as we're trying to find role filters that fail
									.whereRaw(
										`(NOT ? OR (${casePivotView}.text_array_value IS NULL AND ${userPivotView}.text_array_value IS NOT NULL) OR (${casePivotView}.text_array_value IS NOT NULL AND ${userPivotView}.text_array_value IS NULL) OR (${casePivotView}.text_array_value IS NULL AND ${userPivotView}.text_array_value IS NULL))`,
										[knexion.custom.arrayIntersectionQuery(`${userPivotView}.text_array_value`, `${casePivotView}.text_array_value`, true)],
									);
							});
					});
				})
				.orWhere(knex => {
					knex.whereRaw('sys_role_filter.operator = ?', ['!=']);
					knex.where(knex => {
						knex
							.orWhere(knex => {
								// When operator is !=, case field is text, user field is text
								knex
									.where(`${casePivotView}.value_column`, 'text_value')
									.where(`${userPivotView}.value_column`, 'text_value')
									// Inverse logic as we're trying to find role filters that fail
									.whereRaw(`(${casePivotView}.text_value = ${userPivotView}.text_value AND NOT (${casePivotView}.text_value IS NULL AND ${userPivotView}.text_value IS NULL))`);
							})
							.orWhere(knex => {
								// When operator is !=, case field is text, user field is text array
								knex
									.where(`${casePivotView}.value_column`, 'text_value')
									.where(`${userPivotView}.value_column`, 'text_array_value')
									// Inverse logic as we're trying to find role filters that fail
									.whereRaw(
										`(? AND NOT (${casePivotView}.text_value IS NULL AND ${userPivotView}.text_array_value IS NULL))`,
										[knexion.custom.arrayContainsQuery(`${casePivotView}.text_value`, `${userPivotView}.text_array_value`, true)],
									);
							})
							.orWhere(knex => {
								// When operator is !=, case field is text array, user field is text
								knex
									.where(`${casePivotView}.value_column`, 'text_array_value')
									.where(`${userPivotView}.value_column`, 'text_value')
									// Inverse logic as we're trying to find role filters that fail
									.whereRaw(
										`(? AND NOT (${casePivotView}.text_array_value IS NULL AND ${userPivotView}.text_value IS NULL))`,
										[knexion.custom.arrayContainsQuery(`${userPivotView}.text_value`, `${casePivotView}.text_array_value`, true)],
									);
							})
							.orWhere(knex => {
								// When operator is !=, case field is text array, user field is text array
								knex
									.where(`${casePivotView}.value_column`, 'text_array_value')
									.where(`${userPivotView}.value_column`, 'text_array_value')
									// Inverse logic as we're trying to find role filters that fail
									.whereRaw(
										`(? AND NOT (${casePivotView}.text_array_value IS NULL AND ${userPivotView}.text_array_value IS NULL))`,
										[knexion.custom.arrayIntersectionQuery(`${userPivotView}.text_array_value`, `${casePivotView}.text_array_value`, true)],
									);
							});
					});
				});
		});
	knex.whereRaw(
		'? > CASE WHEN ? = \'any\' THEN (? - 1) ELSE 0 END',
		[countQuery, filterOperatorQuery, roleFilterCountQuery],
	);
};
