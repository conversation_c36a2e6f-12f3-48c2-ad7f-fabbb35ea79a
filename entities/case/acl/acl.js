const permHelper = require('../../../lib/core/permission-helper.js');
const userRoleFilterConditions = require('./user-role-filter-conditions.js');

const groupEditPermCode = permHelper.getEditGroupPermissionCode('case');
const conditionForOther = {
	attributes: {
		owner: '{!user.id}',
		investigativeTeamMembers: '{!user.id}',
	},
};
const conditionForCaseAsOwner = {
	attributes: {
		owner: '{user.id}',
		investigativeTeamMembers: '{!user.id}',
	},
};
const conditionForCaseAsInvestigativeTeamMember = {
	attributes: {
		owner: '{!user.id}',
		investigativeTeamMembers: '{user.id}',
	},
};

const conditionForOwnerAndInvestigativeTeamMember = {
	attributes: {
		owner: '{user.id}',
		investigativeTeamMembers: '{user.id}',
	},
};

const conditionForNewCase = {
	attributes: {
		id: null,
	},
};

const conditionForExistingCase = {
	attributes: {
		'!id': null,
	},
};

const conditionForClosedCase = {
	attributes: {
		'!id': null,
		sysSubmitted: true,
		'!dateClosed': null,
	},
};

const conditionForExistingDraftCase = {
	attributes: {
		'!id': null,
		sysSubmitted: false,
	},
};

const conditionForExistingNonDraftCase = {
	attributes: {
		'!id': null,
		sysSubmitted: true,
	},
};

const conditionForNonCanceledCase = {
	attributes: {
		'!canceled': true,
	},
};

const saveFormFilter = {
	id: true,
	owner: true,
	userBlacklist: true,
	investigativeTeamMembers: true,
	// Have to also condier attributes set by workflows
	caseNumber: true,
	parent: true,
	dateRecorded: true,
	assignedBy: true,
	dateAssigned: true,
	createdBySession: true,
	pendingPurgeDate: true,
	datePurged: true,
	purgeReason: true,
	apiType: true,
	confidential: true,
	cancelReason: true,
	cancelDate: true,
	excludeFromPurge: true,
	userLists: true,
	canceled: true,
	copiedToId: true,
	copiedFromId: true,
};

module.exports = permHelper.initialize()
	.filterMarkForPurge()
	.filterPurge()
	.filterPortalFields()
	.filterHotlineFields()
	.filterRestrictEditFields({
		roles: ['create_case_restricted_fields'],
		conditions: [conditionForNewCase],
		actions: ['save_new'],
	})
	.filterRestrictEditFields({
		roles: ['create_case_restricted_fields'],
		conditions: [conditionForExistingDraftCase],
		actions: ['save_existing'],
	})
	.filterRestrictEditFields({
		roles: ['save_case_restricted_fields'],
		conditions: [conditionForExistingNonDraftCase, conditionForOther],
		actions: ['save_existing'],
	})
	.filterRestrictEditFields({
		roles: ['save_case_restricted_fields', 'save_case_restricted_fields_as_owner'],
		conditions: [conditionForExistingNonDraftCase, conditionForCaseAsOwner],
		actions: ['save_existing'],
	})
	.filterRestrictEditFields({
		roles: ['save_case_restricted_fields', 'save_case_restricted_fields_as_investigative_team_member'],
		conditions: [conditionForExistingNonDraftCase, conditionForCaseAsInvestigativeTeamMember],
		actions: ['save_existing'],
	})
	.filterRestrictEditFields({
		roles: [
			'save_case_restricted_fields',
			'save_case_restricted_fields_as_owner',
			'save_case_restricted_fields_as_investigative_team_member',
		],
		conditions: [
			conditionForExistingNonDraftCase,
			conditionForCaseAsOwner,
			conditionForCaseAsInvestigativeTeamMember,
		],
		actions: ['save_existing'],
	})
	.filter({
		name: 'Edit Case Portal Submission Fields On Creation',
		roles: ['edit_case_portal_submission_fields_on_creation'],
		actions: ['save_existing'],
		conditions: [permHelper.conditionForInternalRecord,
			{
				attributes: {
					'!sysSubmitted': true,
					'!id': null,
				},
			}],
		filters: {
			reportedAnonymously: false,
		},
		generatePermissions: ['external'],
	})
	.filter({
		name: 'Edit Case Portal Submission Fields On Creation',
		roles: ['edit_case_portal_submission_fields_on_creation'],
		actions: ['save_new'],
		conditions: [permHelper.conditionForInternalRecord,
			{
				attributes: {
					id: null,
					sysSubmitted: true,
				},
			}],
		filters: {
			reportedAnonymously: false,
		},
		generatePermissions: ['external'],
	})
	.filter({
		name: 'Edit Case Portal Submission Fields After Creation',
		roles: ['edit_case_portal_submission_fields_after_creation'],
		actions: ['save_existing'],
		conditions: [
			{
				attributes: {
					sysSubmitted: true,
					'!id': null,
				},
			}],
		filters: {
			reportedAnonymously: false,
		},
	})
	.filter({
		name: 'Mark Case External',
		roles: ['mark_case_external'],
		actions: ['save_new', 'save_existing'],
		conditions: [],
		filters: {
			externalRecord: false,
		},
	})
	.filter({
		name: 'View Primary Party Field',
		roles: ['view_party'],
		actions: ['list', 'load', 'save_new', 'save_existing'],
		conditions: [],
		filters: {
			primaryPartyId: false,
		},
	})
	// Filter - edit exclude_from_purge case field
	.filter({
		name: 'Edit Do Not Purge Field',
		roles: ['edit_exclude_from_purge'],
		actions: ['save_new', 'save_existing'],
		conditions: [conditionForOther],
		filters: {
			excludeFromPurge: false,
		},
	})
	.filter({
		name: 'Edit Do Not Purge As Owner',
		roles: ['edit_exclude_from_purge', 'edit_exclude_from_purge_as_owner'],
		actions: ['save_new', 'save_existing'],
		conditions: [conditionForCaseAsOwner],
		filters: {
			excludeFromPurge: false,
		},
	})
	.filter({
		name: 'Edit Do Not Purge As Investigative Team Member',
		roles: [
			'edit_exclude_from_purge',
			'edit_exclude_from_purge_as_investigative_team_member',
		],
		actions: ['save_new', 'save_existing'],
		conditions: [conditionForCaseAsInvestigativeTeamMember],
		filters: {
			excludeFromPurge: false,
		},
	})
	.filter({
		name: 'Edit Do Not Purge As Owner And Investigative Team Member',
		roles: [
			'edit_exclude_from_purge',
			'edit_exclude_from_purge_as_owner',
			'edit_exclude_from_purge_as_investigative_team_member',
		],
		actions: ['save_new', 'save_existing'],
		conditions: [conditionForOwnerAndInvestigativeTeamMember],
		filters: {
			excludeFromPurge: false,
		},
	})
	// Filter - view investigative team. Load is handled through workflow
	.filter({
		name: 'View Investigative Team',
		roles: ['view_investigative_team'],
		actions: ['list', 'save_new', 'save_existing'],
		conditions: [conditionForOther],
		filters: {
			investigativeTeamMembers: false,
		},
	})
	.filter({
		name: 'View Investigative Tean As Owner',
		roles: ['view_investigative_team', 'view_investigative_team_as_owner'],
		actions: ['list', 'save_new', 'save_existing'],
		conditions: [conditionForCaseAsOwner],
		filters: {
			investigativeTeamMembers: false,
		},
	})
	.filter({
		name: 'View Investigative Team As Investigative Team Member',
		roles: ['view_investigative_team', 'view_investigative_team_as_investigative_team_member'],
		actions: ['list', 'save_new', 'save_existing'],
		conditions: [conditionForCaseAsInvestigativeTeamMember],
		filters: {
			investigativeTeamMembers: false,
		},
	})
	.filter({
		name: 'View Investigative Team As Owner And Investigative Team Member',
		roles: [
			'view_investigative_team',
			'view_investigative_team_as_owner',
			'view_investigative_team_as_investigative_team_member',
		],
		actions: ['list', 'save_new', 'save_existing'],
		conditions: [conditionForOwnerAndInvestigativeTeamMember],
		filters: {
			investigativeTeamMembers: false,
		},
	})
	// Filter - edit investigative team
	.filter({
		name: 'Edit Investigative Team',
		roles: ['edit_investigative_team'],
		actions: ['save_new', 'save_existing'],
		conditions: [conditionForOther],
		filters: {
			investigativeTeamMembers: false,
		},
	})
	.filter({
		name: 'Edit Investigative Team As Owner',
		roles: ['edit_investigative_team', 'edit_investigative_team_as_owner'],
		actions: ['save_new', 'save_existing'],
		conditions: [conditionForCaseAsOwner],
		filters: {
			investigativeTeamMembers: false,
		},
	})
	.filter({
		name: 'Edit Investigative Team As Investigative Team Member',
		roles: ['edit_investigative_team', 'edit_investigative_team_as_investigative_team_member'],
		actions: ['save_new', 'save_existing'],
		conditions: [conditionForCaseAsInvestigativeTeamMember],
		filters: {
			investigativeTeamMembers: false,
		},
	})
	.filter({
		name: 'Edit Investigative Team As Owner And Investigative Team Member',
		roles: [
			'edit_investigative_team',
			'edit_investigative_team_as_owner',
			'edit_investigative_team_as_investigative_team_member',
		],
		actions: ['save_new', 'save_existing'],
		conditions: [conditionForOwnerAndInvestigativeTeamMember],
		filters: {
			investigativeTeamMembers: false,
		},
	})
	// Filter - view blacklist
	.filter({
		name: 'View Blacklist',
		roles: ['view_blacklist'],
		actions: ['load', 'list', 'save_new', 'save_existing'],
		conditions: [conditionForOther],
		filters: {
			userBlacklist: false,
		},
	})
	.filter({
		name: 'View Blacklist As Owner',
		roles: ['view_blacklist', 'view_blacklist_as_owner'],
		actions: ['load', 'list', 'save_new', 'save_existing'],
		conditions: [conditionForCaseAsOwner],
		filters: {
			userBlacklist: false,
		},
	})
	.filter({
		name: 'View Blacklist As Investigative Team Member',
		roles: ['view_blacklist', 'view_blacklist_as_investigative_team_member'],
		actions: ['load', 'list', 'save_new', 'save_existing'],
		conditions: [conditionForCaseAsInvestigativeTeamMember],
		filters: {
			userBlacklist: false,
		},
	})
	.filter({
		name: 'View Blacklist As Owner And Investigative Team Member',
		roles: [
			'view_blacklist',
			'view_blacklist_as_owner',
			'view_blacklist_as_investigative_team_member',
		],
		actions: ['load', 'list', 'save_new', 'save_existing'],
		conditions: [conditionForOwnerAndInvestigativeTeamMember],
		filters: {
			userBlacklist: false,
		},
	})
	// Filter - edit blacklist
	.filter({
		name: 'Edit Blacklist',
		roles: ['edit_blacklist'],
		actions: ['save_new', 'save_existing'],
		conditions: [conditionForOther],
		filters: {
			userBlacklist: false,
		},
	})
	.filter({
		name: 'Edit Blacklist As Owner',
		roles: ['edit_blacklist', 'edit_blacklist_as_owner'],
		actions: ['save_new', 'save_existing'],
		conditions: [conditionForCaseAsOwner],
		filters: {
			userBlacklist: false,
		},
	})
	.filter({
		name: 'Edit Blacklist As Investigative Team Member',
		roles: ['edit_blacklist', 'edit_blacklist_as_investigative_team_member'],
		actions: ['save_new', 'save_existing'],
		conditions: [conditionForCaseAsInvestigativeTeamMember],
		filters: {
			userBlacklist: false,
		},
	})
	.filter({
		name: 'Edit Blacklist As Owner And Investigative Team Member',
		roles: [
			'edit_blacklist',
			'edit_blacklist_as_owner',
			'edit_blacklist_as_investigative_team_member',
		],
		actions: ['save_new', 'save_existing'],
		conditions: [conditionForOwnerAndInvestigativeTeamMember],
		filters: {
			userBlacklist: false,
		},
	})
	// Filter - assign cases
	.filter({
		name: 'Assign A Case',
		roles: ['assign_a_case'],
		actions: ['save_new'],
		conditions: [conditionForOther, conditionForNewCase],
		filters: {
			owner: false,
		},
	})
	.filter({
		name: 'Assign A Case',
		roles: ['assign_a_case'],
		actions: ['save_existing'],
		conditions: [conditionForOther, conditionForExistingCase],
		filters: {
			owner: false,
		},
	})
	.filter({
		name: 'Assign A Case As Owner',
		roles: ['assign_a_case', 'assign_a_case_as_owner'],
		actions: ['save_existing'],
		conditions: [conditionForCaseAsOwner],
		filters: {
			owner: false,
		},
	})
	.filter({
		name: 'Assign A Case As Investigative Team Member',
		roles: ['assign_a_case', 'assign_a_case_as_investigative_team_member'],
		actions: ['save_existing'],
		conditions: [conditionForCaseAsInvestigativeTeamMember],
		filters: {
			owner: false,
		},
	})
	.filter({
		name: 'Assign A Case As Owner And Investigative Team Member',
		roles: [
			'assign_a_case',
			'assign_a_case_as_owner',
			'assign_a_case_as_investigative_team_member',
		],
		actions: ['save_existing'],
		conditions: [conditionForOwnerAndInvestigativeTeamMember],
		filters: {
			owner: false,
		},
	})
	// Filter - edit confidential case field
	.filter({
		name: 'Edit Confidential Case Field',
		roles: ['edit_confidential_case_field'],
		actions: ['save_new', 'save_existing'],
		conditions: [conditionForOther],
		filters: {
			confidential: false,
		},
	})
	.filter({
		name: 'Edit Confidential Case Field As Owner',
		roles: ['edit_confidential_case_field', 'edit_confidential_case_field_as_owner'],
		actions: ['save_new', 'save_existing'],
		conditions: [conditionForCaseAsOwner],
		filters: {
			confidential: false,
		},
	})
	.filter({
		name: 'Edit Confidential Case Field As Investigative Team Member',
		roles: [
			'edit_confidential_case_field',
			'edit_confidential_case_field_as_investigative_team_member',
		],
		actions: ['save_new', 'save_existing'],
		conditions: [conditionForCaseAsInvestigativeTeamMember],
		filters: {
			confidential: false,
		},
	})
	.filter({
		name: 'Edit Confidential Case Field As Owner And Investigative Team Member',
		roles: [
			'edit_confidential_case_field',
			'edit_confidential_case_field_as_owner',
			'edit_confidential_case_field_as_investigative_team_member',
		],
		actions: ['save_new', 'save_existing'],
		conditions: [conditionForOwnerAndInvestigativeTeamMember],
		filters: {
			confidential: false,
		},
	})
	// filter - edit case form
	.filter({
		name: 'Edit Case Form',
		roles: ['edit_case_form'],
		actions: ['save_existing'],
		conditions: [conditionForOther, {
			attributes: {
				'!id': null,
				sysSubmitted: true,
			},
		}],
		filters: saveFormFilter,
	})
	.filter({
		name: 'Edit Case Form As Owner',
		roles: ['edit_case_form', 'edit_case_form_as_owner'],
		actions: ['save_existing'],
		conditions: [conditionForCaseAsOwner, {
			attributes: {
				'!id': null,
				sysSubmitted: true,
			},
		}],
		filters: saveFormFilter,
	})
	.filter({
		name: 'Edit Case Form As Investigative Team Member',
		roles: ['edit_case_form', 'edit_case_form_as_investigative_team_member'],
		actions: ['save_existing'],
		conditions: [conditionForCaseAsInvestigativeTeamMember, {
			attributes: {
				'!id': null,
				sysSubmitted: true,
			},
		}],
		filters: saveFormFilter,
	})
	.filter({
		name: 'Edit Case Form As Owner And Investigative Team Member',
		roles: [
			'edit_case_form',
			'edit_case_form_as_owner',
			'edit_case_form_as_investigative_team_member',
		],
		actions: ['save_existing'],
		conditions: [conditionForOwnerAndInvestigativeTeamMember, {
			attributes: {
				'!id': null,
				sysSubmitted: true,
			},
		}],
		filters: saveFormFilter,
	})
	.filter({
		name: 'Edit Date Submitted After Submit',
		roles: ['edit_date_submitted_after_submit'],
		actions: ['save_existing'],
		conditions: [{
			attributes: {
				'!id': null,
				sysSubmitted: true,
			},
		}],
		filters: {
			dateSubmitted: false,
		},
	})
	.filterViewSourceData()
	// Denied List / Team List
	.required({
		name: 'Manual Blacklist',
		roles: ['bypass_case_manual_blacklist'],
		actions: ['load', 'list', 'save_existing', 'remove'],
		conditions: [{
			attributes: {
				userBlacklist: '{user.id}',
			},
		}],
	})
	.required({
		name: 'System Blacklist',
		roles: ['bypass_case_system_blacklist'],
		actions: ['load', 'list', 'save_existing', 'remove'],
		conditions: [{
			attributes: {
				systemBlacklist: '{user.id}',
			},
		}],
	})
	// view draft cases
	.required({
		name: 'View Draft Cases',
		roles: ['view_draft_cases'],
		actions: ['load', 'list', 'save_existing', 'remove'],
		conditions: [permHelper.conditionForInternalRecord, {
			attributes: {
				sysSubmitted: false,
				createdBy: '{!user.id}',
			},
		}],
	})
	.required({
		name: 'View External Draft Cases',
		roles: ['view_external_draft_cases'],
		actions: ['load', 'list', 'save_existing', 'remove'],
		conditions: [{
			attributes: {
				externalRecord: true,
				sysSubmitted: false,
				createdBy: '{!user.id}',
				createdBySession: '{!user.authenticatedBySession}',
			},
		}],
	})
	.required({
		name: 'View Cross Session Draft Cases',
		roles: ['view_cross_session_draft_cases'],
		actions: ['load', 'list', 'save_existing', 'remove'],
		conditions: [permHelper.conditionForInternalRecord, {
			attributes: {
				sysSubmitted: false,
				createdBySession: '{!user.loginId}',
			},
		}],
	})
	.required({
		name: 'View Cross Session External Draft Cases',
		roles: ['view_cross_session_external_draft_cases'],
		actions: ['load', 'list', 'save_existing', 'remove'],
		conditions: [{
			attributes: {
				externalRecord: true,
				sysSubmitted: false,
			},
		}, {
			attributes: {
				createdBySession: '{!user.loginId}',
			},
		}, {
			attributes: {
				createdBySession: '{!user.authenticatedBySession}',
			},
		}],
	})
	.required({
		name: 'View Purged Cases',
		roles: ['view_purged_cases'],
		actions: ['load', 'list', 'save_existing', 'remove'],
		conditions: [conditionForOther, {
			attributes: {
				'!datePurged': null,
			},
		}],
	})
	// view canceled cases
	.required({
		name: 'View Canceled Cases',
		roles: ['view_canceled_cases'],
		actions: ['load', 'list', 'save_existing', 'remove'],
		conditions: [{
			attributes: {
				canceled: true,
			},
		}],
	})
	/**
	 * Only for external open cases created by the user
	 * Anonymous users will only be able to view external cases that are created by them
	 */
	.required({
		name: 'View External Draft Cases as Created By',
		roles: ['view_external_draft_cases_as_created_by'],
		actions: ['load', 'list', 'save_existing', 'remove'],
		conditions: [{
			attributes: {
				createdBy: '{user.id}',
				externalRecord: true,
				sysSubmitted: false,
			},
		}],
	})
	// view confidential cases
	.required({
		name: 'View Confidential Cases',
		roles: ['view_confidential_cases'],
		actions: ['load', 'list', 'save_existing', 'remove'],
		conditions: [permHelper.conditionForInternalRecord, conditionForOther, {
			attributes: {
				confidential: true,
			},
		}],
		generatePermissions: ['external', 'external_not_created_by'],
	})
	.required({
		name: 'View Confidential Cases As Owner',
		roles: ['view_confidential_cases', 'view_confidential_cases_as_owner'],
		actions: ['load', 'list', 'save_existing', 'remove'],
		conditions: [conditionForCaseAsOwner, {
			attributes: {
				confidential: true,
			},
		}],
	})
	.required({
		name: 'View Confidential Cases As Investigative Team Member',
		roles: ['view_confidential_cases', 'view_confidential_cases_as_investigative_team_member'],
		actions: ['load', 'list', 'save_existing', 'remove'],
		conditions: [conditionForCaseAsInvestigativeTeamMember, {
			attributes: {
				confidential: true,
			},
		}],
	})
	.required({
		name: 'View Confidential Cases As Owner And Investigative Team Member',
		roles: [
			'view_confidential_cases',
			'view_confidential_cases_as_owner',
			'view_confidential_cases_as_investigative_team_member',
		],
		actions: ['load', 'list', 'save_existing', 'remove'],
		conditions: [conditionForOwnerAndInvestigativeTeamMember, {
			attributes: {
				confidential: true,
			},
		}],
	})
	// view cases
	.required({
		name: 'View Case',
		roles: ['view_case'],
		actions: ['load', 'list', 'save_existing', 'remove'],
		conditions: [
			permHelper.conditionForInternalRecord,
			conditionForOther,
			conditionForNonCanceledCase,
			{
				attributes: {
					sysSubmitted: true,
				},
			},
		],
		generatePermissions: ['external', 'external_not_created_by'],
	})
	.required({
		name: 'View Case As Owner',
		roles: ['view_case', 'view_case_as_owner'],
		actions: ['load', 'list', 'save_existing', 'remove'],
		conditions: [conditionForCaseAsOwner, conditionForNonCanceledCase, {
			attributes: {
				sysSubmitted: true,
			},
		}],
	})
	.required({
		name: 'View Case As Investigative Team Member',
		roles: ['view_case', 'view_case_as_investigative_team_member'],
		actions: ['load', 'list', 'save_existing', 'remove'],
		conditions: [conditionForCaseAsInvestigativeTeamMember, conditionForNonCanceledCase, {
			attributes: {
				sysSubmitted: true,
			},
		}],
	})
	.required({
		name: 'View Case As Owner And Investigative Team Member',
		roles: [
			'view_case',
			'view_case_as_owner',
			'view_case_as_investigative_team_member',
		],
		actions: ['load', 'list', 'save_existing', 'remove'],
		conditions: [conditionForOwnerAndInvestigativeTeamMember, conditionForNonCanceledCase, {
			attributes: {
				sysSubmitted: true,
			},
		}],
	})
	// user role filter
	.required({
		name: 'User Role Filter',
		roles: ['bypass_user_role_filter'],
		actions: ['load', 'list', 'save_existing', 'remove'],
		conditions: [
			conditionForOther,
			{
				attributes: {
					sysSubmitted: true,
				},
			},
			userRoleFilterConditions,
		],
	})
	// create case
	.required({
		name: 'Create Cases',
		roles: ['create_case'],
		actions: ['save_new'],
		conditions: [permHelper.conditionForInternalRecord, {
			attributes: {
				id: null,
			},
		}],
		generatePermissions: ['external'],
	})
	.required({
		name: 'Create Cases',
		roles: ['create_case'],
		actions: ['save_existing'],
		conditions: [permHelper.conditionForInternalRecord, {
			attributes: {
				'!id': null,
				sysSubmitted: false,
			},
		}],
		generatePermissions: ['external'],
	})
	// edit case
	.required({
		name: 'Edit Cases',
		roles: [groupEditPermCode],
		actions: ['save_existing'],
		conditions: [conditionForOther, {
			attributes: {
				'!id': null,
				sysSubmitted: true,
			},
		}],
	})
	.required({
		name: 'Edit Closed Cases',
		roles: ['edit_closed_case'],
		actions: ['save_existing'],
		conditions: [
			conditionForOther,
			conditionForClosedCase,
		],
	})
	.required({
		name: 'Edit Cases As Owner',
		roles: [groupEditPermCode, 'edit_case_as_owner'],
		actions: ['save_existing'],
		conditions: [conditionForCaseAsOwner, {
			attributes: {
				'!id': null,
				sysSubmitted: true,
			},
		}],
	})
	.required({
		name: 'Edit Closed Cases As Owner',
		roles: ['edit_closed_case', 'edit_closed_case_as_owner'],
		actions: ['save_existing'],
		conditions: [
			conditionForCaseAsOwner,
			conditionForClosedCase,
		],
	})
	.required({
		name: 'Edit Cases As Investigative Team Member',
		roles: [groupEditPermCode, 'edit_case_as_investigative_team_member'],
		actions: ['save_existing'],
		conditions: [conditionForCaseAsInvestigativeTeamMember, {
			attributes: {
				'!id': null,
				sysSubmitted: true,
			},
		}],
	})
	.required({
		name: 'Edit Closed Cases As Investigative Team Member',
		roles: ['edit_closed_case', 'edit_closed_case_as_investigative_team_member'],
		actions: ['save_existing'],
		conditions: [
			conditionForCaseAsInvestigativeTeamMember,
			conditionForClosedCase,
		],
	})
	.required({
		name: 'Edit Cases As Owner And Investigative Team Member',
		roles: [
			groupEditPermCode,
			'edit_case_as_owner',
			'edit_case_as_investigative_team_member',
		],
		actions: ['save_existing'],
		conditions: [conditionForOwnerAndInvestigativeTeamMember, {
			attributes: {
				'!id': null,
				sysSubmitted: true,
			},
		}],
	})
	.required({
		name: 'Edit Closed Cases As Owner And Investigative Team Member',
		roles: [
			'edit_closed_case',
			'edit_closed_case_as_owner',
			'edit_closed_case_as_investigative_team_member',
		],
		actions: ['save_existing'],
		conditions: [
			conditionForOwnerAndInvestigativeTeamMember,
			conditionForClosedCase,
		],
	})
	// Remove case
	.required({
		name: 'Remove Cases',
		roles: ['remove_case'],
		actions: ['remove'],
		conditions: [],
	})
	.value();
