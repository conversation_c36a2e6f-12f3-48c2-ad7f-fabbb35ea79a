/**
 *	Will return all case fields being used in any role filters
 */
const _ = require('lodash');

module.exports = function userRoleFilterSelectedFields(opts, callback) {
	const { entities, knexEntity } = opts;
	const entDef = entities.get('sys/role_filter');
	const knexDef = knexEntity(entDef);
	knexDef.list({
		q: {},
		distinct: ['caseField'],
		fields: ['caseField'],
		limit: -1,
	}, (err, roleFilters) => {
		if (err) return callback(err);
		return callback(null, _.map(roleFilters, 'case_field'));
	});
};
