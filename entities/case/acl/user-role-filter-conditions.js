const _ = require('lodash');

function isFieldFeatureDisabled({fieldName, entityFields, enabledFeatures}) {
	const fieldDef = entityFields[fieldName];
	if (!fieldDef || !fieldDef.features) return false;
	return _.difference(fieldDef.features, enabledFeatures).length !== 0;
}

function getRoleFilterFieldValue(opts = {}) {
	const {
		entityObj,
		entityFields,
		fieldName,
		enabledFeatures,
	} = opts;
	const fieldValue = entityObj[fieldName] ?? null;
	const fieldDef = entityFields[fieldName];
	if (!fieldDef) return fieldValue;
	if (isFieldFeatureDisabled({fieldName, entityFields, enabledFeatures})) {
		return null;
	}
	return fieldValue;
}

function getRoleFilterUserFieldValue(fieldName, context) {
	return getRoleFilterFieldValue({
		entityObj: context.user,
		entityFields: context.userEntDefFields,
		enabledFeatures: context.enabledFeatures,
		fieldName,
	});
}

function getRoleFilterCaseFieldValue(caseObj, fieldName, context) {
	return getRoleFilterFieldValue({
		entityObj: caseObj,
		entityFields: context.entDef.fields(),
		enabledFeatures: context.enabledFeatures,
		fieldName,
	});
}

module.exports = {
	fn(obj, context) {
		const filters = _.get(context, 'user.userRoleId__filters') || [];
		const filterOperator = _.get(context, 'user.userRoleId__filterOperator') || 'all';
		// Skip if no filters defined
		if (filters.length === 0) return { ok: false };
		const iterator = filterOperator === 'any'
			? _.some
			: _.every;
		const filterMatch = iterator(filters, (filter) => {
			let isMatch = false;
			const { operator } = filter;
			const caseValue = getRoleFilterCaseFieldValue(obj, filter.caseField, context);
			const userValue = getRoleFilterUserFieldValue(filter.userField, context);
			if (_.isArray(caseValue) && _.isArray(userValue)) {
				isMatch = _.intersection(caseValue, userValue).length > 0;
			} else if (_.isArray(caseValue)) {
				isMatch = _.includes(caseValue, userValue);
			} else if (_.isArray(userValue)) {
				isMatch = _.includes(userValue, caseValue);
			} else if (caseValue === null && userValue === null) {
				// By design, "includes" must have something in common and null on both sides doesn't
				// count
				isMatch = false;
			} else {
				isMatch = caseValue === userValue;
			}
			if (operator === '!=') {
				isMatch = !isMatch;
			}
			return isMatch;
		});
		// ACL Rule applies only if filter doesn't match
		return { ok: !filterMatch };
	},
	esFilter(context, attributePrefix = '') {
		const alwaysTrueTerm = { exists: { field: 'id' }};
		const filters = _.get(context, 'user.userRoleId__filters') || [];
		const filterOperator = _.get(context, 'user.userRoleId__filterOperator') || 'all';
		const esFilters = _.map(filters, (filter) => {
			let result = {};
			const { operator, caseField } = filter;
			const userValue = _.get(context, `user.${filter.userField}`);
			if ((_.isArray(userValue) && userValue.length === 0) || !userValue) {
				result = _.set(result, 'bool.must_not[0]', alwaysTrueTerm);
			} else if (_.isArray(userValue)) {
				result = _.set(result, ['terms', `${attributePrefix}${caseField}._exact`], userValue);
			} else {
				result = _.set(result, ['term', `${attributePrefix}${caseField}._exact`], userValue);
			}
			if (operator === '!=') {
				result = _.set({}, 'bool.must_not[0]', result);
			}
			return result;
		});
		if (esFilters.length === 0) {
			esFilters.push(_.set({}, 'bool.must[0]', alwaysTrueTerm));
		}
		// For the condition to be true, some or all of the ES filters can't be true
		if (filterOperator === 'any') {
			return _.set({}, 'bool.must_not[0].bool.should', esFilters);
		}
		return _.set({}, 'bool.must_not[0].bool.must', esFilters);
	},
	knexFilter: require('./acl-user-role-knex-filter.js'),
	selectedFields: require('./user-role-filter-selected-fields.js'),
};
