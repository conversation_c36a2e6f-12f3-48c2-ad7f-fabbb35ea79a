const _ = require('lodash');
const async = require('async');

module.exports = {
	todosClosed: {
		caption: 'to_dos_must_be_closed',
		fn({
			seneca,
			data,
		}, callback) {
			const todoEnt = seneca.make$('sys/todo');
			todoEnt.list$({
				limit$: 1,
				caseId: data.id,
				status: 'pending',
			}, (err, results) => {
				if (err) return callback(err);
				if (_.isEmpty(results)) return callback(null, true);
				return callback(null, false);
			});
		},
	},
	caseOwnerAssigned: {
		caption: 'case_owner_must_be_assigned',
		fn({data}, callback) {
			if (!data.owner) return callback(null, false);
			return callback(null, true);
		},
	},
	caseChildFormsMustExist: {
		caption: 'the_following_forms_must_be_added_to_the_case',
		requiresContextFields: ['mustExistForms', 'mustMatchAll'],
		enableOnlyFor: ['sys/transition'],
		getAlternateCaption(data = {}, failed = false) {
			const { typeData: { mustMatchAll } } = data;
			if (mustMatchAll) {
				return failed ? 'add_the_following_forms_to_the_case' : 'all_of_the_following_forms_have_been_added_to_the_case';
			}
			return failed ? 'add_at_least_1_of_the_following_forms_to_the_case'
				: 'at_least_1_of_the_following_forms_has_been_added_to_the_case';
		},
		getContextInfo(opts = {}) {
			const { conditionType } = opts;
			const {
				typeData: { mustExistForms__canon: entitiesCanons },
			} = conditionType;
			return _.map(entitiesCanons, (canon) => {
				const inactiveEntity = _.isNil(canon);
				return {
					entityCanon: canon,
					translationInfo: {
						groupName: inactiveEntity ? 'errors' : canon,
						subgroupName: 'general',
						key: inactiveEntity ? 'unknown_entity' : 'name',
					},
					inactiveEntity,
				};
			});
		},
		fn({
			entityService,
			dynamicEntityService,
			knexEntity,
			data,
			includeInactive = false,
			rule,
		}, callback) {
			const getEntity = (canon, cb) => {
				const ent = entityService.get(canon);
				if (ent) {
					return cb(null, ent);
				}
				return dynamicEntityService.get({ entityName: canon }, cb);
			};
			const { context: dataContext, id: caseId} = data;
			const submissionType = rule?.submissionType;
			const { mustMatchAll, mustExistForms__canon: entitiesCanons } = dataContext;
			const submissionTypePortal = submissionType === 'Portal';
			const additionalData = {};
			// If there is no case id we can assume no forms have been added
			if (!caseId) {
				_.forEach(entitiesCanons, (canon) => {
					additionalData[canon] = false;
				});
				return callback(null, false, additionalData);
			}
			async.each(entitiesCanons, (entityCanon, cb) => {
				if (entityCanon === null) {
					if (submissionTypePortal) return cb();
					additionalData[entityCanon] = false;
					return cb(null, false);
				}
				getEntity(entityCanon, (err, childFormEnt) => {
					if (err) return cb(err);
					const customFormEntity = childFormEnt.type === 'custom';
					if (submissionTypePortal && customFormEntity
						&& !childFormEnt.allowOnPortal) return cb();
					const knexEnt = knexEntity(childFormEnt);
					const q = {
						caseId,
						datePurged: null,
					};
					if (childFormEnt.isDynamicEntity()) {
						q.entityId = childFormEnt.dynamicEntityId;
					}
					knexEnt.count({
						q, includeInactive,
					}, (err, count) => {
						if (err) return cb(err);
						additionalData[entityCanon] = count > 0;
						cb();
					});
				});
			}, (err) => {
				if (err) return callback(err);
				const overallResult = mustMatchAll
					? _.every(additionalData, Boolean)
					: _.some(additionalData, Boolean);
				return callback(null, overallResult, additionalData);
			});
		},
	},
	/**
	 * Load all id of linked cases using case id and load all case workflow states in parallel
	 * Load all case data (workflows state ids) using previous result (linked case ids)
	 * Count all 'Open' states using previous result
	 * */
	automaticLinkedCasesClosed: {
		caption: 'all_automatic_linked_cases_must_be_closed',
		fn({
			entityService,
			knexEntity,
			data,
		}, callback) {
			const caseCanon = 'sys/case';
			async.parallel({
				otherLinkedCaseIds: (done) => {
					const linkKnexDef = entityService.get('sys/link');
					const linkKnexEnt = knexEntity(linkKnexDef);
					linkKnexEnt.list({
						limit: -1,
						q: {
							entity1Id: data.id,
							type: 'Automatic',
						},
						fields: ['entity2Id'],
					}, (err, linkedCases) => {
						if (err) return done(err);
						return done(null, _.map(linkedCases, 'entity2Id'));
					});
				},
				entStatusNames: (done) => {
					const workflowKnexDef = entityService.get('sys/workflow');
					const workflowKnexEnt = knexEntity(workflowKnexDef);
					workflowKnexEnt.list({
						q: {
							entityName: caseCanon,
						},
						fields: ['entStatusField'],
					}, (err, entWorkflowFields) => {
						if (err) return done(err);
						return done(null, _.map(entWorkflowFields, 'entStatusField'));
					});
				},
			}, (err, {otherLinkedCaseIds, entStatusNames}) => {
				if (err) return callback(err);
				if (_.isEmpty(otherLinkedCaseIds)) return callback(null, true);
				if (_.isEmpty(entStatusNames)) return callback(null, true);

				const caseKnexDef = entityService.get(caseCanon);
				const caseKnexEnt = knexEntity(caseKnexDef);
				caseKnexEnt.list({
					q: (knex) => {
						_.each(otherLinkedCaseIds, (id) => {
							knex.orWhere(`${caseKnexDef.table}.id`, id);
						});
					},
					fields: entStatusNames,
				}, (err, caseRecords) => {
					if (err) return callback(err);
					if (_.isEmpty(caseRecords)) return callback(null, true);
					const stateIds = _.flatMapDeep(caseRecords, (caseRecord) => {
						const fields = _.pick(caseRecord, entStatusNames);
						return _.values(fields);
					});
					const stateKnexDef = entityService.get('sys/state');
					const stateKnexEnt = knexEntity(stateKnexDef);
					stateKnexEnt.count({
						q: (knex) => {
							knex.whereIn(`${stateKnexDef.table}.id`, _.uniq(stateIds))
								.andWhere(`${stateKnexDef.table}.primary_state`, 'Open');
						},
					}, (err, hasOpenCases) => {
						if (err) return callback(err);
						if (hasOpenCases > 0) return callback(null, false);
						return callback(null, true);
					});
				});
			});
		},
	},
};
