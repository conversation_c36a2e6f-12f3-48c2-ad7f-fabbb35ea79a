<div class="card-small{?model.datePurged} purged{:else}{?model.pendingPurgeDate} scheduled-purge{/model.pendingPurgeDate}{/model.datePurged}">
  <div class="card-header">
    {@entityIcon entity=entity$/}
		{@entityLink entity=entity$ context=model}<div class="card-title">{formattedData.caseNumber|s}</div>{/entityLink}
    <div class="card-label">
			{?model.datePurged}
      	<span class="label label-danger">
					{@resource key="purged" /}
      	</span>
      {:else}
				<span class="label-owner-container">{formattedData.owner|s}</span>
      {/model.datePurged}
    </div>
  </div>
  {#highlightedFields}
    {>small-highlight-tmpl entityName="sys/case" ago=model.ago/}
  {/highlightedFields}
</div>