/*
	Ordered list of tabs to display under case "Details" tab. Add the 'formConfigName' attribute
	to each tab you want populated in the form-builder. Form config must match what is rendered
	in the tab's view.
 */
module.exports = [
	{
		id: 'overview',
		caption: 'overview',
		view() {
			return require('../../public-external/views/case/external-case-overview-view.js');
		},
		default: true,
		formConfigName: 'external-case-overview',
	},
	{
		id: 'dynamic-tabs-insertion-point',
		caption: 'dynamic_tabs_insertion_point',
	},
];
