const reference = {
	displayFields: function(data, auditModel) {
		return auditModel.hideCaseItemNumberReference() !== true ? ['caseNumber'] : [];
	},
};

module.exports = {
	cmd: {
		load: {
			'viewed': {
				options: {
					reference,
				},
			},
		},
		purge: {
			'purged': {
				options: {
					reference,
				},
			},
		},
		links_purge: {
			'purged_links': {
				options: {
					reference,
				},
			},
		},
		save: {
			'created': {
				options: {
					reference,
				},
			},
			'added_case_summary': {
				condition: {
					composite: 'all',
					rules: [{
						path: 'diff.caseSummary.updatedValue',
						comparator: 'exists',
					}, {
						path: 'diff.caseSummary.originalValue',
						comparator: 'nexists',
					}],
				},
				options: {
					changes: {
						displayFields: ['caseSummary'],
					},
					reference,
				},
			},
			'updated_case_summary': {
				condition: {
					composite: 'all',
					rules: [{
						path: 'diff.caseSummary.updatedValue',
						comparator: 'neq',
						value: 'diff.caseSummary.originalValue',
					}, {
						path: 'diff.caseSummary.originalValue',
						comparator: 'exists',
					}, {
						path: 'diff.caseSummary.updatedValue',
						comparator: 'exists',
					}],
				},
				options: {
					changes: {
						displayFields: ['caseSummary'],
					},
					reference,
				},
			},
			'removed_case_summary': {
				condition: {
					composite: 'all',
					rules: [{
						path: 'diff.caseSummary.updatedValue',
						comparator: 'nexists',
					}, {
						path: 'diff.caseSummary.originalValue',
						comparator: 'exists',
					}],
				},
				options: {
					changes: {
						displayFields: ['caseSummary'],
					},
					reference,
				},
			},
			'updated': {
				options: {
					changes: {
						excludeFields: [
							'caseNumber',
							'lastUpdatedDate',
							'lastUpdatedBy',
							'id',
							'owner',
							'daysToClose',
							'systemBlacklist',
						],
					},
					reference,
				},
			},
			'assigned': {
				condition: {
					composite: 'all',
					rules: [{
						path: 'diff.caseNumber',
						comparator: 'nexists',
					}, {
						path: 'diff.owner',
						comparator: 'exists',
					}, {
						path: 'diff.owner.originalValue',
						comparator: 'nexists',
					}, {
						path: 'diff.owner.updatedValue',
						comparator: 'exists',
					}],
				},
				options: {
					changes: {
						displayFields: ['owner', 'reassignReason'],
					},
					reference,
				},
			},
			'reassigned': {
				condition: {
					composite: 'all',
					rules: [{
						path: 'diff.caseNumber',
						comparator: 'nexists',
					}, {
						path: 'diff.owner',
						comparator: 'exists',
					}, {
						path: 'diff.owner.originalValue',
						comparator: 'exists',
					}, {
						path: 'diff.owner.updatedValue',
						comparator: 'exists',
					}],
				},
				options: {
					changes: {
						displayFields: ['owner', 'reassignReason'],
					},
					reference,
				},
			},
			'unassigned': {
				condition: {
					composite: 'all',
					rules: [{
						path: 'diff.caseNumber',
						comparator: 'nexists',
					}, {
						path: 'diff.owner',
						comparator: 'exists',
					}, {
						path: 'diff.owner.originalValue',
						comparator: 'exists',
					}, {
						path: 'diff.owner.updatedValue',
						comparator: 'nexists',
					}],
				},
				options: {
					changes: {
						displayFields: ['owner', 'reassignReason'],
					},
					reference,
				},
			},
			'canceled': {
				condition: {
					composite: 'all',
					rules: [{
						path: 'diff.canceled.updatedValue',
						comparator: 'eq',
						value: true,
					}],
				},
				options: {
					changes: {
						displayFields: ['cancelReason'],
					},
					reference,
				},
			},
			'restored': {
				condition: {
					composite: 'all',
					rules: [{
						path: 'diff.canceled.originalValue',
						comparator: 'eq',
						value: true,
					}, {
						path: 'diff.canceled.updatedValue',
						comparator: 'eq',
						value: false,
					}],
				},
				options: {
					changes: {
						displayFields: ['restoreReason'],
					},
					reference,
				},
			},
		},
		translate_record: {
			status: {
				condition: {
					composite: 'all',
					rules: [
						{
							path: 'entity.intakeTranslationStatus',
							comparator: 'exists',
						},
					],
				},
				options: {
					changes: {
						displayFields: ['intakeTranslationStatus'],
					},
					reference,
				},
			},
		},
	},
};
