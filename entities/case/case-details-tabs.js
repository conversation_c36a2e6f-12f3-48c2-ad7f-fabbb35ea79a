/*
	Ordered list of tabs to display under case "Details" tab. Add the 'formConfigName' attribute
	to each tab you want populated in the form-builder. Form config must match what is rendered
	in the tab's view.
 */
module.exports = [
	{
		id: 'overview',
		caption: 'overview',
		view() {
			return require('../../public/views/case/case-overview-view.js');
		},
		default: true,
		formConfigName: 'case-overview',
		showOnIntake: true,
		showOnPortal: true,
		showOnHotline: true,
	},
	{
		id: 'dynamic-tabs-insertion-point',
		caption: 'dynamic_tabs_insertion_point',
	},
];
