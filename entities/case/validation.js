module.exports = {
	mandatory$: [],
	dependentMandatory$: [
		{
			condition: 'isNew',
			fields: ['intakeMethod'],
		},
		{
			condition: '(isExternal || isHotlineIntakeMethod) && (isNew || isDraft)',
			fields: ['reportedAnonymously'],
			customTranslationKeys: {
				reportedAnonymously: 'would_you_like_to_remain_anonymous',
			},
		},
		{
			condition: 'isExternal && !isHotlineIntakeMethod',
			fields: ['acceptTermsAndConditions'],
		},
	],
};
