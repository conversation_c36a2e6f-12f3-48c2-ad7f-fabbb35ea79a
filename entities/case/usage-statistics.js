const statHelper = require('../../shared/stat-helper.js')('sys_case');

module.exports = [
	{
		category: 'case',
		key: 'totalCases',
		query: statHelper.countActiveEntity(),
	},
	{
		category: 'case',
		key: 'casesBySource',
		query: statHelper.countActiveEntityByPicklistGroup({
			listItemWhere() {
				this.whereNot('sys_listitem.value', 'appointment');
			},
		}),
		options: {
			groupingField: 'record_source',
			picklistName: 'record_sources',
			datasetTranslation(dataset, translate) {
				const filter = statHelper.getTranslationFilter(this.key);
				let datasetTranslationKey = dataset;

				if (dataset === 'web') {
					datasetTranslationKey = { key: 'internal' };
				} else if (dataset === 'external') {
					datasetTranslationKey = { key: 'external'};
				} else if (dataset === 'email') {
					datasetTranslationKey = { key: 'email_source'};
				}

				const datasetTranslation = translate(datasetTranslationKey, true, {dataset}, dataset);
				return translate(filter, true, {dataset: datasetTranslation}, datasetTranslation);
			},
		},
	},
	{
		category: 'case',
		key: 'casesByIntakeMethod',
		query: statHelper.countActiveEntityByPicklistGroup(),
		options: {
			groupingField: 'intake_method',
			picklistName: 'intake_methods',
		},
	},
	{
		category: 'case',
		key: 'totalOpenWebCases',
		query: statHelper.countActiveEntity({
			where() {
				this.whereNull('date_closed')
					.andWhere('record_source', 'web');
			},
		}),
	},
	{
		category: 'case',
		key: 'totalClosedWebCases',
		query: statHelper.countActiveEntity({
			where() {
				this.whereNotNull('date_closed')
					.andWhere('record_source', 'web');
			},
		}),
	},
	{
		category: 'case',
		key: 'totalOpenExternalCases',
		query: statHelper.countActiveEntity({
			where() {
				this.whereNull('date_closed')
					.andWhere('record_source', 'external');
			},
		}),
	},
	{
		category: 'case',
		key: 'totalClosedExternalCases',
		query: statHelper.countActiveEntity({
			where() {
				this.whereNotNull('date_closed')
					.andWhere('record_source', 'external');
			},
		}),
	},
	{
		category: 'case',
		key: 'totalOpenEmailCases',
		query: statHelper.countActiveEntity({
			where() {
				this.whereNull('date_closed')
					.andWhere('record_source', 'email');
			},
		}),
	},
	{
		category: 'case',
		key: 'totalClosedEmailCases',
		query: statHelper.countActiveEntity({
			where() {
				this.whereNotNull('date_closed')
					.andWhere('record_source', 'email');
			},
		}),
	},
	{
		category: 'case',
		key: 'totalOpenOtherRecordCases',
		query: statHelper.countActiveEntity({
			where() {
				this.whereNull('date_closed')
					.whereNotIn('record_source', ['email', 'external', 'web']);
			},
		}),
	},
	{
		category: 'case',
		key: 'totalClosedOtherRecordCases',
		query: statHelper.countActiveEntity({
			where() {
				this.whereNotNull('date_closed')
					.whereNotIn('record_source', ['email', 'external', 'web']);
			},
		}),
	},
	{
		category: 'case',
		key: 'draftCases',
		private: true,
		query: statHelper.countEntity({
			where() {
				this.where('sys_case.sys_submitted', false);
			},
		}),
	},
	{
		category: 'case',
		key: 'casesByType',
		query: statHelper.countActiveEntityByPicklistGroup(),
		options: {
			groupingField: 'case_type',
			picklistName: 'case_types',
		},
	},
	{
		category: 'case',
		key: 'totalClosedCases',
		query: statHelper.countActiveEntity({
			where() {
				this.whereNotNull('date_closed');
			},
		}),
	},
	{
		category: 'case',
		key: 'closedCasesToday',
		query: statHelper.countActiveEntityToday({
			where() {
				this.whereNotNull('date_closed');
			},
		}),
		options: {
			dateField: 'date_closed',
		},
	},
	{
		category: 'case',
		key: 'openCasesToday',
		query: statHelper.countActiveEntityToday({
			where() {
				this.whereNull('date_closed');
			},
		}),
		options: {
			dateField: 'created_date',
		},
	},
	{
		category: 'case',
		key: 'canceledCases',
		query: statHelper.countActiveEntity({
			where() {
				this.where('canceled', true);
			},
		}),
	},
	{
		category: 'case',
		key: 'totalOpenCases',
		query: statHelper.countActiveEntity({
			where() {
				this.whereNull('date_closed');
			},
		}),
	},
	{
		category: 'case',
		key: 'assignedCases',
		query: statHelper.countActiveEntity({
			where() {
				this.whereNotNull('owner');
			},
		}),
	},
	{
		category: 'case',
		key: 'unassignedCases',
		query: statHelper.countActiveEntity({
			where() {
				this.whereNull('owner');
			},
		}),
	},
	{
		category: 'case',
		key: 'assignedOpenCases',
		query: statHelper.countActiveEntity({
			where() {
				this
					.whereNotNull('owner')
					.whereNull('date_closed');
			},
		}),
	},
	{
		category: 'case',
		key: 'unassignedOpenCases',
		query: statHelper.countActiveEntity({
			where() {
				this
					.whereNull('owner')
					.whereNull('date_closed');
			},
		}),
	},
	{
		category: 'case',
		key: 'openBusinessDays',
		query: statHelper.averageActiveEntity(),
		options: {
			averageField: 'open_business_days',
		},
	},
	{
		category: 'case',
		key: 'openCalendarDays',
		query: statHelper.averageActiveEntity(),
		options: {
			averageField: 'open_calendar_days',
		},
	},
	{
		category: 'case',
		key: 'totalBusinessDays',
		query: statHelper.averageActiveEntity(),
		options: {
			averageField: 'total_business_days',
		},
	},
	{
		category: 'case',
		key: 'totalCalendarDays',
		query: statHelper.averageActiveEntity(),
		options: {
			averageField: 'total_calendar_days',
		},
	},
	{
		category: 'case',
		key: 'totalCasesWithUserInInvestigativeTeam',
		query: statHelper.countActiveEntity({
			where() {
				this
					.whereNotNull('investigative_team_members');
			},
		}),
	},
	{
		category: 'case',
		key: 'totalCasesWithUserInBlacklist',
		query: statHelper.countActiveEntity({
			where() {
				this
					.whereNotNull('user_blacklist');
			},
		}),
	},
	{
		category: 'case',
		key: 'totalExternalCasesSubmittedAnonymously',
		query: statHelper.countActiveEntity({
			where(knex) {
				knex.where({
					reported_anonymously: true,
					sys_submitted: true,
				}).andWhere((knex) => {
					knex.orWhere('external_record', true)
						.orWhere('intake_method', 'hotline');
				});
			},
		}),
	},
	{
		category: 'case',
		key: 'totalExternalCasesNotSubmittedAnonymously',
		query: statHelper.countActiveEntity({
			where(knex) {
				knex.where({
					reported_anonymously: false,
					sys_submitted: true,
				}).andWhere((knex) => {
					knex.orWhere('external_record', true)
						.orWhere('intake_method', 'hotline');
				});
			},
		}),
	},
];
