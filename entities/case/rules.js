module.exports = {
	isExcludedFromPurge: function (data) {
		return data.excludeFromPurge === true;
	},
	isMarkedOrPurged: function(data) {
		return !!data.pendingPurgeDate || !!data.datePurged;
	},
	isConfidential: function (data) {
		return data.confidential === true;
	},
	isExternal: function (data) {
		return data.externalRecord === true;
	},
	isNew: function(data){
		return !data.id;
	},
	isDraft: function(data){
		return !!data.id && data.sysActive !== true;
	},
	isHotlineIntakeMethod(data) {
		return data.intakeMethod === 'hotline';
	},
};
