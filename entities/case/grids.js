module.exports = {
	'main-cases': {
		sortColumn: 'createdDate',
		sortOrder: 'desc',
		columns: [
			{ field: 'caseNumber' },
			{ field: 'caseType' },
			{ field: 'lastWorkflowStateId' },
			{ field: 'primaryPartyId' },
			{ field: 'owner' },
			{ field: 'createdDate' },
		],
		defaultDynamicDataFilters: ['createdDate', 'owner', 'caseType'],
	},
	'main-cases-external': {
		sortColumn: 'createdDate',
		sortOrder: 'desc',
		columns: [
			{ field: 'caseNumber' },
			{ field: 'createdDate' },
		],
	},
	'default-grid-view-cases': {
		sortColumn: 'createdDate',
		sortOrder: 'desc',
		columns: [
			{ field: 'caseType' },
			{ field: 'status' },
			{ field: 'primaryPartyId' },
			{ field: 'owner' },
			{ field: 'createdDate' },
		],
		defaultDynamicDataFilters: [{
			fieldName: 'owner',
			fieldValues: ['currentUser'],
			selectedFilterOptions: ['currentUser'],
			operator: 'is_any',
		}, 'caseType', 'createdDate'],
		defaultOptionalStaticDataFilters: [{
			filterName: 'case_status',
			selectedFilterOptions: ['open'],
		}],
	},
	'default-multi-entity-grid-view': {
		sortColumn: 'createdDate',
		sortOrder: 'desc',
		columns: [
			{ field: 'typeText' },
			{ field: 'caseType' },
			{ field: 'status' },
			{ field: 'primaryPartyId' },
			{ field: 'owner' },
			{ field: 'createdDate' },
		],
		defaultDynamicDataFilters: [{
			fieldName: 'owner',
			fieldValues: ['currentUser'],
			selectedFilterOptions: ['currentUser'],
			operator: 'is_any',
		}, 'caseType', 'createdDate'],
		defaultOptionalStaticDataFilters: [{
			filterName: 'case_status',
			selectedFilterOptions: ['open'],
		}],
	},
	'bookmarked-cases': {
		sortColumn: 'createdDate',
		sortOrder: 'desc',
		columns: [
			{ field: 'caseNumber' },
			{ field: 'caseType' },
			{ field: 'owner' },
			{ field: 'createdDate' },
		],
	},
	'canceled-cases': {
		sortColumn: 'createdDate',
		sortOrder: 'desc',
		columns: [
			{ field: 'caseNumber' },
			{ field: 'caseType' },
			{ field: 'owner' },
			{ field: 'cancelDate' },
			{ field: 'createdDate' },
		],
		defaultDynamicDataFilters: ['owner', 'createdDate', 'lastUpdatedDate'],
	},
	'advanced-search-result-cases': {
		sortColumn: 'createdDate',
		sortOrder: 'desc',
		columns: [
			{ field: 'caseNumber' },
			{ field: 'caseType' },
			{ field: 'owner' },
			{ field: 'createdDate' },
		],
	},
	'search-result-cases-schedule-purge': {
		sortColumn: 'createdDate',
		sortOrder: 'desc',
		columns: [
			{ field: 'caseNumber' },
			{ field: 'caseType' },
			{ field: 'owner' },
			{ field: 'createdDate' },
		],
	},
	'search-result-cases-purge': {
		sortColumn: 'createdDate',
		sortOrder: 'desc',
		columns: [
			{ field: 'caseNumber' },
			{ field: 'caseType' },
			{ field: 'owner' },
			{ field: 'createdDate' },
			{ field: 'pendingPurgeDate' },
		],
	},
	'cases-queue': {
		sortColumn: 'createdDate',
		sortOrder: 'desc',
		columns: [
			{ field: 'caseNumber' },
			{ field: 'caseType' },
			{ field: 'owner' },
			{ field: 'createdDate' },
		],
	},
	'list-cases': {
		sortColumn: 'createdDate',
		sortOrder: 'desc',
		columns: [
			{ field: 'caseNumber' },
			{ field: 'caseType' },
			{ field: 'owner' },
			{ field: 'createdDate' },
		],
	},
};
