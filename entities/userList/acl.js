const permHelper = require('../../lib/core/permission-helper.js');

module.exports = permHelper.initialize()
	.filterMarkForPurge()
	.filterPurge()
	.required({
		name: 'View Own User Lists',
		roles: ['view_own_user_list'],
		actions: ['load', 'list', 'save_existing', 'remove'],
		conditions: [{
			attributes: {
				createdBy: '{user.id}',
			},
		}],
	})
	.required({
		name: 'View Others User Lists',
		roles: ['view_others_user_list'],
		actions: ['load', 'list', 'save_existing', 'remove'],
		conditions: [{
			attributes: {
				createdBy: '{!user.id}',
			},
		}],
	})
	.required({
		name: 'Create Own User Lists',
		roles: ['create_own_user_list'],
		actions: ['save_new'],
		conditions: [{
			attributes: {
				id: null,
				createdBy: '{user.id}',
			},
		}],
	})
	.required({
		name: 'Create Others User Lists',
		roles: ['create_others_user_list'],
		actions: ['save_new'],
		conditions: [{
			attributes: {
				id: null,
				createdBy: '{!user.id}',
			},
		}],
	})
	.required({
		name: 'Edit Own User Lists',
		roles: ['edit_own_user_list'],
		actions: ['save_existing'],
		conditions: [{
			attributes: {
				'!id': null,
				createdBy: '{user.id}',
			},
		}],
	})
	.required({
		name: 'Edit Others User Lists',
		roles: ['edit_others_user_list'],
		actions: ['save_existing'],
		conditions: [{
			attributes: {
				'!id': null,
				createdBy: '{!user.id}',
			},
		}],
	})
	.required({
		name: 'Remove Own User Lists',
		roles: ['remove_own_user_list'],
		actions: ['remove'],
		conditions: [{
			attributes: {
				'!id': null,
				createdBy: '{user.id}',
			},
		}],
	})
	.required({
		name: 'Remove Others User Lists',
		roles: ['remove_others_user_list'],
		actions: ['remove'],
		conditions: [{
			attributes: {
				'!id': null,
				createdBy: '{!user.id}',
			},
		}],
	})
	.required({
		name: 'CRUD Access',
		roles: ['agent'],
		actions: ['load', 'list', 'save_new', 'save_existing', 'remove'],
		conditions: [],
	})
	.required({
		name: 'Edit and Delete Bookmarks list',
		roles: ['edit_and_delete_bookmarks_list'],
		actions: ['save_existing', 'remove'],
		conditions: [{
			attributes: {
				name: 'Bookmarks',
			},
		}],
	})

	.value();
