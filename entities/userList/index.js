const extend = require('../extend.js');
const standardConfig = require('../standard-config.js');

const reference = {
	displayFields: [
		'name',
	],
};

module.exports = extend(standardConfig, {
	db: 'default',
	table: 'sys_user_list',
	entity: {
		base: 'sys',
		name: 'user_list',
	},
	api: {
		useGenericApi: true,
	},
	grids: require('./grids.js'),
	validation: require('./validation.js'),
	acl: require('./acl.js'),
	caption: 'List',
	captionPlural: 'Lists',
	addCaption: 'Add List',
	newCaption: 'New List',
	gridDescriptorField: 'name',
	historyNav: require('./history-nav.js'),
	fields: [
		{
			field: 'name',
			type: 'textbox',
			caption: 'Name',
			kind: 'editable',
		},
		/**
		 * Custom kind since it's not a field in the DB, but a computed field through a workflow
		 */
		{
			field: 'itemsCount',
			type: 'number',
			caption: 'Items',
			kind: 'custom',
			kindOptions: {
				flags: {
					audit: false,
					schema: false,
					search: false,
					apiWritable: false,
					apiExternalWritable: false,
					computedOnSave: false,
					computedOnRead: false,
					formVisible: true,
					gridVisible: true,
					gridSortable: false,
					searchVisible: false,
					formattedData: false,
					gridExportable: true,
					reportable: false,
				},
			},
		},
	],
	audit: {
		cmd: {
			load: {
				viewed: {
					options: {
						reference,
					},
				},
			},
			save: {
				created: {
					options: {
						reference,
					},
				},
				updated: {
					options: {
						changes: {
							excludeFields: ['id'],
						},
						reference,
					},
				},
			},
			remove: {
				deleted: {
					options: {
						reference,
					},
				},
			},
		},
	},
});
