const extend = require('../extend.js');
const standardActionConfig = require('../standard-action-config.js');

module.exports = extend(standardActionConfig, {
	db: 'default',
	table: 'sys_set_flag_action',
	entity: {
		base: 'sys',
		name: 'set_flag_action',
	},
	caption: 'Set Flag',
	captionPlural: 'Set Flags',
	addCaption: 'Add Set Flag',
	newCaption: 'New Set Flag',
	workflowActionConfig: {
		formName: 'set-flag-action',
		aclRoles: {
			createACLRole: 'agent',
			editACLRole: 'agent',
			deleteACLRole: 'agent',
		},
		supportedEntities: ['sys/case'],
	},
	audit: require('./audit.js'),
	model() { return require('./model.js'); },
	usageStatistics: require('./usage-statistics.js'),
	fields: [
		{
			field: 'name',
			type: 'textbox',
			caption: 'Name',
			kind: 'editable',
		},
		{
			field: 'setUnset',
			type: 'toggle',
			caption: 'Set/Unset',
			kind: 'editable',
			typeOptions: {
				showCaptionsOnButton: true,
				checkedTranslation: {
					groupName: 'sys/flag',
					subgroupName: 'actions',
					key: 'set',
				},
				uncheckedTranslation: {
					groupName: 'sys/flag',
					subgroupName: 'actions',
					key: 'unset',
				},
			},
		},
		{
			field: 'flagId',
			type: 'flagName',
			caption: 'Flag',
			kind: 'editable',
			typeOptions: {
				entityField: 'targetEntity',
			},
		},
	],
});
