const extend = require('../extend.js');
const standardConfig = require('../standard-config.js');

module.exports = extend(standardConfig, {
	db: 'default',
	table: 'sys_condition',
	entity: {
		base: 'sys',
		name: 'condition',
	},
	search: false,
	api: {
		useGenericApi: true,
	},
	caption: 'Criteria',
	captionPlural: 'Criteria',
	addCaption: 'Add Criteria',
	newCaption: 'New Criteria',
	acl: require('./acl.js'),
	validation: require('./validation'),
	customDependencies: ['sys/workflow', 'sys/transition', 'sys/rule', 'sys/rule_entity_validation'],
	importTransformer: 'condition',
	copyPostAction: 'condition',
	copyTransformer: 'condition',
	fields: [
		{
			field: 'mustMatchAll',
			type: 'yesno',
			caption: 'Must match all filter items',
			kind: 'system',
		},
		// Denormalized filter children into a field, operator, values list for faster access
		{
			field: 'typesDenormalized',
			type: 'json[]',
			caption: 'Criteria Types',
			kind: 'system',
		},
		// Refers to the entity id in the isight_entity table for which this condition belongs to.
		// Optional (as a condition can stand on its own).
		{
			field: 'entityId',
			type: 'entityPicklist',
			caption: 'Entity Name',
			kind: 'editable',
		},
	],
});
