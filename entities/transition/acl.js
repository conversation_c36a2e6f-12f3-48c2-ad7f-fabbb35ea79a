const permHelper = require('../../lib/core/permission-helper.js');

module.exports = permHelper.initialize()
	.filterMarkForPurge()
	.filterPurge()
	.required({
		name: 'Save/Delete Hard Locked Transition',
		roles: ['save_delete_hard_locked_transition'],
		actions: ['save_new', 'save_existing', 'remove'],
		conditions: [{
			attributes: {
				hardLocked: true,
			},
		}],
	})
	.requireWorkflowLoadInheritance()
	.requireWorkflowSaveInheritance()
	.value();
