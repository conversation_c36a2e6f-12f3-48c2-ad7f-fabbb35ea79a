const extend = require('../extend.js');
const standardWorkflowChildConfig = require('../standard-workflow-child-config.js');

module.exports = extend(standardWorkflowChildConfig, {
	table: 'sys_transition',
	entity: {
		base: 'sys',
		name: 'transition',
	},
	caption: 'Step',
	captionPlural: 'Steps',
	addCaption: 'Add Step',
	newCaption: 'New Step',
	gridDescriptorField: 'name',
	bypassValidationOnDraft: true,
	historyNav: [
		{ from: [], to: '/settings/workflow/custom-workflow/{workflowId}' },
	],
	acl: require('./acl.js'),
	audit: require('./audit.js'),
	grids: require('./grids.js'),
	validation: require('./validation.js'),
	importTransformer: 'transition',
	customDependencies: ['sys/state'],
	copyPostAction: 'transition',
	copyTransformer: 'transition',
	usageStatistics: require('./usage-statistics.js'),
	model(){
		return require('../../public/models/workflow-transition-model.js');
	},
	collection(){
		return require('../../public/collections/workflow-transitions-collection.js');
	},
	parents: [
		{
			entity: {
				base: 'sys',
				name: 'workflow',
			},
			field: 'workflowId',
			reportable: true,
		},
	],
	fields: [
		{
			field: 'name',
			type: 'textbox',
			caption: 'Name',
			kind: 'editable',
			typeOptions: {
				nameAttrValue: 'transition-name',
				charMaxTextbox: 30,
			},
		},
		{
			field: 'stateFromId',
			type: 'workflowState',
			caption: 'Status From',
			kind: 'editable',
			typeOptions: {
				entity: 'sys/state',
				textField: 'name',
				showInactiveRecord: true,
				filterBy: 'workflowId',
			},
		},
		{
			field: 'stateToId',
			type: 'workflowState',
			caption: 'Status To',
			kind: 'editable',
			typeOptions: {
				entity: 'sys/state',
				textField: 'name',
				showInactiveRecord: true,
				filterBy: 'workflowId',
			},
		},
		{
			field: 'description',
			type: 'textbox',
			caption: 'Description',
			kind: 'editable',
		},
		{
			field: 'reasons',
			type: 'textbox[]',
			caption: 'Reasons',
			kind: 'editable',
			typeOptions: {
				blankText: 'add',
			},
		},
		{
			field: 'sequence',
			type: 'number',
			caption: 'Sequence',
			kind: 'editable',
			esSort: [
				'sequence',
				'name',
			],
		},
		{
			field: 'conditionId',
			type: 'id',
			caption: 'Condition ID',
			kind: 'hidden-editable',
			excludeFromSaveAndCopy: true,
		},
		{
			field: 'mandatoryFields',
			type: 'fieldPicklist[]',
			caption: 'Mandatory Fields',
			kind: 'editable',
			typeOptions: {
				entityNameField: 'workflowId__entityName',
				filterFlag: ['apiWritable', 'formVisible', 'searchVisible'],
				displayFieldKind: true,
			},
		},
	],
	joins: [
		{
			table: 'sys_workflow',
			referenceField: 'workflowId',
			fields: [
				'entityName',
			],
		},
	],
});
