module.exports = {
	'main-transitions-grid': {
		sortColumn: 'sequence',
		sortOrder: 'asc',
		excludeColumns: ['datePurged', 'pendingPurgeDate', 'purgeReason', 'excludeFromSuggestedLinks'],
		columns: [
			{ field: 'name' },
			{ field: 'stateFromId' },
			{ field: 'stateToId' },
			{ field: 'description' },
			{ field: 'sequence' },
		],
	},
	'transitions-step-grid': {
		sortColumn: 'sequence',
		sortOrder: 'asc',
		excludeColumns: ['datePurged', 'pendingPurgeDate', 'purgeReason', 'excludeFromSuggestedLinks'],
		columns: [
			{ field: 'name' },
			{ field: 'stateFromId' },
			{ field: 'stateToId' },
			{ field: 'description' },
			{ field: 'sequence' },
		],
	},
};
