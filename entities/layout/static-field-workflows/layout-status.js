/* global module */

module.exports = {
	name: 'layout-status-workflow',
	field: 'status',
	entity: {
		zone: undefined,
		base: 'sys',
		name: 'layout',
	},
	values: [
		{
			value: 'inactive',
			indicator: 'default',
			content: [
				{
					condition: {entityType: 'standard'},
					caption: 'content_custom_form',
				},
				{
					condition: {entityType: 'custom'},
					caption: 'content_custom_form',
				},
				{
					condition: {entityType: 'response'},
					caption: 'content_custom_form',
				},
				{
					condition: {entityType: 'data'},
					caption: 'content_data_form',
				},
			],
		},
		{
			value: 'active',
			indicator: 'success',
			content: [
				{
					condition: {entityType: 'standard'},
					caption: 'content_custom_form',
				},
				{
					condition: {entityType: 'custom'},
					caption: 'content_custom_form',
				},
				{
					condition: {entityType: 'response'},
					caption: 'content_custom_form',
				},
				{
					condition: {entityType: 'data'},
					caption: 'content_data_form',
				},
			],
		},
		{
			value: 'pending_changes',
			indicator: 'warning',
			content: [
				{
					condition: {entityType: 'standard'},
					caption: 'content_custom_form',
				},
				{
					condition: {entityType: 'custom'},
					caption: 'content_custom_form',
				},
				{
					condition: {entityType: 'response'},
					caption: 'content_custom_form',
				},
				{
					condition: {entityType: 'data'},
					caption: 'content_data_form',
				},
			],
		},
	],
	strict: true,
	transitions: [
		{
			id: 'layout-create',
			from: [null, undefined],
			to: ['inactive', 'active', 'pending_changes'],
		},
		{
			id: 'layout-activate',
			caption: 'Publish Layout',
			from: ['inactive'],
			to: 'active',
		},
		{
			id: 'layout-deactivate',
			caption: 'Deactivate Layout',
			from: ['active'],
			to: 'inactive',
		},
		{
			id: 'layout-edit',
			caption: 'Edit Layout',
			from: 'active',
			to: 'pending_changes',
		},
		{
			id: 'layout-publish-changes',
			caption: 'Publish Changes',
			from: 'pending_changes',
			to: 'active',
		},
	],
};
