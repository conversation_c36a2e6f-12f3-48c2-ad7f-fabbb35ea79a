# Layout

Add New Layout Type
===========

Option 1

    a. extend sys/layout
    b. set `createTable: false`
	c. set `reuseEsIndex: { index: 'sys_layout', typeField: 'layoutType' }`
    d. should have dynamic fields only
    e. should not have conflicted field names with other layout entities which extend from sys/layout, as same es index is shared beween them
    f. set `layoutType`

    Examples: entities/formLayoutType, entities/tabLayoutType, entities/sectionLayoutType, entities/fieldLayoutType

Option 2

    a. extend sys/layout
    b. set different table
    c. set different esIndex
    d. can set any static/dynamic fields you want
    e. set `layoutType`

