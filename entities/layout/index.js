const extend = require('../extend.js');
const standardLayoutTypeConfig = require('../standard-layout-type-config.js');

module.exports = extend(standardLayoutTypeConfig, {
	db: 'default',
	table: 'sys_layout',
	esIndex: 'sys_layout',
	entity: {
		base: 'sys',
		name: 'layout',
	},
	api: {
		useGenericApi: true,
	},
	reuseEsIndex: {
		index: 'sys_layout',
		typeField: 'layoutType',
	},
	layoutType: 'layout',
	caption: 'Layout',
	captionPlural: 'Layouts',
	addCaption: 'Add Layout',
	newCaption: 'New Layout',
	gridDescriptorField: 'caption',
	acl: require('./acl.js'),
	staticFieldWorkflows: [
		require('./static-field-workflows/layout-status.js'),
	],
	usageStatistics: require('./usage-statistics.js'),
	model() {
		return require('../../public/models/layout-model.js');
	},
	fields: [
		{
			field: 'lookup',
			caption: 'Lookup',
			type: 'toggle',
			kind: 'editable',
			features: ['dataLookupSection'],
		},
		{
			field: 'lookupLabel',
			caption: 'Lookup Label',
			type: 'textbox',
			kind: 'editable',
			features: ['dataLookupSection'],
		},
		{
			field: 'lookupMethod',
			caption: 'Lookup Method',
			type: 'picklist',
			typeOptions: {
				picklistName: 'lookup_methods',
			},
			kind: 'editable',
			features: ['dataLookupSection'],
		},
		{
			field: 'endpoint',
			caption: 'Endpoint',
			type: 'textbox',
			kind: 'editable',
			features: ['dataLookupSection'],
		},
		{
			field: 'httpMethod',
			caption: 'HTTP Method',
			type: 'radio',
			typeOptions: {
				picklistName: 'http_methods',
				orientation: 'horizontal',
			},
			kind: 'editable',
			features: ['dataLookupSection'],
		},
		{
			field: 'httpBody',
			caption: 'HTTP Body',
			type: 'expression',
			kind: 'editable',
			typeOptions: {
				customContextProperty: {
					searchValue: null,
				},
			},
			features: ['dataLookupSection'],
		},
		{
			field: 'authenticationMethod',
			caption: 'Authentication Method',
			type: 'radio',
			typeOptions: {
				picklistName: 'authentication_methods',
				orientation: 'horizontal',
			},
			kind: 'editable',
			features: ['dataLookupSection'],
		},
		{
			field: 'username',
			caption: 'Username',
			type: 'textbox',
			kind: 'editable',
			features: ['dataLookupSection'],
		},
		{
			field: 'password',
			caption: 'Password',
			type: 'secret',
			kind: 'editable',
			typeOptions: {
				writeOnly: true,
			},
			features: ['dataLookupSection'],
		},
		{
			field: 'collectionProperty',
			caption: 'Collection Property',
			type: 'textbox',
			kind: 'editable',
			features: ['dataLookupSection'],
		},
		{
			field: 'grantType',
			caption: 'Grant Type',
			type: 'picklist',
			typeOptions: {
				picklistName: 'grant_types',
			},
			kind: 'editable',
			features: ['dataLookupSection'],
		},
		{
			field: 'accessTokenEndpoint',
			caption: 'Access Token Endpoint',
			type: 'textbox',
			kind: 'editable',
			features: ['dataLookupSection'],
		},
		{
			field: 'clientId',
			caption: 'Client Id',
			type: 'textbox',
			kind: 'editable',
			features: ['dataLookupSection'],
		},
		{
			field: 'clientSecret',
			caption: 'Client Secret',
			type: 'secret',
			kind: 'editable',
			typeOptions: {
				writeOnly: true,
			},
			features: ['dataLookupSection'],
		},
		{
			field: 'scope',
			caption: 'Scope',
			type: 'textbox',
			kind: 'editable',
			features: ['dataLookupSection'],
		},
		{
			field: 'clientAuthenticationOption',
			caption: 'Client Authentication Option',
			type: 'picklist',
			typeOptions: {
				picklistName: 'client_authentication_options',
			},
			kind: 'editable',
			features: ['dataLookupSection'],
		},
	],
});
