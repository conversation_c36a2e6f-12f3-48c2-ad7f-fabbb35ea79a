const permHelper = require('../../lib/core/permission-helper.js');

module.exports = permHelper.initialize()
	.required({
		name: 'View Dynamic Fields',
		roles: ['form_builder'],
		actions: ['load', 'list', 'save_existing', 'remove'],
		conditions: [],
	})
	.required({
		name: 'Create Dynamic Fields',
		roles: ['form_builder'],
		actions: ['save_new'],
		conditions: [{
			attributes: {
				id: null,
			},
		}],
	})
	// no users can be granted this permission
	.filter({
		name: 'Edit Dynamic Field Kind',
		roles: ['edit_dynamic_field_kind'],
		actions: ['save_new', 'save_existing'],
		conditions: [{
			attributes: {
				'!id': null,
				kind: 'dynamic',
			},
		}],
		filters: {
			kind: false,
		},
	})
	.required({
		name: 'Edit Dynamic Fields',
		roles: ['form_builder'],
		actions: ['save_existing'],
		conditions: [{
			attributes: {
				'!id': null,
			},
		}],
	})
	.required({
		name: 'Remove Dynamic Fields',
		roles: ['form_builder'],
		actions: ['remove'],
		conditions: [],
	})
	.value();
