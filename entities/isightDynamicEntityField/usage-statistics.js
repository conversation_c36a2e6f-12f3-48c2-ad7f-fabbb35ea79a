const _ = require('lodash');
const statHelper = require('../../shared/stat-helper.js')('isight_dynamic_entity_field');

module.exports = [
	{
		category: 'customForms',
		key: 'totalCustomFieldsForCustomForms',
		query: statHelper.countActiveEntity({
			where() {
				this.where('kind', 'dynamic')
					.andWhere('sys_active', true);
			},
		}),
	},
	{
		category: 'customForms',
		key: 'computedFieldsForCustomForms',
		query: statHelper.countActiveEntity({
			where() {
				this.where('dynamic_kind', 'isel-computed');
			},
		}),
	},
	{
		category: 'customForms',
		key: 'aggregatedFieldsForCustomForms',
		query: statHelper.countActiveEntity({
			where() {
				this.where('dynamic_kind', 'aggregate');
			},
		}),
	},
	{
		category: 'customForms',
		key: 'coordinatesFieldsForCustomForms',
		query: statHelper.countActiveEntity({
			where() {
				this.where('type', 'coordinate');
			},
		}),
	},
	{
		category: 'customForms',
		key: 'totalDeletedFields',
		query: statHelper.countEntity({
			where() {
				this.where('kind', 'dynamic')
					.where('sys_active', false)
					.andWhere('restorable', true);
			},
		}),
	},
	{
		category: 'customForms',
		key: 'totalActiveFields',
		query: statHelper.countActiveEntity({
			where() {
				this.where('kind', 'dynamic');
			},
		}),
	},
	{
		category: 'customForms',
		key: 'totalActiveDateTimeFields',
		query: statHelper.countActiveEntity({
			where() {
				this.where('kind', 'dynamic')
					.where('type', 'datetime');
			},
		}),
	},
	{
		category: 'customForms',
		key: 'totalRestrictEditFieldsForCustomForms',
		query: statHelper.countActiveEntity({
			where() {
				this.where('kind', 'dynamic')
					.andWhere('restrict_edit', true);
			},
		}),
	},
	{
		category: 'customForms',
		key: 'averageRestrictEditFieldsPerCustomForm',
		query(knex, options, cb) {
			const subQuery = function subSquery() {
				this.count()
					.from('isight_dynamic_entity_field')
					.where('restrict_edit', true)
					.where('sys_active', true)
					.whereNull('deleted_date')
					.groupBy('entity_name')
					.as('average_func');
			};

			knex
				.avg('average_func.count')
				.from(subQuery)
				.asCallback((err, rows) => {
					if (err) return cb(err);
					let formatted = 0;
					if (rows && _.head(rows) && _.head(rows).avg) {
						formatted = parseInt(_.head(rows).avg, 10);
					}
					return cb(null, formatted);
				});
		},
	},
];
