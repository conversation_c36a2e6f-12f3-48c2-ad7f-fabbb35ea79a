/* global $appData */
const $ = require('jquery');
const _ = require('lodash');
const sharedUtils = require('../../../shared/utils.js')();
const BackboneEvents = require('../../../public/lib/backbone-events.js');

const appDataCategory = 'dynamicEntityFields';

function setDynamicEntityFields(callback) {
	$.ajax({
		url: `${$appData.globalConfig.apiRoot}/dynamic_entity_fields`,
		method: 'GET',
		dataType: 'json',
		contentType: 'application/json',
	})
		.done((data) => {
			sharedUtils.updateArray($appData[appDataCategory], data);
			callback();
		});
}

module.exports = function handler(opts) {
	const utils = require('../../../public/lib/utils.js');
	const { patchData } = opts;

	// use patch data to patch $appData
	if (patchData) {
		// Tap in to set an edit-rule on caseId fields
		_.forEach(patchData, (entity) => {
			if (_.has(entity, 'fields.caseId')) {
				_.assign(entity.fields.caseId, {
					editRule: 'isNew && !isAddingToSpecificCase',
					// required to prevent the caseId field being unset by display-rules
					clearOnHide: false,
				});
			}
		});
		utils.patch({
			appDataCategory,
			data: patchData,
			identifierProperties: ['entityId'],
		});
		BackboneEvents.trigger('dynamic-fields-updated', opts);
	} else {
		// otherwise, $appData have to be loaded via requests
		setDynamicEntityFields(() => BackboneEvents.trigger('dynamic-fields-updated', opts));
	}
};
