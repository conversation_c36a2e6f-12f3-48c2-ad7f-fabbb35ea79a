const extend = require('../extend.js');
const isightField = require('../isightField');

// NOTE: This entity is for static and dynamic fields of a dynamic entity,
// not dynamic fields for all entities
module.exports = extend(isightField, {
	db: 'default',
	search: true,
	table: 'isight_dynamic_entity_field',
	entity: {
		base: 'isight',
		name: 'dynamic_entity_field',
	},
	customDependencies: ['isight/dynamic_entity', 'sys/user_role'],
	importTransformer: 'dynamic_entity_field',
	exportTransformer: 'dynamic_entity_field',
	copyTransformer: 'dynamic_entity_field',
	copyPostAction: 'dynamic_entity_field',
	usageStatistics: require('./usage-statistics.js'),
	parents: [
		{
			entity: {
				base: 'isight',
				name: 'dynamic_entity',
			},
			field: 'entityId',
		},
	],
	acl: require('./acl.js'),
	cacheBustGroups: require('./cache-bust-groups.js'),
});
