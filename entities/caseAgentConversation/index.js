const extend = require('../extend.js');
const standardChildConfig = require('../standard-child-config.js');

module.exports = extend(standardChildConfig, {
	features: ['caseAssistant'],
	db: 'default',
	table: 'sys_case_agent_conversation',
	entity: {
		base: 'sys',
		name: 'caseAgentConversation',
	},
	allowRecordLinking: false,
	audit: {
		allowNavigateTo: false,
		cmd: {
			purge: {
				purged: {
					options: {
						reference: {
							displayFields: ['createdDate'],
						},
					},
				},
			},
		},
	},
	search: false,
	allowAdvancedSearch: false,
	allowQuickSearch: false,
	dataExport: false,
	customForm: false,
	report: false,
	excludeFromAggregation: true,
	allowPurge: true,
	includeInDataDictionary: false,
	caption: 'Case Agent Conversation',
	captionPlural: 'Case Agent Conversations',
	addCaption: 'Add Case Agent Conversation',
	newCaption: 'New Case Agent Conversation',
	fields: [
		{
			field: 'title',
			type: 'textbox',
			caption: 'Title',
			kind: 'editable',
		},
		{
			field: 'caseId',
			type: 'case',
			caption: 'Case',
			kind: 'system',
		},
		{
			field: 'userId',
			type: 'user',
			caption: 'User',
			kind: 'system',
		},
	],
	parents: [
		{
			entity: {
				base: 'sys',
				name: 'case',
			},
			field: 'caseId',
		},
		{
			entity: {
				base: 'sys',
				name: 'user',
			},
			field: 'userId',
		},
	],
});
