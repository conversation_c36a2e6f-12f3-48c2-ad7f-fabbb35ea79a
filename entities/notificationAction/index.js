const extend = require('../extend.js');
const standardActionConfig = require('../standard-action-config.js');

module.exports = extend(standardActionConfig, {
	table: 'sys_notification_action',
	entity: {
		base: 'sys',
		name: 'notification_action',
	},
	workflowActionConfig: {
		formName: 'notification-action-details',
		aclRoles: {
			createACLRole: 'agent',
			editACLRole: 'agent',
			deleteACLRole: 'agent',
		},
		supportedRuleTypes: ['Schedule Process', 'Schedule', 'Record Event', 'Workflow'],
	},
	caption: 'Notification',
	captionPlural: 'Notifications',
	addCaption: 'Add Notification',
	newCaption: 'New Notification',
	rules: require('./rules.js'),
	audit: require('./audit.js'),
	validation: require('./validation.js'),
	computeFunctions: require('./compute-functions.js'),
	customDependencies: ['sys/user_role'],
	usageStatistics: require('./usage-statistics.js'),
	model() { return require('./model.js'); },
	fields: [
		{
			field: 'contextUsers',
			type: 'fieldPicklist[]',
			typeOptions: {
				entityNameField: 'targetEntity',
				refreshOnFields: ['targetEntity'],
				filterType: [
					'user',
					'user[]',
					'emailLookup[]',
				],
				excludedFields: ['userBlacklist'],
			},
			caption: 'Include Context Users',
			kind: 'editable',
		},
		{
			field: 'partyType',
			type: 'picklist[]',
			typeOptions: {
				picklistName: 'party_types',
				placeholder: 'search_party_types',
			},
			caption: 'Include Parties',
			kind: 'editable',
		},
		{
			field: 'systemUsers',
			type: 'user[]',
			caption: 'Include System Users',
			kind: 'editable',
		},
		{
			field: 'systemUserRoles',
			caption: 'Include System User Roles',
			kind: 'editable',
			type: 'picklistApi[]',
			typeOptions: {
				picklistName: 'user_roles',
				filter: 'filterUserRoles',
			},
		},
		{
			field: 'contextEmails',
			type: 'fieldPicklist[]',
			typeOptions: {
				entityNameField: 'targetEntity',
				refreshOnFields: ['targetEntity'],
				placeholder: 'Search emails...',
				filterType: [
					'email',
					'email[]',
				],
			},
			caption: 'Include Context Email',
			kind: 'editable',
		},
		{
			field: 'otherAddresses',
			type: 'email[]',
			typeOptions: {
				linkWithSystemUser: false,
			},
			caption: 'Include Other Addresses',
			kind: 'editable',
		},
		{
			field: 'systemTeams',
			caption: 'Include Teams',
			type: 'team[]',
			kind: 'editable',
		},
		{
			caption: 'High Priority',
			field: 'highPriority',
			type: 'checkbox',
			kind: 'editable',
		},
		{
			field: 'subject',
			type: 'textbox',
			caption: 'Subject',
			kind: 'editable',
		},
		{
			field: 'template',
			type: 'texteditor',
			caption: 'Template',
			kind: 'custom',
			kindOptions: {
				flags: {
					audit: true,
					schema: true,
					apiWritable: true,
					gridExportable: false,
					formVisible: true,
					search: true,
					reportable: true,
					apiExternalWritable: false,
					computedOnSave: false,
					computedOnRead: false,
					gridVisible: false,
					gridSortable: false,
					searchVisible: false,
					formattedData: true,
				},
			},
		},
		{
			field: 'fields',
			type: 'fieldPicklist[]',
			typeOptions: {
				entityNameField: 'targetEntity',
				refreshOnFields: ['targetEntity'],
				excludeType: ['imageCropper'],
				// Setting to empty array so typeOption is cleared with data dictionary sync
				excludeKind: [], // TODO remove in 8.2.0
			},
			caption: 'Include Fields',
			kind: 'editable',
		},
		{
			field: 'templateForSMS',
			type: 'textarea',
			typeOptions: {
				charMaxTextArea: 500,
			},
			caption: 'Template for SMS',
			kind: 'custom',
			kindOptions: {
				flags: {
					audit: true,
					schema: true,
					apiWritable: true,
					gridExportable: false,
					formVisible: true,
					search: true,
					reportable: true,
					apiExternalWritable: false,
					computedOnSave: false,
					computedOnRead: false,
					gridVisible: false,
					gridSortable: false,
					searchVisible: false,
					formattedData: true,
				},
			},
		},
		{
			field: 'templateForPhone',
			type: 'textarea',
			typeOptions: {
				charMaxTextArea: 1000,
			},
			caption: 'Template for Phone',
			kind: 'custom',
			kindOptions: {
				flags: {
					audit: true,
					schema: true,
					apiWritable: true,
					gridExportable: false,
					formVisible: true,
					search: true,
					reportable: true,
					apiExternalWritable: false,
					computedOnSave: false,
					computedOnRead: false,
					gridVisible: false,
					gridSortable: false,
					searchVisible: false,
					formattedData: true,
				},
			},
		},
		{
			field: 'summarizedTemplate',
			type: 'texteditor',
			caption: 'Template',
			kind: 'computed',
			kindOptions: {
				computeFunction: 'setSummarizedTemplate',
			},
		},
		{
			caption: 'Method',
			field: 'method',
			type: 'picklist',
			typeOptions: {
				picklistName: 'custom_notification_methods',
			},
			kind: 'editable',
		},
		{
			field: 'otherPhoneNumbers',
			type: 'phone-number[]',
			caption: 'Include Other Phone Numbers',
			kind: 'editable',
		},
	],
	joins: [
		{
			referenceField: 'systemUserRoles',
			table: 'sys_user_role',
			fields: [
				'name',
			],
		},
	],
});
