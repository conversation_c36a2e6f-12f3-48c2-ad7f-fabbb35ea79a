module.exports = {
	mandatory$: [
		'triggerType',
		'method',
	],
	dependentMandatory$: [
		{
			condition: 'isMethodEmail',
			fields: [
				'subject',
				'template',
			],
		},
		{
			condition: 'isMethodSMS',
			fields: [
				'templateForSMS',
			],
		},
		{
			condition: 'isMethodPhone',
			fields: [
				'templateForPhone',
			],
		},
		{
			condition: 'isTriggerTypeDelayed',
			fields: ['delay'],
		},
	],
	invalidTemplate$: [
		'subject',
		'template',
		'templateForPhone',
		'templateForSMS',
	],
};
