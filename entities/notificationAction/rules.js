module.exports = {
	isMethodEmail(data) {
		return data.method === 'email';
	},
	isMethodSMS(data) {
		return data.method === 'sms';
	},
	isMethodPhone(data) {
		return data.method === 'phone';
	},
	isRuleTypeScheduleProcess(data) {
		return data._ruleType === 'Schedule Process';
	},
	isUnassignedIncomingMail(data) {
		return data._recordEvent === 'unassigned-incoming-mail';
	},
	isRecordTypeCase(data){
		return data.targetEntity === 'sys/case';
	},
	isRecordTypeEmail(data){
		return data.targetEntity === 'sys/email';
	},
	isRecordTypeAppointment(data){
		return data.targetEntity === 'sys/appointment';
	},
	isRecordTypeNote(data){
		return data.targetEntity === 'sys/note';
	},
	isRecordTypeFile(data){
		return data.targetEntity === 'sys/attachment';
	},
};
