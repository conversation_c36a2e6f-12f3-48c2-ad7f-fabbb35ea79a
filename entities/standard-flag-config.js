const standardConfig = require('./standard-config');
const extend = require('./extend.js');

module.exports = extend(standardConfig, {
	dynamicFlags: true,
	fields: [
		{
			field: 'flagsData',
			caption: 'Flag Data',
			type: 'json',
			kind: 'hidden',
			excludeFromSaveAndCopy: true,
			excludeFromAutofill: true,
		},
		{
			// id of the flag at the time of being set
			field: 'lastFlagSet',
			caption: 'Last Flag Set',
			type: 'flagName',
			kind: 'system',
			excludeFromSaveAndCopy: true,
			excludeFromAutofill: true,
		},
		{
			// name of the flag at the time of being set
			field: 'lastFlagSetName',
			caption: 'Last Flag Set Name',
			type: 'textbox',
			kind: 'system',
			excludeFromSaveAndCopy: true,
			excludeFromAutofill: true,
		},
		{
			field: 'lastFlagSetBy',
			caption: 'Last Flag Set By',
			type: 'user',
			kind: 'system',
			excludeFromSaveAndCopy: true,
			excludeFromAutofill: true,
		},
		{
			field: 'lastFlagSetDate',
			caption: 'Last Flag Set Date',
			type: 'datetime',
			kind: 'system',
			excludeFromSaveAndCopy: true,
			excludeFromAutofill: true,
		},
	],
});
