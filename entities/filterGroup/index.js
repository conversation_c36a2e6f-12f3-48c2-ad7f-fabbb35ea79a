const _ = require('lodash');
const extend = require('../extend');
const standardConfig = require('../standard-config');

module.exports = extend(standardConfig, {
	db: 'default',
	table: 'sys_filter_group',
	entity: {
		base: 'sys',
		name: 'filter_group',
	},
	search: false,
	caption: 'Filter Group',
	captionPlural: 'Filter Groups',
	addCaption: 'Add Filter Group',
	newCaption: 'New Filter Group',
	acl: require('./acl'),
	validation: require('./validation'),
	computeFunctions: require('./compute-functions.js'),
	customDependencies: (entityService) => {
		const layoutEntCanons = _.map(entityService.getLayoutEntities(), 'entityCanon');
		return [...layoutEntCanons];
	},
	importTransformer: 'filter_group',
	copyPostAction: 'filter_group',
	cacheBustGroups: require('./cache-bust-groups.js'),
	fields: [
		{
			field: 'type',
			caption: 'Type',
			type: 'textbox',
			kind: 'hidden',
		},
		{
			field: 'mustMatchAll',
			caption: 'Must Match All',
			type: 'yesno',
			kind: 'editable',
		},
		{
			field: 'entityName',
			type: 'textbox',
			caption: 'Entity Name',
			kind: 'computed',
			kindOptions: {
				computeFunction: 'entityName',
			},
		},
		{
			field: 'entityId',
			type: 'id',
			caption: 'Entity Name',
			kind: 'editable',
		},
		{
			field: 'originalId',
			type: 'id',
			caption: 'Original Id',
			kind: 'hidden',
		},
		{
			field: 'restorable',
			caption: 'Restorable',
			type: 'yesno',
			kind: 'hidden',
		},
		{
			field: 'features',
			type: 'textbox[]',
			caption: 'Features',
			kind: 'hidden-searchable',
		},
	],
});
