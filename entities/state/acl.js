const permHelper = require('../../lib/core/permission-helper.js');

module.exports = permHelper.initialize()
	.filterMarkForPurge()
	.filterPurge()
	.required({
		name: 'Save/Delete Hard Locked State',
		roles: ['save_delete_hard_locked_state'],
		actions: ['save_new', 'save_existing', 'remove'],
		conditions: [{
			attributes: {
				hardLocked: true,
			},
		}],
	})
	.requireWorkflowLoadInheritance()
	.requireWorkflowSaveInheritance()
	.value();
