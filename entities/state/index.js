const extend = require('../extend.js');
const standardWorkflowChildConfig = require('../standard-workflow-child-config.js');

module.exports = extend(standardWorkflowChildConfig, {
	db: 'default',
	table: 'sys_state',
	entity: {
		base: 'sys',
		name: 'state',
	},
	caption: 'Status',
	captionPlural: 'Statuses',
	addCaption: 'Add Status',
	newCaption: 'New Status',
	grids: require('./grids'),
	historyNav: [
		{ from: [], to: '/settings/workflow/custom-workflow/{workflowId}' },
	],
	acl: require('./acl.js'),
	audit: require('./audit.js'),
	validation: require('./validation'),
	importTransformer: 'state',
	copyPostAction: 'state',
	usageStatistics: require('./usage-statistics.js'),
	model(){
		return require('../../public/models/workflow-state-model.js');
	},
	collection(){
		return require('../../public/collections/workflow-states-collection.js');
	},
	parents: [
		{
			entity: {
				base: 'sys',
				name: 'workflow',
			},
			field: 'workflowId',
			reportable: true,
		},
	],
	fields: [
		{
			field: 'entityId',
			type: 'textbox',
			caption: 'Entity Id',
			kind: 'editable',
		},
		{
			field: 'name',
			type: 'textbox',
			typeOptions: {
				charMaxTextbox: 30,
			},
			caption: 'Status Name',
			kind: 'editable',
		},
		{
			field: 'description',
			type: 'textbox',
			caption: 'Description',
			kind: 'editable',
		},
		{
			field: 'primaryState',
			type: 'picklist',
			typeOptions: {
				picklistName: 'primary_state_types',
			},
			caption: 'Status Type',
			kind: 'editable',
		},
		{
			field: 'default',
			type: 'checkbox',
			caption: 'Default Status',
			kind: 'editable',
		},
	],
});
