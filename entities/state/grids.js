module.exports = {
	'main-states-grid': {
		sortColumn: 'createdDate',
		sortOrder: 'desc',
		excludeColumns: ['datePurged', 'pendingPurgeDate', 'purgeReason', 'excludeFromSuggestedLinks'],
		columns: [
			{ field: 'name' },
			{ field: 'primaryState' },
			{ field: 'description' },
			{ field: 'default' },
		],
	},
	'states-step-grid': {
		sortColumn: 'createdDate',
		sortOrder: 'desc',
		excludeColumns: ['datePurged', 'pendingPurgeDate', 'purgeReason', 'excludeFromSuggestedLinks'],
		columns: [
			{ field: 'name' },
			{ field: 'primaryState' },
			{ field: 'description' },
			{ field: 'default' },
		],
	},
};
