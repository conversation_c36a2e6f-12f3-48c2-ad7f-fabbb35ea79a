const permHelper = require('../../lib/core/permission-helper.js');

module.exports = [{
	type: 'group',
	parentPermission: 'view_system_settings',
	caption: 'Usage Statistics',
	permission: 'usage_statistic',
	disabled: true,
	options: [
		{
			permission: 'view_usage_statistic',
			caption: 'View',
			tooltip: 'View Usage Statistics',
			sequence: 2,
			disabled: true,
		},
		{
			permission: 'create_usage_statistic',
			caption: 'Create',
			sequence: 1,
			disabled: true,
		},
		{
			type: 'group',
			permission: permHelper.getEditGroupPermissionCode('usage_statistic'),
			caption: 'Edit',
			sequence: 3,
			disabled: true,
			dependencies: ['view_usage_statistic'],
			options: [{
				permission: 'edit_usage_statistic',
				caption: 'Save',
				disabled: true,
				dependencies: ['view_usage_statistic'],
			}],
		},
		{
			permission: 'remove_usage_statistic',
			caption: 'Remove',
			sequence: 4,
			disabled: true,
		},
		{
			permission: 'view_private_usage_statistic',
			caption: 'View Private Usage Statistics',
			tooltip: 'View Private Usage Statistics',
			sequence: 5,
			disabled: true,
		},
	],
}];
