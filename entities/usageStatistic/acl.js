const permHelper = require('../../lib/core/permission-helper.js');

module.exports = permHelper.initialize()
	.required({
		name: 'View Usage Statistics',
		roles: ['view_usage_statistic'],
		actions: ['load', 'list', 'save_existing', 'remove'],
		conditions: [],
	})
	.required({
		name: 'View Private Usage Statistics',
		roles: ['view_private_usage_statistic'],
		actions: ['load', 'list', 'save_existing', 'remove'],
		conditions: [{
			attributes: {
				private: true,
			},
		}],
	})
	.required({
		name: 'Create Usage Statistics',
		roles: ['create_usage_statistic'],
		actions: ['save_new'],
		conditions: [{
			attributes: {
				id: null,
			},
		}],
	})
	.required({
		name: 'Edit Usage Statistics',
		roles: ['edit_usage_statistic'],
		actions: ['save_new'],
		conditions: [{
			attributes: {
				'!id': null,
			},
		}],
	})
	.required({
		name: 'Remove Usage Statistics',
		roles: ['remove_usage_statistic'],
		actions: ['remove'],
		conditions: [],
	})
	.value();
