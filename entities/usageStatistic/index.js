const extend = require('../extend.js');
const standardConfig = require('../standard-config.js');

module.exports = extend(standardConfig, {
	db: 'default',
	search: false,
	table: 'sys_usage_statistic',
	entity: {
		base: 'sys',
		name: 'usage_statistic',
	},
	caption: 'Usage Statistic',
	captionPlural: 'Usage Statistics',
	addCaption: 'Add Usage Statistic',
	newCaption: 'New Usage Statistic',
	acl: require('./acl.js'),
	aclHierarchy: require('./acl-hierarchy.js'),
	validation: require('./validation.js'),
	usageStatistics: [],
	fields: [
		{
			field: 'recordedDate',
			type: 'datetime',
			caption: 'Recorded Date',
			kind: 'system',
		},
		{
			field: 'category',
			type: 'textbox',
			caption: 'Category',
			kind: 'system',
		},
		{
			field: 'key',
			type: 'textbox',
			caption: 'Key',
			kind: 'system',
		},
		{
			field: 'dataset',
			type: 'textbox',
			caption: 'Dataset',
			kind: 'system',
		},
		{
			field: 'value',
			type: 'number',
			kind: 'system',
			caption: 'Value',
		},
		{
			field: 'entity',
			type: 'textbox',
			kind: 'system',
			caption: 'Entity',
		},
		{
			field: 'private',
			type: 'yesno',
			kind: 'system',
			caption: 'Private',
		},
	],
});
