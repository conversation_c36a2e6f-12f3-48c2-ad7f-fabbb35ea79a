const _ = require('lodash');
const extend = require('../extend.js');
const layoutEnt = require('../layout/index.js');

module.exports = extend(layoutEnt, {
	createTable: false,
	reuseEsIndex: {
		index: 'sys_layout',
		typeField: 'layoutType',
	},
	layoutType: 'entity-grid',
	includeInElementCount: true,
	entity: {
		base: 'sys',
		name: 'entity_grid_layout_type',
	},
	caption: 'Entity Grid',
	captionPlural: 'Entity Grids',
	addCaption: 'Add Entity Grid',
	newCaption: 'New Entity Grid',
	importTransformer: 'entity_grid_layout_type',
	exportTransformer: 'entity_grid_layout_type',
	customDependencies: entityService => [
		..._.map(entityService.getRootLayoutEntities(), 'entityCanon'), 'isight/entity', 'isight/dynamic_entity',
	],
	validation: require('./validation.js'),
	model() {
		return require('../../public/models/layout-entity-grid-model.js');
	},
});
