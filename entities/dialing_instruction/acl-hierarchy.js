const permHelper = require('../../lib/core/permission-helper.js');

module.exports = [{
	type: 'group',
	caption: 'Dialing Instruction',
	permission: 'view_dialing_instruction_settings',
	parentPermission: 'view_system_settings',
	options: [
		{
			caption: 'View',
			sequence: 2,
			disabled: true,
			permission: 'view_dialing_instruction',
		},
		{
			permission: 'create_dialing_instruction',
			caption: 'Create',
			tooltip: 'Create dialing instructions',
			sequence: 1,
		},
		{
			type: 'group',
			permission: permHelper.getEditGroupPermissionCode('dialing_instructions'),
			caption: 'Edit',
			sequence: 3,
			options: [{
				permission: 'edit_dialing_instruction',
				caption: 'Save',
				tooltip: 'Edit dialing instructions',
			}],
		},
		{
			permission: 'remove_dialing_instruction',
			caption: 'Remove',
			tooltip: 'Delete dialing instructions',
			sequence: 4,
		},
	],
}];
