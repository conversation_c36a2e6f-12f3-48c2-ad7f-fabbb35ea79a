const permHelper = require('../../lib/core/permission-helper.js');

module.exports = permHelper.initialize()
	.required({
		name: 'View Dialing Instructions',
		roles: ['view_dialing_instruction'],
		actions: ['load', 'list', 'save_existing', 'remove'],
		conditions: [],
	})
	.required({
		name: 'Create Dialing Instruction',
		roles: ['create_dialing_instruction'],
		actions: ['save_new'],
		conditions: [{
			attributes: {
				id: null,
			},
		}],
	})
	.required({
		name: 'Edit Dialing Instruction',
		roles: ['edit_dialing_instruction'],
		actions: ['save_new'],
		conditions: [{
			attributes: {
				'!id': null,
			},
		}],
	})
	.required({
		name: 'Remove Dialing Instruction',
		roles: ['remove_dialing_instruction'],
		actions: ['remove'],
		conditions: [],
	})
	.value();
