const extend = require('../extend.js');
const standardConfig = require('../standard-config.js');

module.exports = extend(standardConfig, {
	db: 'default',
	table: 'sys_dialing_instruction',
	entity: {
		base: 'sys',
		name: 'dialing_instruction',
	},
	api: {
		useGenericApi: true,
	},
	allowExternalSearch: true,
	caption: 'Dialing Instruction',
	captionPlural: 'Dialing Instructions',
	addCaption: 'Add Dialing Instruction',
	newCaption: 'New Dialing Instruction',
	gridDescriptorField: 'region',
	acl: require('./acl.js'),
	aclHierarchy: require('./acl-hierarchy.js'),
	grids: require('./grids.js'),
	validation: require('./validation.js'),
	historyNav: require('./history-nav.js'),
	usageStatistics: require('./usage-statistics.js'),
	audit: require('./audit.js'),
	model(){
		return require('../../public/models/dialing-instruction-model.js');
	},
	collection(){
		return require('../../public/collections/dialing-instructions-collection.js');
	},
	view(){
		/* eslint-disable max-len */
		return require('../../public/views/settings/system/dialing-instruction/dialing-instruction-details-view.js');
	},
	fields: [
		{
			field: 'region',
			type: 'textbox',
			kind: 'editable',
			caption: 'Region',
		},
		{
			field: 'phoneNumber',
			type: 'textbox',
			kind: 'editable',
			caption: 'Phone Number',
		},
	],
});
