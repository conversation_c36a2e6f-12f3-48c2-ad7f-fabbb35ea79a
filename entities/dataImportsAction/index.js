const extend = require('../extend.js');
const standardActionConfig = require('../standard-action-config.js');

module.exports = extend(standardActionConfig, {
	db: 'default',
	table: 'sys_data_import_action',
	entity: {
		base: 'sys',
		name: 'data_import_action',
	},
	joins: [{
		referenceField: 'dataImport',
		table: 'sys_data_import',
		fields: [
			'name',
			'fileFormat',
			'importEntity',
		],
	}],
	workflowActionConfig: {
		formName: 'data-imports-action',
		aclRoles: {
			createACLRole: 'agent',
			editACLRole: 'agent',
			deleteACLRole: 'agent',
		},
		supportedRuleTypes: ['Schedule Process'],
	},
	caption: 'SFTP Data Import',
	captionPlural: 'SFTP Data Imports',
	addCaption: 'Add SFTP Data Import',
	newCaption: 'New SFTP Data Import',
	audit: require('./audit.js'),
	validation: require('./validation.js'),
	rules: require('./rules.js'),
	usageStatistics: require('./usage-statistics.js'),
	model() { return require('../../public/models/data-import-action-model.js'); },
	view() { return require('../../public/views/settings/workflow/rules/data-import-action-view.js'); },
	fields: [
		{
			field: 'dataImport',
			caption: 'Data Import',
			type: 'picklistSelectizeApi',
			typeOptions: {
				picklistName: 'data_import',
				cachePicklist: false,
				highlightMissingValue: true,
				missingValueTranslation: 'Unknown Data Import',
			},
			features: ['dataImport'],
			kind: 'editable',
		},
		{
			field: 'uniqueFileName',
			caption: 'Data file name preview',
			type: 'textbox',
			kind: 'editable',
			features: ['dataImport'],
		},
		{
			field: 'uniqueAttachmentFolderName',
			caption: 'Attachment Folder Name',
			type: 'textbox',
			kind: 'editable',
			features: ['dataImport'],
		},
		{
			field: 'uniqueIdentifierFileName',
			features: ['dataImport'],
			type: 'textbox',
			caption: 'Unique filename prefix',
			kind: 'editable',
			typeOptions: {
				charMaxTextbox: 40,
			},
		},
		{
			field: 'keepSourceFiles',
			caption: 'Keep Source Files',
			type: 'checkbox',
			kind: 'editable',
		},
		{
			field: 'validateOnly',
			caption: 'Trigger Dry Run',
			type: 'checkbox',
			kind: 'editable',
		},
	],
});
