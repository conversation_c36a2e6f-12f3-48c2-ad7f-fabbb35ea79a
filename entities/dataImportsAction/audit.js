const reference = {
	displayFields: ['actionType'],
};

module.exports = {
	allowNavigateTo: true,
	child: true,
	parentType: {
		base: 'sys',
		name: 'rule',
	},
	parentFieldId: 'ruleId',
	cmd: {
		load: {
			viewed: {
				options: {
					reference,
				},
			},
		},
		save: {
			created: {
				options: {
					reference,
				},
			},
			updated: {
				options: {
					changes: {
						excludeFields: ['id'],
					},
					reference,
				},
			},
		},
		remove: {
			deleted: {
				options: {
					reference,
				},
			},
		},
	},
};
