var extend = require('../extend.js');
var standardConfig = require('../standard-config.js');

module.exports = extend(standardConfig, {
	db: 'default',
	table: 'sys_role_permission',
	entity: {
		base: 'sys',
		name: 'role_permission',
	},
	validation: require('./validation.js'),
	// Workflow and transition permissions get created in the transition import post action
	customDependencies: ['sys/transition'],
	importTransformer: 'role_permission',
	caption: 'Role Permission',
	captionPlural: 'Role Permissions',
	addCaption: 'Add Role Permission',
	newCaption: 'New Role Permission',
	acl: require('./acl.js'),
	parents: [
		{
			entity: {
				base: 'sys',
				name: 'user_role',
			},
			field: 'userRoleId',
		},
		{
			entity: {
				base: 'sys',
				name: 'permission',
			},
			field: 'permissionId',
		},
	],
	fields: [
		{
			field: 'userRoleId',
			type: 'picklistApi',
			caption: 'Role',
			kind: 'editable',
			typeOptions: {
				picklistName: 'user_roles',
			},
			dbIndex: true,
		},
		{
			field: 'permissionId',
			type: 'id',
			caption: 'Permission Id',
			kind: 'editable',
			dbIndex: true,
		},
	],
	joins: [
		{
			referenceField: 'userRoleId',
			table: 'sys_user_role',
			fields: [
				'name',
			],
		},
		{
			referenceField: 'permissionId',
			table: 'sys_permission',
			fields: [
				'code',
				'dynamic',
			],
		},
	],
	audit: {
		child: true,
		parentType: {
			base: 'sys',
			name: 'user_role',
		},
		parentFieldId: 'userRoleId',
		allowNavigateTo: true,
		cmd: {
			load: {
				'viewed': true,
			},
			save: {
				'created': true,
				'updated': {
					options: {
						changes: {
							excludeFields: ['id'],
						},
					},
				},
			},
			remove: {
				'deleted': true,
			},
		},
	},
});