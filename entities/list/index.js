var extend = require('../extend.js');
var standardConfig = require('../standard-config.js');

module.exports = extend(standardConfig, {
	db: 'default',
	table: 'sys_list',
	entity: {
		base: 'sys',
		name: 'list',
	},
	caption: 'Picklist Type',
	captionPlural: 'Picklist Types',
	addCaption: 'Add Picklist Type',
	newCaption: 'New Picklist Type',
	acl: require('./acl.js'),
	grids: require('./grids.js'),
	validation: require('./validation.js'),
	rules: require('./rules.js'),
	importTransformer: 'list',
	exportTransformer: 'list',
	// Lists don't technically depend on fields but we added this dependency so that we can access
	// the imported fields in the list import transformer
	customDependencies: ['isight/field', 'isight/dynamic_entity_field'],
	cacheBustGroups: require('./cache-bust-groups.js'),
	configurationExport: true,
	exportCaption: 'Picklists',
	model: function(){
		return require('../../public/models/picklist-type-model.js');
	},
	collection: function(){
		return require('../../public/collections/picklist-type-collection.js');
	},
	fields: [
		{
			field: 'name',
			type: 'textbox',
			kind: 'editable',
			caption: 'Name',
		},
		{
			field: 'locked',
			type: 'checkbox',
			kind: 'editable',
			caption: 'Locked',
		},
		{
			field: 'external',
			type: 'checkbox',
			kind: 'editable',
			caption: 'External',
		},
		{
			field: 'parents',
			type: 'picklistApi[]',
			kind: 'editable',
			caption: 'Parent Picklist Types',
			typeOptions: {
				picklistName: 'picklist_types',
			},
		},
		{
			field: 'parentAttributes',
			type: 'textbox[]',
			kind: 'editable',
			caption: 'Parent Attributes',
		},
		{
			field: 'url',
			type: 'textbox',
			kind: 'editable',
			caption: 'Url',
		},
		{
			field: 'text',
			type: 'textbox',
			kind: 'editable',
			caption: 'Text',
		},
		{
			field: 'value',
			type: 'textbox',
			kind: 'editable',
			caption: 'Value',
		},
		{
			field: 'filter',
			type: 'textbox',
			kind: 'editable',
			caption: 'Filter',
		},
		{
			field: 'dynamic',
			type: 'checkbox',
			kind: 'editable',
			caption: 'Dynamic',
		},
		{
			field: 'ttl',
			type: 'number',
			kind: 'editable',
			caption: 'Time-to-live',
		},
		{
			field: 'maxItems',
			type: 'number',
			kind: 'editable',
			caption: 'Max-Items',
		},
		{
			field: 'caption',
			type: 'textbox',
			kind: 'editable',
			caption: 'Caption',
		},
		{
			field: 'formConfigName',
			type: 'textbox',
			kind: 'editable',
			caption: 'Form Config Name',
		},
		{
			field: 'parentEntityCanon',
			type: 'textbox',
			kind: 'editable',
			caption: 'Parent Entity Canon',
		},
		{
			field: 'subText',
			type: 'textbox',
			kind: 'hidden',
			caption: 'Sub text',
		},
		{
			field: 'dataField',
			type: 'textbox',
			kind: 'editable',
			caption: 'Data Field',
		},
		{
			field: 'originalId',
			type: 'id',
			caption: 'Original Id',
			kind: 'hidden',
		},
		{
			field: 'features',
			type: 'textbox[]',
			caption: 'Features',
			kind: 'hidden-searchable',
		},
	],
});
