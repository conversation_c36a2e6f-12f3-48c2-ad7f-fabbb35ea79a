/* global $appData */
const $ = require('jquery');
const _ = require('lodash');

const appDataCategory = 'picklistConfig';

function clearPicklistData(picklistConfigs, picklistName) {
	const picklistType = picklistConfigs[picklistName];
	if (picklistType) {
		delete $appData.picklists.data[picklistType.id];
	}
}

function setPicklistConfigs(currentPicklistConfigs) {
	$.ajax({
		url: `${$appData.globalConfig.apiRoot}/picklist_type`,
		method: 'GET',
		dataType: 'json',
		contentType: 'application/json',
	})
		.done((data) => {
			const newPicklistConfigs = _.mapKeys(data, config => config.name);
			_.each(newPicklistConfigs, (picklistConfig, key) => {
				clearPicklistData(currentPicklistConfigs, key);
				_.set(newPicklistConfigs,
					key,
					_.omitBy(picklistConfig, config => config === null));
			});
			$appData[appDataCategory] = newPicklistConfigs;
		});
}

module.exports = function handler(opts) {
	const utils = require('../../../public/lib/utils.js');
	const currentPicklistConfigs = utils.getPicklistsConfig();
	const { patchData } = opts;
	if (patchData) {
		utils.patch({
			appDataCategory,
			data: patchData,
			identifierProperties: ['name'],
		});
		// note patchData can contain deleted data - value is null
		_.forEach(patchData, (picklist) => {
			if (picklist) {
				const picklistName = picklist.name;
				clearPicklistData(currentPicklistConfigs, picklistName);
			}
		});
	} else {
		setPicklistConfigs(currentPicklistConfigs);
	}
};
