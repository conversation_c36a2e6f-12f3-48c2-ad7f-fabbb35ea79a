var permHelper = require('../../lib/core/permission-helper.js');

module.exports = permHelper.initialize()
	.required({
		name: 'View Picklist Type',
		roles: ['view_picklist_item_settings'],
		actions: ['load', 'list', 'save_existing', 'remove'],
		conditions: [],
	})
	.required({
		name: 'Create Picklist Type',
		roles: ['form_builder'],
		actions: ['save_new'],
		conditions: [{
			attributes: {
				id: null,
			},
		}],
	})
	.required({
		name: 'Edit Picklist Type',
		roles: ['form_builder'],
		actions: ['save_new'],
		conditions: [{
			attributes: {
				'!id': null,
			},
		}],
	})
	.required({
		name: 'Remove Picklist Type',
		roles: ['form_builder'],
		actions: ['remove'],
		conditions: [],
	})
	.value();