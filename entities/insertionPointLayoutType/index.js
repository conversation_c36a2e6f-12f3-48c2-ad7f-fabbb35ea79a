const _ = require('lodash');
const extend = require('../extend.js');
const layoutEnt = require('../layout');

module.exports = extend(layoutEnt, {
	createTable: false,
	reuseEsIndex: {
		index: 'sys_layout',
		typeField: 'layoutType',
	},
	layoutType: 'insertionPoint',
	entity: {
		base: 'sys',
		name: 'insertion_point_layout_type',
	},
	validation: require('./validation.js'),
	caption: 'Insertion Point',
	captionPlural: 'Insertion Points',
	addCaption: 'Add Insertion Point',
	newCaption: 'New Insertion Point',
	importTransformer: 'insertion_point_layout_type',
	exportTransformer: 'insertion_point_layout_type',
	customDependencies: entityService => _.map(entityService.getRootLayoutEntities(), 'entityCanon'),
});
