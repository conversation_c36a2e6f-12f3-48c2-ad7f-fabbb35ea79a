<div class="card-small{?model.datePurged} purged{:else}{?model.pendingPurgeDate} scheduled-purge{/model.pendingPurgeDate}{/model.datePurged}">
	<div class="card-header">
		{@entityIcon entity=entity$/}
		{@entityLink entity=entity$ context=model}<div class="card-title">{formattedData.childNumber|s}</div>{/entityLink}
		<br />
		<div class="card-label" title="{@eq key=model.kind value="generated_template"}{formattedData.templateId|s}{/eq}{@eq key=model.kind value="url"}{formattedData.url|s}{/eq}{@eq key=model.kind value="file_upload"}{#model.files}{name}{/model.files}{/eq}{@eq key=model.kind value="custom_packet"}{model.fileNames|s}{/eq}{@eq key=model.kind value="packet"}{model.fileNames|s}{/eq}">
			{@eq key=model.kind value="generated_template"}{formattedData.templateId|s}{/eq}
			{@eq key=model.kind value="url"}{formattedData.url|s}{/eq}
			{@eq key=model.kind value="file_upload"}
				{#model.files}{name}{/model.files}
			{/eq}
			{@eq key=model.kind value="custom_packet"}{model.fileNames|s}{/eq}
			{@eq key=model.kind value="packet"}{model.fileNames|s}{/eq}
		</div>
	</div>
	{#highlightedFields}
		{>small-highlight-tmpl entityName="sys/attachment" ago=model.ago/}
	{/highlightedFields}
</div>