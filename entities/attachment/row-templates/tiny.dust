<div class="card-tiny{?model.datePurged} purged{:else}{?model.pendingPurgeDate} scheduled-purge{/model.pendingPurgeDate}{/model.datePurged}">
    {@entityLink entity=entity$ context=model target="_blank"}

        <div class="card-header">
            {@entityIcon entity=entity$/}
            <div class="card-title" data-toggle="tooltip" data-placement="top"
                 title='{formattedData.childNumber|s}'>
                {@resource groupName="sys/attachment" subgroupName="general" key="name"/}:
            </div>
            <div class="card-label">
                {@eq key=model.kind value="generated_template"}{formattedData.templateId|s}{/eq}
                {@eq key=model.kind value="url"}{formattedData.url|s}{/eq}
                {@eq key=model.kind value="file_upload"}
                    {#model.files}{name}{/model.files}
                {/eq}
                {@eq key=model.kind value="custom_packet"}{model.fileNames|s}{/eq}
                {@eq key=model.kind value="packet"}{model.fileNames|s}{/eq}
            </div>
            <i class="fa fa-external-link external-link-icon" aria-hidden="true"></i>
            <br/>
        </div>
    {/entityLink}
</div>
