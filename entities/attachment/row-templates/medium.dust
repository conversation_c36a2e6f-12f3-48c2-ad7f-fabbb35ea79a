<td class="card-medium{?model.datePurged} purged{:else}{?model.pendingPurgeDate} scheduled-purge{/model.pendingPurgeDate}{/model.datePurged}">
    <div class="card-header">
		  <div class="card-header">
				{@entityIcon entity=entity$/}
				{@entityLink entity=entity$ context=model}<div class="card-title">{formattedData.childNumber|s}</div>{/entityLink}
		    <div class="card-label">
				{@eq key=model.kind value="generated_template"}{formattedData.templateId|s}{/eq}
				{@eq key=model.kind value="url"}{formattedData.url|s}{/eq}
				{@eq key=model.kind value="file_upload"}
					{#model.files}{name}{/model.files}
				{/eq}
				{@eq key=model.kind value="custom_packet"}{model.fileNames|s}{/eq}
				{@eq key=model.kind value="packet"}{model.fileNames|s}{/eq}
		    </div>
		  </div>
	</div>
	{#highlightedFields}
		{>medium-highlight-tmpl entityName="sys/attachment" ago="{model.ago}"/}
	{/highlightedFields}   
</td>