const permHelper = require('../../lib/core/permission-helper.js');

module.exports = [{
	type: 'group',
	parentPermission: 'case',
	caption: 'Files',
	permission: 'attachment',
	options: [
		{
			permission: 'view_attachment',
			caption: 'View',
			tooltip: 'View Files',
			sequence: 2,
		},
		{
			permission: 'create_attachment',
			caption: 'Create',
			sequence: 1,
			tooltip: 'Add Files',
		},
		{
			type: 'group',
			permission: permHelper.getEditGroupPermissionCode('attachment'),
			caption: 'Edit',
			sequence: 3,
			dependencies: ['view_attachment'],
			options: [
				{
					permission: 'edit_attachment',
					caption: 'Save',
					tooltip: 'Edit Files',
					dependencies: ['view_attachment'],
				},
				{
					caption: 'Set Reporter Access',
					permission: 'mark_attachment_external',
					tooltip: 'Set "Allow Reporter Access" on Files belonging to an external Case when the two-way portal is enabled',
					dependencies: ['view_attachment', 'edit_attachment'],
				},
			],
		},
		{
			permission: 'remove_attachment',
			caption: 'Remove',
			sequence: 4,
			tooltip: 'Delete Files',
			dependencies: ['view_attachment'],
		},
	],
}];
