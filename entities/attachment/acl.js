const _ = require('lodash');
const permHelper = require('../../lib/core/permission-helper.js');

module.exports = permHelper.initialize()
	.filterMarkForPurge()
	.filterPurge()
	.filterChildPortalFields()
	.filter({
		name: 'Mark Attachments External',
		roles: ['mark_attachment_external'],
		actions: ['save_new', 'save_existing'],
		conditions: [],
		filters: {
			externalRecord: false,
		},
	})
	.filterViewSourceData()
	.required({
		name: 'View Attachments',
		roles: ['view_attachment'],
		actions: ['load', 'list', 'save_existing', 'remove'],
		conditions: [permHelper.conditionForInternalRecord, {
			attributes: {
				sysActive: true,
			},
		}],
		generatePermissions: ['external'],
	})
	.required({
		name: 'Create Attachments',
		roles: ['create_attachment'],
		actions: ['save_new'],
		conditions: [permHelper.conditionForInternalRecord, {
			attributes: {
				id: null,
			},
		}],
		generatePermissions: ['external', 'external_case_not_created_by'],
	})
	.required({
		name: 'Edit Attachments',
		roles: ['edit_attachment'],
		actions: ['save_new'],
		conditions: [permHelper.conditionForInternalRecord, {
			attributes: {
				'!id': null,
				sysActive: true,
			},
		}],
		generatePermissions: ['external'],
	})
	.required({
		name: 'Remove Attachments',
		roles: ['remove_attachment'],
		actions: ['remove'],
		conditions: [permHelper.conditionForInternalRecord, {
			attributes: {
				sysActive: true,
			},
		}],
		generatePermissions: ['external'],
	})
	.required({
		name: 'Access Draft Attachments',
		roles: ['access_others_draft_children'],
		actions: ['load', 'list', 'save_existing', 'remove'],
		conditions: [permHelper.conditionForInternalRecord, {
			attributes: {
				'!id': null,
				sysActive: false,
				createdBy: '{!user.id}',
			},
		}],
	})
	.required({
		name: 'Inherit Note Load ACL',
		roles: ['bypass_inherited_acl'],
		actions: ['load', 'list'],
		conditions: [{
			attributes: {
				parentType: 'note',
			},
		}, {
			fn(obj, context) {
				const hasBypassRole = _.includes(context?.user?.perm?.roles, 'bypass_note_load_inheritance_external');
				const isExternal = obj?.externalRecord === true;
				const bypassNoteInheritance = hasBypassRole && isExternal;

				// apply inheritance if true
				return { ok: !bypassNoteInheritance };
			},
			selectedFields(opts, callback) {
				const fields = ['id', 'externalRecord'];
				return callback(null, fields);
			},
		}, 'sys/note::{parentId}::load'],
	})
	.required({
		name: 'Inherit Note ACL',
		roles: ['bypass_inherited_acl'],
		actions: ['save_new', 'save_existing', 'remove'],
		conditions: [{
			attributes: {
				parentType: 'note',
			},
		}, 'sys/note::{parentId}::save'],
	})
	.required({
		name: 'Inherit Todo Load ACL Internal',
		roles: ['bypass_inherited_acl'],
		actions: ['load', 'list'],
		conditions: [{
			attributes: {
				parentType: 'todo',
				'!externalRecord': true,
			},
		}, 'sys/todo::{parentId}::load'],
	})
	.required({
		name: 'Inherit Todo Load ACL External',
		roles: ['bypass_inherited_acl', 'bypass_todo_load_inheritance_external'],
		actions: ['load', 'list'],
		conditions: [{
			attributes: {
				parentType: 'todo',
				externalRecord: true,
			},
		}, 'sys/todo::{parentId}::load'],
	})
	.required({
		name: 'Inherit Todo Save ACL',
		roles: ['bypass_inherited_acl'],
		actions: ['save_new', 'save_existing', 'remove'],
		conditions: [{
			attributes: {
				parentType: 'todo',
			},
		}, 'sys/todo::{parentId}::save'],
	})
	.requireCaseLoadInheritance()
	.required({
		name: 'Inherit Case Save ACL when Parent is not Note/Todo and Record is Interal or Updating Existing External',
		roles: ['bypass_inherited_acl'],
		control: 'required',
		actions: ['save_new', 'save_existing', 'remove'],
		conditions: [{
			or: [{
				attributes: {
					'!caseId': null,
					'!parentType': ['todo', 'note'],
					'!externalRecord': true,
				},
			}, {
				attributes: {
					'!id': null,
					'!caseId': null,
					'!parentType': ['todo', 'note'],
					externalRecord: true,
				},
			}],
		}, 'sys/case::{caseId}::save'],
	})
	.required({
		name: 'Inherit Case Save ACL when Parent is not Note/Todo External',
		roles: ['bypass_inherited_acl', 'bypass_case_save_inheritance_external'],
		control: 'required',
		actions: ['save_new', 'save_existing', 'remove'],
		conditions: [{
			attributes: {
				id: null,
				'!caseId': null,
				'!parentType': ['todo', 'note'],
				externalRecord: true,
			},
		}, 'sys/case::{caseId}::save'],
	})
	.value();
