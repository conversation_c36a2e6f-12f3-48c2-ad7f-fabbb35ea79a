module.exports = {
	isPacket: function (data) {
		return data.kind === 'packet';
	},
	isCustomPacket: function (data) {
		return data.kind === 'custom_packet';
	},

	isManualSelectionOfCaseFilesForPacketRequired: function (data) {
		// NOTE: this rule assumes that data.kind === 'packet'
		//
		return data.packetId__caseFilesInclusionKind === 'manual_selection';
	},
	wasManualSelectionOfCaseFilesForPacketRequired: function (data) {
		// NOTE: this rule assumes that data.kind === 'packet'
		//
		return data.caseFilesInclusionKind === 'manual_selection';
	},

	isManualSelectionOfCaseFilesForCustomPacketRequired: function (data) {
		// NOTE: this rule assumes that data.kind === 'packet'
		//
		return data.caseFilesInclusionKind === 'manual_selection';
	},
	isGeneratedTemplate: function (data) {
		return data.kind === 'generated_template';
	},
	isFileUpload: function (data) {
		return data.kind === 'file_upload';
	},
	isUrl(data) {
		return data.kind === 'url';
	},
	hasActiveShare(data) {
		return !!data.hasActiveShare;
	},
};
