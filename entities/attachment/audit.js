const refFields = [
	'files',
	'description',
];
const reference = {
	displayFields(data, auditModel) {
		return auditModel.hideCaseItemNumberReference() !== true
			? ['childNumber'].concat(refFields)
			: refFields;
	},
};
module.exports = {
	child: true,
	parentType: {
		base: 'sys',
		name: 'case',
	},
	parentFieldId: 'caseId',
	allowNavigateTo: true,
	navigateToType: 'file',
	cmd: {
		load: {
			viewed: {
				options: {
					reference,
				},
			},
		},
		save: {
			created: {
				options: {
					reference,
				},
			},
			updated: {
				options: {
					changes: {
						excludeFields: ['files', 'id'],
					},
					reference,
				},
			},
		},
		remove: {
			deleted: {
				options: {
					reference,
				},
			},
		},
	},
};
