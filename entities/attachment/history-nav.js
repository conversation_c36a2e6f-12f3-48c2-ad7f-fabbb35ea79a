module.exports = [
	// External
	{ from: ['/external/file/new'], to: '/external/files' },
	{ from: ['/external/case/{caseId}/file/new'], to: '/external/case/{caseId}/files' },
	// Case as parent
	{ from: [], to: '/case/{parentId}/files', criterias: { parentType: 'case' }},
	// To-Do as parent
	{ from: ['/to-do/{parentId}/edit', null], to: '/to-do/{parentId}/edit', criterias: { parentType: 'todo' }},
	{ from: ['/to-do/{parentId}/edit', '/file/{id}', null], to: '/to-do/{parentId}/edit', criterias: { parentType: 'todo' }},
	{ from: [], to: '/to-do/{parentId}', criterias: { parentType: 'todo' }},
	// Note as parent
	{ from: ['/note/{parentId}/edit', null], to: '/note/{parentId}/edit', criterias: { parentType: 'note' }},
	{ from: ['/note/{parentId}/edit', '/file/{id}', null], to: '/note/{parentId}/edit', criterias: { parentType: 'note' }},
	{ from: [], to: '/note/{parentId}', criterias: { parentType: 'note' }},
	// Defaults
	{ from: [], to: '/files' },
];
