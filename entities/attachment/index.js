const _ = require('lodash');
const extend = require('../extend.js');
const standardChildConfig = require('../standard-child-config.js');
const packetConfig = require('../standard-packet-config.js');

const parentAttachmentConfig = extend(standardChildConfig, packetConfig);

module.exports = extend(parentAttachmentConfig, {
	db: 'default',
	table: 'sys_attachment',
	entity: {
		base: 'sys',
		name: 'attachment',
	},
	esAlwaysUpsert: true,
	api: {
		useGenericApi: true,
		useGenericApiExternal: true,
		mainFileField: 'files',
	},
	normalizeMultiValuePicklistEntries: true,
	allowExternalFlag: true,
	allowExternalSearch: true,
	customForm: false,
	ruleEvents: true,
	report: false,
	enablePortalUserNotifications: true,
	caption: 'File',
	captionPlural: 'Files',
	addCaption: 'Add File',
	newCaption: 'New File',
	gridDescriptorFn: (context) => {
		const { formattedData: { files, description, childNumber } } = context;
		if (!_.isEmpty(files)) return files;
		if (description) return description;
		return childNumber;
	},
	acl: require('./acl.js'),
	aclHierarchy: require('./acl-hierarchy.js'),
	audit: require('./audit.js'),
	rules: require('./rules.js'),
	grids: require('./grids.js'),
	computeFunctions: require('./compute-functions.js'),
	dataImportConfig: {
		parentMappingGrid: 'data-import-mappings-exclude-source',
		editableGridFields: ['parentRecordTarget'],
		hideDataMapping: true,
	},
	validation: require('./validation.js'),
	historyNav: require('./history-nav.js'),
	icon: 'fa-file',
	includeInDataDictionary: true,
	enableRecordSourceView: true,
	usageStatistics: require('./usage-statistics.js'),
	allowInGridViews: true,
	linkSummaryFields: ['description'],
	staticFieldWorkflows: [
		require('../case/static-field-workflows/external-record.js'),
	],
	model: function(){
		return require('../../public/models/file-model.js');
	},
	collection: function(){
		return require('../../public/collections/files-collection.js');
	},
	view: function(){
		return require('../../public/views/file/file-details-view.js');
	},
	rowTemplates: {
		small: function(){
			return require('./row-templates/small.dust');
		},
		medium: function(){
			return require('./row-templates/medium.dust');
		},
		tiny: function(){
			return require('./row-templates/tiny.dust');
		},
	},
	parents: [
		{
			entity: {
				base: 'sys',
				name: 'case',
			},
			field: 'parentId',
			reportable: true,
			filter: {
				parentType: 'case',
			},
		},
		{
			entity: {
				base: 'sys',
				name: 'note',
			},
			field: 'parentId',
			filter: {
				parentType: 'note',
			},
		},
		{
			entity: {
				base: 'sys',
				name: 'todo',
			},
			field: 'parentId',
			filter: {
				parentType: 'todo',
			},
		},
	],
	fields: [
		{
			field: 'url',
			type: 'url',
			caption: 'URL',
			kind: 'editable',
			showOnPortal: true,
		},
		{
			field: 'files',
			type: 'file[]',
			kind: 'editable-external',
			caption: 'Attachments',
			excludeFromSaveAndCopy: true,
			showOnPortal: true,
			typeOptions: { vectorize: true },
		},
		{
			field: 'parentId',
			type: 'id',
			kind: 'custom',
			kindOptions: {
				flags: {
					audit: false,
					schema: true,
					search: true,
					apiWritable: true,
					apiExternalWritable: true,
					computedOnSave: false,
					computedOnRead: false,
					formVisible: false,
					gridVisible: false,
					gridSortable: false,
					searchVisible: false,
					formattedData: true,
					gridExportable: false,
					reportable: false,
				},
			},
			caption: 'Parent Id',
			dbIndex: true,
			excludeFromRedact: true,
			showOnPortal: true,
		},
		{
			field: 'parentType',
			type: 'code',
			kind: 'custom',
			kindOptions: {
				flags: {
					audit: false,
					schema: true,
					search: true,
					apiWritable: true,
					apiExternalWritable: true,
					computedOnSave: false,
					computedOnRead: false,
					formVisible: false,
					gridVisible: false,
					gridSortable: false,
					searchVisible: false,
					formattedData: true,
					gridExportable: false,
					reportable: false,
				},
			},
			caption: 'Parent Type',
			dbIndex: true,
			excludeFromRedact: true,
			showOnPortal: true,
		},
		{
			field: 'kind',
			type: 'picklist',
			caption: 'Kind',
			typeOptions: {
				picklistName: 'file_kinds',
			},
			kind: 'editable-external',
			showOnPortal: true,
		},
		{
			field: 'generatedFileType',
			type: 'picklist',
			caption: 'File Type',
			typeOptions: {
				picklistName: 'attachment_generated_file_types',
			},
			kind: 'editable',
		},
		{
			field: 'packetId',
			type: 'picklistSelectizeApi',
			typeOptions: {
				picklistName: 'packets',
			},
			caption: 'Packet',
			kind: 'editable',
			dbIndex: true,
		},
		{
			field: 'description',
			type: 'texteditor',
			caption: 'Summary',
			kind: 'editable-external',
			showOnPortal: true,
		},
		{
			field: 'fileShared',
			type: 'yesno',
			typeOptions: {
				allowNull: true,
			},
			caption: 'File Shared',
			kind: 'editable',
			excludeFromSaveAndCopy: true,
		},
		{
			field: 'hasActiveShare',
			caption: 'Is there an Active Shared Link against this File record?',
			type: 'yesno',
			kind: 'computed-read',
			kindOptions: {
				computeFunction: 'hasActiveShare',
			},
			excludeFromSaveAndCopy: true,
			features: ['fileShare'],
		},
		{
			field: 'sharedLinks',
			type: 'code[]',
			caption: 'Shared Links',
			kind: 'custom',
			kindOptions: {
				flags: {
					audit: false,
					schema: true,
					search: true,
					apiWritable: true,
					apiExternalWritable: false,
					computedOnSave: false,
					computedOnRead: false,
					formVisible: false,
					gridVisible: false,
					gridSortable: false,
					searchVisible: false,
					formattedData: true,
					gridExportable: false,
					reportable: false,
				},
			},
			excludeFromSaveAndCopy: true,
			features: ['fileShare'],
		},
		{
			field: 'templateId',
			type: 'picklistApi',
			caption: 'Template Name',
			typeOptions: {
				picklistName: 'templates',
				picklistDependencies: ['templateLocale'],
			},
			kind: 'editable',
			dbIndex: true,
		},
		{
			field: 'templateLocale',
			type: 'picklist',
			caption: 'Locale',
			typeOptions: {
				picklistName: 'supported_languages',
				filter: 'existingLanguagesFilter',
			},
			kind: 'editable-external',
			showOnPortal: true,
		},
		{
			field: 'contentSearchable',
			type: 'checkbox',
			default: false,
			cellTemplate: function (fs) {
				return fs.readFileSync(`${__dirname}/cell-templates/attachment-contentsearchable-cell-tmpl.dust`, 'utf8');
			},
			kind: 'system',
			caption: 'Content Searchable',
			typeOptions: {
				allowNull: true,
			},
			gridWidth: 210,
			dbIndex: true,
		},
		{
			field: 'attachmentDescriptions',
			type: 'textarea',
			kind: 'computed',
			kindOptions: { computeFunction: 'setAttachmentDescriptions' },
			caption: 'Description',
			cellTemplate: function (fs) {
				return fs.readFileSync(
					__dirname + '/cell-templates/attachment-descriptions-cell-tmpl.dust',
					'utf8',
				);
			},
		},
		{
			field: 'name',
			kind: 'hidden',
		},
		{
			field: 'tableOfContentsTemplateKind',
			kind: 'hidden',
		},
		{
			field: 'tableOfContentsTemplate',
			kind: 'hidden',
			typeOptions: {
				supportedFormats: [],
			},
		},
		{
			field: 'didSomeCaseFilesExceedMaxSize',
			caption: 'Were some case files too large',
			type: 'yesno',
			default: false,
			kind: 'hidden',
		},
		{
			field: 'didSomeTemplateBasedFilesExceedMaxSize',
			caption: 'Were some template-based files too large',
			type: 'yesno',
			default: false,
			kind: 'hidden',
		},
		{
			field: 'wereSomeCaseFilesTruncated',
			caption: 'Were some case files truncated',
			type: 'yesno',
			default: false,
			kind: 'hidden',
		},
		{
			field: 'wereSomeTemplateBasedFilesTruncated',
			caption: 'Were some template-based files truncated',
			type: 'yesno',
			default: false,
			kind: 'hidden',
		},
		{
			field: 'wasTableOfContentsTruncated',
			caption: 'Were the table of contents truncated',
			type: 'yesno',
			default: false,
			kind: 'hidden',
		},
		{
			field: 'externalRecord',
			type: 'checkbox',
			caption: 'External',
			kind: 'editable',
			typeOptions: {
				allowNull: false,
			},
			kindOptions: {
				useInDynamicJoins: true,
			},
			showOnPortal: true,
		},
		{
			field: 'documentGenerationMethod',
			default: null,
		},
		{
			field: 'caseFilesInclusionKind',
			default: null,
		},
		{
			field: 'bulkTags',
			type: 'tag[]',
			kind: 'editable',
			caption: 'Bulk Tags',
			features: ['fileEnhancement'],
		},
		{
			field: 'fileSize',
			type: 'textbox',
			kind: 'editable',
			caption: 'File Size',
			features: ['fileEnhancement'],
		},
		{
			field: 'fileFormat',
			type: 'textbox',
			kind: 'editable',
			caption: 'fileFormat',
			features: ['fileEnhancement'],
		},
	],
	joins: [
		{
			referenceField: 'templateId',
			table: 'sys_template',
			fields: [
				'name',
			],
		},
		{
			referenceField: 'packetId',
			table: 'sys_packet',
			fields: [
				'name',
				'caseFilesInclusionKind',
			],
		},
		{
			referenceField: 'parentId',
			table: 'sys_todo',
			fields: [
				'responsible',
			],
		},
		{
			referenceField: 'sourceJob',
			table: 'sys_event',
			fields: [
				'name',
				'jobId',
			],
		},
	],
	customHighlightFields: [{
		field: 'attachment.content',
		fragment_size: 70,
		number_of_fragments: 8,
	}],
});
