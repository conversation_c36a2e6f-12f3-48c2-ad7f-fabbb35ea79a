const statHelper = require('../../shared/stat-helper.js')('sys_attachment');
const sharedUtils = require('../../shared/utils.js')();

module.exports = [
	{
		category: 'attachment',
		key: 'totalAttachments',
		query: statHelper.countActiveEntity(),
	},
	{
		category: 'attachment',
		key: 'attachmentsByKind',
		query: statHelper.countActiveEntityByPicklistGroup(),
		options: {
			groupingField: 'kind',
			picklistName: 'file_kinds',
			datasetTranslation(dataset, translate) {
				return statHelper.getPicklistDatasetTrans(this, dataset, translate);
			},
		},
	},
	{
		category: 'storage',
		key: 'sizeOfFileUploads',
		query(knex, options, callback) {
			const summary = {
				totalSize: 0,
			};
			const {
				pipeline,
				BulkTransformStream,
				DrainStream,
			} = options;
			const listStream = knex
				.select('sys_attachment.files')
				.from('sys_attachment')
				.whereIn('sys_attachment.kind', sharedUtils.getFileUsageKinds())
				.where('sys_attachment.sys_active', true)
				.whereNotNull('sys_attachment.files')
				.stream();
			const accumulatorStream = sharedUtils.getStreamToCalculateTotalSize(BulkTransformStream, summary, 'files');
			const drainStream = new DrainStream();

			return pipeline(
				listStream,
				accumulatorStream,
				drainStream,
				err => callback(err, summary.totalSize),
			);
		},
	},
	{
		category: 'attachment',
		key: 'totalFileAttachmentCount',
		query: statHelper.countActiveEntity({
			where() {
				// postgres uses 1 based numbering for arrays
				// 'files[1]' will return null if value is null or an empty array
				this.whereNotNull('files[1]');
			},
		}),
	},
	{
		category: 'attachment',
		key: 'totalFileShared',
		query: statHelper.countActiveEntity({
			where() {
				this.where(function where() {
					return this
						.whereNotNull('files[1]')
						.whereNotNull('shared_links');
				});
			},
		}),
	},
	{
		category: 'attachment',
		key: 'attachmentsPerCase',
		query: statHelper.countTotalAverageAndHighestByCase(),
	},
];
