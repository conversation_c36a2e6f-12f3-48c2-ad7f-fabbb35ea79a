const _ = require('lodash');
const async = require('async');

module.exports = {
	setAttachmentDescriptions(seneca, entities, callback) {
		callback(null, _.map(entities, (entity) => {
			const { files } = entity;
			if (!files || _.isEmpty(files)) {
				return '';
			}
			if (files.length === 1) {
				return files[0].description;
			}
			return _.map(files, file => ({
				name: file.name,
				description: file.description,
			}));
		}));
	},
	hasActiveShare(seneca, rows, callback) {
		async.mapLimit(rows, 5, (row, done) => {
			if (!row || !row.id) {
				return done(null, false);
			}

			return seneca.make$('sys/shared_link_receipt').list$({
				fn$() {
					this
						.where('sys_shared_link_receipt.attachment_id', row.id)
						.andWhere('shared_link_id_sys_shared_link.status', 'Active');
				},
				limit: 1,
			}, (err, receipts) => {
				if (err) {
					return done(err);
				}

				return done(null, receipts.length !== 0);
			});
		}, callback);
	},
};
