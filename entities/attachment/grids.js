const defaultDynamicDataFilters = ['kind', 'createdDate', 'lastUpdatedDate'];
module.exports = {
	'portal-files': {
		sortColumn: 'files',
		sortOrder: 'desc',
		columns: [
			{ field: 'files' },
			{ field: 'description' },
		],
	},
	'main-files': {
		sortColumn: 'createdDate',
		sortOrder: 'desc',
		columns: [
			{ field: 'childNumber' },
			{ field: 'files' },
			{ field: 'description' },
			{ field: 'createdBy' },
			{ field: 'createdDate' },
			{ field: 'contentSearchable' },
		],
		defaultDynamicDataFilters,
	},
	'main-files-external': {
		sortColumn: 'createdDate',
		sortOrder: 'desc',
		columns: [
			{ field: 'childNumber' },
			{ field: 'createdDate' },
			{ field: 'files' },
			{ field: 'description' },
		],
	},
	'case-files': {
		sortColumn: 'createdDate',
		sortOrder: 'desc',
		columns: [
			{ field: 'number' },
			{ field: 'files' },
			{ field: 'description' },
			{ field: 'createdBy' },
			{ field: 'createdDate' },
			{ field: 'contentSearchable' },
			{ field: 'fileShared' },
		],
		defaultDynamicDataFilters,
		default: true,
	},
	'case-files-external': {
		sortColumn: 'createdDate',
		sortOrder: 'desc',
		columns: [
			{ field: 'number' },
			{ field: 'createdDate' },
			{ field: 'files' },
			{ field: 'description' },
		],
	},
	'case-files-file-upload': {
		sortColumn: 'createdDate',
		sortOrder: 'desc',
		columns: [
			{ field: 'number' },
			{ field: 'files' },
			{ field: 'description' },
			{ field: 'fileType' },
			{ field: 'createdBy' },
			{ field: 'createdDate' },
			{ field: 'contentSearchable' },
			{ field: 'fileShared' },
		],
	},
	'case-files-generated-template': {
		sortColumn: 'createdDate',
		sortOrder: 'desc',
		columns: [
			{ field: 'number' },
			{ field: 'files' },
			{ field: 'templateId' },
			{ field: 'description' },
			{ field: 'createdBy' },
			{ field: 'createdDate' },
			{ field: 'contentSearchable' },
			{ field: 'fileShared' },
		],
	},
	'case-files-url': {
		sortColumn: 'createdDate',
		sortOrder: 'desc',
		columns: [
			{ field: 'number' },
			{ field: 'url' },
			{ field: 'description' },
			{ field: 'createdBy' },
			{ field: 'createdDate' },
		],
	},
	'case-files-packet': {
		sortColumn: 'createdDate',
		sortOrder: 'desc',
		columns: [
			{ field: 'number' },
			{ field: 'files' },
			{ field: 'packetId' },
			{ field: 'description' },
			{ field: 'createdBy' },
			{ field: 'createdDate' },
			{ field: 'contentSearchable' },
			{ field: 'fileShared' },
		],
	},
	'case-files-custom-packet': {
		sortColumn: 'createdDate',
		sortOrder: 'desc',
		columns: [
			{ field: 'number' },
			{ field: 'files' },
			{ field: 'description' },
			{ field: 'createdBy' },
			{ field: 'createdDate' },
			{ field: 'contentSearchable' },
			{ field: 'fileShared' },
		],
	},
	'case-capture-files': {
		sortColumn: 'createdDate',
		sortOrder: 'desc',
		columns: [
			{ field: 'files' },
			{ field: 'createdBy' },
			{ field: 'createdDate' },
		],
	},
	'todo-files': {
		sortColumn: 'createdDate',
		sortOrder: 'desc',
		columns: [
			{ field: 'files' },
			{ field: 'createdBy' },
			{ field: 'createdDate' },
			{ field: 'contentSearchable' },
		],
	},
	'note-files': {
		sortColumn: 'createdDate',
		sortOrder: 'desc',
		columns: [
			{ field: 'files' },
			{ field: 'createdBy' },
			{ field: 'createdDate' },
			{ field: 'contentSearchable' },
		],
	},
	'advanced-search-result-files': {
		sortColumn: 'createdDate',
		sortOrder: 'desc',
		columns: [
			{ field: 'childNumber' },
			{ field: 'files' },
			{ field: 'description' },
			{ field: 'createdBy' },
			{ field: 'createdDate' },
			{ field: 'contentSearchable' },
		],
	},
	'search-result-files-schedule-purge': {
		sortColumn: 'createdDate',
		sortOrder: 'desc',
		columns: [
			{ field: 'childNumber' },
			{ field: 'files' },
			{ field: 'description' },
			{ field: 'createdBy' },
			{ field: 'createdDate' },
			{ field: 'contentSearchable' },
		],
	},
	'search-result-files-purge': {
		sortColumn: 'createdDate',
		sortOrder: 'desc',
		columns: [
			{ field: 'childNumber' },
			{ field: 'files' },
			{ field: 'description' },
			{ field: 'createdBy' },
			{ field: 'createdDate' },
			{ field: 'contentSearchable' },
			{ field: 'pendingPurgeDate' },
		],
	},
	'file-selector': {
		sortColumn: 'createdDate',
		sortOrder: 'desc',
		columns: [
			{ field: 'childNumber'},
			{ field: 'files' },
			{ field: 'description' },
			{ field: 'createdBy' },
			{ field: 'createdDate' },
		],
	},
};
