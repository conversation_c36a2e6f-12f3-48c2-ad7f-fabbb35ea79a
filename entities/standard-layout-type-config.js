const standardLinkedListConfig = require('./standard-linked-list-config');
const extend = require('./extend.js');

module.exports = extend(standardLinkedListConfig, {
	db: 'default',
	search: false,
	allowAdvancedSearch: false,
	dataExport: false,
	customExportColumns: [
		'rootId__originalId',
		'parentId__originalId',
		'previousId__originalId',
		'nextId__originalId',
		'displayRuleGroupId__originalId',
	],
	report: false,
	includeInElementCount: false,
	validation: {
		mandatory$: [
			'layoutType',
		],
	},
	grids: {
		'main-layouts': {
			sortColumn: 'caption',
			sortOrder: 'desc',
			columns: [
				{ field: 'caption' },
				{ field: 'status' },
				{ field: 'entityType' },
				{ field: 'hideOnIntake'},
			],
			defaultDynamicDataFilters: ['status', 'createdDate', 'lastUpdatedDate', 'entityType'],
		},
	},
	historyNav: [{
		from: [],
		to: '/settings/forms/custom-forms',
	}],
	fields: [
		{
			field: 'rootId',
			type: 'id',
			caption: 'Root Id',
			kind: 'hidden-editable',
			dbIndex: true,
			dbIndexType: 'hash',
		},
		{
			field: 'parentId',
			type: 'id',
			caption: 'Parent Id',
			kind: 'hidden-editable',
		},
		{
			field: 'layoutType',
			type: 'textbox',
			caption: 'Layout Type',
			kind: 'editable',
		},
		{
			field: 'caption',
			type: 'textbox',
			caption: 'Caption',
			kind: 'editable',
			typeOptions: {
				charMaxTextbox: 60,
				placeholder: true,
			},
		},
		{
			field: 'helpText',
			caption: 'Help Text',
			type: 'textbox',
			translateData: true,
			kind: 'editable',
		},
		{
			field: 'captionPlural',
			type: 'textbox',
			caption: 'Caption Plural',
			kind: 'editable',
			typeOptions: {
				charMaxTextbox: 62,
				placeholder: true,
			},
		},
		{
			field: 'fieldId',
			type: 'id',
			caption: 'Field Id',
			kind: 'hidden-editable',
			dbIndex: true,
			dbIndexType: 'hash',
		},
		{
			field: 'dynamicEntityId',
			type: 'id',
			caption: 'Dynamic Entity id',
			kind: 'hidden-editable',
		},
		{
			field: 'staticEntityId',
			type: 'entityPicklist',
			caption: 'Static Entity id',
			kind: 'hidden-editable',
		},
		{
			field: 'entityName',
			type: 'textbox',
			caption: 'Entity Name',
			kind: 'system',
		},
		{
			field: 'name',
			type: 'textbox',
			caption: 'Name',
			kind: 'hidden-editable',
		},
		{
			field: 'displayRuleGroupId',
			type: 'id',
			caption: 'Display Rule Group Id',
			kind: 'hidden-editable',
			excludeFromSaveAndCopy: true,
		},
		{
			field: 'status',
			type: 'picklist',
			caption: 'Status',
			typeOptions: {
				picklistName: 'layout_statuses',
			},
			kind: 'editable',
			excludeFromRedact: true,
			excludeFromSaveAndCopy: true,
			cellTemplate(fs) {
				return fs.readFileSync(
					`${__dirname}/../public/templates/cell-templates/layout-status-cell-tmpl.dust`,
					'utf8',
				);
			},
		},
		{
			field: 'locked',
			type: 'yesno',
			caption: 'Locked',
			kind: 'hidden',
		},
		{
			field: 'aboveContextHint',
			type: 'textbox',
			caption: 'Above Context Hint',
			kind: 'hidden',
		},
		{
			field: 'belowContextHint',
			type: 'textbox',
			caption: 'Below Context Hint',
			kind: 'hidden',
		},
		{
			field: 'hasElementsAbove',
			type: 'yesno',
			caption: 'Has Elements Above',
			kind: 'hidden',
		},
		{
			field: 'hasElementsBelow',
			type: 'yesno',
			caption: 'Has Elements Below',
			kind: 'hidden',
		},
		{
			field: 'hideOnIntake',
			type: 'toggle-inverted',
			caption: 'Show on Intake',
			kind: 'editable',
			default: true,
		},
		{
			field: 'allowShowOnIntake',
			type: 'yesno',
			caption: 'Allow Show on Intake',
			kind: 'hidden',
		},
		{
			field: 'sequence',
			type: 'number',
			caption: 'Sequence',
			kind: 'editable',
		},
		{
			field: 'hasStaticAndDynamicTabs',
			type: 'yesno',
			caption: 'Has Static and Dynamic Tabs',
			kind: 'hidden',
		},
		{
			field: 'originalId',
			type: 'id',
			caption: 'Original Id',
			kind: 'hidden',
		},
		{
			field: 'informationBoxType',
			type: 'picklist',
			typeOptions: {
				picklistName: 'information_box_types',
			},
			caption: 'Type',
			kind: 'editable',
		},
		{
			field: 'showOnPortal',
			type: 'toggle',
			caption: 'Show on Portal',
			kind: 'editable',
		},
		{
			field: 'allowShowOnPortal',
			type: 'yesno',
			caption: 'Allow Show on Portal',
			kind: 'hidden',
		},
		{
			field: 'showOnHotline',
			type: 'toggle',
			caption: 'Show on Hotline',
			kind: 'editable',
		},
		{
			field: 'allowShowOnHotline',
			type: 'yesno',
			caption: 'Allow Show on Hotline',
			kind: 'hidden',
			features: ['displayFormsOnHotline'],
		},
		{
			field: 'internalOnly',
			caption: 'Internal Facing',
			type: 'toggle',
			kind: 'editable',
			features: ['nonUserCollaboration'],
		},
		{
			field: 'entityType',
			type: 'picklistSelectize',
			kind: 'system',
			caption: 'Form Type',
			typeOptions: {
				picklistName: 'entity_types',
			},
		},
		{
			field: 'dataForm',
			type: 'entityPicklist',
			caption: 'Data Form',
			kind: 'editable',
			typeOptions: {
				filter: 'dataFormFilter',
				valueField: 'canon',
				entityCategory: 1,
				canCreate: false,
				inactiveCanon: 'unknown_data_form',
			},
		},
		{
			field: 'mappingMode',
			type: 'radio',
			caption: 'Mapping method',
			typeOptions: {
				picklistName: 'mapping_methods',
				orientation: 'vertical',
			},
			kind: 'editable',
			features: ['dataLookupSection'],
			default: 'simple',
		},
		{
			field: 'iselExpression',
			caption: 'ISEL Expression',
			type: 'expression',
			typeOptions: {
				customContextProperty: {
					result: {},
					lookupFields: [],
				},
			},
			kind: 'editable',
			features: ['dataLookupSection'],
		},
		{
			field: 'initMapping',
			type: 'toggle',
			caption: 'Automatically detect mappings',
			kind: 'editable',
			features: ['dataLookupSection'],
		},
		{
			field: 'fieldGroup',
			type: 'code',
			caption: 'Field Group',
			kind: 'system',
		},
		{
			field: 'preExistingLayoutNode',
			type: 'yesno',
			caption: 'Pre-Existing Layout Node',
			kind: 'hidden',
		},
		{
			field: 'gridEntity',
			type: 'entityPicklist',
			caption: 'Entity',
			kind: 'editable',
			typeOptions: {
				valueField: 'canon',
				entityCategory: 2,
				filter: 'gridEntityFilter',
				canCreate: false,
				renderWithIcons: true,
			},
		},
		{
			field: 'fieldGroupSection',
			type: 'id',
			caption: 'Field Group Section',
			kind: 'hidden-editable',
		},
		{
			field: 'features',
			type: 'textbox[]',
			caption: 'Features',
			kind: 'hidden-searchable',
		},
		{
			field: 'localName',
			type: 'code',
			caption: 'Layout System Local Name',
			kind: 'system',
			dbIndex: true,
			dbIndexType: 'hash',
		},
		{
			field: 'path',
			type: 'code',
			caption: 'Layout System Path',
			kind: 'system',
			dbIndex: true,
			dbIndexType: 'hash',
		},
		{
			field: 'mappingData',
			type: 'jsonb',
			caption: 'Mapping Data',
			kind: 'hidden-editable',
			features: ['dataLookupSection'],
		},
	],
});
