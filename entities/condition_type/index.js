const extend = require('../extend.js');
const standardConfig = require('../standard-config.js');

module.exports = extend(standardConfig, {
	db: 'default',
	table: 'sys_condition_type',
	entity: {
		base: 'sys',
		name: 'conditionType',
	},
	search: false,
	parents: [
		{
			entity: {
				base: 'sys',
				name: 'condition',
			},
			field: 'conditionId',
		},
	],
	acl: require('./acl.js'),
	validation: require('./validation'),
	caption: 'Condition Type',
	captionPlural: 'Condition Types',
	addCaption: 'Add Condition Type',
	newCaption: 'New Condition Type',
	importTransformer: 'conditionType',
	copyPostAction: 'conditionType',
	fields: [
		{
			field: 'type',
			type: 'textbox',
			caption: 'Condition Type',
			kind: 'system',
		},
		{
			field: 'conditionId',
			type: 'id',
			caption: 'Condition Id',
			kind: 'system',
		},
		{
			field: 'typeDataId',
			type: 'id',
			caption: 'Condition Type Id',
			kind: 'system',
		},
	],
});
