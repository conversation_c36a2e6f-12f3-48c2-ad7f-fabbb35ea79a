const permHelper = require('../../lib/core/permission-helper.js');

module.exports = permHelper.initialize()
	.filterMarkForPurge()
	.filterPurge()
	.required({
		name: 'Save/Delete Hard Locked Condition Type',
		roles: ['save_delete_hard_locked_condition_type'],
		actions: ['save_new', 'save_existing', 'remove'],
		conditions: [{
			attributes: {
				hardLocked: true,
			},
		}],
	})
	.required({
		name: 'Inherit Condition acl',
		roles: ['bypass_inherited_acl'],
		control: 'required',
		actions: ['load', 'list', 'save_new', 'save_existing', 'remove'],
		conditions: ['sys/condition::{conditionId}'],
	})
	.value();
