const extend = require('../extend.js');
const standardConfig = require('../standard-config.js');

module.exports = extend(standardConfig, {
	db: 'default',
	table: 'sys_translation',
	entity: {
		base: 'sys',
		name: 'translation',
	},
	caption: 'Translation',
	captionPlural: 'Translations',
	addCaption: 'Add Translation',
	newCaption: 'New Translation',
	gridDescriptorField: 'description',
	acl: require('./acl.js'),
	aclHierarchy: require('./acl-hierarchy.js'),
	grids: require('./grids.js'),
	configurationExport: true,
	configurationImport: true,
	importTransformer: 'translation',
	audit: require('./audit.js'),
	allowMetaDataHardDelete: true,
	model() {
		return require('../../public/models/translation-model.js');
	},
	collection() {
		return require('../../public/collections/translations-collection.js');
	},
	fields: [
		{
			field: 'compositeId',
			type: 'textbox',
			kind: 'hidden',
			caption: 'Composite Id',
			dbIndex: true,
			dbIndexType: 'hash',
		},
		{
			field: 'locale',
			type: 'picklist',
			typeOptions: {
				picklistName: 'supported_languages',
				filter: 'existingLanguagesFilter',
			},
			kind: 'editable',
			dbIndex: true,
			caption: 'Language',
		},
		{
			field: 'key',
			type: 'textbox',
			kind: 'editable',
			caption: 'Key',
			dbIndex: true,
		},
		{
			field: 'subgroupName',
			type: 'textbox',
			caption: 'Subgroup',
			kind: 'editable',
			dbIndex: true,
		},
		{
			field: 'groupName',
			type: 'textbox',
			caption: 'Group',
			kind: 'editable',
			dbIndex: true,
		},
		{
			field: 'value',
			type: 'textbox',
			kind: 'editable',
			caption: 'Translation',
			minGridWidth: 400,
			cellTemplate(fs) {
				return fs.readFileSync(
					`${__dirname}/cell-templates/translation-value-cell-tmpl.dust`,
					'utf8',
				);
			},
		},
		{
			field: 'description',
			type: 'textbox',
			kind: 'system',
			caption: 'Description',
		},
		{
			field: 'features',
			type: 'textbox[]',
			caption: 'Features',
			kind: 'hidden-searchable',
		},
	],
	cacheBustGroups: require('./cache-bust-groups.js'),
});
