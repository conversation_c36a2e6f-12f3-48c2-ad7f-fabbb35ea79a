module.exports = [
	{
		cacheKey: 'permCaptionTranslations',
		condition: data => data.subgroupName === 'captions' && data.groupName === 'sys/permission',
		cacheBustEventHandler: require('./cacheBustEventHandlers/permission-translation-update-handler.js'),
		query: {
			groupName: 'sys/permission',
			subgroupName: 'captions',
		},
	},
	{
		cacheKey: 'permTooltipTranslations',
		condition: data => data.subgroupName === 'tooltip' && data.groupName === 'sys/permission',
		cacheBustEventHandler: require('./cacheBustEventHandlers/permission-tooltip-translation-update-handler.js'),
		query: {
			groupName: 'sys/permission',
			subgroupName: 'tooltip',
		},
	},
	{
		cacheKey: 'dynamicEntityGeneralTranslationsUpdate',
		condition: data => !!data.id && data.subgroupName === 'general',
		cacheBustEventHandler: require('./cacheBustEventHandlers/dynamic-entity-general-translations-update-handler.js'),
		query: {
			subgroupName: 'general',
		},
	},
	{
		cacheKey: 'dynamicEntityActionTranslationsUpdate',
		condition: data => !!data.id && data.subgroupName === 'actions',
		cacheBustEventHandler: require('./cacheBustEventHandlers/dynamic-entity-action-translations-update-handler.js'),
		query: {
			subgroupName: 'actions',
		},
	},
	{
		cacheKey: 'addRemovePicklistTranslations',
		condition: data => ['picklist_type', 'picklist'].includes(data.groupName),
		triggerOnSaveRemove: false,
		cacheBustEventHandler: require('./cacheBustEventHandlers/picklist-translations-update-handler.js'),
		query: null,
	},
	{
		cacheKey: 'notificationTranslations',
		condition: data => data.groupName === 'sys/notification',
		triggerOnSaveRemove: false,
		cacheBustEventHandler: require('./cacheBustEventHandlers/notification-translations-update-handler.js'),
	},
	{
		cacheKey: 'relationshipTypeTranslations',
		condition: data => data.groupName === 'sys/relationship_type' && data.subgroupName === 'verbs',
		triggerOnSaveRemove: false,
		cacheBustEventHandler: require('./cacheBustEventHandlers/relationship-type-translations-update-handler.js'),
	},
];
