/* global $appData */
const _ = require('lodash');
const sharedUtils = require('../../../shared/utils.js')();

module.exports = {
	updateDynamicEntitySubgroupTranslations(data, subgroupName) {
		const dynamicEntityCanons = _.map(_.cloneDeep($appData.dynamicEntities), 'canon');
		const dynamicEntitySubgroupTranslations = _.filter(data,
			record => _.includes(dynamicEntityCanons, record.groupName));
		const translationsMap = sharedUtils
			.convertTranslationArrayToObject(dynamicEntitySubgroupTranslations);
		_.each(dynamicEntityCanons, (canon) => {
			const subgroupTranslationsForCanon = _.get(translationsMap, `${canon}.${subgroupName}`, {});
			const canUpdateTranslations = !_.isEmpty($appData.translations[canon]
				&& $appData.translations[canon][subgroupName])
				&& !_.isEmpty(subgroupTranslationsForCanon);
			if (canUpdateTranslations) {
				sharedUtils.updateObject($appData.translations[canon][subgroupName],
					subgroupTranslationsForCanon);
			}
		});
	},
};
