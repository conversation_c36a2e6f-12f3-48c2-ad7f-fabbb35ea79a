/* global $appData */
const _ = require('lodash');
const sharedUtils = require('../../../shared/utils.js')();

module.exports = function handler(opts) {
	const { patchData, dataFetched } = opts;
	// When SSE is enabled, patchData is available, use patch data to update $appData.
	// note patch data can contain deleted data
	if (!_.isEmpty(patchData)) {
		const { locale } = $appData.user;

		// Values are not translated automatically by the system in other languages,
		// however they will eventually get created by the system so we can make this
		// assumption of assigning user's locale in appData over heavy backend computation
		// for these translations.
		const localeRecords = _.map(patchData, data => ({
			...data,
			locale,
		}));
		const convertedLocaleRecords = sharedUtils.convertTranslationRecords(localeRecords);
		const target = $appData.translations;

		_.forEach(convertedLocaleRecords, (value, propertyPath) => {
			if (_.isNil(value)) {
				_.unset(target, propertyPath);
			} else {
				_.set(target, propertyPath, value);
			}
		});
	} else if (!_.isEmpty(dataFetched)) {
		// When SSE is not enabled, dataFetched is available, use fetched data to update $appData.
		// note fetched data doesn't contain deleted ones,
		// so we use a different way to update $appData.translations
		const translationsMap = sharedUtils.convertTranslationArrayToObject(dataFetched);
		_.forEach(translationsMap, (groupData, groupName) => {
			sharedUtils.updateObject($appData.translations[groupName], groupData);
		});
	}
};
