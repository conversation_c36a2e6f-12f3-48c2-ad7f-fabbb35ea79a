/* global $appData */

const _ = require('lodash');
const sharedUtils = require('../../../shared/utils.js')();

module.exports = function notificationTranslationsUpdateHandler(opts) {
	const { patchData = [] } = opts;
	// Since the back-end isn't aware of the locale of the translations being displayed, we need to
	// check here to make sure that we only update translations that match the current locale
	const translationsForCurrentLocale = _.filter(patchData, { locale: $appData.locale });
	if (_.isEmpty(translationsForCurrentLocale)) return;
	const translationsObj = sharedUtils.convertTranslationArrayToObject(translationsForCurrentLocale);
	_.merge($appData.translations, translationsObj);
};
