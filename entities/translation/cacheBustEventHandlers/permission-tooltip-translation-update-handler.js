/* global $appData */
const _ = require('lodash');
const sharedUtils = require('../../../shared/utils.js')();

module.exports = function permissionTooltipTranslationUpdateHandler(opts) {
	const {dataFetched} = opts;
	if (!_.isEmpty(dataFetched)) {
		const translationsDirectory = sharedUtils.convertTranslationArrayToObject(dataFetched);
		sharedUtils.updateObject($appData.translations['sys/permission'].tooltip,
			translationsDirectory['sys/permission'].tooltip);
	}
};
