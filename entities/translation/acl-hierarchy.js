const permHelper = require('../../lib/core/permission-helper.js');

module.exports = [{
	type: 'group',
	parentPermission: 'view_data_settings',
	caption: 'Translations',
	permission: 'translation',
	options: [
		{
			caption: 'View',
			sequence: 2,
			disabled: true,
			permission: 'view_translation',
		},
		{
			caption: 'Create',
			sequence: 1,
			disabled: true,
			permission: 'create_translation',
		},
		{
			type: 'group',
			permission: permHelper.getEditGroupPermissionCode('translation'),
			caption: 'Edit',
			sequence: 3,
			options: [{
				permission: 'edit_translation',
				caption: 'Save',
				tooltip: 'Edit translations',
			}],
		},
		{
			caption: 'Remove',
			sequence: 4,
			disabled: true,
			permission: 'remove_translation',
		},
	],
}];
