const extend = require('../extend.js');
const standardFilterChildConfig = require('../standard-filter-child-config');

const parentField = 'parentId';
const parentEntity = {
	base: 'sys',
	name: 'gridDataFilter',
};

module.exports = extend(standardFilterChildConfig, {
	table: 'sys_grid_data_filter_child',
	entity: {
		base: 'sys',
		name: 'gridDataFilterChild',
	},
	parents: [
		{
			entity: parentEntity,
			field: parentField,
		},
	],
	caption: 'Grid Data Filter Item',
	captionPlural: 'Grid Data Filter Items',
	addCaption: 'Add Grid Data Filter Item',
	newCaption: 'New Grid Data Filter Item',
	// TODO: acl
	// TODO: rules
	validation: require('./validation'),
	fields: [{
		field: 'selectedFilterOptions',
		type: 'textbox[]',
		caption: 'Selected Filter Options',
		kind: 'editable',
	}],
});
