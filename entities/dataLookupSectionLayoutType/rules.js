module.exports = {
	isHttpRequestMethod(data){
		return data.lookupMethod === 'http_request';
	},
	isPutPostMethod(data){
		return data.httpMethod === 'put' || data.httpMethod === 'post';
	},
	isBasicAuthentication(data){
		return data.authenticationMethod === 'basic';
	},
	isExpressionMode(data) {
		return data.mappingMode === 'expression';
	},
	isOAuthAuthentication(data) {
		return data.authenticationMethod === 'oAuth';
	},
	isSimpleMode(data) {
		return data.mappingMode === 'simple';
	},
};
