module.exports = {
	mandatory$: [
		'rootId',
		'parentId',
		'caption',
		'lookupMethod',
	],
	pattern$: [{
		fields: ['caption'],
		pattern: '[a-zA-Z0-9]',
		code: 'atLeastOneLetterOrNumber$',
	}],
	dependentMandatory$: [
		{
			condition: 'isHttpRequestMethod',
			fields: [
				'endpoint',
				'httpMethod',
				'authenticationMethod',
			],
		},
		{
			condition: 'isBasicAuthentication',
			fields: [
				'username',
				'password',
			],
		},
		{
			condition: 'isExpressionMode',
			fields: ['iselExpression'],
		},
		{
			condition: 'isOAuthAuthentication',
			fields: [
				'grantType',
				'accessTokenEndpoint',
				'clientId',
				'clientSecret',
				'clientAuthenticationOption',
			],
		},
	],
};
