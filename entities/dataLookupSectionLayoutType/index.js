const _ = require('lodash');
const extend = require('../extend.js');
const layoutEnt = require('../layout/index.js');

module.exports = extend(layoutEnt, {
	createTable: false,
	reuseEsIndex: {
		index: 'sys_layout',
		typeField: 'layoutType',
	},
	layoutType: 'data-lookup-section',
	entity: {
		base: 'sys',
		name: 'data_lookup_section_layout_type',
	},
	caption: 'Data Lookup Section',
	captionPlural: 'Data Lookup Sections',
	addCaption: 'Add Data Lookup Section',
	newCaption: 'New Data Lookup Section',
	importTransformer: 'data_lookup_section_layout_type',
	exportTransformer: 'data_lookup_section_layout_type',
	customDependencies: entityService => [
		..._.map(entityService.getRootLayoutEntities(), 'entityCanon'), 'isight/entity', 'isight/dynamic_entity',
	],
	validation: require('./validation.js'),
	model() {
		return require('../../public/models/layout-data-lookup-section-model.js');
	},
	rules: require('./rules.js'),
});
