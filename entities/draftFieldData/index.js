const extend = require('../extend.js');
const standardConfig = require('../standard-config.js');

module.exports = extend(standardConfig, {
	db: 'default',
	table: 'sys_draft_field_data',
	entity: {
		base: 'sys',
		name: 'draftFieldData',
	},
	search: false,
	allowAdvancedSearch: false,
	allowQuickSearch: false,
	dataExport: false,
	customForm: false,
	report: false,
	excludeFromAggregation: true,
	caption: 'Draft Field Data',
	captionPlural: 'Draft Field Data',
	addCaption: 'Add Draft Field Data',
	newCaption: 'New Draft Field Data',
	validation: {
		mandatory$: ['parentId', 'fieldId', 'status'],
	},
	fields: [
		{
			field: 'parentId',
			caption: 'Parent Id',
			type: 'id',
			kind: 'system',
		},
		{
			field: 'fieldId',
			caption: 'Field Id',
			type: 'id',
			kind: 'system',
		},
		{
			field: 'content',
			caption: 'Content',
			type: 'texteditor',
			kind: 'system',
		},
		{
			field: 'status',
			caption: 'Status',
			type: 'textbox',
			kind: 'system',
		},
		{
			field: 'metaData',
			type: 'json',
			kind: 'system',
			caption: 'Meta Data',
		},
	],
});
