const _ = require('lodash');
const permHelper = require('../../lib/core/permission-helper.js');
const linkFieldJoinMappings = require('../../field-types/link-entity-id/joinMapping.js');

const caseMapping = _.find(linkFieldJoinMappings.dbMappings, { table: 'sys_case' });
const caseJoins = [
	...caseMapping.fields || [],
	'flagsData',
];
const conditionForOtherCaseType = {
	attributes: {
		'!type': ['Manual', 'Suggested', 'Automatic'],
	},
};

module.exports = permHelper.initialize()
	.required({
		name: 'View Manual Links',
		roles: ['view_manual_link'],
		actions: ['load', 'list', 'save_existing', 'remove'],
		conditions: [
			{
				attributes: {
					type: ['Manual'],
				},
			},
		],
	})
	.required({
		name: 'View Suggested Links',
		roles: ['view_suggested_link'],
		actions: ['load', 'list', 'save_existing', 'remove'],
		conditions: [{
			attributes: {
				type: 'Suggested',
			},
		}],
	})
	.required({
		name: 'View Automatic Links',
		roles: ['view_automatic_link'],
		actions: ['load', 'list', 'save_existing', 'remove'],
		conditions: [{
			attributes: {
				type: 'Automatic',
			},
		}],
	})
	.required({
		name: 'View Other Links',
		roles: ['view_other_link'],
		actions: ['load', 'list', 'save_existing', 'remove'],
		conditions: [conditionForOtherCaseType],
	})
	.required({
		name: 'Create Manual Links',
		roles: ['create_manual_link'],
		actions: ['save_new', 'save_existing'],
		conditions: [
			{
				attributes: {
					id: null,
					type: ['Manual'],
				},
			},
		],
	})
	.required({
		name: 'Create Suggested Links',
		roles: ['create_suggested_link'],
		actions: ['save_new', 'save_existing'],
		conditions: [{
			attributes: {
				id: null,
				type: 'Suggested',
			},
		}],
	})
	.required({
		name: 'Create Suggested Links',
		roles: ['create_suggested_link'],
		actions: ['save_new', 'save_existing'],
		conditions: [{
			attributes: {
				'!id': null,
				type: 'Suggested',
				status: 'Undecided',
			},
		}],
	})
	.required({
		name: 'Create Other Links',
		roles: ['create_other_link'],
		actions: ['save_new', 'save_existing'],
		conditions: [conditionForOtherCaseType, {
			attributes: {
				id: null,
			},
		}],
	})
	.required({
		name: 'Edit Manual Links',
		roles: ['edit_manual_link'],
		actions: ['save_new', 'save_existing'],
		conditions: [
			{
				attributes: {
					'!id': null,
					type: ['Manual'],
				},
			},
		],
	})
	.required({
		name: 'Edit Suggested Links',
		roles: ['edit_suggested_link'],
		actions: ['save_new', 'save_existing'],
		conditions: [{
			attributes: {
				'!id': null,
				type: 'Suggested',
			},
		}],
	})
	.required({
		name: 'Edit Other Links',
		roles: ['edit_other_link'],
		actions: ['save_new', 'save_existing'],
		conditions: [conditionForOtherCaseType, {
			attributes: {
				'!id': null,
			},
		}],
	})
	.required({
		name: 'Remove Manual Link',
		roles: ['remove_manual_link'],
		actions: ['remove'],
		conditions: [
			{
				attributes: {
					type: ['Manual'],
				},
			},
		],
	})
	.required({
		name: 'Remove Suggested Link',
		roles: ['remove_suggested_link'],
		actions: ['remove'],
		conditions: [{
			attributes: {
				type: 'Suggested',
			},
		}],
	})
	.required({
		name: 'Remove Other Link',
		roles: ['remove_other_link'],
		actions: ['remove'],
		conditions: [conditionForOtherCaseType],
	})
	// Case Inherit when `viewRelatedCasesWithoutAccess` is disabled
	.required({
		name: 'Inherit Linked Case 1 ACL',
		roles: ['bypass_inherited_acl'],
		features: ['!viewRelatedCasesWithoutAccess'],
		actions: ['load', 'list', 'save_new', 'save_existing', 'remove'],
		conditions: [{
			attributes: {
				entity1Type: 'sys/case',
			},
		}, 'sys/case::{entity1Id}::load'],
	})
	.required({
		name: 'Inherit Linked Case 2 ACL',
		roles: ['bypass_inherited_acl'],
		features: ['!viewRelatedCasesWithoutAccess'],
		actions: ['load', 'list', 'save_new', 'save_existing', 'remove'],
		conditions: [{
			attributes: {
				entity2Type: 'sys/case',
			},
		}, 'sys/case::{entity2Id}::load'],
	})
	// Case inherit when `viewRelatedCasesWithoutAccess` is enabled
	// If both records are not cases then you must have Case access
	.required({
		name: 'Inherit Case 1 ACL',
		roles: ['bypass_inherited_acl'],
		features: ['viewRelatedCasesWithoutAccess'],
		actions: ['load', 'list', 'save_new', 'save_existing', 'remove'],
		conditions: [{
			attributes: {
				entity1Type: 'sys/case',
				'!entity2Type': 'sys/case',
			},
		}, 'sys/case::{entity1Id}::load'],
	})
	.required({
		name: 'Inherit Case 2 ACL',
		roles: ['bypass_inherited_acl'],
		features: ['viewRelatedCasesWithoutAccess'],
		actions: ['load', 'list', 'save_new', 'save_existing', 'remove'],
		conditions: [{
			attributes: {
				entity2Type: 'sys/case',
				'!entity1Type': 'sys/case',
			},
		}, 'sys/case::{entity2Id}::load'],
	})
	// Reject all actions on Case <-> Case links where users have access to neither case.
	.required({
		name: 'View & Edit Case Links With No Case Access',
		roles: ['bypass_inherited_acl'],
		features: ['viewRelatedCasesWithoutAccess'],
		actions: ['load', 'list', 'save_new', 'save_existing', 'remove'],
		conditions: [
			{
				attributes: {
					entity2Type: 'sys/case',
					entity1Type: 'sys/case',
				},
			},
			[
				'sys/case::{entity1Id}::load',
				'sys/case::{entity2Id}::load',
			],
		],
	})
	// Users with partial access perms may view suggested Case <-> Case links while
	// only having access to one case, regardless of their status.
	.required({
		name: 'View Suggested Case Links With Partial Access',
		roles: ['bypass_inherited_acl', 'view_suggested_case_links_with_partial_access'],
		features: ['viewRelatedCasesWithoutAccess'],
		actions: ['load', 'list'],
		conditions: [
			{
				attributes: {
					entity2Type: 'sys/case',
					entity1Type: 'sys/case',
					type: 'Suggested',
				},
			},
			{
				or: [
					'sys/case::{entity1Id}::load',
					'sys/case::{entity2Id}::load',
				],
			},
		],
	})
	// Since users may view Case <-> Case links while only having access to one case,
	// We must ensure any sensitive joins to the no-access case are filtered.
	// We only allow joins to the the `caseNumber` & `owner` of the no-access case.
	.filter({
		name: 'Filter Case 1 Joins',
		roles: ['bypass_inherited_acl'],
		features: ['viewRelatedCasesWithoutAccess'],
		actions: ['load', 'list'],
		conditions: [
			{
				attributes: {
					entity1Type: 'sys/case',
					entity2Type: 'sys/case',
				},
			},
			'sys/case::{entity1Id}::load',
		],
		filters: caseJoins.reduce((filters, field) => {
			if (field !== 'owner' && field !== 'caseNumber') {
				filters[`entity1Id__${field}`] = false;
			}
			return filters;
		}, {}),
	})
	.filter({
		name: 'Filter Case 2 Joins',
		roles: ['bypass_inherited_acl'],
		features: ['viewRelatedCasesWithoutAccess'],
		actions: ['load', 'list'],
		conditions: [
			{
				attributes: {
					entity1Type: 'sys/case',
					entity2Type: 'sys/case',
				},
			},
			'sys/case::{entity2Id}::load',
		],
		filters: caseJoins.reduce((filters, field) => {
			if (field !== 'owner' && field !== 'caseNumber') {
				filters[`entity2Id__${field}`] = false;
			}
			return filters;
		}, {}),
	})
	// Reject all actions on Non-suggested links when the user has partial access
	.required({
		name: 'View Non-Suggested Case Links With Partial Access',
		roles: ['bypass_inherited_acl'],
		features: ['viewRelatedCasesWithoutAccess'],
		actions: ['load', 'list', 'save_existing', 'save_new', 'remove'],
		conditions: [
			{
				attributes: {
					entity2Type: 'sys/case',
					entity1Type: 'sys/case',
					'!type': 'Suggested',
				},
			},
			{
				or: [
					'sys/case::{entity1Id}::load',
					'sys/case::{entity2Id}::load',
				],
			},
		],
	})
	// Users can save undecided suggested links between two cases with access to only
	// one of the two cases.
	.required({
		name: 'Save Undecided Suggested Case Links With Partial Access',
		roles: ['bypass_inherited_acl', 'view_suggested_case_links_with_partial_access'],
		features: ['viewRelatedCasesWithoutAccess'],
		actions: ['save_existing', 'save_new'],
		conditions: [
			{
				attributes: {
					entity2Type: 'sys/case',
					entity1Type: 'sys/case',
					status: 'Undecided',
					type: 'Suggested',
				},
			},
			{
				or: [
					'sys/case::{entity1Id}::load',
					'sys/case::{entity2Id}::load',
				],
			},
		],
	})
	// Prevent partial access users from being able to remove undecided links
	.required({
		name: 'Remove Suggested Case Links With Partial Access',
		roles: ['bypass_inherited_acl'],
		features: ['viewRelatedCasesWithoutAccess'],
		actions: ['remove'],
		conditions: [
			{
				attributes: {
					entity2Type: 'sys/case',
					entity1Type: 'sys/case',
					type: 'Suggested',
				},
			},
			{
				or: [
					'sys/case::{entity1Id}::load',
					'sys/case::{entity2Id}::load',
				],
			},
		],
	})
	// Prevent partial access users from updating suggested links once they are decided
	.required({
		name: 'Save Decided Case Links With Partial Access',
		roles: ['bypass_inherited_acl'],
		features: ['viewRelatedCasesWithoutAccess'],
		actions: ['save_existing', 'save_new'],
		conditions: [
			{
				attributes: {
					entity2Type: 'sys/case',
					entity1Type: 'sys/case',
					type: 'Suggested',
					'!status': 'Undecided',
				},
			},
			{
				or: [
					'sys/case::{entity1Id}::load',
					'sys/case::{entity2Id}::load',
				],
			},
		],
	})
	.required({
		name: 'Inherit Linked Profile 1 ACL',
		roles: ['bypass_inherited_acl'],
		actions: ['load', 'list', 'save_new', 'save_existing', 'remove'],
		conditions: [{
			attributes: {
				entity1Type: 'sys/person',
			},
		}, 'sys/person::{entity1Id}::load'],
	})
	.required({
		name: 'Inherit Linked Profile 2 ACL',
		roles: ['bypass_inherited_acl'],
		actions: ['load', 'list', 'save_new', 'save_existing', 'remove'],
		conditions: [{
			attributes: {
				entity2Type: 'sys/person',
			},
		}, 'sys/person::{entity2Id}::load'],
	})
	.requireRecordLinkingInheritance()
	.requireParentRecordLinkingInheritance()
	.requireRootRecordLinkingInheritance()
	.filter({
		name: 'Can modify entity on existing link',
		roles: ['link_modify_entity'],
		actions: ['save_new', 'save_existing'],
		filters: {
			'entity1Id': false,
			'entity2Id': false,
			'type': false,
		},
		conditions: [{
			attributes: {
				'!id': null,
			},
		}],
	})
	.value();
