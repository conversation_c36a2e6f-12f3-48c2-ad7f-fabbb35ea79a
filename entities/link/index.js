var extend = require('../extend.js');
var standardConfig = require('../standard-config.js');
var esQueryHelper = require('../../shared/es-query-helpers.js');

module.exports = extend(standardConfig, {
	db: 'default',
	table: 'sys_link',
	entity: {
		base: 'sys',
		name: 'link',
	},
	caption: 'Link',
	captionPlural: 'Links',
	addCaption: 'Add Link',
	newCaption: 'New Link',
	importTransformer: 'link',
	gridDescriptorField: 'otherEntityId',
	validation: require('./validation.js'),
	audit: require('./audit.js'),
	acl: require('./acl.js'),
	aclHierarchy: require('./acl-hierarchy.js'),
	grids: require('./grids.js'),
	includeInDataDictionary: true,
	rules: require('./rules.js'),
	dataExport: true,
	report: true,
	reportOptions: {
		joins: [
			{ parent: 'id', child: 'entity_1_id', bracketsBefore: 1 },
			{
				parent: 'id',
				child: 'entity_2_id',
				bracketsAfter: 1,
				whereClauseOperator: 'OR',
			},
			{ parent: 'context_yellowfin_username', child: 'context_yellowfin_username' },
		],
	},
	usageStatistics: require('./usage-statistics.js'),
	excludeFromAggregation: true,
	fields: [
		{
			field: 'latestSnapshotId',
			type: 'id',
			kind: 'hidden',
			caption: 'Snapshot',
			dbIndex: true,
		},
		{
			field: 'entity1Id',
			type: 'linkEntityId',
			kind: 'editable',
			caption: 'Record',
			dbIndex: true,
			typeOptions: {
				relatedEntityField: 'entity1Type',
				includeLinkSummary: true,
			},
		},
		{
			field: 'entity2Id',
			type: 'linkEntityId',
			kind: 'editable',
			caption: 'Linked Record',
			dbIndex: true,
			typeOptions: {
				relatedEntityField: 'entity2Type',
				includeLinkSummary: true,
			},
		},
		{
			field: 'case1Id',
			type: 'case',
			kind: 'custom',
			caption: 'Linked From',
			kindOptions: {
				flags: {
					audit: true,
					schema: false,
					search: false,
					apiWritable: false,
					apiExternalWritable: false,
					computedOnSave: false,
					computedOnRead: false,
					formVisible: false,
					gridVisible: false,
					gridSortable: false,
					searchVisible: false,
					formattedData: false,
					gridExportable: false,
					reportable: false,
				},
			},
		},
		{
			field: 'case2Id',
			type: 'case',
			kind: 'custom',
			caption: 'Linked To',
			kindOptions: {
				flags: {
					audit: true,
					schema: false,
					search: false,
					apiWritable: false,
					apiExternalWritable: false,
					computedOnSave: false,
					computedOnRead: false,
					formVisible: false,
					gridVisible: false,
					gridSortable: false,
					searchVisible: false,
					formattedData: false,
					gridExportable: false,
					reportable: false,
				},
			},
		},
		{
			field: 'entity1Type',
			type: 'entityName',
			kind: 'editable',
			caption: 'Record Type',
			cellTemplate: function(fs){
				return fs.readFileSync(
					__dirname + '/cell-templates/entity-type-cell-tmpl.dust',
					'utf8',
				);
			},
		},
		{
			field: 'entity2Type',
			type: 'entityName',
			kind: 'editable',
			caption: 'Linked Record Type',
			cellTemplate: function(fs){
				return fs.readFileSync(
					__dirname + '/cell-templates/entity-type-cell-tmpl.dust',
					'utf8',
				);
			},
		},
		{
			field: 'linkedBy',
			caption: 'Linked By',
			type: 'user',
			kind: 'system',
		},
		{
			field: 'otherEntityId',
			type: 'linkEntityId',
			kind: 'custom',
			caption: 'Linked Record',
			kindOptions: {
				flags: {
					audit: false,
					schema: false,
					search: false,
					apiWritable: false,
					apiExternalWritable: false,
					computedOnSave: false,
					computedOnRead: false,
					formVisible: true,
					gridVisible: true,
					gridSortable: false,
					searchVisible: false,
					formattedData: false,
					gridExportable: false,
					reportable: false,
				},
			},
			typeOptions: {
				relatedEntityField: 'otherEntityType',
				includeLinkSummary: true,
			},
		},
		{
			field: 'otherCaseType',
			type: 'caseType',
			kind: 'custom',
			caption: 'Case Type',
			dbIndex: true,
			kindOptions: {
				flags: {
					audit: false,
					schema: true,
					search: true,
					apiWritable: false,
					apiExternalWritable: false,
					computedOnSave: false,
					computedOnRead: false,
					formVisible: true,
					gridVisible: true,
					gridSortable: true,
					searchVisible: false,
					formattedData: true,
					gridExportable: false,
					reportable: false,
				},
			},
			typeOptions: {
				relatedEntityField: 'otherEntityId',
			},
		},
		{
			field: 'currentEntityFlags',
			type: 'flags',
			kind: 'custom',
			caption: 'Current Entity Flags',
			kindOptions: {
				flags: {
					audit: false,
					schema: false,
					search: false,
					apiWritable: false,
					apiExternalWritable: false,
					computedOnSave: false,
					computedOnRead: false,
					formVisible: true,
					gridVisible: true,
					gridSortable: false,
					searchVisible: false,
					formattedData: false,
					gridExportable: false,
					reportable: false,
				},
			},
		},
		{
			field: 'otherEntityFlags',
			type: 'flags',
			kind: 'custom',
			caption: 'Flags',
			kindOptions: {
				flags: {
					audit: false,
					schema: false,
					search: false,
					apiWritable: false,
					apiExternalWritable: false,
					computedOnSave: false,
					computedOnRead: false,
					formVisible: true,
					gridVisible: true,
					gridSortable: false,
					searchVisible: false,
					formattedData: false,
					gridExportable: false,
					reportable: false,
				},
			},
		},
		{
			field: 'otherEntityType',
			type: 'code',
			kind: 'custom',
			caption: 'Linked Record Type',
			kindOptions: {
				flags: {
					audit: false,
					schema: false,
					search: false,
					apiWritable: false,
					apiExternalWritable: false,
					computedOnSave: false,
					computedOnRead: false,
					formVisible: true,
					gridVisible: true,
					gridSortable: false,
					searchVisible: false,
					formattedData: false,
					gridExportable: false,
					reportable: false,
				},
			},
			cellTemplate: function (fs) {
				return fs.readFileSync(
					__dirname + '/cell-templates/entity-type-cell-tmpl.dust',
					'utf8',
				);
			},
		},
		{
			field: 'status',
			type: 'picklist',
			caption: 'Status',
			kind: 'editable',
			typeOptions: {
				picklistName: 'link_statuses',
			},
			cellTemplate: function (fs) {
				return fs.readFileSync(
					__dirname + '/cell-templates/link-status-cell-tmpl.dust',
					'utf8',
				);
			},
			dbIndex: true,
		},
		{
			field: 'type',
			type: 'picklist',
			caption: 'Link Method',
			kind: 'editable',
			typeOptions: {
				picklistName: 'link_types',
			},
			dbIndex: true,
		},
		{
			field: 'reason',
			type: 'textarea',
			caption: 'Reason',
			kind: 'editable',
		},
		{
			field: 'matches',
			type: 'json[]',
			caption: 'Matches',
			kind: 'custom',
			kindOptions: {
				flags: {
					audit: false,
					schema: false,
					search: false,
					apiWritable: true,
					apiExternalWritable: false,
					computedOnSave: false,
					computedOnRead: false,
					formVisible: false,
					gridVisible: false,
					gridSortable: false,
					searchVisible: false,
					formattedData: false,
					gridExportable: false,
					reportable: false,
				},
			},
		},
		{
			field: 'numberOfMatches',
			type: 'number',
			caption: 'Number of Matches',
			kind: 'hidden',
		},
		{
			field: 'relevance',
			type: 'decimal',
			kind: 'system',
			caption: 'Relevance',
			typeOptions: {
				format: '0%',
			},
		},
		{
			field: 'summary',
			type: 'textarea',
			kind: 'custom',
			caption: 'Summary',
			kindOptions: {
				flags: {
					audit: false,
					schema: false,
					search: false,
					apiWritable: false,
					apiExternalWritable: false,
					computedOnSave: false,
					computedOnRead: false,
					formVisible: false,
					gridVisible: true,
					gridSortable: false,
					searchVisible: false,
					formattedData: false,
					gridExportable: false,
					reportable: false,
				},
			},
			cellTemplate: function(fs){
				return fs.readFileSync(
					__dirname + '/cell-templates/summary-tmpl.dust',
					'utf8',
				);
			},
		},
		{
			field: 'relationshipTypeId',
			type: 'relationshipType',
			typeOptions: {
				picklistName: 'relationship_types',
				missingValueTranslation: 'Unknown Record Link Type',
				primaryVerbField: 'relationshipTypePrimaryVerb',
			},
			kind: 'editable',
			caption: 'Record Link Type',
		},
		{
			field: 'relationshipTypePrimaryVerb',
			type: 'yesno',
			kind: 'custom',
			kindOptions: {
				flags: {
					audit: true,
					schema: true,
					search: true,
					readable: true,
					apiWritable: true,
					apiExternalWritable: false,
					computedOnSave: false,
					computedOnRead: false,
					iselComputedOnSave: false,
					aggregateField: false,
					submitOnly: false,
					formVisible: false,
					gridVisible: false,
					gridSortable: false,
					searchVisible: false,
					formattedData: true,
					gridExportable: false,
					reportable: true,
				},
			},
			caption: 'Relationship Primary Verb',
		},
		{
			field: 'otherCaseOwner',
			type: 'user',
			kind: 'custom',
			caption: 'Case Owner',
			features: ['viewRelatedCasesWithoutAccess'],
			kindOptions: {
				flags: {
					audit: false,
					schema: false,
					search: false,
					apiWritable: false,
					apiExternalWritable: false,
					computedOnSave: false,
					computedOnRead: false,
					formVisible: true,
					gridVisible: true,
					gridSortable: false,
					searchVisible: false,
					formattedData: false,
					gridExportable: false,
					reportable: false,
				},
			},
		},
		{
			field: 'entity1ParentId',
			type: 'linkEntityId',
			kind: 'editable',
			caption: 'Record Parent',
			dbIndex: true,
			typeOptions: {
				relatedEntityField: 'entity1ParentType',
			},
		},
		{
			field: 'entity1ParentType',
			type: 'entityName',
			kind: 'editable',
			caption: 'Record Parent Type',
		},
		{
			field: 'entity1RootId',
			type: 'linkEntityId',
			kind: 'editable',
			caption: 'Record Root',
			dbIndex: true,
			typeOptions: {
				relatedEntityField: 'entity1RootType',
			},
		},
		{
			field: 'entity1RootType',
			type: 'entityName',
			kind: 'editable',
			caption: 'Record Root Type',
		},
		{
			field: 'entity2ParentId',
			type: 'linkEntityId',
			kind: 'editable',
			caption: 'Linked Record Parent',
			dbIndex: true,
			typeOptions: {
				relatedEntityField: 'entity2ParentType',
			},
		},
		{
			field: 'entity2ParentType',
			type: 'entityName',
			kind: 'editable',
			caption: 'Linked Record Parent Type',
		},
		{
			field: 'entity2RootId',
			type: 'linkEntityId',
			kind: 'editable',
			caption: 'Linked Record Root',
			dbIndex: true,
			typeOptions: {
				relatedEntityField: 'entity2RootType',
			},
		},
		{
			field: 'entity2RootType',
			type: 'entityName',
			kind: 'editable',
			caption: 'Linked Record Root Type',
		},
	],
	queryFilter: function({query, args}){
		return query.and([
			args.showInactiveRecords !== true && query.is('sysActive', true),
			args.showCanceledCases$ !== true && esQueryHelper.mustNot({
				term: {
					entity1Id__canceled: true,
				},
			}),
			args.showCanceledCases$ !== true && esQueryHelper.mustNot({
				term: {
					entity2Id__canceled: true,
				},
			}),
		]);
	},
	dynamicParents: [
		{
			entityField: 'entity1Type',
			field: 'entity1Id',
			filterFn(context) {
				const { entDef: { allowRecordLinking, entityCanon } } = context;
				if (!allowRecordLinking) return null;
				return { entity1Type: entityCanon };
			},
		},
		{
			entityField: 'entity2Type',
			field: 'entity2Id',
			filterFn(context) {
				const { entDef: { allowRecordLinking, entityCanon } } = context;
				if (!allowRecordLinking) return null;
				return { entity2Type: entityCanon };
			},
		},
	],
	parents: [
		{
			entity: {
				base: 'sys',
				name: 'case',
			},
			reportable: true,
			field: 'entity1Id',
			filter: {
				entity1Type: 'sys/case',
			},
		},
		{
			entity: {
				base: 'sys',
				name: 'case',
			},
			reportable: true,
			field: 'entity2Id',
			filter: {
				entity2Type: 'sys/case',
			},
		},
		{
			entity: {
				base: 'sys',
				name: 'person',
			},
			reportable: true,
			field: 'entity1Id',
			filter: {
				entity1Type: 'sys/person',
			},
		},
		{
			entity: {
				base: 'sys',
				name: 'person',
			},
			reportable: true,
			field: 'entity2Id',
			filter: {
				entity2Type: 'sys/person',
			},
		},
	],
	customESQueries: {
		entityCaseId: function (context) {
			const { value } = context;
			return {
				bool: {
					should: [
						{
							term: { entity1Id__caseId: value },
						},
						{
							term: { entity2Id__caseId: value },
						},
					],
				},
			};
		},
		entityOtherRecordSubmitted: function (context) {
			const { value } = context;
			return {
				bool: {
					should: [
						{
							bool: {
								must: [
									{ term: { entity1Id: value } },
									{ term: { entity2Id__sysSubmitted: true } },
								],
							},
						},
						{
							bool: {
								must: [
									{ term: { entity2Id: value } },
									{ term: { entity1Id__sysSubmitted: true } },
								],
							},
						},
					],
				},
			};
		},
	},
});
