const _ = require('lodash');
const statHelper = require('../../shared/stat-helper.js')('sys_link');

module.exports = [
	{
		category: 'link',
		key: 'suggestedLinks',
		query: statHelper.countActiveEntity({
			where: {
				type: 'Suggested',
			},
		}),
	},
	{
		category: 'link',
		key: 'suggestedLinksByStatus',
		query: statHelper.countActiveEntityByPicklistGroup({
			where: {
				type: 'Suggested',
			},
		}),
		options: {
			groupingField: 'status',
			picklistName: 'link_statuses',
			datasetTranslation(dataset, translate) {
				return statHelper.getPicklistDatasetTrans(this, dataset, translate);
			},
		},
	},
	{
		category: 'link',
		key: 'manualLinks',
		query: statHelper.countActiveEntity({
			where: {
				type: 'Manual',
			},
		}),
	},
	{
		category: 'link',
		key: 'manualCaseToCaseLinks',
		query: statHelper.countActiveEntity({
			where() {
				this
					.where('type', 'Manual')
					.andWhere(function subquery() {
						this
							.where('entity_1_type', 'sys/case')
							.andWhere('entity_2_type', 'sys/case');
					});
			},
		}),
	},
	{
		category: 'link',
		key: 'suggestedCaseToCaseLinks',
		query: statHelper.countActiveEntity({
			where() {
				this
					.where('type', 'Suggested')
					.andWhere(function subquery() {
						this
							.where('entity_1_type', 'sys/case')
							.andWhere('entity_2_type', 'sys/case');
					});
			},
		}),
	},
	{
		category: 'link',
		key: 'highestTotalManualCaseToCaseLinksForACase',
		query(knex, options, cb) {
			const subQuery = function subQuery(){
				this.count()
					.from('sys_link')
					.where('sys_active', true)
					.where('entity_1_type', 'sys/case')
					.where('entity_2_type', 'sys/case')
					.andWhere('type', 'Manual')
					.groupBy('entity_1_id')
					.as('counts');
			};
			knex('sys_listitem')
				.max('counts.count')
				.from(subQuery)
				.asCallback((err, result) => {
					if (err) return cb(err);
					let formatted = 0;
					if (result && _.head(result) && _.head(result).max) {
						formatted = parseInt(_.head(result).max, 10);
					}
					return cb(null, formatted);
				});
		},
	},
	{
		category: 'link',
		key: 'highestTotalSuggestedCaseToCaseLinksForACase',
		query(knex, options, cb) {
			const subQuery = function subQuery(){
				this.count()
					.from('sys_link')
					.where('sys_active', true)
					.where('entity_1_type', 'sys/case')
					.where('entity_2_type', 'sys/case')
					.andWhere('type', 'Suggested')
					.groupBy('entity_1_id')
					.as('counts');
			};
			knex('sys_listitem')
				.max('counts.count')
				.from(subQuery)
				.asCallback((err, result) => {
					if (err) return cb(err);
					let formatted = 0;
					if (result && _.head(result) && _.head(result).max) {
						formatted = parseInt(_.head(result).max, 10);
					}
					return cb(null, formatted);
				});
		},
	},
	{
		category: 'link',
		key: 'averageManualCaseToCaseLinks',
		query(knex, options, cb) {
			const subQuery = function subSquery() {
				this.count()
					.from('sys_link')
					.where('sys_active', true)
					.where('entity_1_type', 'sys/case')
					.where('entity_2_type', 'sys/case')
					.andWhere('type', 'Manual')
					.groupBy('entity_1_id')
					.as('average_func');
			};

			knex
				.avg('average_func.count')
				.from(subQuery)
				.asCallback((err, rows) => {
					if (err) return cb(err);
					let formatted = 0;
					if (rows && _.head(rows) && _.head(rows).avg) {
						formatted = parseInt(_.head(rows).avg, 10);
					}
					return cb(null, formatted);
				});
		},
	},
	{
		category: 'link',
		key: 'averageManualCaseToCaseLinks',
		query(knex, options, cb) {
			const subQuery = function subSquery() {
				this.count()
					.from('sys_link')
					.where('sys_active', true)
					.where('entity_1_type', 'sys/case')
					.where('entity_2_type', 'sys/case')
					.andWhere('type', 'Suggested')
					.groupBy('entity_1_id')
					.as('average_func');
			};

			knex
				.avg('average_func.count')
				.from(subQuery)
				.asCallback((err, rows) => {
					if (err) return cb(err);
					let formatted = 0;
					if (rows && _.head(rows) && _.head(rows).avg) {
						formatted = parseInt(_.head(rows).avg, 10);
					}
					return cb(null, formatted);
				});
		},
	},
	{
		category: 'link',
		key: 'manualPersonToPersonLinks',
		query: statHelper.countActiveEntity({
			where() {
				this
					.where('type', 'Manual')
					.andWhere(function subquery() {
						this
							.where('entity_1_type', 'sys/person')
							.andWhere('entity_2_type', 'sys/person');
					});
			},
		}),
	},
	{
		category: 'link',
		key: 'manualCaseToPersonLinks',
		query: statHelper.countActiveEntity({
			where() {
				this
					.where('type', 'Manual')
					.andWhere(function subquery() {
						this
							.where(function subQuery() {
								this
									.where('entity_1_type', 'sys/case')
									.andWhere('entity_2_type', 'sys/person');
							})
							.orWhere(function subQuery() {
								this
									.where('entity_1_type', 'sys/person')
									.andWhere('entity_2_type', 'sys/case');
							});
					});
			},
		}),
	},
	{
		category: 'link',
		key: 'highestTotalManualCaseToPersonLinksForACase',
		query(knex, options, cb) {
			const subQuery = function subQuery(){
				this.count()
					.from('sys_link')
					.where('sys_active', true)
					.where(function subquery() {
						this
							.where(function subQuery1() {
								this
									.where('entity_1_type', 'sys/case')
									.andWhere('entity_2_type', 'sys/person');
							})
							.orWhere(function subQuery2() {
								this
									.where('entity_1_type', 'sys/person')
									.andWhere('entity_2_type', 'sys/case');
							});
					})
					.andWhere('type', 'Manual')
					.groupBy('entity_1_id')
					.as('counts');
			};
			knex('sys_listitem')
				.max('counts.count')
				.from(subQuery)
				.asCallback((err, result) => {
					if (err) return cb(err);
					let formatted = 0;
					if (result && _.head(result) && _.head(result).max) {
						formatted = parseInt(_.head(result).max, 10);
					}
					return cb(null, formatted);
				});
		},
	},
	{
		category: 'link',
		key: 'averageManualCaseToPersonLinks',
		query(knex, options, cb) {
			const subQuery = function subSquery() {
				this.count()
					.from('sys_link')
					.where('sys_active', true)
					.where(function subquery() {
						this
							.where(function subQuery1() {
								this
									.where('entity_1_type', 'sys/case')
									.andWhere('entity_2_type', 'sys/person');
							})
							.orWhere(function subQuery2() {
								this
									.where('entity_1_type', 'sys/person')
									.andWhere('entity_2_type', 'sys/case');
							});
					})
					.andWhere('type', 'Manual')
					.groupBy('entity_1_id')
					.as('average_func');
			};

			knex
				.avg('average_func.count')
				.from(subQuery)
				.asCallback((err, rows) => {
					if (err) return cb(err);
					let formatted = 0;
					if (rows && _.head(rows) && _.head(rows).avg) {
						formatted = parseInt(_.head(rows).avg, 10);
					}
					return cb(null, formatted);
				});
		},
	},
	{
		category: 'link',
		key: 'editedLinks',
		query: statHelper.countActiveDistinctField({
			where() {
				this.whereNot('sys_link.last_updated_by', 0);
			},
		}),
		options: {
			field: 'last_updated_by',
		},
	},
	{
		category: 'link',
		key: 'editedLinksByRole',
		query(knex, options, callback) {
			knex.select('sys_user_role.name')
				.countDistinct('sys_link.last_updated_by as count')
				.from('sys_link')
				.rightJoin('sys_user', 'sys_user.id', 'sys_link.last_updated_by')
				.rightJoin('sys_user_role', 'sys_user.user_role_id', 'sys_user_role.id')
				.whereNotNull('sys_user_role.name')
				.where('sys_link.sys_active', true)
				.orWhereNull('sys_link.sys_active')
				.andWhereNot('sys_user_role.name', 'Anonymous')
				.groupBy('sys_user_role.name')
				.asCallback((err, rows) => {
					if (err) return callback(err);
					const cleanedCounts = {};
					_.each(rows, (row) => {
						const group = row.name || 'notDefined';
						cleanedCounts[group] = parseInt(row.count, 10);
					});
					return callback(null, cleanedCounts);
				});
		},
		options: {
			datasetTranslation(dataset, translate) {
				return statHelper.getPicklistDatasetTrans(this, dataset, translate);
			},
		},
	},
];
