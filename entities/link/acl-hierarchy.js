module.exports = [
	{
		type: 'group',
		caption: 'Links',
		permission: 'link',
		sequence: 5,
		options: [
			{
				type: 'group',
				permission: 'suggested_link',
				caption: 'Suggested',
				options: [
					{
						permission: 'create_suggested_link',
						caption: 'Create',
						tooltip: 'Add suggested Case links',
						sequence: 1,
					},
					{
						permission: 'view_suggested_link',
						caption: 'View',
						tooltip: 'View suggested Case links',
						sequence: 2,
					},
					{
						permission: 'edit_suggested_link',
						caption: 'Edit',
						tooltip: 'Edit suggested Case links',
						sequence: 3,
						dependencies: ['view_suggested_link'],
					},
					{
						permission: 'remove_suggested_link',
						caption: 'Remove',
						sequence: 4,
						tooltip: 'Delete suggested Case links',
						dependencies: ['view_suggested_link'],
					},
					{
						permission: 'view_suggested_case_links_with_partial_access',
						caption: 'View limited case details',
						sequence: 5,
						tooltip: 'Allows the user to see limited information (such as case number, case owner) for case links they do not have full access to. Case contents remain inaccessible.',
						dependencies: ['view_suggested_link'],
						features: ['viewRelatedCasesWithoutAccess'],
					},
				],
			},
			{
				type: 'group',
				permission: 'manual_link',
				caption: 'Manual',
				options: [
					{
						permission: 'create_manual_link',
						caption: 'Create',
						tooltip: 'Manually add a Case link',
						sequence: 1,
					},
					{
						permission: 'view_manual_link',
						caption: 'View',
						tooltip: 'View manually added Case links',
						sequence: 2,
					},
					{
						permission: 'edit_manual_link',
						caption: 'Edit',
						tooltip: 'Edit manually added Case links',
						sequence: 3,
						dependencies: ['view_manual_link'],
					},
					{
						permission: 'remove_manual_link',
						caption: 'Remove',
						tooltip: 'Delete manually added Case links',
						sequence: 4,
						dependencies: ['view_manual_link'],
					},
				],
			},
			{
				type: 'group',
				permission: 'automatic_link',
				caption: 'Automatic',
				options: [
					{
						permission: 'view_automatic_link',
						caption: 'View',
						tooltip: 'View automatically added Case links',
						sequence: 1,
					},
				],
			},
		],
	},
];
