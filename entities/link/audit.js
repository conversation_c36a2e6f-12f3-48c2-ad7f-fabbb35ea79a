module.exports = {
	child: true,
	allowNavigateTo: false,
	parentType(entityData) {
		if (entityData.relationshipTypeId) {
			return 'sys/case';
		}
		return entityData.entity1Type;
	},
	parentFieldFn(entityData) {
		if (entityData.relationshipTypeId) {
			return entityData.entity1Id__caseId || entityData.entity2Id__caseId;
		}
		return entityData.entity1Id;
	},
	cmd: {
		load: {
			'viewed': {
				options: {
					reference: {
						displayFields(data, auditModel) {
							const identityField = data.entity2Id ? 'entity2Id' : 'case2Id';
							return [identityField, 'reason'];
						},
					},
				},
			},
		},
		save: {
			'created_manual': {
				condition: {
					composite: 'all',
					rules: [
						{
							path: 'diff.id.originalValue',
							comparator: 'nexists',
						}, {
							path: 'diff.type.updatedValue',
							comparator: 'eq',
							value: 'Manual',
						},
					],
				},
				options: {
					reference: {
						displayFields(data, auditModel) {
							const identityField = data.entity2Id ? 'entity2Id' : 'case2Id';
							return [identityField, 'reason', 'type', 'linkedBy'];
						},
					},
				},
			},
			'created_manual_record': {
				condition: {
					composite: 'all',
					rules: [
						{
							path: 'diff.id.originalValue',
							comparator: 'nexists',
						}, {
							path: 'entity.relationshipTypeId',
							comparator: 'exists',
						},
					],
				},
				options: {
					reference: {
						displayFields() {
							return ['entity1Id', 'relationshipTypeId', 'entity2Id', 'type', 'linkedBy'];
						},
					},
				},
			},
			'updated_manual_record': {
				condition: {
					composite: 'all',
					rules: [
						{
							path: 'entity.id',
							comparator: 'exists',
						}, {
							path: 'entity.relationshipTypeId',
							comparator: 'exists',
						},
						{
							path: 'diff.relationshipTypeId.originalValue',
							comparator: 'exists',
						},
					],
				},
				options: {
					changes: {
						displayFields: ['relationshipTypeId'],
					},
					reference: {
						displayFields() {
							return ['entity1Id', 'relationshipTypeId', 'entity2Id'];
						},
					},
				},
			},
			'created_automatic': {
				condition: {
					composite: 'all',
					rules: [{
						composite: 'any',
						rules: [
							{
								path: 'diff.status.updatedValue',
								comparator: 'eq',
								value: 'Approved',
							}, {
								path: 'diff.status.updatedValue',
								comparator: 'eq',
								value: 'Dismissed',
							},
						],
					}, {
						composite: 'all',
						rules: [
							{
								path: 'diff.status.originalValue',
								comparator: 'neq',
								value: 'Approved',
							}, {
								path: 'diff.status.originalValue',
								comparator: 'neq',
								value: 'Dismissed',
							},
						],
					}],
				},
				options: {
					reference: {
						displayFields(data, auditModel) {
							const identityField = data.entity2Id ? 'entity2Id' : 'case2Id';
							return [identityField, 'reason', 'type', 'status', 'linkedBy'];
						},
					},
				},
			},
			'updated': {
				options: {
					changes: {
						displayFields: ['status', 'linkedBy', 'reason', 'relevance'],
					},
					reference: {
						displayFields(data, auditModel) {
							const identityField = data.entity2Id ? 'entity2Id' : 'case2Id';
							return [identityField, 'reason'];
						},
					},
				},
			},
		},
		remove: {
			'deleted_manual_record': {
				condition: {
					composite: 'all',
					rules: [
						{
							path: 'entity.id',
							comparator: 'exists',
						},
						{
							path: 'entity.relationshipTypeId',
							comparator: 'exists',
						},
					],
				},
				options: {
					reference: {
						displayFields: ['entity1Id', 'relationshipTypeId', 'entity2Id'],
					},
				},
			},
			'deleted': {
				options: {
					reference: {
						displayFields(data, auditModel) {
							const identityField = data.entity2Id ? 'entity2Id' : 'case2Id';
							return [identityField];
						},
					},
				},
			},
		},
	},
};
