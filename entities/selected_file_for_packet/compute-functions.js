const _ = require('lodash');
const logger = require('../../plugins/log').file(__filename);


module.exports = {
	getSelectedFileName(seneca, rows, callback) {
		if (rows.length === 0) {
			logger.warn('Expected the "rows" arg to be a non-empty array');

			return callback(null, null);
		}

		if (rows.length > 1) {
			logger.warn(`Expected the "rows" array to only contain one element, - found ${rows.length}`);

			//
			// fall-through, keep executing
			//
		}


		const { selectedAttachmentId } = rows[0];

		return seneca.make('sys/attachment')
			.load$(selectedAttachmentId, (err, result) => {
				if (err) {
					return callback(err);
				}

				const fileName = _.get(result, 'files[0].name', null);

				return callback(null, [fileName]);
			});
	},
};
