const permHelper = require('../../lib/core/permission-helper.js');

module.exports = permHelper.initialize()
	.required({
		name: 'Inherit attachment edit ACL',
		roles: ['bypass_inherited_acl'],
		actions: ['save_new', 'save_existing', 'remove'],
		conditions: ['sys/attachment::{packetAttachmentId}::save'],
	})
	.required({
		name: 'Inherit attachment view ACL',
		roles: ['bypass_inherited_acl'],
		actions: ['load', 'list'],
		conditions: ['sys/attachment::{packetAttachmentId}'],
	})
	.value();
