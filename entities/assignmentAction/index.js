const extend = require('../extend.js');
const standardActionConfig = require('../standard-action-config.js');

module.exports = extend(standardActionConfig, {
	table: 'sys_assignment_action',
	entity: {
		base: 'sys',
		name: 'assignment_action',
	},
	workflowActionConfig: {
		formName: 'assignment-action',
		aclRoles: {
			createACLRole: 'agent',
			editACLRole: 'agent',
			deleteACLRole: 'agent',
		},
		supportedEntities: ['sys/case'],
	},
	caption: 'Assignment',
	captionPlural: 'Assignments',
	addCaption: 'Add Assignment',
	newCaption: 'New Assignment',
	audit: require('./audit.js'),
	rules: require('./rules.js'),
	usageStatistics: require('./usage-statistics.js'),
	model() { return require('./model.js'); },
	fields: [
		{
			field: 'assignmentField',
			type: 'fieldPicklist',
			typeOptions: {
				entityNameField: 'targetEntity',
				refreshOnFields: ['targetEntity'],
				filterType: ['user[]', 'user'],
				filterFlag: 'apiWritable',
			},
			kind: 'editable',
			caption: 'Assignment Field',
		},
		{
			field: 'userType',
			type: 'radio',
			kind: 'editable',
			caption: 'Responsible User Type',
			typeOptions: {
				picklistName: 'responsible_user_types',
				refreshOnFields: ['assignmentField'],
			},
		},
		{
			field: 'contextUsers',
			type: 'fieldPicklist[]',
			typeOptions: {
				entityNameField: 'targetEntity',
				filterType: ['user', 'user[]'],
				filter: 'filterAssignmentField',
				refreshOnFields: ['assignmentField', 'targetEntity'],
			},
			caption: 'Include Context Users',
			kind: 'editable',
		},
		{
			field: 'systemUsers',
			type: 'user[]',
			caption: 'Include System Users',
			kind: 'editable',
		},
	],
});
