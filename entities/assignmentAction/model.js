/* globals $appData */
const _ = require('lodash');
const BBModel = require('../../public/lib/backbone-model.js');

module.exports = BBModel.extend({
	urlRoot: `${$appData.globalConfig.apiRoot}/assignment_action`,
	entity: {
		base: 'sys',
		name: 'assignment_action',
	},
	ignore: ['targetEntity'],
	idAttribute: 'id',
	save(attrs, options) {
		// Make any model.set inside save silent so as not reset some values for systemUsers or
		// contextUsers for not being visible.
		_.extend(options, {silent: true});
		return BBModel.prototype.save.call(this, attrs, options);
	},
});
