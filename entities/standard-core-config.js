module.exports = {
	joins: [],
	grids: {},
	table: null,
	parents: [],
	entity: null,
	search: false,
	dynamicDataStore: false,
	staticFieldWorkflows: [],
	acl: {
		accessControls: [],
		allowedProperties: [],
	},
	aclRolesForCopy: [],
	api: {
		writableFields: [
			// If flag is true, existing primary entities
			// will be reset when an entity becomes new primary entity
			'resetPrimaries',
		],
	},
	importTransformer: 'default',
	customExportColumns: [],
	customDependencies: [],
	excludeFromDataExport: ['dynamicData'],
	icon: 'fa-file',
	customForm: false,
	type: 'standard',
	gridFilterExcludeFields: [],
	gridFilterExcludeFieldsByPerm: {},
	backgroundColor: '#EADCFA',
	textColor: '#484848',
	audit: {
		allowNavigateTo: true,
		cmd: {
			load: {
				viewed: {
					options: {
						reference: {
							displayFields: [
								'createdDate',
							],
						},
					},
				},
			},
			save: {
				created: {
					options: {
						reference: {
							displayFields: [
								'createdDate',
							],
						},
					},
				},
				updated: {
					options: {
						changes: {
							excludeFields: ['id'],
						},
						reference: {
							displayFields: [
								'createdDate',
							],
						},
					},
				},
			},
			remove: {
				deleted: {
					options: {
						reference: {
							displayFields: [
								'createdDate',
							],
						},
					},
				},
			},
		},
	},
	allowPurge: false,
	fields: [
		{
			caption: 'Id',
			field: 'id',
			type: 'primary',
			kind: 'custom',
			kindOptions: {
				useInDynamicJoins: true,
				flags: {
					audit: true,
					schema: true,
					search: true,
					apiWritable: true,
					apiExternalWritable: true,
					computedOnSave: false,
					computedOnRead: false,
					formVisible: false,
					gridVisible: true,
					gridSortable: false,
					searchVisible: false,
					formattedData: true,
					gridExportable: false,
					reportable: true,
				},
			},
			excludeFromSaveAndCopy: true,
			excludeFromRedact: true,
			excludeFromAutofill: true,
			// Must be included for acl
			alwaysInApiSelectedFields: true,
			showOnPortal: true,
			showOnHotline: true,
			dataImportMappableOverride: true,
		},
		{
			caption: 'Created By',
			field: 'createdBy',
			type: 'user',
			kind: 'system',
			excludeFromSaveAndCopy: true,
			excludeFromAutofill: true,
			showOnHotline: true,
			kindOptions: {
				useInDynamicJoins: true,
			},
		},
		{
			caption: 'Created Date',
			field: 'createdDate',
			type: 'datetime',
			kind: 'system',
			gridWidth: 200,
			dbIndex: true,
			excludeFromSaveAndCopy: true,
			excludeFromRedact: true,
			excludeFromAutofill: true,
			showOnPortal: true,
			showOnHotline: true,
		},
		{
			caption: 'Last Updated By',
			field: 'lastUpdatedBy',
			type: 'user',
			kind: 'system',
			excludeFromSaveAndCopy: true,
			excludeFromAutofill: true,
			showOnHotline: true,
		},
		{
			caption: 'Last Updated Date',
			field: 'lastUpdatedDate',
			type: 'datetime',
			kind: 'system',
			gridWidth: 200,
			dbIndex: true,
			excludeFromSaveAndCopy: true,
			excludeFromRedact: true,
			excludeFromAutofill: true,
			showOnHotline: true,
		},
		{
			caption: 'Deleted By',
			field: 'deletedBy',
			type: 'user',
			kind: 'custom',
			excludeFromSaveAndCopy: true,
			excludeFromAutofill: true,
			kindOptions: {
				flags: {
					audit: false,
					schema: true,
					search: true,
					apiWritable: false,
					apiExternalWritable: false,
					computedOnSave: false,
					computedOnRead: false,
					formVisible: false,
					gridVisible: false,
					gridSortable: false,
					searchVisible: false,
					formattedData: true,
					gridExportable: false,
					reportable: false,
				},
			},
		},
		{
			caption: 'Deleted Date',
			field: 'deletedDate',
			type: 'datetime',
			kind: 'custom',
			dbIndex: true,
			excludeFromSaveAndCopy: true,
			excludeFromRedact: true,
			excludeFromAutofill: true,
			kindOptions: {
				flags: {
					audit: false,
					schema: true,
					search: true,
					apiWritable: false,
					apiExternalWritable: false,
					computedOnSave: false,
					computedOnRead: false,
					formVisible: false,
					gridVisible: false,
					gridSortable: false,
					searchVisible: false,
					formattedData: true,
					gridExportable: false,
					reportable: false,
				},
			},
		},
		{
			field: 'sysActive',
			type: 'yesno',
			kind: 'custom',
			caption: 'Active',
			dbIndex: true,
			kindOptions: {
				useInDynamicJoins: true,
				flags: {
					audit: false,
					schema: true,
					search: true,
					apiWritable: false,
					apiExternalWritable: false,
					computedOnSave: false,
					computedOnRead: false,
					formVisible: false,
					gridVisible: false,
					gridSortable: false,
					searchVisible: false,
					formattedData: true,
					gridExportable: false,
					reportable: true,
				},
			},
			excludeFromRedact: true,
			excludeFromSaveAndCopy: true,
			excludeFromAutofill: true,
			alwaysInApiSelectedFields: true,
			showOnPortal: true,
			showOnHotline: true,
		},
		{
			field: 'sysSubmitted',
			type: 'yesno',
			kind: 'custom',
			caption: 'Submitted',
			kindOptions: {
				useInDynamicJoins: true,
				flags: {
					audit: false,
					schema: true,
					search: true,
					apiWritable: true,
					apiExternalWritable: true,
					computedOnSave: false,
					computedOnRead: false,
					formVisible: false,
					gridVisible: false,
					gridSortable: false,
					searchVisible: false,
					formattedData: true,
					gridExportable: false,
					reportable: false,
				},
			},
			excludeFromRedact: true,
			excludeFromSaveAndCopy: true,
			excludeFromAutofill: true,
			showOnPortal: true,
			showOnHotline: true,
		},
		{
			field: 'sysHidden',
			type: 'yesno',
			kind: 'hidden',
			caption: 'Hidden',
			excludeFromRedact: true,
			excludeFromSaveAndCopy: true,
			excludeFromAutofill: true,
			showOnHotline: true,
		},
		{
			caption: 'System Processing',
			field: 'sysProcessing',
			type: 'yesno',
			kind: 'custom',
			kindOptions: {
				flags: {
					audit: true,
					schema: true,
					search: true,
					apiWritable: false,
					apiExternalWritable: false,
					computedOnSave: false,
					computedOnRead: false,
					formVisible: true,
					gridVisible: true,
					gridSortable: true,
					searchVisible: true,
					formattedData: true,
					gridExportable: true,
					reportable: false,
				},
			},
			excludeFromRedact: true,
			excludeFromAutofill: true,
			showOnHotline: true,
		},
		{
			field: 'dynamicData',
			type: 'jsonb',
			caption: 'Dynamic Data',
			kind: 'hidden',
			showOnHotline: true,
		},
	],
	esDefaultFilters(){
		return null;
	},
	queryFilter({query, args}){
		return query.and([
			args.showInactiveRecords !== true && query.is('sysActive', true),
		]);
	},
};
