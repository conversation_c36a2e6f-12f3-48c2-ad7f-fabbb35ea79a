const permHelper = require('../../lib/core/permission-helper.js');

module.exports = permHelper.initialize()
	.required({
		name: 'Inherit packet edit ACL',
		roles: ['bypass_inherited_acl'],
		actions: ['save_new', 'save_existing', 'remove'],
		conditions: ['sys/packet::{packetId}::save'],
	})
	.required({
		name: 'Inherit packet view ACL',
		roles: ['bypass_inherited_acl'],
		actions: ['load', 'list'],
		conditions: ['sys/packet::{packetId}'],
	})
	.value();
