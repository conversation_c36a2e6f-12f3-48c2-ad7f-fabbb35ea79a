const extend = require('../extend.js');
const standardConfig = require('../standard-config.js');

module.exports = extend(standardConfig, {
	db: 'default',
	table: 'sys_packet_template',
	entity: {
		base: 'sys',
		name: 'packet_template',
	},
	customForm: false,
	api: {
		useGenericApi: true,
	},
	validation: require('./validation.js'),
	configurationImport(ctx = {}){
		return ctx.pdftronEnabled === true;
	},
	caption: 'Packet Template',
	captionPlural: 'Templates',
	addCaption: 'Add Packet Template',
	newCaption: 'New Packet Template',
	acl: require('./acl.js'),
	model() {
		return require('../../public/models/packet-template-model.js');
	},
	grids: require('./grids.js'),
	parents: [
		{
			entity: {
				base: 'sys',
				name: 'template',
			},
			field: 'templateId',
		},
		{
			entity: {
				base: 'sys',
				name: 'packet',
			},
			field: 'packetId',
			filter: {
				parentType: 'packet',
			},
		},
		{
			entity: {
				base: 'sys',
				name: 'attachment',
			},
			field: 'packetId',
			filter: {
				parentType: 'attachment',
			},
		},
	],
	fields: [
		{
			field: 'locale',
			type: 'picklist',
			caption: 'Locale',
			typeOptions: {
				picklistName: 'supported_languages',
				filter: 'existingLanguagesFilter',
			},
			kind: 'editable',
		},
		{
			field: 'templateId',
			type: 'picklistSelectizeApi',
			caption: 'Template',
			kind: 'editable',
			typeOptions: {
				picklistName: 'templates',
			},
			dbIndex: true,
		},
		{
			field: 'packetId',
			type: 'id',
			caption: 'Packet Id',
			kind: 'hidden-editable',
			dbIndex: true,
		},
		{
			field: 'order',
			type: 'number',
			caption: 'Order',
			kind: 'editable',
			dbIndex: true,
		},
		{
			field: 'parentType',
			type: 'code',
			kind: 'custom',
			kindOptions: {
				flags: {
					audit: false,
					schema: true,
					search: true,
					apiWritable: true,
					apiExternalWritable: false,
					computedOnSave: false,
					computedOnRead: false,
					formVisible: false,
					gridVisible: false,
					gridSortable: false,
					searchVisible: false,
					formattedData: true,
					gridExportable: false,
					reportable: false,
				},
			},
			caption: 'Parent Type',
			dbIndex: true,
			excludeFromRedact: true,
		},
	],
	joins: [
		{
			referenceField: 'templateId',
			table: 'sys_template',
			fields: [
				'name',
			],
		},
		{
			referenceField: 'packetId',
			table: 'sys_packet',
			fields: [
				'name',
			],
		},
	],
	audit: {
		child: true,
		parentType: {
			base: 'sys',
			name: 'packet',
		},
		parentFieldId: 'packetId',
		allowNavigateTo: true,
		cmd: {
			load: {
				viewed: true,
			},
			save: {
				created: true,
				updated: {
					options: {
						changes: {
							excludeFields: ['id'],
						},
					},
				},
			},
			remove: {
				deleted: true,
			},
		},
	},
});
