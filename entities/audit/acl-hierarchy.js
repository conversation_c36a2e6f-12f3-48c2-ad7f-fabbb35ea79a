const permHelper = require('../../lib/core/permission-helper.js');

module.exports = [{
	type: 'group',
	caption: 'History',
	permission: 'audit',
	disabled: true,
	options: [
		{
			caption: 'View',
			sequence: 6,
			disabled: true,
			permission: 'view_audit_log',
		},
		{
			caption: 'Create',
			sequence: 7,
			disabled: true,
			permission: 'create_audit_log',
		},
		{
			type: 'group',
			caption: 'Edit',
			sequence: 8,
			disabled: true,
			permission: permHelper.getEditGroupPermissionCode('log'),
			options: [{
				caption: 'Save',
				disabled: true,
				permission: 'edit_audit_log',
			}],
		},
		{
			caption: 'Remove',
			sequence: 9,
			disabled: true,
			permission: 'remove_audit_log',
		},
	],
}];
