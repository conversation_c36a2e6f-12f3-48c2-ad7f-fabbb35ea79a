const permHelper = require('../../lib/core/permission-helper.js');

const entityObjectsToBypass = [
	'sys/case',
	'sys/user',
	'sys/user_role',
];

module.exports = permHelper.initialize()
	.entity({
		name: 'Clean audit log entity fields',
		actions: ['load', 'list'],
		conditions: [],
		entityFields: [{
			name: 'diff',
			typeObjectAttributeName: 'entity',
			type: 'objectType',
		}, {
			name: 'entity',
			type: 'objectType',
		}],
	})
	.filter({
		name: 'View Audit Ip',
		roles: ['view_audit_ip'],
		actions: ['load', 'list'],
		conditions: [],
		filters: {
			loginIp: false,
		},
	})
	.filter({
		name: 'View Audit Login',
		roles: ['view_audit_login'],
		actions: ['load', 'list'],
		conditions: [],
		filters: {
			loginId: false,
		},
	})
	.required({
		name: 'View Case Audit Logs',
		roles: ['view_case_history'],
		actions: ['load', 'list', 'save_existing', 'remove'],
		conditions: [{
			attributes: {
				objectType: 'sys/case',
			},
		}],
	})
	.required({
		name: 'View Case Children Audit Logs',
		roles: ['view_case_history'],
		actions: ['load', 'list', 'save_existing', 'remove'],
		conditions: [{
			attributes: {
				parentType: 'sys/case',
				'!objectType': 'sys/case',
			},
		}],
	})
	// No one should have access to this
	.required({
		name: 'View Shared Link Access Audit Logs',
		roles: ['view_shared_link_access_audit_log'],
		actions: ['load', 'list', 'save_existing', 'remove'],
		conditions: [{
			attributes: {
				objectType: 'sys/shared_link_access',
			},
		}],
	})
	.required({
		name: 'View Shared Link Receipt Audit Logs',
		roles: ['view_shared_link_audit_log'],
		actions: ['load', 'list', 'save_existing', 'remove'],
		conditions: [{
			attributes: {
				objectType: 'sys/shared_link_receipt',
			},
		}],
	})
	.required({
		name: 'View Shared Link Audit Logs',
		roles: ['view_shared_link_audit_log'],
		actions: ['load', 'list', 'save_existing', 'remove'],
		conditions: [{
			attributes: {
				objectType: 'sys/shared_link',
			},
		}],
	})
	.required({
		name: 'View User Profile Audit Logs',
		roles: ['view_user_audit_log'],
		actions: ['load', 'list', 'save_existing', 'remove'],
		conditions: [{
			attributes: {
				objectType: 'sys/user',
			},
		}],
	})
	.required({
		name: 'View User Profile Children Audit Logs',
		roles: ['view_user_audit_log'],
		actions: ['load', 'list', 'save_existing', 'remove'],
		conditions: [{
			attributes: {
				parentType: 'sys/user',
				'!objectType': 'sys/user',
			},
		}],
	})
	.required({
		name: 'View User Role Audit Logs',
		roles: ['view_user_role_audit_log'],
		actions: ['load', 'list', 'save_existing', 'remove'],
		conditions: [{
			attributes: {
				objectType: 'sys/user_role',
			},
		}],
	})
	.required({
		name: 'View User Role Children Audit Logs',
		roles: ['view_user_role_audit_log'],
		actions: ['load', 'list', 'save_existing', 'remove'],
		conditions: [{
			attributes: {
				parentType: 'sys/user_role',
				'!objectType': 'sys/user_role',
			},
		}],
	})
	.required({
		name: 'View User Activity Audit Logs',
		roles: ['view_user_activity_log'],
		actions: ['load', 'list', 'save_existing', 'remove'],
		conditions: [{
			attributes: {
				'!userId': '0',
				'!objectType': entityObjectsToBypass,
				'!parentType': entityObjectsToBypass,
			},
		}],
	})
	.required({
		name: 'View System Audit Logs',
		roles: ['view_system_audit_log'],
		actions: ['load', 'list', 'save_existing', 'remove'],
		conditions: [{
			attributes: {
				'userId': '0',
				'!objectType': entityObjectsToBypass,
				'!parentType': entityObjectsToBypass,
			},
		}],
	})
	.required({
		name: 'View Export Audit Logs',
		roles: ['view_export_audit_log'],
		actions: ['load', 'list'],
		conditions: [{
			attributes: {
				action: 'export',
			},
		}],
	})
	.required({
		name: 'View Import Audit Logs',
		roles: ['view_import_audit_log'],
		actions: ['load', 'list'],
		conditions: [{
			attributes: {
				action: 'import',
			},
		}],
	})
	.required({
		name: 'View Audit Logs',
		roles: ['view_audit_log'],
		actions: ['load', 'list', 'save_existing', 'remove'],
		conditions: [],
	})
	.required({
		name: 'Create Audit Log',
		roles: ['create_audit_log'],
		actions: ['save_new'],
		conditions: [{
			attributes: {
				id: null,
			},
		}],
	})
	.required({
		name: 'Edit Audit Log',
		roles: ['edit_audit_log'],
		actions: ['save_new'],
		conditions: [{
			attributes: {
				'!id': null,
			},
		}],
	})
	.required({
		name: 'Remove Audit Log',
		roles: ['remove_audit_log'],
		actions: ['remove'],
		conditions: [],
	})
	.value();