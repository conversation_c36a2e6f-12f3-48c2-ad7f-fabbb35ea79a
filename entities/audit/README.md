# Audit

New action to Audit
===========

Here are the steps to add a new action to an existing entity for which to log
an entity record;

- look for the test of that audit entity, run the test, make sure it is green
- add a test which tests that an audit record is saved for the new action you 
	want to define, make sure the test fails
- add the new action under the appropriate cmd in `entities/x/audit.js`
- make sure the cmd into which you want to plug the audit is there, if not, 
 	add it
- write the appropriate condition and options for that action
- condition is written using verdict.js library, for more information of how to
	use this library see: https://github.com/rfink/verdict.js
- in options, you can use two attributes: reference and changes, under which
	you can defined the excluded fields or the displayed fields(which one suits
	your needs best)
- run the tests for that audit entity, make sure they pass
- run ALL the tests, make sure they pass
- if necessary, make the appropriate changes to the UI


New entity to Audit
==========

Here are the steps to add a new entity for which to log audit records:

- add a test that tries to find an audit record for the entity type you want
	to add
- make sure that test fails
- add your entity definition in `entities/x/audit.js`
- in `services/business-logic/common/workflow/audit-entities` make sure to add
 	one of the functions defined there to the commands you defined in the 
 	entity definition
- run the test which executes the commands you defined in entity definition
- make sure the tests pass
- re-initialize the DB
- re-initialize the ES index
- run the tests again, make sure they pass
- run all the tests, make sure they pass
- if necessary, make the appropriate changes to the UI view where your new entity log records
	will be displayed 
