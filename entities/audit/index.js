var extend = require('../extend.js');
var standardConfig = require('../standard-config.js');

module.exports = extend(standardConfig, {
	db: 'audit',
	table: 'audit_log',
	entity: {
		base: 'audit',
		name: 'log',
	},
	caption: 'Audit Log',
	captionPlural: 'Audit Logs',
	addCaption: 'Add Audit Log',
	newCaption: 'New Audit Log',
	allowPurge: true,
	disableFormattedData: true,
	acl: require('./acl.js'),
	esAggregates: require('./es-aggregates.js'),
	aclHierarchy: require('./acl-hierarchy.js'),
	model: function(){
		return require('../../public/models/audit-model.js');
	},
	collection: function(){
		return require('../../public/collections/audit-collection.js');
	},
	esDefaultFilters: function(){
		var query = this.query('es');
		return query.is_not('hidden', true).toQuery();
	},
	queryFilter: function({query, args}){
		return query.and([
			args.filterSharedLinkAccess$ === true && query.is_not('objectType', 'sys/shared_link_access'),
			args.filterMessage$ === true && query.is_not('objectType', 'sys/message'),
			args.filterUnknown$ === true && query.is_not('userId', 'unknown'),
			args.filterLinkMatch$ === true && query.is_not('objectType', 'sys/link_match'),
			args.filterGrid$ === true && query.not(query.or([
				// allow export grid/import audit entries
				query.and([
					query.is('objectType', 'sys/gridFilter'),
					query.and([
						query.is_not('action', 'export'),
						query.is_not('action', 'import'),
					]),
				]),
				query.is('parentType', 'sys/gridFilter'),
			])),
			args.filterSystemLoad$ === true && query.not(query.and([
				query.is('userId', '0'),
				query.is('action', 'load'),
			])),
			args.filterAccesssRequest$ === true && query.is_not('objectType', 'sys/access_request'),
		]);
	},
	gridFilterExcludeFields: ['differences', 'loginIp'],
	fields: [
		{
			field: 'id',
			type: 'primary',
			kind: 'system',
			excludeFromRedact: true,
			caption: 'ID',
			alwaysInApiSelectedFields: true,
		},
		{
			field: 'createdBy',
			type: 'id',
			kind: 'system',
			caption: 'Created By',
		},
		{
			field: 'createdDate',
			type: 'datetime',
			kind: 'system',
			excludeFromRedact: true,
			caption: 'Created Date',
		},
		{
			field: 'lastUpdatedBy',
			type: 'id',
			kind: 'system',
			caption: 'Last Updated By',
		},
		{
			field: 'lastUpdatedDate',
			type: 'datetime',
			kind: 'system',
			excludeFromRedact: true,
			caption: 'Last Updated Date',
		},
		{
			field: 'timestamp',
			type: 'datetime',
			kind: 'system',
			excludeFromRedact: true,
			caption: 'Date',
		},
		{
			field: 'userId',
			type: 'id',
			kind: 'system',
			caption: 'User ID',
			alwaysInApiSelectedFields: true,
		},
		{
			field: 'loginId',
			type: 'id',
			kind: 'system',
			caption: 'Login ID',
		},
		{
			field: 'loginIp',
			type: 'loginIp',
			kind: 'system',
			caption: 'Login IP',
		},
		{
			field: 'objectType',
			type: 'code',
			kind: 'system',
			excludeFromRedact: true,
			caption: 'Object Type',
			alwaysInApiSelectedFields: true,
		},
		{
			field: 'objectId',
			type: 'id',
			kind: 'system',
			excludeFromRedact: true,
			caption: 'Object ID',
			alwaysInApiSelectedFields: true,
		},
		{
			field: 'parentId',
			type: 'id',
			kind: 'system',
			excludeFromRedact: true,
			caption: 'Parent ID',
			alwaysInApiSelectedFields: true,
		},
		{
			field: 'parentType',
			type: 'code',
			kind: 'system',
			excludeFromRedact: true,
			caption: 'Parent Type',
			alwaysInApiSelectedFields: true,
		},
		{
			field: 'action',
			type: 'code',
			kind: 'custom',
			cellTemplate: function(fs){
				return fs.readFileSync(
					__dirname + '/cell-templates/history-action-cell-tmpl.dust',
					'utf8',
				);
			},
			caption: 'Action',
			excludeFromRedact: true,
			kindOptions: {
				flags: {
					audit: true,
					schema: true,
					search: true,
					apiWritable: false,
					apiExternalWritable: false,
					computedOnSave: false,
					computedOnRead: false,
					formVisible: true,
					gridVisible: true,
					gridSortable: false,
					searchVisible: true,
					gridExportable: true,
					reportable: true,
				},
			},
			alwaysInApiSelectedFields: true,
		},
		{
			field: 'actionTranslationKey',
			type: 'code',
			kind: 'hidden',
			excludeFromRedact: true,
			caption: 'Action',
			alwaysInApiSelectedFields: true,
		},
		{
			field: 'entity',
			type: 'json',
			kind: 'hidden',
			caption: 'Entity',
			alwaysInApiSelectedFields: true,
		},
		{
			field: 'diff',
			type: 'json',
			kind: 'hidden',
			caption: 'Diff',
			alwaysInApiSelectedFields: true,
		},
		{
			field: 'deletedBy',
			type: 'id',
			kind: 'hidden',
			caption: 'Deleted By',
		},
		{
			field: 'reference',
			type: 'textbox',
			kind: 'custom',
			kindOptions: {
				flags: {
					audit: false,
					schema: false,
					search: false,
					apiWritable: false,
					apiExternalWritable: false,
					computedOnSave: false,
					computedOnRead: false,
					formVisible: false,
					gridVisible: true,
					gridSortable: false,
					searchVisible: false,
					gridExportable: false,
					reportable: false,
				},
			},
			cellTemplate: function(fs){
				return fs.readFileSync(
					__dirname + '/cell-templates/history-reference-cell-tmpl.dust',
					'utf8',
				);
			},
			caption: 'Reference',
		},
		{
			field: 'differences',
			type: 'textbox',
			kind: 'custom',
			kindOptions: {
				flags: {
					audit: false,
					schema: false,
					search: false,
					apiWritable: false,
					apiExternalWritable: false,
					computedOnSave: false,
					computedOnRead: false,
					formVisible: false,
					gridVisible: true,
					gridSortable: false,
					searchVisible: false,
					gridExportable: false,
					reportable: false,
				},
			},
			caption: 'Differences',
			cellTemplate: function (fs) {
				return fs.readFileSync(
					__dirname + '/cell-templates/history-changes-cell-tmpl.dust',
					'utf8',
				);
			},
		},
		{
			field: 'hidden',
			type: 'yesno',
			kind: 'hidden-searchable',
			caption: 'Hidden Audit Record',
			excludeFromRedact: true,
		},
		{
			caption: 'Date Submitted',
			field: 'dateSubmitted',
			type: 'datetime',
			kind: 'custom',
			excludeFromAutofill: true,
			kindOptions: {
				computeFunction: 'dateSubmitted',
				flags: {
					audit: false,
					schema: true,
					search: false,
					apiWritable: true,
					apiExternalWritable: false,
					computedOnSave: true,
					computedOnRead: false,
					formVisible: true,
					gridVisible: true,
					gridSortable: true,
					searchVisible: false,
					gridExportable: true,
					reportable: true,
				},
			},
			showOnHotline: true,
			showOnPortal: true,
		},
	],
});
