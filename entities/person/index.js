const extend = require('../extend.js');
const standardConfig = require('../standard-config.js');

module.exports = extend(standardConfig, {
	db: 'default',
	table: 'sys_person',
	entity: {
		base: 'sys',
		name: 'person',
	},
	joins: [
		{
			referenceField: 'sourceJob',
			table: 'sys_event',
			fields: [
				'name',
				'jobId',
			],
		},
	],
	api: {
		useGenericApi: true,
	},
	normalizeMultiValuePicklistEntries: true,
	partiallyDynamic: true,
	dataExport: true,
	caption: 'Profile',
	captionPlural: 'Profiles',
	addCaption: 'Add Profile',
	newCaption: 'New Profile',
	gridDescriptorField: 'personName',
	acl: require('./acl.js'),
	aclHierarchy: require('./acl-hierarchy.js'),
	audit: require('./audit.js'),
	ruleEvents: true,
	allowDataAggregation: true,
	allowAdvancedSearch: true,
	allowQuickSearch: true,
	allowPurge: true,
	grids: require('./grids.js'),
	rules: require('./rules.js'),
	validation: require('./validation.js'),
	computeFunctions: require('./compute-functions.js'),
	usageStatistics: require('./usage-statistics.js'),
	historyNav: require('./history-nav.js'),
	icon: 'fa-address-book',
	includeInDataDictionary: true,
	enableRecordSourceView: true,
	allowInGridViews: true,
	linkSummaryFields: ['emailAddress'],
	report: true,
	reportOptions: {
		joins: [
			{ parent: 'entity_1_id', child: 'id', bracketsBefore: 1 },
			{
				parent: 'entity_2_id',
				child: 'id',
				bracketsAfter: 1,
				whereClauseOperator: 'OR',
			},
			{ parent: 'context_yellowfin_username', child: 'context_yellowfin_username' },
		],
	},
	model() {
		return require('../../public/models/person-model.js');
	},
	collection() {
		return require('../../public/collections/people-collection.js');
	},
	view() {
		return require('../../public/views/person/person-details-view.js');
	},
	rowTemplates: {
		small(){
			return require('./row-templates/small.dust');
		},
		medium(){
			return require('./row-templates/medium.dust');
		},
		tiny(){
			return require('./row-templates/tiny.dust');
		},
	},
	staticFieldWorkflows: [require('./static-field-workflows/person-purge-flag-status.js')],
	fields: [
		{
			field: 'firstName',
			type: 'textbox',
			caption: 'First Name',
			kind: 'editable',
			excludeFromSaveAndCopy: true,
			cellTemplate(fs) {
				return fs.readFileSync(
					`${__dirname}/cell-templates/person-first-name-cell-tmpl.dust`,
					'utf8',
				);
			},
		},
		{
			field: 'lastName',
			type: 'textbox',
			caption: 'Last Name',
			kind: 'editable',
			excludeFromSaveAndCopy: true,
			cellTemplate(fs) {
				return fs.readFileSync(
					`${__dirname}/cell-templates/person-last-name-cell-tmpl.dust`,
					'utf8',
				);
			},
		},
		{
			field: 'middleInitial',
			type: 'textbox',
			caption: 'Middle Initial',
			kind: 'editable',
			excludeFromSaveAndCopy: true,
		},
		{
			field: 'dateOfBirth',
			type: 'date',
			typeOptions: {
				datePickerEndDate: '0d',
			},
			caption: 'Date of Birth',
			kind: 'editable',
			excludeFromSaveAndCopy: true,
		},
		{
			field: 'personName',
			type: 'textbox',
			caption: 'Name',
			kind: 'computed',
			kindOptions: {
				computeFunction: 'personName',
			},
			excludeFromSaveAndCopy: true,
			dataImportMappableOverride: true,
		},
		{
			field: 'address',
			type: 'textbox',
			caption: 'Address',
			kind: 'editable',
		},
		{
			field: 'city',
			type: 'textbox',
			caption: 'City',
			kind: 'editable',
		},
		{
			field: 'stateProvince',
			type: 'picklist',
			caption: 'State / Province',
			typeOptions: {
				picklistName: 'states_and_provinces',
			},
			kind: 'editable',
		},
		{
			field: 'country',
			type: 'country',
			caption: 'Country',
			kind: 'editable',
		},
		{
			field: 'zipCodePostalCode',
			type: 'textbox',
			caption: 'Zip Code / Postal Code',
			kind: 'editable',
		},
		{
			field: 'homePhone',
			type: 'phone-number',
			caption: 'Home Phone #',
			kind: 'editable',
		},
		{
			field: 'workPhone',
			type: 'phone-number',
			caption: 'Work Phone #',
			kind: 'editable',
		},
		{
			field: 'emailAddress',
			type: 'email',
			caption: 'Email address',
			kind: 'editable',
			excludeFromSaveAndCopy: true,
			typeOptions: {
				linkWithSystemUser: true,
				disableWhitelist: true,
			},
		},
		{
			field: 'lastSnapshotGenerationDate',
			type: 'datetime',
			caption: 'Last Snapshot Generation Date',
			kind: 'system',
			excludeFromRedact: true,
		},
		{
			field: 'picture',
			type: 'imageCropper',
			typeOptions: {
				hideWhenReadOnly: true,
			},
			kind: 'editable',
			search: false,
			caption: 'Profile Picture',
		},
		// We're making this field writeable for person
		{
			field: 'excludeFromPurge',
			kind: 'editable',
		},
	],
});
