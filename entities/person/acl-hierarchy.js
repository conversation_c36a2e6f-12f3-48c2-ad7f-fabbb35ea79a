const permHelper = require('../../lib/core/permission-helper.js');

module.exports = [{
	type: 'group',
	caption: 'Profile',
	permission: 'person',
	sequence: 3,
	options: [
		{
			permission: 'view_person',
			caption: 'View',
			tooltip: 'View profiles',
			sequence: 1,
		},
		{
			permission: 'create_person',
			caption: 'Create',
			tooltip: 'Create profiles',
			sequence: 2,
			dependencies: ['view_person'],
		},
		{
			permission: 'create_person_restricted_fields',
			caption: 'Create Restricted Fields',
			tooltip: 'Add restricted field values',
			dependencies: ['create_person', 'view_person'],
			sequence: 3,
		},
		{
			type: 'group',
			permission: permHelper.getEditGroupPermissionCode('person'),
			caption: 'Edit',
			tooltip: 'Edit profiles',
			sequence: 4,
			dependencies: ['view_person'],
			options: [
				{
					permission: 'edit_person',
					caption: 'Save',
					tooltip: 'Edit unrestricted fields',
					dependencies: ['view_person'],
				},
				{
					permission: 'edit_person_exclude_from_purge',
					caption: 'Do Not Purge',
					tooltip: 'Mark Profile as Do Not Purge',
					dependencies: ['view_person'],
				},
				{
					permission: 'edit_person_restricted_fields',
					caption: 'Save Restricted Fields',
					tooltip: 'Edit restricted fields',
					dependencies: ['view_person', 'edit_person'],
				},
			],
		},
		{
			permission: 'remove_person',
			caption: 'Remove',
			tooltip: 'Delete profiles',
			sequence: 5,
			dependencies: ['view_person'],
		},
	],
}];
