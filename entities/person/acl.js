const permHelper = require('../../lib/core/permission-helper.js');

const saveFormFilter = {
	id: true,
	pendingPurgeDate: true,
	datePurged: true,
	purgeReason: true,
	apiType: true,
	excludeFromPurge: true,
};

module.exports = permHelper.initialize()
	.filterMarkForPurge()
	.filterPurge()
	.filterRestrictEditFields({
		name: 'Save Restricted Fields',
		roles: ['edit_person_restricted_fields'],
		actions: ['save_existing'],
		conditions: [
			{
				attributes: {
					'!id': null,
				},
			},
		],
	})
	.filterRestrictEditFields({
		name: 'Create Restricted Fields',
		roles: ['create_person_restricted_fields'],
		actions: ['save_new'],
		conditions: [
			{
				attributes: {
					id: null,
				},
			},
		],
	})
	.filter({
		name: 'Edit Do Not Purge Field',
		roles: ['edit_person_exclude_from_purge'],
		actions: ['save_new', 'save_existing'],
		filters: {
			excludeFromPurge: false,
		},
	})
	// filter - edit person form
	.filter({
		name: 'Edit Person Form',
		roles: ['edit_person'],
		actions: ['save_existing'],
		filters: saveFormFilter,
	})
	.filterViewSourceData()
	.required({
		name: 'People Search',
		roles: ['view_person'],
		actions: ['load', 'list'],
		conditions: [],
	})
	.required({
		name: 'Create Person',
		roles: ['create_person'],
		actions: ['save_new'],
		conditions: [{
			attributes: {
				id: null,
			},
		}],
	})
	.required({
		name: 'Edit Person',
		roles: ['edit_person_group'],
		actions: ['save_existing'],
		conditions: [{
			attributes: {
				'!id': null,
			},
		}],
	})
	.required({
		name: 'Remove Person',
		roles: ['remove_person'],
		actions: ['remove'],
		conditions: [],
	})
	.value();
