module.exports = {
	'main-people': {
		sortColumn: 'lastName',
		sortOrder: 'asc',
		columns: [
			{ field: 'lastName' },
			{ field: 'firstName' },
			{ field: 'address' },
			{ field: 'city' },
			{ field: 'stateProvince' },
		],
		defaultDynamicDataFilters: ['createdDate', 'lastUpdatedDate'],
	},
	'search-result-people-schedule-purge': {
		sortColumn: 'lastName',
		sortOrder: 'asc',
		columns: [
			{ field: 'lastName' },
			{ field: 'firstName' },
			{ field: 'address' },
			{ field: 'city' },
			{ field: 'stateProvince' },
		],
	},
	'advanced-search-result-people': {
		sortColumn: 'lastName',
		sortOrder: 'asc',
		columns: [
			{ field: 'lastName' },
			{ field: 'firstName' },
			{ field: 'address' },
			{ field: 'city' },
			{ field: 'stateProvince' },
		],
	},
	'search-result-people-purge': {
		sortColumn: 'lastName',
		sortOrder: 'asc',
		columns: [
			{ field: 'lastName' },
			{ field: 'firstName' },
			{ field: 'address' },
			{ field: 'city' },
			{ field: 'stateProvince' },
		],
	},
	'search-result-person': {
		sortColumn: 'lastName',
		sortOrder: 'asc',
		columns: [
			{ field: 'lastName' },
			{ field: 'firstName' },
			{ field: 'city' },
			{ field: 'stateProvince' },
		],
	},
};
