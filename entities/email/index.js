var extend = require('../extend.js');
var standardChildConfig = require('../standard-child-config.js');

module.exports = extend(standardChildConfig, {
	db: 'default',
	table: 'sys_email',
	entity: {
		base: 'sys',
		name: 'email',
	},
	api: {
		useGenericApi: true,
		mainFileField: 'attachments',
	},
	normalizeMultiValuePicklistEntries: true,
	customForm: false,
	ruleEvents: true,
	report: false,
	caption: 'Email',
	captionPlural: 'Emails',
	addCaption: 'Add Email',
	newCaption: 'New Email',
	gridDescriptorFn: require('./gridDescriptorFn.js'),
	acl: require('./acl.js'),
	aclHierarchy: require('./acl-hierarchy.js'),
	audit: require('./audit.js'),
	rules: require('./rules.js'),
	grids: require('./grids.js'),
	validation: require('./validation.js'),
	computeFunctions: require('./compute-functions.js'),
	historyNav: require('./history-nav.js'),
	icon: 'fa-envelope',
	includeInDataDictionary: true,
	enableRecordSourceView: true,
	linkSummaryFields: ['subject'],
	usageStatistics: require('./usage-statistics.js'),
	rowTemplates: {
		small: function(){
			return require('./row-templates/small.dust');
		},
		medium: function(){
			return require('./row-templates/medium.dust');
		},
		tiny: function(){
			return require('./row-templates/tiny.dust');
		},
	},
	esDefaultFilters: function(){
		var query = this.query('es');
		return query.and([
			query.is_not_empty('caseId'),
			query.is_not('caseId__canceled', true),
			query.is_not('sysActive', false),
			query.is_not('sysSubmitted', false),
		]).toQuery();
	},
	model: function(){
		return require('../../public/models/email-model.js');
	},
	collection: function(){
		return require('../../public/collections/email-threads-collection.js');
	},
	gridFilterExcludeFields: ['senderName'],
	fields: [
		{
			field: 'parentId',
			type: 'id',
			kind: 'hidden',
			caption: 'Parent ID',
			dbIndex: true,
			excludeFromRedact: true,
		},
		{
			field: 'forwardedEmailId',
			type: 'id',
			kind: 'custom',
			kindOptions: {
				flags: {
					audit: false,
					schema: true,
					search: true,
					apiWritable: true,
					apiExternalWritable: false,
					computedOnSave: false,
					computedOnRead: false,
					formVisible: false,
					gridVisible: false,
					gridSortable: false,
					searchVisible: true,
					formattedData: true,
					gridExportable: false,
					reportable: false,
				},
			},
			caption: 'Forwarded Email ID',
			dbIndex: true,
			excludeFromRedact: true,
		},
		{
			field: 'emailThreadId',
			type: 'id',
			kind: 'custom',
			caption: 'Email Thread ID',
			kindOptions: {
				flags: {
					audit: false,
					schema: true,
					search: true,
					apiWritable: true,
					apiExternalWritable: false,
					computedOnSave: false,
					computedOnRead: false,
					formVisible: false,
					gridVisible: false,
					gridSortable: false,
					searchVisible: false,
					formattedData: true,
					gridExportable: false,
					reportable: false,
				},
				useInDynamicJoins: true,
			},
			dbIndex: true,
			excludeFromRedact: true,
			alwaysInApiSelectedFields: true,
		},
		{
			field: 'senderId',
			type: 'user',
			caption: 'From',
			kind: 'custom',
			kindOptions: {
				flags: {
					audit: false,
					schema: true,
					search: true,
					apiWritable: false,
					apiExternalWritable: false,
					computedOnSave: false,
					computedOnRead: false,
					formVisible: false,
					gridVisible: false,
					gridSortable: false,
					searchVisible: false,
					formattedData: true,
					gridExportable: false,
					reportable: false,
				},
			},
			alwaysInApiSelectedFields: true,
		},
		{
			field: 'threadCount',
			type: 'number',
			caption: 'Count',
			kind: 'custom',
			kindOptions: {
				computeFunction: 'setThreadCount',
				flags: {
					audit: false,
					schema: true,
					search: true,
					apiWritable: false,
					apiExternalWritable: false,
					computedOnSave: true,
					computedOnRead: false,
					formVisible: true,
					gridVisible: true,
					gridSortable: true,
					searchVisible: false,
					formattedData: true,
					gridExportable: true,
					reportable: true,
				},
			},
			cellTemplate: function (fs) {
				return fs.readFileSync(
					__dirname + '/cell-templates/email-count-cell-tmpl.dust',
					'utf8',
				);
			},
			excludeFromRedact: true,
		},
		{
			field: 'senderEmail',
			type: 'code',
			caption: 'Sender Email',
			kind: 'system',
			alwaysInApiSelectedFields: true,
		},
		{
			field: 'recipients',
			type: 'emailLookup[]',
			caption: 'Recipients',
			kind: 'editable',
			dataImportMappableOverride: false,
		},
		{
			field: 'ccRecipients',
			type: 'emailLookup[]',
			caption: 'Cc',
			kind: 'editable',
			dataImportMappableOverride: true,
		},
		{
			field: 'bccRecipients',
			caption: 'Bcc',
			type: 'emailLookup[]',
			kind: 'custom',
			kindOptions: {
				flags: {
					audit: false,
					schema: true,
					search: true,
					apiWritable: true,
					apiExternalWritable: false,
					computedOnSave: false,
					computedOnRead: false,
					formVisible: true,
					gridVisible: true,
					gridSortable: true,
					searchVisible: false,
					formattedData: true,
					gridExportable: false,
					reportable: false,
				},
			},
			dataImportMappableOverride: false,
		},
		{
			field: 'subject',
			type: 'textbox',
			caption: 'Subject Line',
			kind: 'editable',
			gridWidth: 150,
		},
		{
			field: 'body',
			type: 'texteditor',
			caption: 'Body',
			kind: 'editable',
		},
		{
			field: 'bodyHtml',
			type: 'texteditor',
			kind: 'hidden',
			caption: 'Body HTML',
			// HACK: Currently the system ignores the search flag defined in kind options
			//		 Only way to control that is to specify the flag here.
			//		 Can be removed once this ticket is resolved: ITPL-10556
			search: false,
		},
		{
			field: 'bodyText',
			type: 'textarea',
			kind: 'hidden',
			caption: 'Body Text',
			// HACK: Currently the system ignores the search flag defined in kind options
			//		 Only way to control that is to specify the flag here.
			//		 Can be removed once this ticket is resolved: ITPL-10556
			search: false,
		},
		{
			field: 'dateSent',
			type: 'datetime',
			kind: 'system',
			caption: 'Date Sent',
		},
		{
			field: 'attachments',
			type: 'file[]',
			kind: 'editable',
			caption: 'Attachments',
			typeOptions: {
				vectorize: true,
				caseIdField: 'caseId',
				hideCaption: true,
				maxFileCount: 5,
				maxTotalAttachmentSize: 20 * 1024 * 1024, // 20 MB Max
			},
		},
		{
			field: 'direction',
			type: 'picklist',
			kind: 'system',
			caption: 'Direction',
			typeOptions: {
				picklistName: 'email_directions',
			},
			kindOptions: {
				useInDynamicJoins: true,
			},
			dataImportMappableOverride: true,
		},
		{
			field: 'emailSources',
			type: 'picklist[]',
			kind: 'editable',
			caption: 'Email Sources',
			typeOptions: {
				picklistName: 'email_sources',
			},
			dataImportMappableOverride: false,
		},
		{
			field: 'splitMessage',
			type: 'textbox',
			kind: 'hidden',
			caption: 'Split Message',
		},
		{
			field: 'highPriority',
			type: 'checkbox',
			caption: 'High Priority',
			kind: 'editable',
			dataImportMappableOverride: false,
		},
		{
			field: 'latestSent',
			type: 'checkbox',
			typeOptions: {
				allowNull: true,
			},
			caption: 'Latest Sent',
			kind: 'custom',
			kindOptions: {
				flags: {
					audit: true,
					schema: true,
					search: true,
					apiWritable: false,
					apiExternalWritable: false,
					computedOnSave: false,
					computedOnRead: false,
					formVisible: false,
					gridVisible: false,
					gridSortable: false,
					gridExportable: false,
					searchVisible: false,
					formattedData: true,
					reportable: true,
				},
			},
			dbIndex: true,
			excludeFromRedact: true,
		},
		{
			field: 'failedRecipients',
			type: 'code[]',
			kind: 'system',
			caption: 'Failed Recipients',
		},
		{
			field: 'senderName',
			type: 'textbox',
			kind: 'custom',
			caption: 'Sender',
			gridWidth: 150,
			kindOptions: {
				computeFunction: 'setSenderName',
				flags: {
					audit: false,
					schema: false,
					search: true,
					apiWritable: false,
					apiExternalWritable: false,
					computedOnSave: false,
					computedOnRead: true,
					formVisible: false,
					gridVisible: true,
					gridSortable: false,
					searchVisible: false,
					formattedData: true,
					gridExportable: true,
					reportable: false,
				},
			},
			alwaysInApiSelectedFields: true,
		},
		{
			field: 'parentEmailSender',
			type: 'email',
			kind: 'custom',
			caption: 'Parent Email Sender',
			kindOptions: {
				flags: {
					audit: false,
					schema: true,
					search: true,
					apiWritable: true,
					apiExternalWritable: false,
					computedOnSave: false,
					computedOnRead: false,
					formVisible: false,
					gridVisible: true,
					gridSortable: true,
					searchVisible: false,
					formattedData: true,
					gridExportable: true,
					reportable: false,
				},
			},
		},
		{
			field: 'number',
			kind: 'hidden',
			caption: 'Number',
		},
		{
			field: 'childNumber',
			kind: 'hidden',
			caption: 'Child Number',
		},
	],
	joins: [
		{
			referenceField: 'caseId',
			table: 'sys_case',
			fields: ['owner'],
		},
	],
});
