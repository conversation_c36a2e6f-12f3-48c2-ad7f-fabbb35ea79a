const defaultDynamicDataFilters = ['dateSent'];
module.exports = {
	'main-emails': {
		sortColumn: 'createdDate',
		sortOrder: 'desc',
		columns: [
			{ field: 'caseId' },
			{ field: 'senderName' },
			{ field: 'subject' },
			{ field: 'body' },
			{ field: 'createdDate' },
			{ field: 'threadCount' },
		],
		defaultDynamicDataFilters,
	},
	'case-emails': {
		sortColumn: 'createdDate',
		sortOrder: 'desc',
		columns: [
			{ field: 'senderName' },
			{ field: 'subject' },
			{ field: 'body' },
			{ field: 'createdDate' },
			{ field: 'threadCount' },
		],
		defaultDynamicDataFilters,
	},
	'incoming-mail': {
		sortColumn: 'createdDate',
		sortOrder: 'desc',
		excludeColumns: ['threadCount', 'senderEmail'],
		columns: [
			{ field: 'senderName' },
			{ field: 'subject' },
			{ field: 'body' },
			{ field: 'createdDate' },
		],
		defaultDynamicDataFilters: ['createdDate', 'lastUpdatedDate'],
	},
	'advanced-search-result-emails': {
		sortColumn: 'createdDate',
		sortOrder: 'desc',
		excludeColumns: ['threadCount'],
		columns: [
			{ field: 'caseId' },
			{ field: 'senderName' },
			{ field: 'subject' },
			{ field: 'body' },
			{ field: 'createdDate' },
			{ field: 'threadCount' },
		],
	},
	'search-result-emails-schedule-purge': {
		sortColumn: 'createdDate',
		sortOrder: 'desc',
		excludeColumns: ['threadCount'],
		columns: [
			{ field: 'caseId' },
			{ field: 'senderName' },
			{ field: 'subject' },
			{ field: 'body' },
			{ field: 'createdDate' },
			{ field: 'threadCount' },
		],
	},
	'search-result-emails-purge': {
		sortColumn: 'createdDate',
		sortOrder: 'desc',
		excludeColumns: ['threadCount'],
		columns: [
			{ field: 'caseId' },
			{ field: 'senderName' },
			{ field: 'subject' },
			{ field: 'body' },
			{ field: 'createdDate' },
			{ field: 'threadCount' },
			{ field: 'pendingPurgeDate' },
		],
	},
};
