const statHelper = require('../../shared/stat-helper.js')('sys_email');
const sharedUtils = require('../../shared/utils.js')();

module.exports = [
	{
		category: 'email',
		key: 'totalEmails',
		query: statHelper.countActiveEntity(),
	},
	{
		category: 'email',
		key: 'emailReplies',
		query: statHelper.countActiveEntity({
			where() {
				this.whereNotNull('sys_email.parent_id');
			},
		}),
	},
	{
		category: 'email',
		key: 'emailThreads',
		query: statHelper.countActiveDistinctField({
			where: {
				latest_sent: true,
			},
		}),
		options: {
			field: 'email_thread_id',
		},
	},
	{
		category: 'email',
		key: 'emailsWithAttachments',
		query: statHelper.countActiveEntity({
			where() {
				this.whereNotNull('sys_email.attachments');
			},
		}),
	},
	{
		category: 'settings_system',
		key: 'incomingEmails',
		query: statHelper.countEntity({
			where() {
				this.whereNull('sys_email.case_id');
			},
		}),
	},
	{
		category: 'storage',
		key: 'sizeOfEmailAttachments',
		query(knex, options, callback) {
			const summary = {
				totalSize: 0,
			};
			const {
				pipeline,
				BulkTransformStream,
				DrainStream,
			} = options;
			const listStream = knex
				.select('sys_email.attachments')
				.from('sys_email')
				.where('sys_email.sys_active', true)
				.whereNotNull('sys_email.attachments')
				.stream();
			const accumulatorStream = sharedUtils.getStreamToCalculateTotalSize(BulkTransformStream, summary, 'attachments');
			const drainStream = new DrainStream();

			return pipeline(
				listStream,
				accumulatorStream,
				drainStream,
				err => callback(err, summary.totalSize),
			);
		},
	},
	{
		category: 'email',
		key: 'activeOutgoingEmailsWithAttachments',
		query: statHelper.countActiveEntity({
			where() {
				this
					.whereNotNull('attachments')
					.where('direction', 'outbound');
			},
		}),
	},
	{
		category: 'email',
		key: 'activeIncomingEmailsWithAttachments',
		query: statHelper.countActiveEntity({
			where() {
				this
					.whereNotNull('attachments')
					.where('direction', 'inbound');
			},
		}),
	},
	{
		category: 'email',
		key: 'activeOutgoingEmails',
		query: statHelper.countActiveEntity({
			where() {
				this.where('direction', 'outbound');
			},
		}),
	},
	{
		category: 'email',
		key: 'activeIncomingEmails',
		query: statHelper.countActiveEntity({
			where() {
				this.where('direction', 'inbound');
			},
		}),
	},
	{
		category: 'email',
		key: 'unassignedActiveIncomingEmails',
		query: statHelper.countActiveEntity({
			where() {
				this.whereNull('case_id');
			},
		}),
	},
	{
		category: 'email',
		key: 'emailsPerCase',
		query: statHelper.countTotalAverageAndHighestByCase(),
	},
];
