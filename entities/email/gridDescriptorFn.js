const _ = require('lodash');
const utils = require('../../shared/utils.js')();

function truncate(text, maxChars) {
	return `${text.slice(0, maxChars).trim()}…`;
}

function getBodyText(body) {
	let bodyText = utils.stripHtmlTags(body);
	// Remove any previous email content if the email is a reply. We only want to include the body
	// of the current email thread.
	const previousEmailStart = bodyText.search(/\s*________________________________/);
	if (previousEmailStart >= 0) bodyText = bodyText.substring(0, previousEmailStart);
	return bodyText;
}

module.exports = (context) => {
	const { formattedData: { subject, body } } = context;
	const maxChars = 50;
	let truncatedSubject = subject;
	if (subject.length > maxChars) truncatedSubject = truncate(subject, maxChars);
	if (_.isEmpty(body)) return truncatedSubject;
	let truncatedBody = getBodyText(body);
	if (truncatedBody.length > maxChars) truncatedBody = truncate(truncatedBody, maxChars);
	return `${truncatedSubject} / ${truncatedBody}`;
};
