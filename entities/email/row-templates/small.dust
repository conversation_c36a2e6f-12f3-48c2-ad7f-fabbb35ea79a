<div class="card-small{?model.datePurged} purged{:else}{?model.pendingPurgeDate} scheduled-purge{/model.pendingPurgeDate}{/model.datePurged}">
	<div class="card-header">
		{@entityIcon entity=entity$/}
		{@entityLink entity=entity$ context=model}<div class="card-title">{formattedData.caseId|s}</div>{/entityLink}
		<div class="card-label" title="{formattedData.subject|s}">
	      {formattedData.subject|s}
	    </div>
	</div>
	{#highlightedFields}
		{>small-highlight-tmpl entityName="sys/email" ago=model.ago/}
	{/highlightedFields}
</div>