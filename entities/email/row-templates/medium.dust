<td data-title="{@resource key="type" /}" class="card-medium{?model.datePurged} purged{:else}{?model.pendingPurgeDate} scheduled-purge{/model.pendingPurgeDate}{/model.datePurged}">
    <div class="card-header">
		  <div class="card-header">
				{@entityIcon entity=entity$/}
				{@entityLink entity=entity$ context=model}<div class="card-title">{formattedData.caseId|s}</div>{/entityLink}
		    <div class="card-label">
				{formattedData.subject|s}
		    </div>
		  </div>
	</div>
	{#highlightedFields}
		{>medium-highlight-tmpl entityName="sys/email" ago="{model.ago}"/}
	{/highlightedFields}   
</td>