var _ = require('lodash');
var async = require('async');

module.exports = {
	setSenderName: function (seneca, rows, callback) {
		var names = _.map(rows, function (row) {
			return row.senderId__name || row.senderEmail;
		});
		callback(null, names);
	},
	setThreadCount: function(seneca, rows, callback) {
		async.mapLimit(rows, 5, function (row, callback) {
			if (!row.latestSent) return callback();
			if (!row.emailThreadId) return callback();
			var emailEnt = seneca.make$('sys/email');
			emailEnt.list$({
				limit$: 'ALL',
				emailThreadId: row.emailThreadId,
			}, function (err, emailsInThread) {
				if (err) return callback(err);
				var count = emailsInThread.length;
				var isCurrentEmailIncluded = row.id && !!_.find(emailsInThread, { id: row.id });
				if (!isCurrentEmailIncluded) {
					count++;
				}
				return callback(null, count);
			});
		}, callback);
	},
};