var permHelper = require('../../lib/core/permission-helper.js');

module.exports = permHelper.initialize()
	.filterMarkForPurge()
	.filterPurge()
	/**
	 * No user-role will have this permission so the bccRecipients will always will be filtered out
	 * This will not apply if email was sent by the logged-in user
	 */
	.filter({
		name: 'View Bcc Recipients as Sender',
		roles: ['view_bcc_as_sender'],
		actions: ['load', 'list'],
		conditions: [{
			attributes: {
				createdBy: '{!user.id}',
			},
		}],
		filters: {
			bccRecipients: false,
		},
	})
	.required({
		name: 'View Emails',
		roles: ['view_email'],
		actions: ['load', 'list', 'save_existing', 'remove'],
		conditions: [],
	})
	.required({
		name: 'View Unassigned Incoming Emails',
		roles: ['view_incoming_email_unassigned'],
		actions: ['load', 'list', 'save_existing', 'remove'],
		conditions: [{
			attributes: {
				caseId: null,
				direction: 'inbound',
			},
		}],
	})
	.required({
		name: 'Create Emails',
		roles: ['create_email'],
		actions: ['save_new'],
		conditions: [{
			attributes: {
				id: null,
			},
		}],
	})
	.required({
		name: 'Edit Unassigned Incoming Emails',
		roles: ['edit_incoming_email_unassigned'],
		actions: ['save_existing'],
		conditions: [{
			attributes: {
				'!id': null,
				caseId: null,
				direction: 'inbound',
			},
		}],
	})
	.required({
		name: 'Edit Assigned Emails',
		roles: ['edit_email_assigned'],
		actions: ['save_existing'],
		conditions: [{
			attributes: {
				'!id': null,
				'!caseId': null,
				sysActive: true,
			},
		}],
	})
	.required({
		name: 'Edit Outbound Emails',
		roles: ['edit_email_outbound'],
		actions: ['save_existing'],
		conditions: [{
			attributes: {
				'!id': null,
				direction: 'outbound',
				sysActive: true,
			},
		}],
	})
	.required({
		name: 'Remove incoming mail without caseId',
		roles: ['remove_incoming_email_unassigned'],
		actions: ['remove'],
		conditions: [{
			attributes: {
				caseId: null,
				direction: 'inbound',
			},
		}],
	})
	.required({
		name: 'Remove assigned mail',
		roles: ['remove_email_assigned'],
		actions: ['remove'],
		conditions: [{
			attributes: {
				'!caseId': null,
			},
		}],
	})
	.required({
		name: 'Remove outbound mail',
		roles: ['remove_email_outbound'],
		actions: ['remove'],
		conditions: [{
			attributes: {
				direction: 'outbound',
			},
		}],
	})
	.requireCaseLoadInheritance()
	.requireCaseSaveInheritance()
	.value();
