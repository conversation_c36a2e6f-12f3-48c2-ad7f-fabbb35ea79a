module.exports = [
	{
		type: 'group',
		caption: 'Emails',
		parentPermission: 'case',
		permission: 'email',
		options: [
			{
				permission: 'view_email',
				caption: 'View',
				tooltip: 'View Case emails',
				sequence: 2,
			},
			{
				permission: 'create_email',
				caption: 'Create',
				tooltip: 'Send emails from a Case',
				sequence: 1,
			},
			{
				caption: 'Edit',
				sequence: 3,
				disabled: true,
				permission: 'edit_email',
			},
			{
				caption: 'Remove',
				sequence: 4,
				disabled: true,
				permission: 'remove_email',
			},
		],
	},
	{
		type: 'group',
		caption: 'Incoming Emails',
		parentPermission: 'view_system_settings',
		permission: 'email_settings',
		options: [
			{
				caption: 'Create',
				sequence: 1,
				disabled: true,
				permission: 'create_incoming_email_unassigned',
			},
			{
				permission: 'view_incoming_email_unassigned',
				caption: 'View',
				tooltip: 'View incoming emails',
				sequence: 2,
			},
			{
				permission: 'edit_incoming_email_unassigned',
				caption: 'Edit',
				tooltip: 'Route incoming emails to their appropriate case',
				sequence: 3,
				dependencies: ['view_incoming_email_unassigned'],
			},
			{
				permission: 'remove_incoming_email_unassigned',
				caption: 'Remove',
				tooltip: 'Delete incoming emails',
				sequence: 4,
				dependencies: ['view_incoming_email_unassigned'],
			},
		],
	},
];
