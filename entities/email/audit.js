const refFields = [
	'subject',
];
const reference = {
	displayFields(data, auditModel) {
		return auditModel.hideCaseItemNumberReference() !== true
			? ['childNumber'].concat(refFields)
			: refFields;
	},
};
module.exports = {
	child: true,
	parentType: {
		base: 'sys',
		name: 'case',
	},
	parentFieldId: 'caseId',
	allowNavigateTo: true,
	cmd: {
		load: {
			'viewed': {
				options: {
					reference,
				},
			},
		},
		save: {
			'assigned_case': {
				condition: {
					composite: 'all',
					rules: [{
						path: 'diff.id.updatedValue',
						comparator: 'nexists',
					}, {
						path: 'diff.caseId__caseNumber',
						comparator: 'exists',
					}],
				},
				options: {
					reference,
				},
			},
			'outbound_email': {
				condition: {
					composite: 'all',
					rules: [{
						path: 'diff.direction',
						comparator: 'exists',
					}, {
						path: 'diff.direction.updatedValue',
						comparator: 'eq',
						value: 'outbound',
					}],
				},
				options: {
					reference,
				},
			},

			'inbound_email': {
				condition: {
					composite: 'all',
					rules: [{
						path: 'diff.direction',
						comparator: 'exists',
					}, {
						path: 'diff.direction.updatedValue',
						comparator: 'eq',
						value: 'inbound',
					}],
				},
				options: {
					reference,
				},
			},
			// This action shouldn't appear as an email update doesn't happen unless
			// it falls into one of the categories above.
			'updated': {
				options: {
					changes: {
						excludeFields: [
							'id',
							'skipWorkflows',
							'latestSent',
						],
					},
				},
			},
		},
		remove: {
			'deleted': {
				options: {
					reference,
				},
			},
		},
	},
};