# Dynamic Flags

Allows end user admins to create and manage custom `Flags` that can be tagged to records. Flags are pills containing text (similar to Confidential and Do Not Purge), and are used to identify and classify cases. Users with the appropriate permissions can set flags to a case using the case menu. Flags will appear beside the bookmark icon when set to a case.

> :information_source: **NOTE**: Flags are currently only designed to support the `case` entity.

## Usage

### Creation

Flags can be created and managed under Settings > Data > Flags. Define the flag name and description, select the flag background colour in a colour selector.

For each entity (e.g. case), flags with duplicated names cannot be created.

For each entity (e.g. case), the total number of flags has a limit of 10 by default.

### Permission

The permission to create and manage flags is under Settings > Data > Flags in the permission hierarchy.

After creating a Flag, following permissions will appear in the permission hierarchy to grant or deny the ability for a user to set or unset this flag in a case: Case > Edit > Flags, Case > Ownership > Case Owner > Edit > Flags, Case > Ownership > Investigative Team Member > Edit > Flags.

### Set/Unset

The flags set/unset options will be displayed in the case menu. If set, the flag will appear beside the bookmark icon. If unset, the flag will disappear beside the bookmark icon.

Additional Case fields have been added to capture "Last Flag Set", "Last Flag Set Date", "Last Flag Set By".

### Import/Export

This entity is included in "Configuration Import & Export".

## Extending

Extends `standard-config`.

> :warning: **WARNING**: This is a standardized platform entity that drives out of box functionality and it is not recommended to extend this entity.
