const extend = require('../extend.js');
const standardConfig = require('../standard-config.js');

module.exports = extend(standardConfig, {
	db: 'default',
	table: 'sys_flag',
	entity: {
		base: 'sys',
		name: 'flag',
	},
	caption: 'Flag',
	captionPlural: 'Flags',
	addCaption: 'Add Flag',
	newCaption: 'New Flag',
	gridDescriptorField: 'name',
	validation: require('./validation.js'),
	historyNav: require('./history-nav.js'),
	acl: require('./acl.js'),
	aclHierarchy: require('./acl-hierarchy.js'),
	configurationExport: true,
	exportTransformer: 'flag',
	configurationImport: true,
	importTransformer: 'flag',
	usageStatistics: require('./usage-statistics.js'),
	cacheBustGroups: require('./cache-bust-groups.js'),
	fields: [
		{
			field: 'entityName',
			// TODO: Update this to use something new that sets name not id
			type: 'entityPicklist',
			caption: 'Record Type',
			kind: 'editable',
			typeOptions: {
				filter: 'customFlagEntitiesFilter',
				valueField: 'name',
				canCreate: false,
				defaultSorting: false,
			},
		},
		{
			field: 'name',
			type: 'textbox',
			caption: 'Name',
			kind: 'editable',
			typeOptions: {
				charMaxTextbox: 30,
			},
		},
		{
			field: 'nameAsCode',
			type: 'textbox',
			caption: 'Name As Code',
			kind: 'hidden',
		},
		{
			field: 'description',
			type: 'textarea',
			typeOptions: {
				charMaxTextArea: 250,
			},
			caption: 'Description',
			kind: 'editable',
		},
		{
			field: 'color',
			type: 'color',
			kind: 'editable',
			caption: 'Color',
			cellTemplate(fs) {
				return fs.readFileSync(
					`${__dirname}/cell-templates/flag-color-cell-tmpl.dust`,
					'utf8',
				);
			},
		},
		{
			field: 'entStatusField',
			caption: 'Entity Status Field',
			type: 'textbox',
			kind: 'system',
		},
		{
			field: 'entDateField',
			caption: 'Entity Date Field',
			type: 'textbox',
			kind: 'system',
		},
		{
			field: 'entSetByField',
			caption: 'Entity Set By Field',
			type: 'textbox',
			kind: 'system',
		},
	],
});
