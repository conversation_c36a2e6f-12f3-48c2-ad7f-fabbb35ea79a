const permHelper = require('../../lib/core/permission-helper.js');

module.exports = permHelper.initialize()
	.required({
		name: 'View Flag Records',
		roles: ['view_flag_records'],
		actions: ['load', 'list', 'save_existing', 'remove'],
		conditions: [],
	})
	.required({
		name: 'View Flag Settings',
		roles: ['view_flag'],
		actions: ['save_new', 'save_existing', 'remove'],
		conditions: [],
	})
	.required({
		name: 'Create Flag',
		roles: ['create_flag'],
		actions: ['save_new'],
		conditions: [{
			attributes: {
				id: null,
			},
		}],
	})
	.required({
		name: 'Edit Flag',
		roles: ['edit_flag'],
		actions: ['save_new'],
		conditions: [{
			attributes: {
				'!id': null,
			},
		}],
	})
	.required({
		name: 'Remove Flag',
		roles: ['remove_flag'],
		actions: ['remove'],
		conditions: [],
	})
	.value();
