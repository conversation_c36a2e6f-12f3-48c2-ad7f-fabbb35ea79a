/* global $appData */
const $ = require('jquery');
const sharedUtils = require('../../../shared/utils.js')();
const BackboneEvents = require('../../../public/lib/backbone-events.js');

const appDataCategory = 'flags';

function setFlags(callback) {
	$.ajax({
		url: `${$appData.globalConfig.apiRoot}/flags`,
		method: 'GET',
		dataType: 'json',
		contentType: 'application/json',
	}).done((data) => {
		sharedUtils.updateObject($appData[appDataCategory], data);
		callback();
	});
}

module.exports = function flagsUpdateHandler(opts) {
	const utils = require('../../../public/lib/utils.js');
	const { patchData } = opts;
	// use patch data to patch $appData
	if (patchData) {
		utils.patch({
			appDataCategory,
			data: patchData,
			identifierProperties: ['id'],
		});
		BackboneEvents.trigger('flags-updated', opts);
	} else {
		// otherwise, $appData have to be loaded via requests
		setFlags(() => BackboneEvents.trigger('flags-updated', opts));
	}
};
