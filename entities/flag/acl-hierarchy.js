const permHelper = require('../../lib/core/permission-helper.js');

module.exports = [{
	type: 'group',
	caption: 'Flags',
	permission: 'view_flag_settings',
	parentPermission: 'view_data_settings',
	options: [
		{
			caption: 'View Flag Settings',
			tooltip: 'View the Flag settings page',
			sequence: 2,
			permission: 'view_flag',
		},
		{
			permission: 'create_flag',
			caption: 'Create',
			tooltip: 'Create a flag',
			dependencies: ['view_flag'],
			sequence: 1,
		},
		{
			type: 'group',
			permission: permHelper.getEditGroupPermissionCode('flag'),
			caption: 'Edit',
			dependencies: ['view_flag'],
			sequence: 3,
			options: [{
				permission: 'edit_flag',
				caption: 'Save',
				tooltip: 'Edit a flag',
				dependencies: ['view_flag'],
			}],
		},
		{
			permission: 'remove_flag',
			caption: 'Remove',
			tooltip: 'Delete a flag',
			dependencies: ['view_flag'],
			sequence: 4,
		},
	],
}];
