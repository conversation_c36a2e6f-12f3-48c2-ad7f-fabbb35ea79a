var extend = require('../extend.js');
var standardConfig = require('../standard-config.js');

module.exports = extend(standardConfig, {
	db: 'default',
	search: true,
	table: 'isight_entity',
	entity: {
		base: 'isight',
		name: 'entity',
	},
	caption: 'Form',
	captionPlural: 'Forms',
	addCaption: 'Add Form',
	newCaption: 'New Form',
	acl: require('./acl.js'),
	computeFunctions: require('./compute-functions.js'),
	model: function(){
		return require('../../public/models/entity-model.js');
	},
	collection: function(){
		return require('../../public/collections/entity-collection.js');
	},
	grids: require('./grids.js'),
	historyNav: require('./history-nav.js'),
	validation: require('./validation.js'),
	rules: require('./rules.js'),
	fields: [
		{
			field: 'name',
			type: 'textbox',
			caption: 'Entity Name',
			kind: 'system',
			dbIndex: true,
		},
		{
			field: 'base',
			type: 'code',
			caption: 'Entity Base',
			kind: 'system',
			dbIndex: true,
		},
		{
			field: 'canon',
			type: 'textbox',
			caption: 'Entity Canon',
			kind: 'computed',
			kindOptions: {
				computeFunction: 'computeEntityCanon',
			},
		},
		{
			field: 'es_index',
			type: 'code',
			caption: 'Elasticsearch Index',
			kind: 'system',
		},
		{
			field: 'caption',
			type: 'textbox',
			caption: 'Caption',
			kind: 'system',
		},
		{
			field: 'createdBy',
			type: 'id',
			kind: 'system',
		},
		{
			field: 'lastUpdatedBy',
			type: 'id',
			kind: 'system',
		},
		{
			field: 'deletedBy',
			type: 'id',
			kind: 'system',
		},
		{
			field: 'caption',
			type: 'textbox',
			kind: 'custom',
			kindOptions: {
				flags: {
					audit: false,
					schema: false,
					search: false,
					apiWritable: true,
					apiExternalWritable: false,
					computedOnSave: false,
					computedOnRead: false,
					formVisible: true,
					gridVisible: false,
					gridSortable: false,
					searchVisible: false,
					formattedData: false,
					gridExportable: false,
					reportable: false,
				},
			},
			caption: 'Caption',
			typeOptions: {
				charMaxTextbox: 60,
			},
		},
		{
			field: 'captionPlural',
			type: 'textbox',
			kind: 'custom',
			kindOptions: {
				flags: {
					audit: false,
					schema: false,
					search: false,
					apiWritable: true,
					apiExternalWritable: false,
					computedOnSave: false,
					computedOnRead: false,
					formVisible: true,
					gridVisible: false,
					gridSortable: false,
					searchVisible: false,
					formattedData: false,
					gridExportable: false,
					reportable: false,
				},
			},
			caption: 'Caption Plural',
			typeOptions: {
				charMaxTextbox: 62,
			},
		},
		{
			field: 'layoutId',
			caption: 'Layout Id',
			type: 'id',
			kind: 'system',
		},
		{
			field: 'copiedLayoutId',
			caption: 'Copied Layout Id',
			type: 'id',
			kind: 'system',
		},
		{
			field: 'parentEntityId',
			caption: 'Parent Form',
			type: 'entityPicklist',
			kind: 'editable',
			typeOptions: {
				canCreate: false,
				defaultSorting: false,
			},
		},
		{
			field: 'dynamic',
			caption: 'Dynamic',
			type: 'yesno',
			kind: 'system',
		},
		{
			field: 'type',
			type: 'picklistSelectize',
			kind: 'editable',
			caption: 'Type',
			typeOptions: {
				picklistName: 'entity_types',
			},
		},
	],
	// TODO: Update isight_entity's logic to point to new tables for dynamic entities
	cacheBustGroups: require('./cache-bust-groups.js'),
	queryFilter(opts) {
		const { query, enabledFeatures } = opts;
		const subQueries = [standardConfig.queryFilter(opts)];
		if (!enabledFeatures.includes('nonUserCollaboration')) {
			subQueries.push(query.is_not('name', 'request'));
		}
		return query.and(subQueries);
	},
});
