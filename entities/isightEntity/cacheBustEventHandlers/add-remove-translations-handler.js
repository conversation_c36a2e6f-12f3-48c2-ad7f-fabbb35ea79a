/* global $appData */
const $ = require('jquery');
const _ = require('lodash');
const sharedUtils = require('../../../shared/utils.js')();
const BackboneEvents = require('../../../public/lib/backbone-events.js');

function updateAppDataTranslations(translationsArray) {
	const translationsMap = sharedUtils.convertTranslationArrayToObject(translationsArray);
	_.forEach(translationsMap, (translations, canon) => {
		if (_.isNil($appData.translations[canon])) {
			$appData.translations[canon] = {};
		}
		sharedUtils.updateObject($appData.translations[canon],
			translationsMap[canon]);
	});
}

function setTranslations(callback) {
	$.ajax({
		url: `${$appData.globalConfig.apiRoot}/list_dynamic_entity_translations`,
		method: 'GET',
		dataType: 'json',
		contentType: 'application/json',
	})
		.done((data) => {
			if (!_.isEmpty(data)) {
				updateAppDataTranslations(data);
			}
			callback();
		});
}

module.exports = function handler(opts) {
	const { patchData } = opts;
	// use patch data to patch $appData
	if (patchData) {
		const { locale } = $appData.user;
		const patchDataForLocale = _.filter(patchData, data => data.locale === locale);
		updateAppDataTranslations(patchDataForLocale);
		BackboneEvents.trigger('dynamic-entity-translations-updated', opts);
	} else {
		// otherwise, $appData have to be loaded via requests
		setTranslations(() => BackboneEvents.trigger('dynamic-entity-translations-updated', opts));
	}
};
