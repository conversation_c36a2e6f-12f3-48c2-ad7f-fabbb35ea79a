var permHelper = require('../../lib/core/permission-helper.js');

module.exports = permHelper.initialize()
	.required({
		name: 'View caseiq entities',
		roles: ['view_isight_entity'],
		actions: ['load', 'list', 'save_existing', 'remove'],
		conditions: [],
	})
	.required({
		name: 'Create caseiq entities',
		roles: ['create_isight_entity'],
		actions: ['save_new'],
		conditions: [{
			attributes: {
				id: null,
			},
		}],
	})
	.required({
		name: 'Edit caseiq entities',
		roles: ['edit_isight_entity'],
		actions: ['save_new'],
		conditions: [{
			attributes: {
				'!id': null,
			},
		}],
	})
	.required({
		name: 'Remove caseiq entities',
		roles: ['remove_isight_entity'],
		actions: ['remove'],
		conditions: [],
	})
	.value();