var permHelper = require('../../lib/core/permission-helper.js');

module.exports = permHelper.initialize()
	.required({
		name: 'View Standard Responses',
		roles: ['view_standard_response'],
		actions: ['load', 'list', 'save_existing', 'remove'],
		conditions: [],
	})
	.required({
		name: 'Create Standard Responses',
		roles: ['create_standard_response'],
		actions: ['save_new'],
		conditions: [{
			attributes: {
				id: null,
			},
		}],
	})
	.required({
		name: 'Edit Standard Responses',
		roles: ['edit_standard_response'],
		actions: ['save_new'],
		conditions: [{
			attributes: {
				'!id': null,
			},
		}],
	})
	.required({
		name: 'Remove Standard Responses',
		roles: ['remove_standard_response'],
		actions: ['remove'],
		conditions: [],
	})
	.value();