const permHelper = require('../../lib/core/permission-helper.js');

module.exports = [{
	type: 'group',
	caption: 'Standard Response',
	permission: 'view_standard_response_settings',
	parentPermission: 'view_data_settings',
	options: [
		{
			caption: 'View',
			sequence: 2,
			disabled: true,
			permission: 'view_standard_response',
		},
		{
			permission: 'create_standard_response',
			caption: 'Create',
			tooltip: 'Create a standard email response',
			sequence: 1,
		},
		{
			type: 'group',
			permission: permHelper.getEditGroupPermissionCode('standard_response'),
			caption: 'Edit',
			sequence: 3,
			options: [{
				permission: 'edit_standard_response',
				caption: 'Save',
				tooltip: 'Edit a standard email response',
			}],
		},
		{
			permission: 'remove_standard_response',
			caption: 'Remove',
			tooltip: 'Delete a standard email response',
			sequence: 4,
		},
	],
}];
