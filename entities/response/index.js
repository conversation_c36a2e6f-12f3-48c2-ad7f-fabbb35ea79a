var extend = require('../extend.js');
var standardConfig = require('../standard-config.js');

module.exports = extend(standardConfig, {
	db: 'default',
	table: 'sys_response',
	entity: {
		base: 'sys',
		name: 'response',
	},
	caption: 'Standard Response',
	captionPlural: 'Standard Responses',
	addCaption: 'Add Standard Response',
	newCaption: 'New Standard Response',
	gridDescriptorField: 'name',
	acl: require('./acl.js'),
	aclHierarchy: require('./acl-hierarchy.js'),
	grids: require('./grids.js'),
	validation: require('./validation.js'),
	historyNav: require('./history-nav.js'),
	usageStatistics: require('./usage-statistics.js'),
	audit: require('./audit.js'),
	configurationExport: true,
	importTransformer: 'response',
	model: function(){
		return require('../../public/models/standard-response-model.js');
	},
	collection: function(){
		return require('../../public/collections/standard-responses-collection.js');
	},
	view: function(){
		/*eslint-disable max-len*/
		return require('../../public/views/settings/data/standard-response/standard-response-details-view.js');
	},
	fields: [
		{
			field: 'name',
			type: 'textbox',
			kind: 'editable',
			caption: 'Response Name',
		},
		{
			field: 'body',
			type: 'texteditor',
			kind: 'editable',
			caption: 'Response Distribution',
		},
		{
			field: 'type',
			type: 'code',
			kind: 'hidden',
			caption: 'Type',
		},
		{
			field: 'locale',
			type: 'picklist',
			typeOptions: {
				picklistName: 'supported_languages',
				filter: 'existingLanguagesFilter',
			},
			kind: 'editable',
			caption: 'Locale',
			dbIndex: true,
		},
		{
			field: 'rank',
			type: 'number',
			kind: 'editable',
			caption: 'List Sequence',
		},
	],
});
