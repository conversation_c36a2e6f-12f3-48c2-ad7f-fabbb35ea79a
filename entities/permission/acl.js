var permHelper = require('../../lib/core/permission-helper.js');

module.exports = permHelper.initialize()
	.required({
		name: 'View Permission',
		roles: ['view_permission'],
		actions: ['load', 'list', 'save_existing', 'remove'],
		conditions: [],
	})
	.required({
		name: 'Create Permission',
		roles: ['create_permission'],
		actions: ['save_new'],
		conditions: [{
			attributes: {
				id: null,
			},
		}],
	})
	.required({
		name: 'Edit Permission',
		roles: ['edit_permission'],
		actions: ['save_new'],
		conditions: [{
			attributes: {
				'!id': null,
			},
		}],
	})
	.required({
		name: 'Remove Permission',
		roles: ['remove_permission'],
		actions: ['remove'],
		conditions: [],
	})
	.value();