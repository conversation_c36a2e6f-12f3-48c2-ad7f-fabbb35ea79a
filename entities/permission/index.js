var extend = require('../extend.js');
var standardConfig = require('../standard-config.js');
/**
 * disable	dynamic	->	combinations
 * false	false	-> deleted permission
 * true		false	-> user specific permission/
 * true		true	->
 * false	true	-> active permission
 */
module.exports = extend(standardConfig, {
	db: 'default',
	table: 'sys_permission',
	caption: 'Permission',
	captionPlural: 'Permissions',
	addCaption: 'Add Permission',
	newCaption: 'New Permission',
	entity: {
		base: 'sys',
		name: 'permission',
	},
	acl: require('./acl.js'),
	importTransformer: 'permission',
	search: false,
	fields: [
		// system field to determine the parents.
		// can be set by options or parentPermission
		{
			field: 'parentId',
			type: 'id',
			caption: 'Parent Id',
			kind: 'system',
		},
		// only for ui to render as a group
		{
			field: 'isGroup',
			type: 'yesno',
			caption: 'Is Group',
			kind: 'system',
		},
		// permission name
		{
			field: 'code',
			type: 'textbox',
			caption: 'Code',
			kind: 'system',
			dbIndex: true,
		},
		// to disable the permission from front-end
		{
			field: 'disabled',
			type: 'yesno',
			caption: 'Disabled',
			kind: 'system',
		},
		{
			field: 'sequence',
			type: 'number',
			caption: 'Sequence',
			kind: 'system',
		},
		// ui->dynamic: true. back-end->dynamic: false
		{
			field: 'selectedByDefault',
			type: 'yesno',
			caption: 'Selected By Default',
			kind: 'system',
		},
		// permissions that this permission depends on
		{
			field: 'dependencies',
			type: 'code[]',
			caption: 'Dependencies',
			kind: 'system',
		},
		// disables in front end. standard and blacklist perms given to every user are not dynamic
		{
			field: 'dynamic',
			type: 'yesno',
			caption: 'Dynamic',
			kind: 'system',
		},
		// blacklist permissions
		{
			field: 'restricted',
			type: 'yesno',
			caption: 'Restricted',
			kind: 'system',
		},
		// would prefer to call this field 'dynamic' but the keyword is already taken. this field
		// indicates which permissions are dynamically and specially created for this specific app.
		// exmaples are permission for workflows or dynamic entities.
		{
			field: 'onDemand',
			type: 'yesno',
			caption: 'On Demand',
			kind: 'system',
		},
		{
			field: 'features',
			type: 'textbox[]',
			caption: 'Features',
			kind: 'hidden-searchable',
		},
	],
	joins: [
		{
			referenceField: 'parentId',
			table: 'sys_permission',
			fields: [
				'code',
				'onDemand',
			],
		},
	],
});
