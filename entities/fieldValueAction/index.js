const extend = require('../extend.js');
const standardActionConfig = require('../standard-action-config.js');

module.exports = extend(standardActionConfig, {
	table: 'sys_field_value_action',
	entity: {
		base: 'sys',
		name: 'field_value_action',
	},
	workflowActionConfig: {
		formName: 'set-field-value-action',
		aclRoles: {
			createACLRole: 'agent',
			editACLRole: 'agent',
			deleteACLRole: 'agent',
		},
		unsupportedEntities: ['sys/email'],
	},
	caption: 'Set Field Value',
	captionPlural: 'Set Field Values',
	addCaption: 'Add Set Field Value',
	newCaption: 'New Set Field Value',
	rules: require('./rules.js'),
	audit: require('./audit.js'),
	validation: require('./validation.js'),
	usageStatistics: require('./usage-statistics.js'),
	model() { return require('./model.js'); },
	view() {
		return require('../../public/views/settings/workflow/rules/field-value-action-view.js');
	},
	fields: [
		{
			field: 'fieldName',
			type: 'fieldPicklist',
			typeOptions: {
				ignoreAttributesNotInList: true,
				entityNameField: 'targetEntity',
				excludeType: ['user', 'user[]', 'primaryParty', 'party', 'party[]', 'file[]', 'imageCropper'],
				filterFlag: ['apiWritable', 'formVisible', 'searchVisible'],
				excludeDataFormFields: true,
				excludedFields: [
					'cancelDate',
					'cancelReason',
					'reassignReason',
					'acceptTermsAndConditions',
					'restoreReason',
					'purgeReason',
					'dateSubmitted',
					'canceled',
					'caseId',
					'kind',
					'templateId',
					'templateLocale',
					'status',
					'primaryEntity',
					'due',
					'startDate',
					'endDate',
					'allDayEvent',
					'duration',
					'fileShared',
					'generatedFileType',
					'packetId',
				],
				excludedFieldsByEntityName: {
					'sys/appointment': ['duration', 'allDayEvent'],
					'sys/attachment': [
						'externalRecord',
						'shouldAddPageNumbers',
						'shouldAddTableOfContents',
						'caseFilesInclusionKind',
						'documentGenerationMethod',
					],
					'sys/case': [
						'externalRecord',
						'aiBlocksCertified',
						'reportedAnonymously',
					],
					'sys/party': [
						'externalRecord',
						'portalReporterParty',
					],
					'sys/note': ['externalRecord'],
				},
				excludedFieldsDynamicEntities: [
					'externalRecord',
				],
				searchableFields: false,
			},
			kind: 'editable',
			caption: 'Field',
		},
		{
			field: 'isExpression',
			type: 'yesno',
			caption: 'Set the value using an expression?',
			kind: 'editable',
		},
		{
			field: 'expression',
			type: 'expression',
			typeOptions: {
				entityField: 'targetEntity',
			},
			caption: 'ISEL Expression',
			kind: 'custom',
			kindOptions: {
				flags: {
					audit: false,
					schema: true,
					search: true,
					apiWritable: true,
					apiExternalWritable: false,
					computedOnSave: false,
					computedOnRead: false,
					formVisible: false,
					gridVisible: true,
					gridSortable: false,
					searchVisible: false,
					formattedData: true,
					gridExportable: false,
					reportable: false,
				},
			},
		},
		{
			field: 'fieldType',
			type: 'textbox',
			caption: 'Field Type',
			kind: 'hidden-editable',
		},
		{
			field: 'fieldValues',
			type: 'code[]',
			caption: 'Value',
			kind: 'hidden-editable',
		},
		{
			field: 'fieldValuesFormatted',
			type: 'code[]',
			caption: 'Value',
			kind: 'editable',
		},
		{
			field: 'joinData',
			type: 'json[]',
			caption: 'Join Data',
			kind: 'hidden-editable',
		},
	],
});
