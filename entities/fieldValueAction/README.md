# Set Field Value Action

The Set Field Value action updates a record’s field value with a value set by the user when a record’s criteria match the rule criteria. Set Field Value rules can be created and managed under Settings > Workflow > Rules.

After selecting the record type, specify the record field to change. Set the value that will be used to update the field. The selection method options include dropdowns of pre-set options, textboxes, radio buttons etc. depending on the type of record field chosen.
