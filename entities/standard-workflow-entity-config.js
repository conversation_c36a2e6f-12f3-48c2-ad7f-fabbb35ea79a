/**
 * This config is for entities that support custom workflows (e.g. case).
 */
module.exports = {
	fields: [
		{
			field: 'lastWorkflowId',
			type: 'workflowName',
			kind: 'system',
			caption: 'Last Workflow',
			showOnHotline: true,
		},
		{
			field: 'lastWorkflowStateId',
			type: 'state',
			kind: 'system',
			caption: 'Last Workflow Status',
			cellTemplate(fs) {
				return fs.readFileSync(
					`${__dirname}/../public/templates/cell-templates/last-workflow-state-id-cell-tmpl.dust`,
					'utf8',
				);
			},
			showOnHotline: true,
		},
		{
			field: 'lastWorkflowTransitionBy',
			type: 'user',
			kind: 'system',
			caption: 'Last Workflow Step By',
			showOnHotline: true,
		},
		{
			field: 'lastWorkflowTransitionDate',
			type: 'datetime',
			kind: 'system',
			caption: 'Last Workflow Step Date',
			showOnHotline: true,
		},
		{
			field: 'lastWorkflowTransitionReason',
			type: 'textbox',
			kind: 'system',
			caption: 'Last Workflow Step Reason',
		},
		{
			field: 'workflowData',
			type: 'json',
			kind: 'hidden',
			caption: 'Workflow Data',
			showOnHotline: true,
		},
		{
			field: 'workflowStatuses',
			type: 'textbox',
			kind: 'system',
			caption: 'Workflow Statuses',
			showOnHotline: true,
		},
	],
};
