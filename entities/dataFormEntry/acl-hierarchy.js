const permHelper = require('../../lib/core/permission-helper.js');

module.exports = [{
	type: 'group',
	caption: 'Data Form Entries',
	permission: 'view_data_form_entry_settings',
	parentPermission: 'view_data_settings',
	options: [
		{
			permission: 'create_data_form_entry',
			caption: 'Create',
			tooltip: 'Add Data Form Entries',
			sequence: 1,
		},
		{
			permission: 'view_data_form_entry',
			caption: 'View',
			tooltip: 'View Data Form Entries',
			sequence: 2,
		},
		{
			type: 'group',
			permission: permHelper.getEditGroupPermissionCode('data_form_entry'),
			caption: 'Edit',
			dependencies: ['view_data_form_entry'],
			sequence: 3,
			options: [{
				permission: 'edit_data_form_entry',
				caption: 'Save',
				tooltip: 'Edit Data Form Entries',
				dependencies: ['view_data_form_entry'],
			}],
		},
		{
			permission: 'remove_data_form_entry',
			caption: 'Remove',
			tooltip: 'Delete Data Form Entries',
			sequence: 4,
			dependencies: ['view_data_form_entry'],
		},
	],
}];
