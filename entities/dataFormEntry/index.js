const extend = require('../extend.js');
const standardConfig = require('../standard-config.js');

module.exports = extend(standardConfig, {
	db: 'default',
	search: true,
	customForm: false,
	table: 'sys_data_form_entry',
	entity: {
		base: 'sys',
		name: 'data_form_entry',
	},
	validation: require('./validation.js'),
	allowAdvancedSearch: false,
	allowQuickSearch: false,
	allowExternalSearch: true,
	allowPurge: false,
	dataExport: false,
	report: false,
	dynamicDataStore: true,
	caption: 'Data Form Entry',
	captionPlural: 'Data Form Entries',
	addCaption: 'Add Data Form Entry',
	newCaption: 'New Data Form Entry',
	acl: require('./acl.js'),
	aclHierarchy: require('./acl-hierarchy.js'),
	model() {
		return require('../../public/models/dynamic-data-model.js');
	},
	view() {
		return require('../../public/views/settings/data/data-form-entries/data-form-entry-details-view.js');
	},
	esDefaultFilters() {
		const query = this.query('es');
		return query.and([
			query.is_not('sysActive', false),
			query.is_not('sysSubmitted', false),
		]).toQuery();
	},
	historyNav: [
		{ from: [], to: '/settings/data/data-form-entries' },
	],
	fields: [
		{
			field: 'entityId',
			caption: 'Entity Id',
			type: 'id',
			kind: 'hidden-editable',
			excludeFromRedact: true,
		},
	],
	joins: [
		{
			referenceField: 'entityId',
			table: 'isight_dynamic_entity',
			fields: [
				'name',
				'allowOnPortal',
				'allowOnHotline',
			],
		},
	],
});
