const _ = require('lodash');
const permHelper = require('../../lib/core/permission-helper.js');

module.exports = permHelper.initialize()
	.filterMarkForPurge()
	.filterPurge()
	.filter({
		name: 'View Static Data Form Entry Fields',
		roles: ['view_static_data_form_entry_fields'],
		control: 'filter',
		actions: ['list', 'load'],
		conditions: [],
		filters: {
			// Filter out all static fields, excluding a select few necessary for operation
			fn(obj, context) {
				const staticFieldWhitelist = [
					'id',
					'entityId',
					'entityId__name',
				];
				const entDefFields = context?.entDef?.fields();
				const filter = {};
				_.each(entDefFields, (fieldDef) => {
					if (fieldDef.kind !== 'dynamic' && !_.includes(staticFieldWhitelist, fieldDef.field)) {
						filter[fieldDef.field] = false;
					}
				});
				return filter;
			},
		},
	})
	// Users with the `view_data_form_entry` permission can view entries for all data forms. Those
	// with a permission specific to the data form can only view entries belonging to that form.
	.required({
		name: 'View Data Form Entries',
		roles: ['view_data_form_entry'],
		actions: ['list', 'load', 'save_existing', 'remove'],
		conditions: [{
			attributes: {
				'!id': null,
				sysActive: true,
			},
		}, {
			fn: (obj, context) => {
				const { entityId } = obj;
				const roles = context.user?.perm?.roles;
				if (!roles || roles.length === 0) return { ok: true };
				// Don't apply this ACL rule if the user has the data form permission
				if (_.includes(roles, `dataForm-view:${entityId}`)) return { ok: false };
				// Don't apply this ACL rule if user is external, and data form is allowOnPortal: true
				if (_.includes(roles, 'external_user') && obj.entityId__allowOnPortal === true) return { ok: false };
				// Don't apply this ACL rule if user is a hotline agent,
				// and data form is allowOnHotline: true
				if (_.includes(roles, 'hotline_agent') && obj.entityId__allowOnHotline === true) return { ok: false };
				return { ok: true };
			},
			esFilter(context, attributePrefix = '') {
				const roles = context.user?.perm?.roles;
				const accessibleEntityIds = [];
				// Grant access to data forms with allowOnPortal: true to external users
				if (_.includes(roles, 'external_user')) {
					return {
						bool: {
							must_not: [{ term: { entityId__allowOnPortal: true } }],
						},
					};
				}
				// Grant access to data forms with allowOnHotline: true to hotline agents
				if (_.includes(roles, 'hotline_agent')) {
					return {
						bool: {
							must_not: [{ term: { entityId__allowOnHotline: true } }],
						},
					};
				}
				_.each(roles, (permissionCode) => {
					if (permissionCode?.includes('dataForm-view:')) {
						accessibleEntityIds.push(permissionCode.split(':')[1]);
					}
				});
				// Restrict access to all data form entries if the user doesn't have access to
				// any of the data forms
				if (accessibleEntityIds.length === 0) {
					return {
						bool: {
							must: [{
								exists: {
									field: 'id',
								},
							}],
						},
					};
				}
				// Selectively grant access to the data form entries based on which data forms
				// the user has access to
				const esFilter = { bool: { must_not: [] } };
				_.each(accessibleEntityIds, (entityId) => {
					const filterTerm = { term: { 'entityId._exact': entityId } };
					esFilter.bool.must_not.push(filterTerm);
				});
				return esFilter;
			},
			selectedFields(opts, callback) {
				return callback(null, ['id', 'entityId']);
			},
		}],
	})
	.required({
		name: 'Create Data Form Entries',
		roles: ['create_data_form_entry'],
		actions: ['save_new'],
		conditions: [{
			attributes: {
				id: null,
			},
		}],
	})
	.required({
		name: 'Edit Data Form Entries',
		roles: ['edit_data_form_entry'],
		actions: ['save_existing'],
		conditions: [{
			attributes: {
				'!id': null,
			},
		}],
	})
	.required({
		name: 'Remove Data Form Entries',
		roles: ['remove_data_form_entry'],
		actions: ['remove'],
		conditions: [],
	})
	.value();
