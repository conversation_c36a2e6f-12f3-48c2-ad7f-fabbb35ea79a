const permHelper = require('../../lib/core/permission-helper.js');

module.exports = [{
	type: 'group',
	parentPermission: 'case',
	caption: 'Comments',
	permission: 'comment',
	options: [
		{
			permission: 'view_comment',
			caption: 'View',
			tooltip: 'View Comments',
			sequence: 2,
		},
		{
			permission: 'create_comment',
			caption: 'Create',
			tooltip: 'Add Comments',
			sequence: 1,
		},
		{
			type: 'group',
			caption: 'Edit',
			sequence: 3,
			disabled: true,
			permission: permHelper.getEditGroupPermissionCode('comment'),
			options: [{
				caption: 'Save',
				disabled: true,
				permission: 'edit_comment',
			}],
		},
		{
			permission: 'remove_comment',
			caption: 'Remove',
			tooltip: 'Delete Comments',
			sequence: 4,
			dependencies: ['view_comment'],
		},
	],
}];
