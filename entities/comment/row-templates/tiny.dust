<div class="card-tiny{?model.datePurged} purged{:else}{?model.pendingPurgeDate} scheduled-purge{/model.pendingPurgeDate}{/model.datePurged}">
    {@entityLink entity=entity$ context=model target="_blank"}

        <div class="card-header">
            {@entityIcon entity=entity$/}
            <div class="card-title" data-toggle="tooltip" data-placement="top"
                 title='{@resource groupName="sys/comment" subgroupName="general" key="name"/} {@resource key="_on"/} {formattedData.parentType|s} #: {formattedData.parentNumber|s}'>
                {@resource groupName="sys/comment" subgroupName="general" key="name"/}
            </div>
            <i class="fa fa-external-link external-link-icon" aria-hidden="true"></i>
            <br/>
        </div>
    {/entityLink}
</div>
