var permHelper = require('../../lib/core/permission-helper.js');

module.exports = permHelper.initialize()
	.filterMarkForPurge()
	.filterPurge()
	.required({
		name: 'View Comments',
		roles: ['view_comment'],
		actions: ['load', 'list', 'save_existing', 'remove'],
		conditions: [],
	})
	.required({
		name: 'Create Comments',
		roles: ['create_comment'],
		actions: ['save_new'],
		conditions: [{
			attributes: {
				id: null,
			},
		}],
	})
	.required({
		name: 'Edit Comments',
		roles: ['edit_comment'],
		actions: ['save_new'],
		conditions: [{
			attributes: {
				'!id': null,
			},
		}],
	})
	.required({
		name: 'Remove Comments',
		roles: ['remove_comment'],
		actions: ['remove'],
		conditions: [],
	})
	.requireCaseLoadInheritance()
	.required({
		name: 'Inherit case save acl when parent is not note/todo',
		roles: ['bypass_inherited_acl'],
		control: 'required',
		actions: ['save_new', 'save_existing', 'remove'],
		conditions: [{
			attributes: {
				'!caseId': null,
				'!parentType': ['todo', 'note'],
			},
		}, 'sys/case::{caseId}::save'],
	})
	.required({
		name: 'Inherit note load acl',
		roles: ['bypass_inherited_acl'],
		actions: ['load', 'list'],
		conditions: [{
			attributes: {
				parentType: 'note',
			},
		}, 'sys/note::{parentId}::load'],
	})
	.required({
		name: 'Inherit note save acl',
		roles: ['bypass_inherited_acl'],
		actions: ['save_new', 'save_existing', 'remove'],
		conditions: [{
			attributes: {
				parentType: 'note',
			},
		}, 'sys/note::{parentId}::save'],
	})
	.required({
		name: 'Inherit todo load acl',
		roles: ['bypass_inherited_acl'],
		actions: ['load', 'list'],
		conditions: [{
			attributes: {
				parentType: 'todo',
			},
		}, 'sys/todo::{parentId}::load'],
	})
	.required({
		name: 'Inherit todo save acl',
		roles: ['bypass_inherited_acl'],
		actions: ['save_new', 'save_existing', 'remove'],
		conditions: [{
			attributes: {
				parentType: 'todo',
			},
		}, 'sys/todo::{parentId}::save'],
	})
	.value();
