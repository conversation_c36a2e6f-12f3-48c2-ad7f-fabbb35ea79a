module.exports = {
	child: true,
	parentType: {
		base: 'sys',
		name: 'case',
	},
	parentFieldId: 'caseId',
	allowNavigateTo: true,
	navigateTo: {
		id: 'parentId',
		type: 'parentType',
	},
	cmd: {
		save: {
			'created': {
				options: {
					changes: {
						displayFields: ['details'],
					},
					reference: {
						displayFields: ['parentType', 'parentNumber'],
					},
				},
			},
		},
		remove: {
			'deleted': {
				options: {
					reference: {
						displayFields: ['parentType', 'parentNumber'],
					},
				},
			},
		},
	},
};
