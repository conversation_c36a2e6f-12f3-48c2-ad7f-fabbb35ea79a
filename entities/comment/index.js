var extend = require('../extend.js');
var standardConfig = require('../standard-config.js');

module.exports = extend(standardConfig, {
	db: 'default',
	table: 'sys_comment',
	entity: {
		base: 'sys',
		name: 'comment',
	},
	caption: 'Comment',
	captionPlural: 'Comments',
	addCaption: 'Add Comment',
	newCaption: 'New Comment',
	gridDescriptorField: 'details',
	acl: require('./acl.js'),
	aclHierarchy: require('./acl-hierarchy.js'),
	audit: require('./audit.js'),
	grids: require('./grids.js'),
	validation: require('./validation.js'),
	icon: 'fa-comment',
	includeInDataDictionary: true,
	dataExport: true,
	allowPurge: true,
	report: false,
	excludeFromAggregation: true,
	usageStatistics: require('./usage-statistics.js'),
	model: function(){
		return require('../../public/models/comment-model.js');
	},
	collection: function(){
		return require('../../public/collections/comments-collection.js');
	},
	view: function(){
		return require('../../public/views/comment/editable-comment-view.js');
	},
	parents: [
		{
			entity: {
				base: 'sys',
				name: 'note',
			},
			field: 'parentId',
			filter: {
				parentType: 'note',
			},
		},
		{
			entity: {
				base: 'sys',
				name: 'todo',
			},
			field: 'parentId',
			filter: {
				parentType: 'todo',
			},
		},
		{
			entity: {
				base: 'sys',
				name: 'case',
			},
			field: 'caseId',
			reportable: true,
		},
	],
	esDefaultFilters: function(){
		var query = this.query('es');
		return query.and([
			query.is_not('caseId__canceled', true),
			query.is_not('sysActive', false),
			query.is_not('sysSubmitted', false),
		]).toQuery();
	},
	rowTemplates: {
		small: function(){
			return require('./row-templates/small.dust');
		},
		tiny: function(){
			return require('./row-templates/tiny.dust');
		},
	},
	fields: [
		{
			field: 'parentId',
			type: 'id',
			kind: 'custom',
			kindOptions: {
				flags: {
					audit: true,
					schema: true,
					search: true,
					apiWritable: true,
					apiExternalWritable: false,
					computedOnSave: false,
					computedOnRead: false,
					formVisible: false,
					gridVisible: false,
					gridSortable: false,
					searchVisible: false,
					formattedData: true,
					gridExportable: false,
					reportable: false,
				},
			},
			dbIndex: true,
			caption: 'Parent Id',
		},
		{
			field: 'parentType',
			type: 'code',
			caption: 'Parent Type',
			kind: 'custom',
			kindOptions: {
				flags: {
					audit: true,
					schema: true,
					search: true,
					apiWritable: true,
					apiExternalWritable: false,
					computedOnSave: false,
					computedOnRead: false,
					formVisible: false,
					gridVisible: false,
					gridSortable: false,
					searchVisible: false,
					formattedData: true,
					gridExportable: false,
					reportable: false,
				},
			},
			dbIndex: true,
		},
		{
			field: 'parentNumber',
			type: 'textbox',
			caption: 'Parent #',
			kind: 'editable',
		},
		{
			field: 'details',
			type: 'textarea',
			caption: 'Comment',
			kind: 'editable',
		},
		{
			field: 'caseId',
			type: 'case',
			caption: 'Case',
			kind: 'editable',
		},
	],
	joins: [
		{
			referenceField: 'parentId',
			table: 'sys_todo',
			fields: [
				'responsible',
			],
		},
	],
});
