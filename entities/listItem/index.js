var extend = require('../extend.js');
var standardConfig = require('../standard-config.js');

module.exports = extend(standardConfig, {
	db: 'default',
	table: 'sys_listitem',
	entity: {
		base: 'sys',
		name: 'listItem',
	},
	caption: 'Picklist',
	captionPlural: 'Picklists',
	addCaption: 'Add Picklist',
	newCaption: 'New Picklist',
	gridDescriptorField: 'value',
	acl: require('./acl.js'),
	aclHierarchy: require('./acl-hierarchy.js'),
	computeFunctions: require('./compute-functions.js'),
	historyNav: require('./history-nav.js'),
	grids: require('./grids.js'),
	validation: require('./validation.js'),
	rules: require('./rules.js'),
	configurationImport: true,
	importTransformer: 'listItem',
	copyTransformer: 'list_item',
	customDependencies: ['sys/list'],
	audit: require('./audit.js'),
	usageStatistics: require('./usage-statistics.js'),
	model: function(){
		return require('../../public/models/picklist-item-model.js');
	},
	collection: function(){
		return require('../../public/collections/picklists-collection.js');
	},
	view: function(){
		return require('../../public/views/settings/picklist-type/editable-picklist-item-view.js');
	},
	joins: [
		{
			table: 'sys_list',
			referenceField: 'picklistTypeId',
			fields: ['parentEntityCanon', 'locked'],
		},
	],
	gridFilterExcludeFields: ['name'],
	fields: [
		{
			field: 'picklistTypeId',
			type: 'id',
			kind: 'hidden-editable',
			caption: 'Picklist Type ID',
			alwaysInApiSelectedFields: true,
		},
		{
			field: 'name',
			type: 'picklistName',
			kind: 'custom',
			caption: 'Picklist',
			dbIndex: true,
			kindOptions: {
				flags: {
					audit: true,
					schema: true,
					search: true,
					apiWritable: true,
					apiExternalWritable: false,
					computedOnSave: false,
					computedOnRead: false,
					formVisible: true,
					gridVisible: true,
					gridSortable: true,
					searchVisible: true,
					formattedData: true,
					gridExportable: true,
					reportable: false,
				},
			},
		},
		{
			field: 'form',
			type: 'entityName',
			typeOptions: {
				excludeFullyStaticEntities: true,
			},
			caption: 'Form',
			kind: 'custom',
			kindOptions: {
				computeFunction: 'form',
				flags: {
					aggregateField: false,
					apiExternalWritable: false,
					apiWritable: false,
					audit: true,
					computedOnRead: true,
					computedOnSave: false,
					formVisible: true,
					gridExportable: true,
					gridSortable: true,
					gridVisible: true,
					iselComputedOnSave: false,
					readable: true,
					reportable: false,
					schema: false,
					search: true,
					searchVisible: true,
					formattedData: true,
				},
			},
		},
		{
			field: 'value',
			type: 'picklistValue',
			kind: 'editable',
			caption: 'Value',
			dbIndex: true,
		},
		{
			field: 'parents',
			type: 'code[]',
			kind: 'custom',
			kindOptions: {
				flags: {
					audit: true,
					schema: true,
					search: true,
					apiWritable: true,
					apiExternalWritable: false,
					computedOnSave: false,
					computedOnRead: false,
					formVisible: true,
					gridVisible: true,
					gridSortable: false,
					searchVisible: true,
					formattedData: true,
					gridExportable: true,
					reportable: false,
				},
			},
			caption: 'Parent Values',
		},
		{
			field: 'rank',
			type: 'number',
			kind: 'editable',
			caption: 'Sequence',
			esSort: [
				'rank',
				'value',
			],
		},
		{
			field: 'relatedData',
			type: 'json',
			kind: 'custom',
			kindOptions: {
				flags: {
					audit: true,
					schema: true,
					search: false,
					apiWritable: true,
					apiExternalWritable: false,
					computedOnSave: false,
					computedOnRead: false,
					formVisible: false,
					gridVisible: false,
					gridSortable: false,
					searchVisible: false,
					formattedData: false,
					gridExportable: false,
					reportable: false,
				},
			},
			caption: 'Related Data',
		},
		{
			field: 'external',
			type: 'checkbox',
			typeOptions: {
				allowNull: true,
			},
			kind: 'editable',
			caption: 'External',
		},
		{
			field: 'originalId',
			type: 'id',
			caption: 'Original Id',
			kind: 'hidden',
		},
		{
			field: 'features',
			type: 'textbox[]',
			caption: 'Features',
			kind: 'hidden-searchable',
		},
	],
	customESQueries: {
		locked: function (context) {
			const { value = false } = context;
			return {
				bool: {
					must: [{
						term: {
							picklistTypeId__locked: value,
						},
					}],
				},
			};
		},
	},
});
