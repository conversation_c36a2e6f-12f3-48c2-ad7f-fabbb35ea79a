const _ = require('lodash');
const statHelper = require('../../shared/stat-helper.js')('sys_listitem');

module.exports = [
	{
		category: 'listItem',
		key: 'picklistValues',
		query: statHelper.countActiveEntity(),
	},
	{
		category: 'listItem',
		key: 'picklistTypes',
		query: statHelper.countActiveDistinctField(),
		options: {
			field: 'name',
		},
	},
	{
		category: 'listItem',
		key: 'picklistValuesOfTypeWithHighestValue',
		query(knex, options, cb) {
			const subQuery = function subQuery(){
				this.count().from('sys_listitem').groupBy('name').as('counts');
			};
			knex('sys_listitem')
				.max('counts.count')
				.from(subQuery)
				.asCallback((err, result) => {
					if (err) return cb(err);
					return cb(null, parseInt(_.head(result).max, 10));
				});
		},
	},
];
