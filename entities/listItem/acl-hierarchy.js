const permHelper = require('../../lib/core/permission-helper.js');

module.exports = [{
	type: 'group',
	caption: 'Picklist',
	permission: 'picklist_item_settings',
	parentPermission: 'view_forms_settings',
	options: [
		{
			caption: 'View',
			tooltip: 'View picklist values',
			sequence: 1,
			permission: 'view_picklist_item_settings',
		},
		{
			caption: 'Create',
			tooltip: 'Create picklist values',
			sequence: 2,
			permission: 'create_picklist_item',
			dependencies: ['view_picklist_item_settings'],
		},
		{
			type: 'group',
			caption: 'Edit',
			tooltip: 'Edit picklist values',
			sequence: 3,
			permission: permHelper.getEditGroupPermissionCode('list_item'),
			dependencies: ['view_picklist_item_settings'],
			options: [{
				caption: 'Save',
				tooltip: 'Edit picklist values',
				permission: 'edit_picklist_item',
				dependencies: ['view_picklist_item_settings'],
			}],
		},
		{
			caption: 'Remove',
			tooltip: 'Delete picklist values',
			sequence: 4,
			permission: 'remove_picklist_item',
			dependencies: ['view_picklist_item_settings'],
		},
	],
}];
