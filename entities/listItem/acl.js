var permHelper = require('../../lib/core/permission-helper.js');

module.exports = permHelper.initialize()
	// View
	.required({
		name: 'view picklist items',
		roles: ['view_picklist_item'],
		actions: ['load', 'list', 'save_existing', 'remove'],
		conditions: [],
	})
	.required({
		name: 'View/Edit External Picklist Item',
		roles: ['view_external_picklist_item'],
		actions: ['load', 'list', 'save_existing', 'remove'],
		conditions: [{
			attributes: {
				external: true,
			},
		}],
	})
	.required({
		name: 'View/Edit Internal Picklist Item',
		roles: ['view_internal_picklist_item'],
		actions: ['load', 'list', 'save_existing', 'remove'],
		conditions: [{
			attributes: {
				'!external': true,
			},
		}],
	})
	// Create
	.required({
		name: 'Create picklist items',
		roles: ['create_picklist_item'],
		actions: ['save_new'],
		conditions: [{
			attributes: {
				id: null,
			},
		}],
	})
	// Edit
	.required({
		name: 'Edit picklist items',
		roles: ['edit_picklist_item'],
		actions: ['save_new'],
		conditions: [{
			attributes: {
				'!id': null,
			},
		}],
	})
	// Remove
	.required({
		name: 'Remove picklist items',
		roles: ['remove_picklist_item'],
		actions: ['remove'],
		conditions: [],
	})
	.value();
