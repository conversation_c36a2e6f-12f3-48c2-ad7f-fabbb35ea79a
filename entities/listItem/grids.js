module.exports = {
	'main-listitems': {
		sortColumn: 'name',
		sortOrder: 'asc',
		columns: [
			{ field: 'name' },
			{ field: 'parents' },
			{ field: 'value' },
			{ field: 'form'},
			{ field: 'rank' },
		],
		defaultDynamicDataFilters: ['createdBy', 'createdDate', 'lastUpdatedDate'],
	},
	'field-config-listitems': {
		sortColumn: 'rank',
		sortOrder: 'asc',
		columns: [
			{ field: 'rank' },
			{ field: 'parents' },
			{ field: 'value' },
		],
	},
};
