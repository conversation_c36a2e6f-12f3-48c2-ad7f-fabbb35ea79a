const permHelper = require('../../lib/core/permission-helper.js');

module.exports = [{
	type: 'group',
	features: ['dataImport'],
	caption: 'Data Imports',
	permission: 'view_data_import_settings',
	parentPermission: 'view_system_settings',
	options: [
		{
			permission: 'view_data_import',
			caption: 'View data imports',
			features: ['dataImport'],
			tooltip: 'View the Data Import settings page',
			sequence: 1,
		},
		{
			permission: 'create_data_import',
			caption: 'Create',
			features: ['dataImport'],
			tooltip: 'Add Data Import',
			sequence: 2,
			dependencies: ['view_data_import'],
		},
		{
			type: 'group',
			permission: permHelper.getEditGroupPermissionCode('data_import'),
			caption: 'Edit',
			features: ['dataImport'],
			sequence: 3,
			dependencies: ['view_data_import'],
			options: [{
				permission: 'edit_data_import',
				caption: 'Save',
				features: ['dataImport'],
				tooltip: 'Edit Data Import',
				dependencies: ['view_data_import'],
			}],
		},
		{
			permission: 'remove_data_import',
			caption: 'Remove',
			features: ['dataImport'],
			tooltip: 'Delete Data Import and/or its Statuses and Steps',
			sequence: 4,
			dependencies: ['view_data_import'],
		},
	],
}];
