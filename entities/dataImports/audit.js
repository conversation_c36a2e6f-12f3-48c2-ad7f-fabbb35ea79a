const reference = {
	displayFields: [
		'name',
	],
};

module.exports = {
	allowNavigateTo: true,
	cmd: {
		load: {
			viewed: {
				options: {
					reference,
				},
			},
		},
		save: {
			created: {
				options: {
					reference,
				},
			},
			updated: {
				options: {
					reference,
					changes: {
						excludeFields: [
							'id',
						],
					},
				},
			},
			activated: {
				condition: {
					composite: 'all',
					rules: [{
						path: 'diff.status.originalValue',
						comparator: 'neq',
						value: 'active',
					}, {
						path: 'diff.status.updatedValue',
						comparator: 'eq',
						value: 'active',
					}],
				},
				options: {
					reference,
				},
			},
			saved_as_inactive: {
				condition: {
					composite: 'all',
					rules: [{
						path: 'diff.status.originalValue',
						comparator: 'eq',
						value: 'draft',
					}, {
						path: 'diff.status.updatedValue',
						comparator: 'eq',
						value: 'inactive',
					}],
				},
				options: {
					reference,
				},
			},
			deactivated: {
				condition: {
					composite: 'all',
					rules: [{
						path: 'diff.status.originalValue',
						comparator: 'neq',
						value: 'draft',
					}, {
						path: 'diff.status.updatedValue',
						comparator: 'eq',
						value: 'inactive',
					}],
				},
				options: {
					reference,
				},
			},
		},
		remove: {
			deleted: {
				options: {
					reference,
				},
			},
		},
	},
};
