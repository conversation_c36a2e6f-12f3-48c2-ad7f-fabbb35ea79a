const async = require('async');

/** Calculate the count of fields defined in mapping by the user.
 *
 * @param sececa {Seneca} A Seneca instance.
 * @param entities {Entity[]} A list of entities.
 *
 * @returns {Integer} The difference in whole days.
 */
function countMappingFields(seneca, entities, callback) {
	return async.map(entities, (entity, mapCallback) => {
		if (!(entity.fieldMapping)) {
			return mapCallback(null, 0);
		}

		const mapping = entity.fieldMapping;
		const count = Object.keys(mapping).length;

		return mapCallback(null, count);
	}, callback);
}

module.exports = {
	countMappingFields,
};
