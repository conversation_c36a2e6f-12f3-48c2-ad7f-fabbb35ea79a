module.exports = {
	name: 'status-show-workflow',
	field: 'status',
	entity: {
		zone: undefined,
		base: 'sys',
		name: 'data_import',
	},
	values: [
		{
			value: 'draft',
			indicator: 'warning',
		},
		{
			value: 'new',
			indicator: 'warning',
		},
		{
			value: 'active',
			indicator: 'success',
		},
		{
			value: 'inactive',
			indicator: 'default',
		},
		{
			value: 'pending',
			indicator: 'warning',
		},
	],
	strict: true,
	transitions: [
		{
			id: 'data-import-translation-status-initial',
			from: [null, undefined, 'draft'],
			to: 'new',
		},
		{
			id: 'data-import-translation-status-complete',
			from: 'new',
			to: 'active',
		},
		{
			id: 'data-import-translation-status-closed',
			from: ['active', 'new'],
			to: 'inactive',
		},
		{
			id: 'data-import-translation-status-modified',
			from: 'active',
			to: 'pending',
		},
		{
			id: 'data-import-translation-status-reopen',
			from: ['inactive', 'pending'],
			to: 'active',
		},
	],
	conditions: [],
};
