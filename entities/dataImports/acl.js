const permHelper = require('../../lib/core/permission-helper.js');

module.exports = permHelper.initialize()
	.required({
		name: 'View Data Import Settings',
		features: ['dataImport'],
		roles: ['view_data_import'],
		actions: ['load', 'list', 'save_existing', 'remove'],
		conditions: [],
	})
	.required({
		name: 'Create Data Import',
		features: ['dataImport'],
		roles: ['create_data_import'],
		actions: ['save_new'],
		conditions: [{
			attributes: {
				id: null,
			},
		}],
	})
	.required({
		name: 'Create Data Import',
		features: ['dataImport'],
		roles: ['create_data_import'],
		actions: ['save_existing'],
		conditions: [{
			attributes: {
				'!id': null,
				sysSubmitted: false,
			},
		}],
	})
	.required({
		name: 'Edit Data Import',
		features: ['dataImport'],
		roles: ['edit_data_import'],
		actions: ['save_existing'],
		conditions: [{
			attributes: {
				'!id': null,
				sysSubmitted: true,
			},
		}],
	})
	.required({
		name: 'Remove Data Import',
		features: ['dataImport'],
		roles: ['remove_data_import'],
		actions: ['remove'],
		conditions: [],
	})
	.required({
		name: 'Save/Delete Hard Locked Data Import',
		features: ['dataImport'],
		roles: ['save_delete_hard_locked_data_import'],
		actions: ['save_new', 'save_existing', 'remove'],
		conditions: [{
			attributes: {
				hardLocked: true,
			},
		}],
	})
	.value();
