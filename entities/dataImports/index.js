const extend = require('../extend.js');
const standardConfig = require('../standard-config.js');

module.exports = extend(standardConfig, {
	db: 'default',
	table: 'sys_data_import',
	entity: {
		base: 'sys',
		name: 'data_import',
	},
	bypassValidationOnDraft: true,
	caption: 'Data Import',
	captionPlural: 'Data Imports',
	addCaption: 'Add Data Import',
	newCaption: 'New Data Import',
	gridDescriptorField: 'name',
	api: {
		useGenericApi: true,
	},
	acl: require('./acl.js'),
	aclRolesForCopy: ['edit_data_import'],
	aclHierarchy: require('./acl-hierarchy.js'),
	audit: require('./audit'),
	grids: require('./grids.js'),
	validation: require('./validation'),
	historyNav: require('./history-nav.js'),
	rules: require('./rules.js'),
	configurationExport: true,
	copyPostAction: 'data_import',
	computeFunctions: require('./compute-functions.js'),
	model() {
		return require('../../public/models/data-import-model.js');
	},
	staticFieldWorkflows: [],
	fields: [
		{
			field: 'name',
			features: ['dataImport'],
			type: 'textbox',
			caption: 'Name',
			kind: 'editable',
		},
		{
			field: 'importEntity',
			features: ['dataImport'],
			// Use entity picklist to list all entities, can be filtered as well
			type: 'entityPicklist',
			caption: 'Import Entity',
			kind: 'editable',
			typeOptions: {
				valueField: 'canon',
				canCreate: false,
				filter: 'dataImportsEntityFilter',
				entityCategory: 2,
				renderWithIcons: true,
			},
		},
		{
			field: 'delimiter',
			features: ['dataImport'],
			type: 'picklist',
			caption: 'Delimiter',
			kind: 'editable',
			default: ',',
			typeOptions: {
				picklistName: 'data_import_file_delimiters',
			},
		},
		{
			field: 'type',
			features: ['dataImport'],
			type: 'picklist',
			caption: 'Type',
			kind: 'editable',
			typeOptions: {
				picklistName: 'data_import_types',
			},
		},
		{
			field: 'importMethod',
			features: ['dataImport'],
			type: 'picklist',
			caption: 'Import Method',
			kind: 'editable',
			typeOptions: {
				picklistName: 'data_import_methods',
			},
		},
		{
			field: 'updateMethod',
			features: ['dataImport'],
			type: 'picklist',
			caption: 'Update Method',
			kind: 'editable',
			typeOptions: {
				picklistName: 'data_import_update_methods',
			},
		},
		{
			field: 'fieldMapping',
			features: ['dataImport'],
			type: 'json',
			caption: 'Field Mapping',
			kind: 'editable',
		},
		{
			field: 'dateFormat',
			features: ['dataImport'],
			type: 'picklist',
			caption: 'Date Format Override',
			kind: 'editable',
			typeOptions: {
				picklistName: 'date_formats',
			},
		},
		{
			field: 'countryCodeOverride',
			features: ['dataImport'],
			type: 'textbox',
			caption: 'Country Code Override',
			kind: 'editable',
		},
		{
			field: 'phoneNumberOverride',
			features: ['dataImport'],
			type: 'textbox',
			caption: 'Phone Number Override',
			kind: 'editable',
		},
		{
			field: 'showAdvancedOptions',
			type: 'checkbox',
			typeOptions: {
				allowNull: true,
			},
			caption: 'Show Advanced Options',
			kind: 'editable',
		},
		{
			field: 'fieldsMappingCount',
			features: ['dataImport'],
			type: 'number',
			caption: 'Fields Mapping Count',
			kind: 'computed',
			kindOptions: {
				computeFunction: 'countMappingFields',
			},
		},
		{
			field: 'distributionList',
			features: ['dataImport'],
			type: 'email[]',
			caption: 'Error Notification Recipients',
			kind: 'editable',
		},
		{
			field: 'file',
			type: 'file[]',
			kind: 'editable-external',
			caption: 'Upload file',
			typeOptions: {
				supportedFormats: ['.csv', '.txt', '.xlsx'],
				mode: 'validation',
				maxFileCount: 1,
			},
			excludeFromSaveAndCopy: true,
		},
		{
			field: 'fileFormat',
			features: ['dataImport'],
			type: 'picklist',
			caption: 'File Format',
			kind: 'editable',
			typeOptions: {
				picklistName: 'data_import_file_formats',
			},
		},
		{
			field: 'userLookupField',
			caption: 'User Lookup',
			type: 'fieldPicklist',
			typeOptions: {
				entityName: 'sys/user',
				filter: 'dataImportUserLookupFilter',
				filterFlag: ['apiWritable', 'formVisible'],
				dropdownParent: 'body',
			},
			features: ['dataImport'],
			kind: 'editable',
		},
		{
			field: 'defaultUserRoleId',
			type: 'picklistSelectizeApi',
			typeOptions: {
				picklistName: 'user_roles',
				missingValueTranslation: 'Unknown User Role',
			},
			caption: 'Default User Role',
			kind: 'editable',
			dbIndex: true,
			esSort: [
				'defaultUserRoleId__name._exactKeywordLowercase',
			],
		},
		{
			field: 'encodingOverride',
			features: ['dataImport'],
			type: 'picklist',
			caption: 'Encoding Override',
			kind: 'editable',
			typeOptions: {
				picklistName: 'data_import_file_encodings',
			},
		},
	],
	joins: [
		{
			referenceField: 'defaultUserRoleId',
			table: 'sys_user_role',
			fields: [
				'name',
				'filterOperator',
				'features',
			],
		},
	],
});
