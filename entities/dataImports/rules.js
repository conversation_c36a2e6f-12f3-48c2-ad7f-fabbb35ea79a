module.exports = {
	isNotNew(data) { return Boolean(data.id); },
	isEntitySelected(data) {
		return Boolean(data.importEntity);
	},
	isScheduleBatchProcess(data) { return Boolean(data.type === 'scheduled_batch');},
	fileTypeEqualsXlsx(data) {
		return Boolean(data.fileFormat === 'xlsx');
	},
	isAttachmentEntity(data) {
		return data.importEntity === 'sys/attachment';
	},
	isShowAdvancedOptionsTrue(data) {
		return Boolean(data.showAdvancedOptions);
	},
	isUserImport(data) { return data.importEntity === 'sys/user'; },

	isCountryCodeRequired(data) {
		return data.phoneNumberOverride !== undefined && data.phoneNumberOverride !== null;
	},
};
