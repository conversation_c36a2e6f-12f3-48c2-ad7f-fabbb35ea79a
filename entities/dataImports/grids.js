module.exports = {
	'main-data-imports': {
		sortColumn: 'name',
		sortOrder: 'asc',
		excludeColumns: ['datePurged', 'pendingPurgeDate', 'purgeReason', 'excludeFromSuggestedLinks'],
		columns: [
			{ field: 'name' },
			{ field: 'importEntity' },
			{ field: 'type' },
			{ field: 'importMethod' },
			{ field: 'lastUpdatedDate' },
			{ field: 'lastUpdatedBy' },
		],
		defaultDynamicDataFilters: ['createdDate', 'lastUpdatedDate'],
	},
};
