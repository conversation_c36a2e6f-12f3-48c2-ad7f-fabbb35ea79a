module.exports = {
	mandatory$: [
		'name',
		'importEntity',
		'type',
		'importMethod',
		'distributionList',
		'fileFormat',
	],
	dependentMandatory$: [
		{
			condition: '!fileTypeEqualsXlsx && !isAttachmentEntity',
			fields: ['delimiter'],
		},
		{
			condition: '!isAttachmentEntity',
			fields: ['updateMethod'],
		},
		{
			condition: 'isUserImport',
			fields: ['defaultUserRoleId'],
		},
		{
			condition: 'isCountryCodeRequired',
			fields: ['countryCodeOverride'],
		},
	],
	pattern$: [{
		fields: ['name'],
		pattern: '[a-zA-Z0-9]',
		code: 'atLeastOneLetterOrNumber$',
	}],
};
