const permHelper = require('../../lib/core/permission-helper.js');

module.exports = [{
	permission: 'view_user_role_settings',
	type: 'group',
	caption: 'User Role',
	parentPermission: 'view_access_settings',
	options: [
		{
			permission: 'view_user_role',
			caption: 'View',
			tooltip: 'View User Role',
			disabled: true,
			sequence: 1,
		},
		{
			permission: 'create_user_role',
			caption: 'Create',
			tooltip: 'Create a user role',
			sequence: 2,
		},
		{
			type: 'group',
			permission: permHelper.getEditGroupPermissionCode('user_role'),
			caption: 'Edit',
			sequence: 3,
			options: [{
				permission: 'edit_user_role',
				caption: 'Save',
				tooltip: 'Edit a user role',
			}],
		},
		{
			permission: 'remove_user_role',
			caption: 'Remove',
			tooltip: 'Remove a user role',
			sequence: 4,
		},
		{
			permission: 'view_user_role_audit_log',
			caption: 'View User Role History',
			tooltip: 'View actions done to a user role',
			sequence: 5,
		},
	],
}];
