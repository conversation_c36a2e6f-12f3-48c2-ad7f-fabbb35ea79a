const _ = require('lodash');
const permHelper = require('../../lib/core/permission-helper.js');

function isUserRoleFeatureEnabled(obj, enabledFeatures = []) {
	if (!obj.features || !Array.isArray(obj.features) || obj.features.length === 0) {
		return true;
	}
	// Check if all features in the userRole are present in enabledFeatures
	return _.difference(obj.features, enabledFeatures).length === 0;
}

function isTwoWayPortalOptionsEnabled(context) {
	return context.options?.enablePortal
		&& context.options?.portalSubmissionAccess === 'Two-Way';
}

module.exports = permHelper.initialize()
	.required({
		name: 'View User Roles',
		roles: ['view_user_role'],
		actions: ['load', 'list', 'save_existing', 'remove'],
		conditions: [],
	})
	.required({
		name: 'View System User Roles',
		roles: ['view_system_user_role'],
		actions: ['load', 'list', 'save_existing', 'remove'],
		conditions: [{
			attributes: {
				system: true,
			},
		}],
	})
	.required({
		name: 'View Locked Disabled User Roles',
		roles: ['view_locked_disabled_user_role'],
		actions: ['load', 'list', 'save_existing', 'save_new', 'remove'],
		conditions: [{
			attributes: {
				enabled: false,
				locked: true,
			},
		}],
	})
	.required({
		name: 'Access Disabled Feature User Roles',
		roles: ['access_disabled_feature_user_role'],
		actions: ['load', 'list', 'save_existing', 'save_new', 'remove'],
		conditions: [{
			attributes: {
				'!features': null,
			},
		}, {
			fn(obj, context) {
				if (!isUserRoleFeatureEnabled(obj, context.enabledFeatures)) return { ok: true };
				return { ok: false };
			},
			esFilter(context, attributePrefix = '') {
				return {
					bool: {
						must_not: [
							{ terms: { features: context.enabledFeatures } },
						],
					},
				};
			},
			selectedFields(opts, callback) {
				return callback(null, ['id', 'features']);
			},
		}],
	})
	.required({
		name: 'Access Disabled Hotline User Role',
		roles: ['access_disabled_hotline_user_role'],
		actions: ['load', 'list', 'save_existing', 'save_new', 'remove'],
		conditions: [{
			attributes: {
				name: 'Hotline Agent',
			},
		}, {
			fn(obj, context) {
				if (!isTwoWayPortalOptionsEnabled(context)) return { ok: true };
				return { ok: false };
			},
			esFilter(context, attributePrefix = '') {
				if (!isTwoWayPortalOptionsEnabled(context)) {
					return {
						bool: {
							must_not: [{
								term: {
									name: 'Hotline Agent',
								},
							}],
						},
					};
				}
				return { bool: { must_not: [{ exists: { field: 'id' } }] } };
			},
			selectedFields(opts, callback) {
				return callback(null, ['id', 'name']);
			},
		}],
	})
	.required({
		name: 'Create User Roles',
		roles: ['create_user_role'],
		actions: ['save_new'],
		conditions: [{
			attributes: {
				id: null,
			},
		}],
	})
	.required({
		name: 'Create Locked User Roles',
		roles: ['create_locked_user_role'],
		actions: ['save_new'],
		conditions: [{
			attributes: {
				id: null,
				locked: true,
			},
		}],
	})
	.required({
		name: 'Edit User Roles',
		roles: ['edit_user_role'],
		actions: ['save_new'],
		conditions: [{
			attributes: {
				'!id': null,
			},
		}],
	})
	.required({
		name: 'Edit Locked User Roles',
		roles: ['edit_locked_user_role'],
		actions: ['save_new'],
		conditions: [{
			attributes: {
				'!id': null,
				locked: true,
			},
		}],
	})
	.required({
		name: 'Remove User Roles',
		roles: ['remove_user_role'],
		actions: ['remove'],
		conditions: [],
	})
	.required({
		name: 'Remove Locked User Roles',
		roles: ['remove_locked_user_role'],
		actions: ['remove'],
		conditions: [{
			attributes: {
				locked: true,
			},
		}],
	})
	.value();
