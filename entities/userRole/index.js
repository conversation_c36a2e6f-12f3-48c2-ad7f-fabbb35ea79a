const extend = require('../extend.js');
const standardConfig = require('../standard-config.js');

module.exports = extend(standardConfig, {
	db: 'default',
	table: 'sys_user_role',
	entity: {
		base: 'sys',
		name: 'user_role',
	},
	configurationExport: true,
	importTransformer: 'user_role',
	caption: 'User Role',
	captionPlural: 'User Roles',
	addCaption: 'Add User Role',
	newCaption: 'New User Role',
	gridDescriptorField: 'name',
	acl: require('./acl.js'),
	aclHierarchy: require('./acl-hierarchy.js'),
	grids: require('./grids.js'),
	audit: require('./audit.js'),
	validation: require('./validation.js'),
	rules: require('./rules.js'),
	historyNav: require('./history-nav.js'),
	usageStatistics: require('./usage-statistics.js'),
	model() {
		return require('../../public/models/user-role-model.js');
	},
	collection() {
		return require('../../public/collections/user-roles-collection.js');
	},
	api: {
		writableFields: [
			'permissions',
		],
	},
	fields: [
		{
			field: 'name',
			type: 'textbox',
			caption: 'Name',
			kind: 'editable',
			excludeFromSaveAndCopy: true,
		},
		{
			field: 'description',
			type: 'textarea',
			typeOptions: {
				charMaxTextArea: 140,
			},
			caption: 'Description',
			kind: 'editable',
		},
		{
			field: 'system',
			type: 'checkbox',
			caption: 'System',
			kind: 'system',
			excludeFromSaveAndCopy: true,
		},
		{
			field: 'filters',
			type: 'json[]',
			caption: 'Filters',
			kind: 'custom',
			kindOptions: {
				flags: {
					audit: false,
					schema: false,
					search: false,
					apiWritable: true,
					apiExternalWritable: false,
					computedOnSave: false,
					computedOnRead: false,
					formVisible: false,
					gridVisible: false,
					gridSortable: false,
					searchVisible: false,
					formattedData: false,
					gridExportable: false,
					reportable: false,
				},
			},
		},
		{
			field: 'filterOperator',
			type: 'code',
			caption: 'Filter Operator',
			kind: 'editable',
			dbIndex: true,
		},
		{
			field: 'yellowfinRoleCode',
			type: 'code',
			caption: 'Yellowfin Role Code',
			kind: 'system',
		},
		{
			field: 'locked',
			type: 'checkbox',
			caption: 'Locked',
			kind: 'system',
			excludeFromSaveAndCopy: true,
		},
		{
			field: 'enabled',
			type: 'checkbox',
			caption: 'Enabled',
			kind: 'system',
			default: true,
			excludeFromSaveAndCopy: true,
		},
		{
			field: 'features',
			type: 'textbox[]',
			caption: 'Features',
			kind: 'hidden-searchable',
		},
	],
});
