/* global module */

/**
 *
 *		Case Status Workflow.
 *
 *		Allows for monitoring value changes with transitions, enfore access control between
 *		transitions, set variables between values and during transitions.
 *
 *		NOTE: The file name of the config file must be unique. It can be anywhere in sub folders, but
 *					all files must have a unique name. This is for the UI, you can refer to a workflow by
 *					name (file name).
 *
 *		List of reserved variables for values -> onSet, values -> onUnset & transitions -> set:
 *
 *			{now}					Replace with the current time on execution.
 *
 */

module.exports = {
	name: 'todo-status-workflow',
	/** *
	 *		Use this attribute for setting which field of an entity the workflow revolves around.
	 */
	field: 'status',
	/** *
	 *		Use to identify which entity the field relates to.
	 */
	entity: {
		zone: undefined,
		base: 'sys',
		name: 'todo',
	},
	/** *
	 *		This is an optional array of values for the workflow. This is a list of explicit
	 *		valid values for the given field. The system won't accept other values than the
	 *		ones defined.
	 *
	 *		Available value propertries are:
	 *			value: 		The value of the value the field would have.
	 *			onSet: 		Ojbect of values to set when the workflow enters the value.
	 *			onUnset: 	Object of values to set when the workflow leaves the value.
	 */
	values: [
		{
			value: 'pending',
			indicator: 'warning',
		},
		{
			value: 'purged',
			indicator: 'danger',
		},
		{
			value: 'closed',
			onSet: {
				dateCompleted: '{now}',
				closedBy: '{user.id}',
			},
			indicator: 'success',
		},
	],
	/** *
	 *		Use this to ensure a transition is given each time the value changes.
	 */
	strict: true,
	/** *
	 *		Array of transitions, a set of from and to values the values change from.
	 *
	 *		Available transition properties:
	 *			id: 			The unique identifyer for the transition.
	 *			from: 		The originalValue from the diff to evaluate against.
	 *			to: 			The updatedValue from the diff to evaluate against.
	 *			roles: 		Array of roles. The user should have at least ONE of them in order to make this
	 *								transition.
	 *			set:  		List of properties to set when transition is active.
	 */
	transitions: [
		{
			id: 'todo-create',
			from: [null, undefined],
			to: 'pending',
		},
		{
			id: 'complete-todo',
			caption: 'todo_closed',
			from: 'pending',
			to: 'closed',
		},
	],
};
