const extend = require('../extend.js');
const standardChildConfig = require('../standard-child-config.js');

module.exports = extend(standardChildConfig, {
	db: 'default',
	table: 'sys_todo',
	entity: {
		base: 'sys',
		name: 'todo',
	},
	joins: [
		{
			referenceField: 'sourceJob',
			table: 'sys_event',
			fields: [
				'name',
				'jobId',
			],
		},
	],
	normalizeMultiValuePicklistEntries: true,
	customForm: false,
	ruleEvents: true,
	caption: 'To-Do',
	captionPlural: 'To-Dos',
	addCaption: 'Add To-Do',
	newCaption: 'New To-Do',
	acl: require('./acl.js'),
	aclHierarchy: require('./acl-hierarchy.js'),
	audit: require('./audit.js'),
	rules: require('./rules.js'),
	grids: require('./grids.js'),
	validation: require('./validation.js'),
	historyNav: require('./history-nav.js'),
	icon: 'fa-check-square',
	includeInDataDictionary: true,
	enableRecordSourceView: true,
	backgroundColor: '#FFF4E7',
	allowInGridViews: true,
	usageStatistics: require('./usage-statistics.js'),
	linkSummaryFields: ['details'],
	model(){
		return require('../../public/models/todo-model.js');
	},
	collection(){
		return require('../../public/collections/todos-collection.js');
	},
	view(){
		return require('../../public/views/todo/todo-details-view.js');
	},
	calendarView(){
		return require('../../public/views/todo/todo-event-view.js');
	},
	api: {
		writableFields: [
			'needMoreInfo',
		],
	},
	rowTemplates: {
		small(){
			return require('./row-templates/small.dust');
		},
		medium(){
			return require('./row-templates/medium.dust');
		},
		tiny(){
			return require('./row-templates/tiny.dust');
		},
	},
	staticFieldWorkflows: [require('./static-field-workflows/todo-status.js')],
	calendarMapping: {
		title: 'childNumber',
		subtitle: 'todoType',
		description: 'details',
		startDate: 'due',
		endDate: 'due',
		allDay: true,
	},
	fields: [
		{
			field: 'assignedBy',
			type: 'user',
			caption: 'Assigned By',
			kind: 'system',
			excludeFromSaveAndCopy: true,
		},
		{
			field: 'responsible',
			type: 'user',
			caption: 'Responsible',
			kind: 'editable',
			kindOptions: {
				useInDynamicJoins: true,
			},
			typeOptions: {
				roleFilter: 'todo_owner',
			},
			dataImportMappableOverride: true,
		},
		{
			field: 'dateCompleted',
			type: 'datetime',
			caption: 'Date Completed',
			kind: 'system',
			excludeFromSaveAndCopy: true,
			dataImportMappableOverride: true,
		},
		{
			field: 'closedBy',
			type: 'user',
			caption: 'Closed by',
			kind: 'system',
			excludeFromSaveAndCopy: true,
			dataImportMappableOverride: true,
		},
		{
			field: 'details',
			type: 'texteditor',
			caption: 'Details',
			kind: 'editable',
		},
		{
			field: 'due',
			type: 'date',
			caption: 'Due Date',
			kind: 'editable',
		},
		{
			field: 'emailReminder',
			type: 'numberRangePicklist',
			caption: 'Email Reminder',
			kind: 'editable',
			typeOptions: {
				startValue: 0,
				endValue: 30,
			},
		},
		{
			field: 'activeReminder',
			type: 'textbox',
			caption: 'Active Reminder',
			kind: 'hidden',
			excludeFromSaveAndCopy: true,
		},
		{
			field: 'todoType',
			type: 'picklist',
			caption: 'To-Do Type',
			typeOptions: {
				picklistName: 'todo_types',
			},
			kind: 'editable',
		},
		{
			field: 'status',
			type: 'picklist',
			caption: 'Status',
			typeOptions: {
				picklistName: 'todo_statuses',
			},
			kind: 'editable',
			excludeFromRedact: true,
			excludeFromSaveAndCopy: true,
			cellTemplate(fs) {
				return fs.readFileSync(
					`${__dirname}/cell-templates/todo-status-cell-tmpl.dust`,
					'utf8',
				);
			},
		},
	],
});
