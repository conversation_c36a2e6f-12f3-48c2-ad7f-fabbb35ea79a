const statHelper = require('../../shared/stat-helper.js')('sys_todo');

module.exports = [
	{
		category: 'todo',
		key: 'totalTodos',
		query: statHelper.countActiveEntity(),
	},
	{
		category: 'todo',
		key: 'todosByStatus',
		query: statHelper.countActiveEntityByPicklistGroup(),
		options: {
			groupingField: 'status',
			picklistName: 'todo_statuses',
			datasetTranslation(dataset, translate) {
				return statHelper.getPicklistDatasetTrans(this, dataset, translate);
			},
		},
	},
	{
		category: 'todo',
		key: 'todosPerCase',
		query: statHelper.countTotalAverageAndHighestByCase(),
	},
];
