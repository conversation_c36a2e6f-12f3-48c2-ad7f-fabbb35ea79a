const refFields = [
	'details',
];
const reference = {
	displayFields(data, auditModel) {
		return auditModel.hideCaseItemNumberReference() !== true
			? ['childNumber'].concat(refFields)
			: refFields;
	},
};
module.exports = {
	child: true,
	parentType: {
		base: 'sys',
		name: 'case',
	},
	parentFieldId: 'caseId',
	allowNavigateTo: true,
	navigateToType: 'to-do',
	cmd: {
		load: {
			viewed: {
				options: {
					reference,
				},
			},
		},
		save: {
			created: {
				options: {
					changes: {
						displayFields: ['responsible'],
						excludeFields: ['activeReminder'],
					},
					reference,
				},
			},
			updated: {
				options: {
					changes: {
						excludeFields: [
							'id',
							'activeReminder',
						],
					},
					reference,
				},
			},
			assigned: {
				condition: {
					composite: 'all',
					rules: [{
						path: 'diff.responsible',
						comparator: 'exists',
					}, {
						path: 'diff.responsible.originalValue',
						comparator: 'exists',
					}, {
						path: 'diff.responsible.originalValue',
						comparator: 'neq',
						value: 'diff.responsible.updatedValue',
					}],
				},
				options: {
					changes: {
						displayFields: ['responsible'],
					},
					reference,
				},
			},
			completed: {
				condition: {
					composite: 'all',
					rules: [{
						path: 'diff.status.updatedValue',
						comparator: 'eq',
						value: 'closed',
					}],
				},
				options: {
					reference,
				},
			},
		},
		remove: {
			deleted: {
				options: {
					reference,
				},
			},
		},
	},
};
