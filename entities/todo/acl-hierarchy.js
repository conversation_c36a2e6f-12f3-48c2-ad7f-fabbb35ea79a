module.exports = [{
	type: 'group',
	caption: 'To-Dos',
	parentPermission: 'case',
	permission: 'todo',
	options: [
		{
			type: 'group',
			permission: 'view_todo',
			caption: 'View',
			sequence: 2,
			options: [
				{
					permission: 'view_my_todos',
					caption: 'My To-Dos',
					tooltip: 'View owned To-Dos',
				},
				{
					permission: 'view_others_todos',
					caption: 'Others To-Dos',
					tooltip: 'View To-Dos owned by other users',
				},
			],
		},
		{
			permission: 'create_todo',
			caption: 'Create',
			tooltip: 'Add To-Dos',
			sequence: 1,
		},
		{
			type: 'group',
			permission: 'edit_todo',
			caption: 'Edit',
			tooltip: 'Update To-Dos',
			sequence: 3,
			dependencies: ['view_todo'],
			options: [
				{
					permission: 'reassign_todo',
					caption: 'Reassign',
					tooltip: 'Reassign a To-Do',
					dependencies: ['view_todo'],
				},
				{
					permission: 'complete_others_todos',
					caption: 'Complete Others To-Dos',
					tooltip: 'Complete To-Dos owned by other users',
					dependencies: ['view_todo'],
				},
				{
					permission: 'complete_my_todos',
					caption: 'Complete My To-Dos',
					tooltip: 'Complete owned To-Dos',
					dependencies: ['view_todo'],
				},
				{
					permission: 'edit_others_todos',
					caption: 'Edit Others To-Dos',
					tooltip: 'Edit To-Dos owned by other users',
					dependencies: ['view_todo'],
				},
				{
					permission: 'edit_my_todos',
					caption: 'Edit My To-Dos',
					tooltip: 'Edit owned To-Dos',
					dependencies: ['view_todo'],
				},
			],
		},
		{
			type: 'group',
			permission: 'remove_todo',
			caption: 'Remove',
			sequence: 4,
			dependencies: ['view_todo'],
			options: [
				{
					permission: 'remove_unassigned_todo',
					caption: 'Unassigned To-Dos',
					tooltip: 'Delete unassigned To-Dos',
					dependencies: ['view_todo'],
				},
				{
					permission: 'remove_assigned_todo',
					caption: 'Assigned To-Dos',
					tooltip: 'Delete assigned To-Do',
					dependencies: ['view_todo'],
				},
			],
		},
		{
			permission: 'todo_owner',
			caption: 'To-Do Owner',
			tooltip: 'Can own To-Dos',
			dependencies: ['view_my_todos'],
		},
	],
}];
