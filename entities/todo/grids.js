const defaultDynamicDataFilters = ['status', 'responsible', 'due', 'createdDate'];

module.exports = {
	'main-todos': {
		sortColumn: 'createdDate',
		sortOrder: 'desc',
		columns: [
			{ field: 'childNumber' },
			{ field: 'responsible' },
			{ field: 'details' },
			{ field: 'todoType' },
			{ field: 'status' },
			{ field: 'due' },
			{ field: 'createdDate' },
		],
		defaultDynamicDataFilters,
	},
	'case-todos': {
		sortColumn: 'createdDate',
		sortOrder: 'desc',
		columns: [
			{ field: 'number' },
			{ field: 'responsible' },
			{ field: 'details' },
			{ field: 'todoType' },
			{ field: 'status' },
			{ field: 'due' },
			{ field: 'createdDate' },
		],
		defaultDynamicDataFilters,
		default: true,
	},
	'advanced-search-result-todos': {
		sortColumn: 'createdDate',
		sortOrder: 'desc',
		columns: [
			{ field: 'childNumber' },
			{ field: 'responsible' },
			{ field: 'details' },
			{ field: 'todoType' },
			{ field: 'status' },
			{ field: 'due' },
			{ field: 'createdDate' },
		],
	},
	'search-result-todos-schedule-purge': {
		sortColumn: 'createdDate',
		sortOrder: 'desc',
		columns: [
			{ field: 'childNumber' },
			{ field: 'responsible' },
			{ field: 'details' },
			{ field: 'todoType' },
			{ field: 'status' },
			{ field: 'due' },
			{ field: 'createdDate' },
		],
	},
	'search-result-todos-purge': {
		sortColumn: 'createdDate',
		sortOrder: 'desc',
		columns: [
			{ field: 'childNumber' },
			{ field: 'responsible' },
			{ field: 'details' },
			{ field: 'todoType' },
			{ field: 'status' },
			{ field: 'due' },
			{ field: 'pendingPurgeDate' },
			{ field: 'createdDate' },
		],
	},
};
