<div class="card-small{?model.datePurged} purged{:else}{?model.pendingPurgeDate} scheduled-purge{/model.pendingPurgeDate}{/model.datePurged}">
    <div class="card-header">
		{@entityIcon entity=entity$/}
		{@entityLink entity=entity$ context=model}<div class="card-title">{formattedData.childNumber|s}</div>{/entityLink}
        <div class="card-label">
            {@eq key=model.status value="pending"}
                <span class="label label-warning label-tag">
            {:else}
                {@eq key=model.status value="purged"}
                    <span class="label label-danger label-tag">
                {:else}
                    {@eq key=model.status value="closed"}
                        <span class="label label-success label-tag">
                    {:else}
                        <span class="label label-default label-tag">
                    {/eq}
                {/eq}
            {/eq}
            {@resource groupName="picklist" subgroupName="todo_statuses" key=model.status /}
            <span class="label-owner-container">{formattedData.responsible|s}</span>
        </span>
        </div>
    </div>
    {#highlightedFields}
        {>small-highlight-tmpl entityName="sys/todo" ago=model.ago/}
    {/highlightedFields}
</div>