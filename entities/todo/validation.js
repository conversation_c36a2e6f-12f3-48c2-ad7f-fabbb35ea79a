module.exports = {
	mandatory$: [
		'caseId',
		'todoType',
		'details',
	],
	dependentMandatory$: [{
		condition: 'isTodoTypeOther',
		fields: ['other'],
	}, {
		condition: 'hasEmailReminder',
		fields: ['due'],
	}],
	dateRange$: [{
		date: 'due',
		minimumDate: {
			date: 'createdDate',
		},
	}],
	caseId: {
		type$: 'string',
	},
	// dateCompleted: {type$: 'date'},
	details: {
		type$: 'string',
	},
	responsible: {
		type$: 'string',
	},
	todoType: {
		type$: 'string',
	},
};
