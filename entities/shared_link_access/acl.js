const permHelper = require('../../lib/core/permission-helper.js');

module.exports = permHelper.initialize()
	.required({
		name: 'View Shared Link Access',
		roles: ['view_shared_link_access'],
		actions: ['load', 'list', 'save_new', 'save_existing', 'remove'],
		conditions: [],
	})
	.required({
		name: 'Create Shared Link Access',
		roles: ['create_shared_link_access'],
		actions: ['save_new'],
		conditions: [{
			attributes: {
				id: null,
			},
		}],
	})
	.required({
		name: 'Edit Shared Link Access',
		roles: ['edit_shared_link_access'],
		actions: ['save_new'],
		conditions: [{
			attributes: {
				'!id': null,
			},
		}],
	})
	.required({
		name: 'Remove Shared Link Access',
		roles: ['remove_shared_link_access'],
		actions: ['remove'],
		conditions: [],
	})
	.required({
		name: 'Inherit Shared Link ACL',
		roles: ['bypass_inherited_acl'],
		actions: ['load', 'list', 'save_new', 'save_existing', 'remove'],
		conditions: ['sys/shared_link::{sharedLinkId}'],
	})
	.value();
