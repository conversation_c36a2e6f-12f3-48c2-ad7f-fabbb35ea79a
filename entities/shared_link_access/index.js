const extend = require('../extend.js');
const standardConfig = require('../standard-config.js');

module.exports = extend(standardConfig, {
	db: 'default',
	table: 'sys_shared_link_access',
	entity: {
		base: 'sys',
		name: 'shared_link_access',
	},
	caption: 'Shared Link Access',
	captionPlural: 'Shared Links Access',
	addCaption: 'Add Shared Link Access',
	newCaption: 'New Shared Link Access',
	acl: require('./acl.js'),
	allowPurge: true,
	api: {
		useGenericApi: true,
	},
	features: ['fileShare'],
	model() {
		return require('../../public/models/shared-link-access-model.js');
	},
	collection() {
		return require('../../public/collections/shared-link-access-collection.js');
	},
	parents: [
		{
			entity: {
				base: 'sys',
				name: 'shared_link',
			},
			field: 'sharedLinkId',
		},
	],
	fields: [
		{
			field: 'sharedLinkId',
			type: 'id',
			caption: 'Shared Link ID',
			kind: 'editable',
			excludeFromRedact: true,
		},
		{
			field: 'failedAccessAttempts',
			type: 'number',
			caption: 'Failed Access Attempts',
			kind: 'editable',
		},
		{
			field: 'locked',
			type: 'yesno',
			caption: 'Locked',
			kind: 'editable',
		},
		{
			field: 'recipient',
			type: 'email',
			caption: 'Recipient',
			kind: 'editable',
		},
		{
			field: 'verificationCode',
			type: 'textbox',
			caption: 'Verification Code',
			kind: 'editable',
		},
	],
	joins: [
		{
			referenceField: 'sharedLinkId',
			table: 'sys_shared_link',
			fields: [
				'status',
				'filesShared',
				'expiryDate',
				'message',
				'subject',
				'provideDownloadLink',
			],
		},
	],
});
