const extend = require('../extend.js');
const standardConfig = require('../standard-config.js');

module.exports = extend(standardConfig, {
	db: 'default',
	table: 'sys_entity_evaluator_context',
	entity: {
		base: 'sys',
		name: 'entityEvaluatorContext',
	},
	search: false,
	caption: 'Entity Evaluator Context',
	captionPlural: 'Entity Evaluator Context',
	addCaption: 'Add Entity Evaluator Context',
	newCaption: 'New Entity Evaluator Context',
	importTransformer: 'entity_evaluator_context',
	customDependencies: ['sys/conditionType'],
	copyPostAction: 'entity_evaluator_context',
	model() { return require('./model.js'); },
	fields: [
		{
			field: 'mustExistForms',
			type: 'entityPicklist[]',
			kind: 'editable',
			caption: 'All these forms must be added',
			typeOptions: {
				entityCategory: 2,
				childFormsOnly: true,
				canCreate: false,
				parentEntityName: 'sys/case',
				filter: 'mustExistFormsFilter',
			},
		},
		{
			field: 'mustMatchAll',
			type: 'toggle',
			caption: 'Must Match All',
			kind: 'editable',
			typeOptions: {
				greyOnOff: false,
				allowNull: true,
				showCaptionsOnButton: true,
				checkedTranslation: { key: 'all' },
				uncheckedTranslation: { key: 'any' },
			},
		},
	],
});
