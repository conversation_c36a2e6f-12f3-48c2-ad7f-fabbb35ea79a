/* globals $appData */
const _ = require('lodash');
const entities = require('../../entities');
const dynamicEntities = require('../../dynamic-entities');
const BBModel = require('../../public/lib/backbone-model.js');

module.exports = BBModel.extend({
	urlRoot: `${$appData.globalConfig.apiRoot}/entity_evaluator_context`,
	entity: {
		base: 'sys',
		name: 'entityEvaluatorContext',
	},
	defaults: {
		mustMatchAll: true,
	},
	rules: {
		isTypeCaseChildFormsMustExist: ({ type }) => type === 'caseChildFormsMustExist',
		hasMultipleForms: ({mustExistForms}) => _.size(mustExistForms) > 1,
		mustMatchAll: ({mustMatchAll}) => mustMatchAll === true,
	},
	idAttribute: 'id',
	preinitialize(attributes, options){
		BBModel.prototype.preinitialize.call(this, attributes, options);
		if (options.parentModel.get('submissionType') === 'Portal') {
			const mustExistFormsCanon = [];
			_.each(attributes.mustExistForms__canon, (entityCanon) => {
				if (entities.get(entityCanon)) {
					mustExistFormsCanon.push(entityCanon);
					return;
				}
				if (dynamicEntities.get({ entityName: entityCanon })){
					const entDef = dynamicEntities.get({ entityName: entityCanon });
					if (entDef.allowOnPortal) {
						mustExistFormsCanon.push(entityCanon);
						return;
					}
				}
				mustExistFormsCanon.push(null);
			});
			attributes.mustExistForms__canon = mustExistFormsCanon;
		}
	},
});
