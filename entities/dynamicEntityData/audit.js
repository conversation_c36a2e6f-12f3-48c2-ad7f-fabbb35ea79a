
const reference = {
	displayFields(data, auditModel) {
		return auditModel.hideCaseItemNumberReference() !== true ? ['childNumber'] : [];
	},
};

module.exports = {
	child: true,
	parentType: {
		base: 'sys',
		name: 'case',
	},
	parentFieldId: 'caseId',
	allowNavigateTo: true,
	cmd: {
		load: {
			viewed: {
				options: {
					reference,
				},
			},
		},
		save: {
			created: {
				options: {
					reference,
				},
			},
			updated: {
				options: {
					changes: {
						excludeFields: ['id'],
					},
					reference,
				},
			},
		},
		remove: {
			deleted: {
				options: {
					reference,
				},
			},
		},
		translate_record: {
			status: {
				condition: {
					composite: 'all',
					rules: [
						{
							path: 'entity.intakeTranslationStatus',
							comparator: 'exists',
						},
					],
				},
				options: {
					changes: {
						displayFields: ['intakeTranslationStatus'],
					},
					reference,
				},
			},
		},
	},
};
