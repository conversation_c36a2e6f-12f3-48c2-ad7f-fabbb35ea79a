const _ = require('lodash');
const extend = require('../extend.js');
const standardChildConfig = require('../standard-child-config.js');
const standardIntakeTranslationConfig = require('../standard-intake-translation-config.js');

const standardIntakeTranslationChildConfig = extend(
	standardIntakeTranslationConfig, standardChildConfig,
);

module.exports = extend(standardIntakeTranslationChildConfig, {
	db: 'default',
	search: true,
	customForm: false,
	table: 'sys_dynamic_entity_data',
	entity: {
		base: 'sys',
		name: 'dynamic_entity_data',
	},
	normalizeMultiValuePicklistEntries: true,
	validation: require('./validation.js'),
	allowAdvancedSearch: false,
	allowQuickSearch: false,
	allowPurge: false,
	allowRecordLinking: false,
	dataExport: false,
	report: false,
	dynamicDataStore: true,
	enablePortalUserNotifications: true,
	enableTranslation: true,
	enableRecordSourceView: true,
	caption: 'Dynamic Entity Data',
	captionPlural: 'Dynamic Entities Data',
	addCaption: 'Add Dynamic Entity Data',
	newCaption: 'New Dynamic Entity Data',
	acl: require('./acl.js'),
	aclHierarchy: require('./acl-hierarchy.js'),
	audit: require('./audit.js'),
	usageStatistics: require('./usage-statistics.js'),
	staticFieldWorkflows: [
		_.cloneDeep(require('../case/static-field-workflows/external-record.js')),
		require('./static-field-workflows/dynamic-entity-data-intake-translation-status.js'),
	],
	model() {
		return require('../../public/models/dynamic-data-model.js');
	},
	view() {
		return require('../../public/views/forms/dynamic-form-details-view.js');
	},
	fields: [
		{
			field: 'entityId',
			caption: 'Entity Id',
			type: 'id',
			kind: 'hidden-editable',
			showOnPortal: true,
			excludeFromRedact: true,
			kindOptions: {
				useInDynamicJoins: true,
			},
		},
		{
			field: 'externalRecord',
			type: 'checkbox',
			caption: 'External',
			kind: 'editable',
			typeOptions: {
				allowNull: false,
			},
			kindOptions: {
				useInDynamicJoins: true,
			},
			showOnPortal: true,
			showOnHotline: true,
			excludeFromAutofill: true,
			dataImportMappableOverride: false,
		},
	],
	joins: [
		{
			referenceField: 'entityId',
			table: 'isight_dynamic_entity',
			fields: [
				'name',
				'type',
			],
		},
		{
			referenceField: 'sourceJob',
			table: 'sys_event',
			fields: [
				'name',
				'jobId',
			],
		},
	],
});
