module.exports = {
	name: 'dynamic-entity-data-intake-translation-status',
	field: 'intakeTranslationStatus',
	entity: {
		zone: undefined,
		base: 'sys',
		name: 'dynamic_entity_data',
	},
	values: [
		{
			value: 'complete',
			indicator: 'success',
		},
		{
			value: 'pending',
			indicator: 'warning',
		},
		{
			value: 'error',
			indicator: 'danger',
		},
		{
			value: null,
		},
	],
	strict: true,
	transitions: [
		{
			id: 'dynamic-entity-data-intake-translation-status-blank',
			from: [null, undefined],
			to: [null, undefined],
		},
		{
			id: 'dynamic-entity-data-intake-translation-status-initial',
			from: [null, undefined],
			to: 'pending',
		},
		{
			id: 'dynamic-entity-data-intake-translation-status-complete',
			from: 'pending',
			to: 'complete',
		},
		{
			id: 'dynamic-entity-data-intake-translation-status-error',
			from: 'pending',
			to: 'error',
		},
		{
			id: 'dynamic-entity-data-intake-translation-status-pending',
			from: ['complete', 'error'],
			to: 'pending',
		},
	],
	conditions: [],
	displayRule: 'shouldShowTranslationStatusWorkflow',
};
