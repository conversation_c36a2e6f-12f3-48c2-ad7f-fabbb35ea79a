const _ = require('lodash');

module.exports = [
	{
		category: 'dynamicEntityData',
		key: 'numberOfCustomFormsByName',
		query(knex, options, callback) {
			knex
				.select('isight_dynamic_entity.id', 'isight_dynamic_entity.name')
				.count()
				.from('sys_dynamic_entity_data')
				.innerJoin('isight_dynamic_entity', 'sys_dynamic_entity_data.entity_id', 'isight_dynamic_entity.id')
				.where('sys_dynamic_entity_data.sys_active', true)
				.andWhere('isight_dynamic_entity.sys_active', true)
				.groupBy('isight_dynamic_entity.id')
				.asCallback((err, rows) => {
					if (err) return callback(err);
					const cleanedCounts = {};
					_.each(rows, (row) => {
						cleanedCounts[row.name] = parseInt(row.count, 10);
					});
					return callback(null, cleanedCounts);
				});
		},
	},
];
