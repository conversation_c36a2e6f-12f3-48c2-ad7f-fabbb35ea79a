const _ = require('lodash');
const permHelper = require('../../lib/core/permission-helper.js');

function hasFormPermission(obj, entityId, type, roles, action) {
	if (!['custom', 'response'].includes(type)) return false;
	return _.some(roles, (role) => {
		if (type === 'custom') return role === `customForm-${action}:${entityId}`;
		if (type === 'response') return role === `responseForm-${action}:${entityId}`;
	});
}

function buildFormConditionFn(action) {
	return (obj, context) => {
		const { entityId, entityId__type: entityIdType } = obj;
		const { entDef, dynamicEntityDef, user } = context;
		const roles = user?.perm?.roles;
		const userId = user?.id;
		const userAuthenticatedBySession = user?.authenticatedBySession;
		if (!roles || roles.length === 0) return { ok: true };
		const type = dynamicEntityDef?.type || entityIdType;
		if (hasFormPermission(obj, entityId, type, roles, action)) return { ok: false };
		// External users
		if (obj.externalRecord && entDef.allowOnPortal && _.includes(roles, `${action}_custom_forms_external`)){
			const submittedCase = obj.caseId__sysActive === true;
			// prevent users from creating record on submitted cases
			if (action === 'create' && submittedCase) return { ok: true };
			const userIdIsObjCreatedBy = obj.createdBy === userId;
			const userIdIsCaseCreatedBy = obj.caseId__createdBy === userId;
			const userIdIsCaseReportedBy = obj.caseId__reportedBy === userId;
			const userAuthBySessionIsCaseCreatedBySession = obj.caseId__createdBySession
				=== userAuthenticatedBySession;
			if ((userIdIsCaseCreatedBy || userAuthBySessionIsCaseCreatedBySession)
				&& userIdIsObjCreatedBy) return { ok: false };
			if ((
				userIdIsCaseCreatedBy || userAuthBySessionIsCaseCreatedBySession || userIdIsCaseReportedBy)
				&& action === 'view'
				&& obj.externalRecord === true
				&& _.includes(roles, 'view_custom_forms_external')
			) return { ok: false };
		}
		return { ok: true };
	};
}

function buildFieldLevelAclFilterFn(action) {
	return (obj, context) => {
		const { entityId, entityId__type: entityIdType } = obj;
		const { entDef, dynamicEntityDef, user } = context;
		const filter = {};
		const roles = user?.perm?.roles;
		if (!roles || roles.length === 0) return filter;
		const type = dynamicEntityDef?.type || entityIdType;
		if (hasFormPermission(obj, entityId, type, roles, action)) return filter;
		const entDefFields = entDef?.fields();
		_.each(entDefFields, (fieldDef) => {
			if (fieldDef.restrictEdit) {
				filter[fieldDef.field] = false;
			}
		});
		return filter;
	};
}

module.exports = permHelper.initialize()
	.filterMarkForPurge()
	.filterPurge()
	.filterChildPortalFields({
		joinFieldsWhitelist: [
			'entityId__name',
		],
	})
	.filter({
		name: 'Create Form Data Restricted Fields',
		roles: ['bypass_dynamic_form_acl', 'create_custom_forms_external'],
		actions: ['save_new'],
		conditions: [
			{ attributes: { id: null }},
		],
		filters: {
			fn: buildFieldLevelAclFilterFn('create_restricted_fields'),
		},
	})
	.filter({
		name: 'Create Form Data Restricted Fields',
		roles: ['bypass_dynamic_form_acl', 'create_custom_forms_external'],
		actions: ['save_existing'],
		conditions: [
			{ attributes: { '!id': null, sysActive: false }},
		],
		filters: {
			fn: buildFieldLevelAclFilterFn('create_restricted_fields'),
		},
	})
	.filter({
		name: 'Edit Form Data Restricted Fields',
		roles: ['bypass_dynamic_form_acl', 'edit_custom_forms_external'],
		actions: ['save_existing'],
		conditions: [
			{ attributes: { '!id': null, sysActive: true }},
		],
		filters: {
			fn: buildFieldLevelAclFilterFn('edit_restricted_fields'),
		},
	})
	.required({
		name: 'View Form Data',
		roles: ['bypass_dynamic_form_acl'],
		actions: ['list', 'load', 'save_existing', 'remove'],
		conditions: [
			{ attributes: { '!id': null, sysActive: true }},
			{
				fn: buildFormConditionFn('view'),
				esFilter(context, attributePrefix = '') {
					const { entDef, user } = context;
					const roles = user?.perm?.roles;
					const accessibleEntityIds = [];
					_.each(roles, (permissionCode) => {
						if (permissionCode?.includes('customForm-view:')
							|| permissionCode?.includes('responseForm-view:')) {
							accessibleEntityIds.push(permissionCode.split(':')[1]);
						}
					});
					if (entDef?.allowOnPortal && roles.includes('view_custom_forms_external')){
						accessibleEntityIds.push(entDef.entityId);
					}
					if (accessibleEntityIds.length === 0) {
						return {
							bool: {
								must: [
									{
										exists: {
											field: 'id',
										},
									},
								],
							},
						};
					}
					const esFilter = { bool: { must_not: [] }};
					_.each(accessibleEntityIds, (entityId) => {
						const filterTerm = { term: {[`${attributePrefix}entityId._exact`]: entityId }};
						esFilter.bool.must_not.push(filterTerm);
					});
					return esFilter;
				},
				knexFilter(knex, knexion) {
					const permissionPrefixes = 'customForm|responseForm';
					const accessibleEntityIds = knexion
						// Use a regular expression to isolate the id from the view form permission
						.select(knexion.raw(`(REGEXP_MATCHES(code, '(${permissionPrefixes})-view:(.+)'))[2]`))
						.from('vw_user_permissions')
						.where('user_id', knexion.raw('u.id'))
						.whereRaw(`code SIMILAR TO '(${permissionPrefixes})-view:%'`);

					knex.whereNotIn('entity_id', accessibleEntityIds);
				},
				selectedFields(opts, callback) {
					return callback(null, ['id', 'entityId']);
				},
			},
		],
	})
	.required({
		name: 'Create Form Data',
		roles: ['bypass_dynamic_form_acl'],
		actions: ['save_new'],
		conditions: [
			{ attributes: { id: null }},
			{
				fn: buildFormConditionFn('create'),
				selectedFields(opts, callback) {
					return callback(null, ['id', 'entityId']);
				},
			},
		],
	})
	.required({
		name: 'Edit Form Data',
		roles: ['bypass_dynamic_form_acl'],
		actions: ['save_existing'],
		conditions: [
			{ attributes: { '!id': null, sysActive: true }},
			{
				fn: buildFormConditionFn('edit'),
				selectedFields(opts, callback) {
					return callback(null, ['id', 'entityId']);
				},
			},
		],
	})
	.required({
		name: 'Remove Form Data',
		roles: ['bypass_dynamic_form_acl'],
		actions: ['remove'],
		conditions: [
			{ attributes: { '!id': null, sysActive: true }},
			{
				fn: buildFormConditionFn('remove'),
				selectedFields(opts, callback) {
					return callback(null, ['id', 'entityId']);
				},
			},
		],
	})
	.required({
		name: 'Access Draft Form Data on Intake',
		roles: ['access_others_draft_children'],
		actions: ['list', 'load', 'save_existing', 'remove'],
		conditions: [permHelper.conditionForInternalRecord,
			{ attributes: { '!id': null, sysActive: false, createdBy: '{!user.id}' }},
		],
	})
	.requireCaseLoadInheritance()
	.requireCaseSaveInheritance()
	.value();
