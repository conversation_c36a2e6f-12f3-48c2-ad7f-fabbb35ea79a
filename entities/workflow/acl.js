const permHelper = require('../../lib/core/permission-helper.js');

module.exports = permHelper.initialize()
	.required({
		name: 'View Workflow Records',
		roles: ['view_dynamic_workflow_records'],
		actions: ['load', 'list'],
		conditions: [],
	})
	.required({
		name: 'View Workflow Settings',
		roles: ['view_dynamic_workflow'],
		actions: ['save_new', 'save_existing', 'remove'],
		conditions: [],
	})
	.required({
		name: 'Create Workflow',
		roles: ['create_dynamic_workflow'],
		actions: ['save_new'],
		conditions: [{
			attributes: {
				id: null,
			},
		}],
	})
	.required({
		name: 'Create Workflow',
		roles: ['create_dynamic_workflow'],
		actions: ['save_existing'],
		conditions: [{
			attributes: {
				'!id': null,
				sysSubmitted: false,
			},
		}],
	})
	.required({
		name: 'Edit Workflow',
		roles: ['edit_dynamic_workflow'],
		actions: ['save_existing'],
		conditions: [{
			attributes: {
				'!id': null,
				sysSubmitted: true,
			},
		}],
	})
	.required({
		name: 'Remove Workflow',
		roles: ['remove_dynamic_workflow'],
		actions: ['remove'],
		conditions: [],
	})
	.required({
		name: 'Save/Delete Hard Locked Workflow',
		roles: ['save_delete_hard_locked_workflow'],
		actions: ['save_new', 'save_existing', 'remove'],
		conditions: [{
			attributes: {
				hardLocked: true,
			},
		}],
	})
	.value();
