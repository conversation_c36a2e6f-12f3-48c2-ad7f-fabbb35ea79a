const statHelper = require('../../shared/stat-helper.js')('sys_workflow');

module.exports = [
	{
		category: 'workflows',
		key: 'totalActiveWorkflows',
		query: statHelper.countActiveEntity({
			where() {
				this.where('status', 'active');
			},
		}),
	},
	{
		category: 'workflows',
		key: 'totalInactiveWorkflows',
		query: statHelper.countActiveEntity({
			where() {
				this.where('status', 'inactive');
			},
		}),
	},
];
