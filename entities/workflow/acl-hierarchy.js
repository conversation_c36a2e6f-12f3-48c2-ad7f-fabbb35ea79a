const permHelper = require('../../lib/core/permission-helper.js');

module.exports = [{
	type: 'group',
	caption: 'Workflows',
	permission: 'view_workflow_settings',
	parentPermission: 'settings',
	options: [{
		type: 'group',
		caption: 'Workflow',
		permission: 'workflow',
		sequence: 1,
		options: [
			{
				permission: 'view_dynamic_workflow',
				caption: 'View Workflow Settings',
				tooltip: 'View the Workflow settings page',
				sequence: 1,
			},
			{
				permission: 'create_dynamic_workflow',
				caption: 'Create',
				tooltip: 'Add Workflow',
				sequence: 2,
				dependencies: ['view_dynamic_workflow'],
			},
			{
				type: 'group',
				permission: permHelper.getEditGroupPermissionCode('workflow'),
				caption: 'Edit',
				sequence: 3,
				dependencies: ['view_dynamic_workflow'],
				options: [{
					permission: 'edit_dynamic_workflow',
					caption: 'Save',
					tooltip: 'Edit Workflow',
					dependencies: ['view_dynamic_workflow'],
				}],
			},
			{
				permission: 'remove_dynamic_workflow',
				caption: 'Remove',
				tooltip: 'Delete Workflow and/or its Statuses and Steps',
				sequence: 4,
				dependencies: ['view_dynamic_workflow'],
			},
		],
	}],
}];
