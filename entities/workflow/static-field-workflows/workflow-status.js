/* global module */

module.exports = {
	name: 'workflow-status-workflow',
	field: 'status',
	entity: {
		zone: undefined,
		base: 'sys',
		name: 'workflow',
	},
	values: [
		{
			value: 'draft',
			indicator: 'default',
		},
		{
			value: 'inactive',
			indicator: 'default',
		},
		{
			value: 'active',
			indicator: 'success',
		},
		{
			value: 'pending_changes',
			indicator: 'warning',
		},
	],
	strict: true,
	transitions: [
		{
			id: 'workflow-create',
			from: [null, undefined],
			to: ['draft', 'inactive', 'active', 'pending_changes'],
		},
		{
			id: 'workflow-activate',
			caption: 'Activate Workflow',
			from: ['draft', 'inactive'],
			to: 'active',
		},
		{
			id: 'workflow-deactivate',
			caption: 'Deactivate Workflow',
			from: ['draft', 'active'],
			to: 'inactive',
		},
		{
			id: 'workflow-edit',
			caption: 'Edit Workflow',
			from: 'active',
			to: 'pending_changes',
		},
		{
			id: 'workflow-publish-changes',
			caption: 'Publish Changes',
			from: 'pending_changes',
			to: 'active',
		},
	],
};
