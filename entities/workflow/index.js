const extend = require('../extend.js');
const standardConfig = require('../standard-config.js');

module.exports = extend(standardConfig, {
	db: 'default',
	table: 'sys_workflow',
	entity: {
		base: 'sys',
		name: 'workflow',
	},
	bypassValidationOnDraft: true,
	caption: 'Workflow',
	captionPlural: 'Workflows',
	addCaption: 'Add Workflow',
	newCaption: 'New Workflow',
	gridDescriptorField: 'name',
	acl: require('./acl.js'),
	aclRolesForCopy: ['edit_dynamic_workflow'],
	aclHierarchy: require('./acl-hierarchy.js'),
	audit: require('./audit'),
	grids: require('./grids.js'),
	computeFunctions: require('./compute-functions.js'),
	validation: require('./validation'),
	historyNav: require('./history-nav.js'),
	rules: require('./rules.js'),
	importTransformer: 'workflow',
	configurationExport: true,
	exportTransformer: 'workflow',
	copyPostAction: 'workflow',
	usageStatistics: require('./usage-statistics.js'),
	staticFieldWorkflows: [
		require('./static-field-workflows/workflow-status.js'),
	],
	model() {
		return require('../../public/models/workflow-model.js');
	},
	fields: [
		{
			field: 'entityId',
			type: 'entityPicklist',
			caption: 'Record Type',
			kind: 'editable',
			typeOptions: {
				filter: 'workflowEntitiesFilter',
				canCreate: false,
				defaultSorting: false,
				defaultEntityCanon: 'sys/case',
			},
		},
		{
			field: 'entityName',
			type: 'textbox',
			caption: 'Entity Name',
			kind: 'computed',
			kindOptions: {
				computeFunction: 'entityName',
			},
		},
		{
			field: 'conditionId',
			type: 'id',
			caption: 'Condition ID',
			kind: 'hidden-editable',
			excludeFromSaveAndCopy: true,
		},
		{
			field: 'name',
			type: 'textbox',
			caption: 'Name',
			kind: 'editable',
		},
		{
			field: 'description',
			type: 'textarea',
			caption: 'Description',
			kind: 'editable',
			typeOptions: {
				charMaxTextArea: 250,
			},
		},
		{
			field: 'visibleExternally',
			type: 'toggle',
			caption: 'Visible Externally',
			kind: 'editable',
			features: ['externalWorkflows'],
		},
		{
			field: 'defaultStateId',
			type: 'workflowState',
			caption: 'Status Type',
			kind: 'system',
			excludeFromSaveAndCopy: true,
			excludeFromAutofill: true,
			typeOptions: {
				entity: 'sys/state',
				textField: 'name',
			},
		},
		{
			field: 'status',
			type: 'picklist',
			caption: 'Status',
			typeOptions: {
				picklistName: 'workflow_statuses',
			},
			kind: 'editable',
			excludeFromRedact: true,
			excludeFromSaveAndCopy: true,
			cellTemplate(fs) {
				return fs.readFileSync(
					`${__dirname}/cell-templates/workflow-status-cell-tmpl.dust`,
					'utf8',
				);
			},
		},
		{
			field: 'entStatusField',
			caption: 'Entity Status Field',
			type: 'textbox',
			kind: 'system',
		},
		{
			field: 'entDateField',
			caption: 'Entity Date Field',
			type: 'textbox',
			kind: 'system',
		},
		{
			field: 'entSetByField',
			caption: 'Entity Set By Field',
			type: 'textbox',
			kind: 'system',
		},
		{
			field: 'entReasonField',
			caption: 'Entity Reason Field',
			type: 'textbox',
			kind: 'system',
		},
	],
});
