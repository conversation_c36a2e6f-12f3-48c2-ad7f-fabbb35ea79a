# Workflows

## Summary

Workflows are configurable by end user administrators to create custom workflows for their users. A workflow consists of Statuses (named states), Steps (named transitions between states) and Rules (automated rules that trigger actions on events such as a Step being triggered). In the sections that follow, we will refer to the new “custom workflows” simply as “workflows.”

## Usage

Workflows can be created and accessed by navigating to “Settings > Workflows > Custom Workflows”.

### Wizard

To create a new workflow, we provide an easy-to-use wizard that breaks the process down into a series of smaller steps. Once a step has been completed, users can navigate back to previous steps if they wish to make changes.

> :warning: **WARNING**: While users can navigate back to previous steps in the workflow wizard to make changes, leaving the wizard loses all progress. After leaving the wizard, users must restart from the beginning.

Before completing the wizard, users arrive at the summary step which gives an overview of the workflow that is about to be created. Upon pressing the “Finish” button, they are presented with a popup that asks whether they would like to activate the workflow or save the workflow as inactive. See the section below on statuses to learn more about each.

### Default Workflow

To provide users with out-of-the-box functionality, platform ships with a default case Status workflow. This workflow provides basic functionality for stepping cases between open and closed statuses and can be customized by users as desired.

### Entity Support

> :information_source: **NOTE**: As of 7.0 and 8.0, workflows are only designed to be created for the case entity. However, support can tentatively be added to additional entities by adding the `customWorkflows` property with a value of `true`.

### Permissions

When a workflow is activated (i.e. the workflow status changes to Active), or if new steps are added to a Pending Changes workflow and then published, ACL permissions are created in the system and must be manually assigned to user roles before users can step a case through the workflow. These permissions will not appear under “Settings > Access > User Roles > select a user role > Permissions” until the workflow has been activated or the changes published.

Without the appropriate permissions, users will still be able to see the workflow status pill on a case but will not be able to step the case through any of the workflow’s statuses.

> :warning: **WARNING**: Workflow permissions need to be manually assigned for each step before users can step a case through its statuses. By default, no permissions are assigned when a workflow is created.

### Editing

The process of editing a workflow varies based on the current workflow status.

#### Inactive Workflows

For Inactive workflows, users can make edits to the workflow as they would any other entity in Case IQ. They press Edit, make their changes, and then press Save. Changes are saved directly to the record.

#### Active Workflows

For Active workflows, however, we have designed it so that edits are made in a non-destructive manner so as to not disrupt the Active workflow being used by the system. When a user first presses Edit on an Active workflow, a complete copy of the workflow (and all of its children) is created behind the scenes. The result is a new Pending Changes workflow for the user to make their edits to. As the process of creating this Pending Changes workflow may take some time, we display a spinner to the user along with a message letting them know that we are preparing the workflow for editing.

Users can save edits to this workflow as many times as they like, and when they are finished, they can publish their changes (which copies the changes onto the Active workflow and deletes the Pending Changes workflow). Users can also choose to discard their edits at any point, thus deleting the Pending Changes workflow. Since users cannot edit an Active workflow directly, we hide the standard add/edit/delete buttons that normally appear on the workflow’s inline forms until the user creates a Pending Changes workflow. Otherwise, these buttons (including batch actions on grids) are unavailable on the workflow details page.

When it comes time for a user to publish the changes in the Pending Changes workflow, we display a popup with a summary of the changes that were made to the workflow. This popup includes information on the number of statuses, steps, and rules that were added, updated, or deleted. This can help the user understand the scope of the changes being made.

If the changes being published include one or more deleted statuses that are currently being used by existing case records, a case “status migration” needs to occur. This is to ensure that no cases are left stranded in a deleted status. We present users with another popup (that appears before the changes summary popup) which asks them to map deleted statuses to existing statuses. If a deleted status has no cases that use it, the status will not appear in the status migration popup. As the status migration occurs behind the scenes separately from the publish process (so that publishing does not have to wait for a potentially large amount of cases being updated), users will receive a notification when the status migration is complete.

> :information_source: **NOTE**: During workflow status migrations, users have the ability to choose any new status they wish. We do not enforce any of the criteria normally found on a step and so the responsibility falls on the user to choose a status that makes sense.

#### Custom Workflow Definition Statuses

In the system, a workflow definition under Settings can have one of the following statuses (much like a to-do can have a status, e.g. Pending, Closed).

The workflow status being described in this section refers to the “status” property of a workflow that represents the status of the workflow in the system. This is not to be confused with the workflow child entity called “state”, which on the front-end is shown to users as “status”. The state workflow child is used by cases to indicate the case’s status for a given workflow.

##### Draft

A workflow with a “Draft” status means that it is still in the process of being created via the wizard. This status is used by the system and is not user-facing. For the workflow record, both sysSubmitted and sysActive are false, whereas for workflow children (states, transitions, rules), sysSubmitted is true but sysActive remains false.

##### Inactive

A workflow with an “Inactive” status means that it has been fully created but is not yet being used by the system. Users can access the workflow from the workflows grid but cases will not show a status pill for this workflow, nor can a case be stepped through this workflow. The workflow record and all of its children have both sysSubmitted and sysActive set to true.

##### Active

A workflow with an “Active” status means that it has been fully created and is being used by the system. Users can access the workflow from the workflows grid and a status pill for this workflow shows on cases. Users can step a case through its statuses, assuming that the various criteria on the step have been met. The workflow record and all of its children have both sysSubmitted and sysActive set to true.

##### Pending Changes

A workflow with a “Pending Changes” status represents a copy of the “Active” workflow that was created for editing purposes. When both an “Active” and “Pending Changes” version of the workflow exist in the system, it’s the “Pending Changes” workflow that shows in the grid. However, the “Active” workflow remains in use by the system and cases will continue to reflect the status pills and steps of the “Active” workflow. See the section below on Editing for further information.

### Import / Export

Workflows are now available as an option for **Configuration Export and Import**.

During the export, only Active and Inactive workflows are exported. Any workflows that have Pending Changes copies will not have the pending changes exported.

During the import, new workflows (i.e. workflows that don’t already exist in the system with the same id) are imported as Inactive regardless of the workflow status of when it was exported. For existing workflows (i.e. workflows that exist in the system with the same id), one of two things occurs depending on the status of the existing workflow. If the existing workflow is Active, the workflow imports as a Pending Changes copy so that the existing Active workflow is unaltered. Otherwise, if the existing workflow is Inactive or Pending Changes, the imported workflow overwrites the existing workflow. Hard-locked workflows are never imported.

## Extending

Extends `standard-config`.

> :warning: **WARNING**: This is a standardized platform entity that drives out of box functionality and it is not recommended to extend this entity.

### Bootstrapping Data

#### Predefined Data

You may define workflows in your config that will be setup automatically via `make populate-workflows` as json files under `data/workflows/`.

***data/workflows/example-workflow.json***

```json
{
	"workflow": {
		"name": "Example",
		"entityName": "sys/case",
		"status": "active",
		"hardLocked": true,
		"description": "A example workflow."
	},
	"states": [
	  {
		"name": "West",
		"primaryState": "Open",
		"description": "",
		"default": true
	  },
	  {
		"name": "East",
		"primaryState": "Closed",
		"description": ""
	  }
	],
	"transitions": [
	  {
		"name": "Easternize",
		"from": "West",
		"to": "East",
		"description": ""
	  },
	  {
		"name": "Westernize",
		"from": "East",
		"to": "West",
		"description": "",
		"condition": {
		  "types": [{
			"type": "filter",
			"filters": [{
				"fieldName": "caseType",
				"operator": "is",
				"fieldValues": [
				  "Case Type 1"
				]
			}]
		  }]
		}
	  }
	],
	"rules": [
	  {
		"transition": "Easternize",
		"name": "Action Time",
		"actions": [
			{
			  "actionType": "sys/notification_action",
			  "triggerType": "Immediate",
			  "method": "email",
			  "subject": "Subject",
			  "contextUsers": ["owner"],
			  "template": "Case has changed directions"
			}
		  ]
	  }
	]
  }
```

#### Hard Locked

A `hard-locked` property can be set on workflows that puts them (and their children) in a read-only mode. This allows for workflows bundled with the app to remain unchanged by users over the lifecycle of the app.

To hard-lock a workflow, set the `hardLocked` property in the workflow JSON definition to `true`. Since the workflow is uneditable on the front-end by users, the only way to make updates to hard-locked workflows is to update the JSON file and run the sync script.
