var extend = require('../extend.js');
var standardConfig = require('../standard-config.js');

module.exports = extend(standardConfig, {
	db: 'default',
	table: 'sys_emailrule',
	rules: require('./rules.js'),
	entity: {
		base: 'sys',
		name: 'emailRule',
	},
	caption: 'Email Rule',
	captionPlural: 'Email Rules',
	addCaption: 'Add Email Rule',
	newCaption: 'New Email Rule',
	gridDescriptorField: 'ruleType',
	acl: require('./acl.js'),
	aclHierarchy: require('./acl-hierarchy.js'),
	grids: require('./grids.js'),
	historyNav: require('./history-nav.js'),
	configurationExport: true,
	audit: require('./audit.js'),
	model: function(){
		return require('../../public/models/email-rule-model.js');
	},
	collection: function(){
		return require('../../public/collections/email-rules-collection.js');
	},
	view: function(){
		return require('../../public/views/settings/system/email-rule/email-rule-details-view.js');
	},
	fields: [
		{
			caption: 'Rule Type',
			field: 'ruleType',
			type: 'picklist',
			typeOptions: {
				picklistName: 'email_rule_types',
			},
			kind: 'editable',
			gridWidth: 250,
		},
		{
			caption: 'Sender Address(es)',
			field: 'emailFrom',
			type: 'email[]',
			typeOptions: {
				linkWithSystemUser: false,
				disableWhitelist: true,
			},
			kind: 'editable',
			gridWidth: 250,
		},
		{
			caption: 'Recipient Address(es)',
			field: 'validRecipients',
			type: 'email[]',
			typeOptions: {
				linkWithSystemUser: false,
				disableWhitelist: true,
			},
			kind: 'editable',
			gridWidth: 250,
		},
		{
			caption: 'System Inbox Address',
			field: 'emailTo',
			type: 'email',
			kind: 'editable',
			gridWidth: 250,
			typeOptions: {
				linkWithSystemUser: false,
				disableWhitelist: true,
			},
		},
		{
			field: 'caseAttributes',
			type: 'json',
			kind: 'editable',
			caption: 'Case Values',
		},
		{
			caption: 'Accept All Senders',
			field: 'acceptAllSenders',
			type: 'checkbox',
			kind: 'editable',
		},
	],
});