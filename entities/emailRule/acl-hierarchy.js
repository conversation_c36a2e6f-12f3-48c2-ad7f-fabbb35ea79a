const permHelper = require('../../lib/core/permission-helper.js');

module.exports = [{
	type: 'group',
	permission: 'view_email_rule_settings',
	caption: 'Email Rules',
	parentPermission: 'view_system_settings',
	options: [
		{
			permission: 'view_email_rule',
			caption: 'View',
			tooltip: 'View email rules',
			sequence: 1,
		},
		{
			permission: 'create_email_rule',
			caption: 'Create',
			tooltip: 'Create email rules',
			sequence: 2,
			dependencies: ['view_email_rule'],
		},
		{
			type: 'group',
			permission: permHelper.getEditGroupPermissionCode('email_rule'),
			caption: 'Edit',
			sequence: 3,
			dependencies: ['view_email_rule'],
			options: [{
				permission: 'edit_email_rule',
				caption: 'Save',
				tooltip: 'Edit email rules',
				dependencies: ['view_email_rule'],
			}],
		},
		{
			permission: 'remove_email_rule',
			caption: 'Remove',
			tooltip: 'Delete email rules',
			sequence: 4,
			dependencies: ['view_email_rule'],
		},
	],
}];
