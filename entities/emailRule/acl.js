var permHelper = require('../../lib/core/permission-helper.js');

module.exports = permHelper.initialize()
	.required({
		name: 'View Email Rules',
		roles: ['view_email_rule'],
		actions: ['load', 'list', 'save_existing', 'remove'],
		conditions: [],
	})
	.required({
		name: 'Create Email Rules',
		roles: ['create_email_rule'],
		actions: ['save_new'],
		conditions: [{
			attributes: {
				id: null,
			},
		}],
	})
	.required({
		name: 'Edit Email Rules',
		roles: ['edit_email_rule'],
		actions: ['save_new'],
		conditions: [{
			attributes: {
				'!id': null,
			},
		}],
	})
	.required({
		name: 'Remove Email Rules',
		roles: ['remove_email_rule'],
		actions: ['remove'],
		conditions: [],
	})
	.value();