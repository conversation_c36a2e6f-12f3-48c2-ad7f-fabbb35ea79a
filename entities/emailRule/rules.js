var _ = require('lodash');
module.exports = {
	notAcceptingAllSenders: function (data) {
		return data.acceptAllSenders === false;
	},
	isOutgoingEmailFilter: function (data) {
		return data.ruleType === 'Outgoing Email Filter';
	},
	isNotOutgoingEmailFilter: function (data) {
		return !_.isEmpty(data.ruleType) && data.ruleType !== 'Outgoing Email Filter';
	},
	isNotIncomingEmailFilter: function (data) {
		return !_.isEmpty(data.ruleType) && data.ruleType !== 'Incoming Email Filter';
	},
	isIncomingEmailFilter: function (data){
		return data.ruleType === 'Incoming Email Filter';
	},

};