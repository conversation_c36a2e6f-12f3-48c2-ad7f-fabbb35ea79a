# Email Rules

The system provides functionality that allows for inbound emails. This functionality is controlled by Email Rules, which are criteria that, when met, trigger actions in the system or block emails.

The setup of these rules are available to admin users (via specific perms), and hence can be found under Settings.

## Rule Criteria

In order for a rule to execute an action, its criteria must be met. This criteria consists of two fields, Sender Address(es) and a System Inbox Address. Both fields represent email addresses.

### Sender Address(es)

When an email comes in to the system, for the rule to apply, the sender of the email must be included in the rule's Sender Address(es) list, which is a type of whitelist for a specific rule.

Multiple addresses can be added.

The system supports wildcard functionality, allowing users to have more generic rules and target a domain instead of a specific email address.

For example, if the rule should apply on any email that comes in from the Case IQ domain, instead of specifying multiple email addresses, a wildcard address can be specified such as `*@caseiq.com` to target all emails that come from the Case IQ domain. The `*` character represents that the email address can be anything (wildcard), proceeded with `@caseiq.com` in the address.

#### Accept All Senders

The system allows users to define rules that act on any sender address. This means that the system will not take the sender address into account when determining if an email meets the rule's criteria.

This feature can be enabled by setting `acceptAllSendersEnabled` to `true` in your config's `options.global` file and then set on specific rules.

> :warning: **WARNING**: Enabling this feature is potentially dangerous, as it opens the system up to accept every email sent to it. Rules should be as granular as possible.

### System Inbox Address

This is the address emails must be sent to in order for the rule to apply, and is the address users will be sending emails to expecting a specific rule to apply and an action to happen.

## Rule Type

A rule type represents an action that occurs when the rule criteria is met. There are currently two types of rules that can be created in the system.

### New Case Creation

This type creates new cases in the system when the criteria is met.

In this example, any emails coming from the `@caseiq.com` domain and sent to the `<EMAIL>` address will create a new case. The new case will be populated with data defined in the Case Values section.

#### Case Values

These are static values users can define when creating the rule that will be set in the case automatically when it is created by the system. These fields can be customized to each project's requirements.

#### Case Inbox

This type creates new emails in the system, attached to a specific case. The link between email and case is made by adding the case number to the subject.

The case number must match an existing case in the system, and must be at the beginning of the subject. The system separates the email subject's content by a whitespace in order to find the case number, hence any text following the case number in the subject must be separated by a white space (e.g. "CASE-1 This email is about a case").

## Extending

Extends `standard-config`.

> :warning: **WARNING**: This is a standardized platform entity that drives out of box functionality and it is not recommended to extend this entity.
