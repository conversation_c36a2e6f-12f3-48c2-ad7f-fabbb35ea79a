const extend = require('../extend.js');
const standardActionConfig = require('../standard-action-config.js');

module.exports = extend(standardActionConfig, {
	db: 'default',
	table: 'sys_document_action',
	entity: {
		base: 'sys',
		name: 'document_action',
	},
	caption: 'Generated Document',
	captionPlural: 'Generate Documents',
	addCaption: 'Add Generated Document',
	newCaption: 'New Generated Document',
	workflowActionConfig: {
		formName: 'generate-document-action',
		aclRoles: {
			createACLRole: 'agent',
			editACLRole: 'agent',
			deleteACLRole: 'agent',
		},
		supportedEntities: ['sys/case'],
	},
	validation: require('./validation'),
	audit: require('./audit'),
	usageStatistics: require('./usage-statistics.js'),
	model() {
		return require('./model');
	},
	fields: [
		{
			field: 'generatedFileType',
			type: 'picklist',
			caption: 'File Type',
			typeOptions: {
				picklistName: 'attachment_generated_file_types',
			},
			kind: 'editable',
		},
		{
			field: 'templateLocale',
			type: 'picklist',
			caption: 'Locale',
			typeOptions: {
				picklistName: 'supported_languages',
				filter: 'existingLanguagesFilter',
			},
			kind: 'editable',
		},
		{
			field: 'templateId',
			type: 'picklistApi',
			caption: 'Template Name',
			typeOptions: {
				picklistName: 'templates',
				picklistDependencies: ['templateLocale'],
			},
			kind: 'editable',
			dbIndex: true,
		},
	],
	joins: [
		{
			referenceField: 'templateId',
			table: 'sys_template',
			fields: [
				'name',
			],
		},
	],
});
