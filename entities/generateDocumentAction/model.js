/* globals $appData */
const BBModel = require('../../public/lib/backbone-model.js');

module.exports = BBModel.extend({
	urlRoot: `${$appData.globalConfig.apiRoot}/document_action`,
	entity: {
		base: 'sys',
		name: 'document_action',
	},
	idAttribute: 'id',
	defaults: {
		templateLocale: $appData.user.locale,
	},
	save(attrs, options) {
		return BBModel.prototype.save.call(this, attrs, options);
	},
});
