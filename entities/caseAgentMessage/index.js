const extend = require('../extend.js');
const standardPurgeConfig = require('../standard-purge-config.js');

module.exports = extend(standardPurgeConfig, {
	features: ['caseAssistant'],
	db: 'default',
	table: 'sys_case_agent_message',
	entity: {
		base: 'sys',
		name: 'caseAgentMessage',
	},
	audit: {
		allowNavigateTo: false,
		cmd: {
			purge: {
				purged: {
					options: {
						reference: {
							displayFields: ['createdDate'],
						},
					},
				},
			},
		},
	},
	allowAdvancedSearch: false,
	allowQuickSearch: false,
	dataExport: false,
	customForm: false,
	report: false,
	excludeFromAggregation: true,
	includeInDataDictionary: false,
	caption: 'Case Agent Message',
	captionPlural: 'Case Agent Messages',
	addCaption: 'Add Case Agent Message',
	newCaption: 'New Case Agent Message',
	fields: [
		{
			field: 'conversationId',
			type: 'id',
			caption: 'Conversation',
			kind: 'system',
		},
		{
			field: 'type',
			caption: 'Message Type',
			type: 'textbox',
			kind: 'system',
		},
		{
			field: 'content',
			caption: 'Message Content',
			type: 'texteditor',
			kind: 'editable',
		},
	],
	parents: [
		{
			entity: {
				base: 'sys',
				name: 'caseAgentConversation',
			},
			field: 'conversationId',
		},
	],
});
