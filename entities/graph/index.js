const extend = require('../extend.js');
const standardConfig = require('../standard-config.js');

module.exports = extend(standardConfig, {
	entity: {
		base: 'sys',
		name: 'graph',
	},
	search: false,
	caption: 'Graph',
	addCaption: 'Add Graph',
	newCaption: 'New Graph',
	captionPlural: 'Graphs',
	icon: 'fa-chart-bar',
	fields: [
		{
			field: 'graphType',
			type: 'picklist',
			caption: 'Graph Type',
			kind: 'system',
			typeOptions: {
				picklistName: 'graph_types',
			},
		},
		{
			field: 'dateFilter',
			type: 'picklist',
			caption: 'Date Filter',
			kind: 'system',
			typeOptions: {
				picklistName: 'date_filters',
			},
		},
		{
			field: 'startDate',
			type: 'date',
			caption: 'Start Date',
			kind: 'system',
		},
		{
			field: 'endDate',
			type: 'date',
			caption: 'End Date',
			kind: 'system',
		},
		{
			field: 'categories',
			type: 'picklistApi[]',
			caption: 'Categories',
			kind: 'system',
			typeOptions: {
				picklistName: 'usage_stats_categories',
			},
		},
		{
			field: 'sets',
			type: 'picklistApi[]',
			caption: 'Sets',
			kind: 'system',
			typeOptions: {
				picklistName: 'usage_stats_concatenated_sets_single_parent',
				picklistDependencies: ['categories'],
			},
		},
	],
});
