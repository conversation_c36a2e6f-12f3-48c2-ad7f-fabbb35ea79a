const standardConfig = require('./standard-config');
const extend = require('./extend.js');

module.exports = extend(standardConfig, {
	db: 'default',
	gridDescriptorField: 'name',
	lockWithParent: true,
	api: {
		useGenericApi: true,
	},
	parents: [
		{
			entity: {
				base: 'sys',
				name: 'workflow',
			},
			field: 'workflowId',
			reportable: true,
		},
	],
	fields: [
		{
			field: 'workflowId',
			type: 'id',
			caption: 'Workflow Id',
			kind: 'hidden-editable',
		},
	],
	joins: [
		{
			table: 'sys_workflow',
			referenceField: 'workflowId',
			fields: ['name', 'conditionId', 'hardLocked', 'status', 'sysActive'],
		},
	],
});
