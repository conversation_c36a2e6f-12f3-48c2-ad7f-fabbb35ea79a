const reference = {
	displayFields(data, auditModel) {
		const displayFields = ['actionType', 'todoType'];
		return data.responsibleUserType === 'Context'
			? displayFields.concat(['contextUser'])
			: displayFields.concat(['systemUser']);
	},
};

module.exports = {
	allowNavigateTo: true,
	child: true,
	parentType: {
		base: 'sys',
		name: 'rule',
	},
	parentFieldId: 'ruleId',
	cmd: {
		load: {
			viewed: {
				options: {
					reference,
				},
			},
		},
		save: {
			created: {
				options: {
					reference,
				},
			},
			updated: {
				options: {
					changes: {
						excludeFields: ['id'],
					},
					reference,
				},
			},
		},
		remove: {
			deleted: {
				options: {
					reference,
				},
			},
		},
	},
};
