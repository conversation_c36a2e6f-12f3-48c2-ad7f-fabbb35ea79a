module.exports = {
	mandatory$: [
		'triggerType',
		'responsibleUserType',
		'todoType',
		'details',
	],
	dependentMandatory$: [
		{
			condition: 'isTriggerTypeDelayed',
			fields: ['delay'],
		},
		{
			condition: 'isResponsibleUserTypeContext',
			fields: ['contextUser'],
		},
		{
			condition: 'isResponsibleUserTypeSystem',
			fields: ['systemUser'],
		},
		{
			condition: 'isSetDueDate',
			fields: ['numberOfDays', 'dateType'],
		},
	],
	numberOfDays: {
		min$: 0,
		max$: 1095,
	},
};
