const extend = require('../extend.js');
const standardActionConfig = require('../standard-action-config.js');

module.exports = extend(standardActionConfig, {
	table: 'sys_automated_todo_action',
	entity: {
		base: 'sys',
		name: 'automated_todo_action',
	},
	caption: 'Automated To-Do',
	captionPlural: 'Automated To-Dos',
	addCaption: 'Add Automated To-Do',
	newCaption: 'New Automated To-Do',
	rules: require('./rules.js'),
	validation: require('./validation.js'),
	audit: require('./audit.js'),
	usageStatistics: require('./usage-statistics.js'),
	model() {
		return require('../../public/models/automated-todo-action-model.js');
	},
	workflowActionConfig: {
		formName: 'automated-todo-action',
		aclRoles: {
			createACLRole: 'agent',
			editACLRole: 'agent',
			deleteACLRole: 'agent',
		},
		unsupportedEntities: ['sys/person'],
		unsupportedEvents: {
			'sys/email': ['unassigned-incoming-mail'],
		},
	},
	fields: [
		// To-do Content
		{
			caption: 'Responsible User Type',
			field: 'responsibleUserType',
			type: 'radio',
			typeOptions: {
				orientation: 'vertical',
				picklistName: 'responsible_user_types',
			},
			kind: 'editable',
		},
		{
			caption: 'Context User',
			field: 'contextUser',
			type: 'fieldPicklist',
			typeOptions: {
				entityNameField: 'targetEntity',
				refreshOnFields: ['targetEntity'],
				filterType: ['user'],
				// Setting to empty array so typeOption is cleared with data dictionary sync
				excludeKind: [], // TODO remove in 8.2.0
			},
			kind: 'editable',
		},
		{
			caption: 'System User',
			field: 'systemUser',
			type: 'user',
			typeOptions: {
				roleFilter: 'todo_owner',
			},
			kind: 'editable',
		},
		{
			caption: 'Details',
			field: 'details',
			type: 'texteditor',
			kind: 'editable',
		},
		{
			caption: 'To-Do Type',
			field: 'todoType',
			type: 'picklist',
			typeOptions: {
				picklistName: 'todo_types',
			},
			kind: 'editable',
		},
		{
			caption: 'Set Due Date',
			field: 'setDueDate',
			type: 'checkbox',
			typeOptions: {
				allowNull: false,
			},
			kind: 'editable',
		},
		{
			caption: 'Send Email Reminder',
			field: 'emailReminder',
			type: 'numberRangePicklist',
			typeOptions: {
				startValue: 0,
				endValue: 30,
			},
			kind: 'editable',
		},
		{
			caption: 'Date Type',
			field: 'dateType',
			type: 'radio',
			typeOptions: {
				picklistName: 'date_types',
			},
			kind: 'editable',
		},
		{
			caption: 'Number Of Days',
			field: 'numberOfDays',
			type: 'number',
			typeOptions: {
				precision: 2,
				scale: 0,
			},
			kind: 'editable',
		},
	],
});
