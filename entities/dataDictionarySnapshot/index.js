const extend = require('../extend');
const standardConfig = require('../standard-config.js');

module.exports = extend(standardConfig, {
	db: 'default',
	table: 'sys_data_dictionary_snapshot',
	entity: {
		base: 'sys',
		name: 'data_dictionary_snapshot',
	},
	caption: 'Data Dictionary Snapshot',
	captionPlural: 'Data Dictionary Snapshots',
	addCaption: 'Add Data Dictionary Snapshot',
	newCaption: 'New Data Dictionary Snapshot',
	acl: require('./acl.js'),
	dataExport: false,
	allowPurge: false,
	report: false,
	search: false,
	fields: [
		{
			field: 'fileId',
			type: 'code',
			kind: 'system',
			caption: 'File Id',
		},
		{
			field: 'fileName',
			type: 'textbox',
			kind: 'system',
			caption: 'File Name',
		},
	],
});
