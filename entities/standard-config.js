var extend = require('./extend.js');
var standardPurgeConfig = require('./standard-purge-config.js');

module.exports = extend(standardPurgeConfig, {
	search: true,
	allowPurge: false,
	fields: [
		{
			caption: 'Lists',
			field: 'userLists',
			type: 'code[]',
			kind: 'custom',
			kindOptions: {
				flags: {
					audit: false,
					schema: true,
					search: true,
					apiWritable: true,
					apiExternalWritable: false,
					computedOnSave: false,
					computedOnRead: false,
					formVisible: false,
					gridVisible: false,
					gridSortable: false,
					searchVisible: false,
					formattedData: true,
					gridExportable: false,
					reportable: false,
				},
			},
			excludeFromRedact: true,
			excludeFromAutofill: true,
		},
		{
			field: 'transitions',
			type: 'picklistApi',
			caption: 'Transitions',
			kind: 'custom',
			excludeFromAutofill: true,
			kindOptions: {
				flags: {
					audit: false,
					schema: false,
					search: false,
					apiWritable: false,
					apiExternalWritable: false,
					computedOnSave: false,
					computedOnRead: false,
					gridExportable: false,
					formVisible: false,
					gridVisible: false,
					gridSortable: false,
					searchVisible: false,
					formattedData: false,
					reportable: false,
				},
			},
			typeOptions: {
				picklistName: 'entity_transitions',
			},
		},
		{
			field: 'hardLocked',
			type: 'yesno',
			caption: 'Hard Locked',
			kind: 'custom',
			kindOptions: {
				flags: {
					audit: true,
					schema: true,
					search: true,
					apiWritable: false,
					apiExternalWritable: false,
					computedOnSave: false,
					computedOnRead: false,
					formVisible: true,
					gridVisible: false,
					gridSortable: false,
					searchVisible: true,
					formattedData: true,
					gridExportable: false,
					reportable: false,
				},
			},
			showOnHotline: true,
		},
		{
			caption: 'Yellowfin Context User ID',
			field: 'contextUserId',
			type: 'id',
			kind: 'custom',
			excludeFromSaveAndCopy: true,
			excludeFromAutofill: true,
			kindOptions: {
				flags: {
					audit: false,
					schema: false,
					search: false,
					apiWritable: false,
					apiExternalWritable: false,
					computedOnSave: false,
					computedOnRead: false,
					formVisible: false,
					gridVisible: false,
					gridSortable: false,
					searchVisible: false,
					formattedData: false,
					gridExportable: false,
					reportable: false,
				},
			},
			yfSourceFilter: true,
		},
		{
			field: 'apiType',
			type: 'textbox',
			caption: 'API Type',
			kind: 'custom',
			excludeFromAutofill: true,
			kindOptions: {
				flags: {
					audit: false,
					schema: true,
					search: false,
					apiWritable: true,
					apiExternalWritable: true,
					computedOnSave: false,
					computedOnRead: false,
					formVisible: false,
					gridVisible: false,
					gridSortable: false,
					searchVisible: false,
					formattedData: false,
					gridExportable: false,
					reportable: false,
				},
			},
			showOnPortal: true,
			showOnHotline: true,
		},
		{
			caption: 'Exclude From Suggest Links',
			field: 'excludeFromSuggestedLinks',
			type: 'yesno',
			kind: 'custom',
			kindOptions: {
				flags: {
					audit: true,
					schema: true,
					search: true,
					apiWritable: false,
					apiExternalWritable: false,
					computedOnSave: false,
					computedOnRead: false,
					formVisible: true,
					gridVisible: true,
					gridSortable: true,
					searchVisible: true,
					formattedData: true,
					gridExportable: true,
					reportable: false,
				},
			},
			excludeFromAutofill: true,
			showOnHotline: true,
		},
		{
			caption: 'Source Tag',
			field: 'sourceTag',
			type: 'textbox',
			kind: 'hidden',
			excludeFromAutofill: true,
		},
		{
			caption: 'Source Data',
			field: 'sourceData',
			type: 'script',
			kind: 'hidden',
			excludeFromAutofill: true,
		},
		{
			caption: 'Source Id',
			field: 'sourceId',
			type: 'textbox',
			kind: 'hidden',
			excludeFromAutofill: true,
			dataImportMappableOverride: true,
		},
		{
			field: 'sourceJob',
			caption: 'Source Job',
			type: 'id',
			kind: 'system',
		},
		{
			field: 'recordSource',
			type: 'picklist',
			typeOptions: {
				picklistName: 'record_sources',
			},
			caption: 'Record Source',
			kind: 'system',
			excludeFromAutofill: true,
			excludeFromRedact: true,
			showOnPortal: true,
			showOnHotline: true,
			alwaysInApiSelectedFields: true,
			dataImportDefaultValue: 'data_import',
		},
		{
			field: 'copiedFromId',
			type: 'id',
			caption: 'Copied From Id',
			kind: 'hidden-searchable',
		},
		{
			field: 'copiedToId',
			type: 'id',
			caption: 'Copied To Id',
			kind: 'hidden-searchable',
		},
		{
			caption: 'Date Submitted',
			field: 'dateSubmitted',
			type: 'datetime',
			kind: 'custom',
			excludeFromAutofill: true,
			kindOptions: {
				computeFunction: 'dateSubmitted',
				flags: {
					audit: false,
					schema: true,
					search: true,
					apiWritable: true,
					apiExternalWritable: false,
					computedOnSave: true,
					computedOnRead: false,
					formVisible: true,
					gridVisible: true,
					gridSortable: true,
					searchVisible: true,
					formattedData: true,
					gridExportable: true,
					reportable: true,
				},
			},
			showOnHotline: true,
			showOnPortal: true,
		},
		{
			caption: 'Computed Search',
			field: 'computedSearch',
			type: 'json',
			kind: 'hidden',
			excludeFromSaveAndCopy: true,
			// We redact computedSearch explicitly
			excludeFromRedact: true,
			excludeFromAutofill: true,
			alwaysInApiSelectedFields: true,
		},
	],
	computeFunctions: require('./standard-compute-functions.js'),
});
