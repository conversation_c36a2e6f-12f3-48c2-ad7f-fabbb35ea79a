const extend = require('../extend.js');
const standardConfig = require('../standard-config.js');

const parentField = 'gridFilterId';
const parentEntity = {
	base: 'sys',
	name: 'gridFilter',
};

module.exports = extend(standardConfig, {
	db: 'default',
	table: 'sys_grid_static_filter',
	entity: {
		base: 'sys',
		name: 'gridStaticFilter',
	},
	search: false,
	caption: 'Grid Static Filter',
	captionPlural: 'Grid Static Filters',
	addCaption: 'Add Grid Static Filter',
	newCaption: 'New Grid Static Filter',
	parents: [
		{
			entity: parentEntity,
			field: parentField,
		},
	],
	fields: [
		{
			field: parentField,
			type: 'id',
			kind: 'editable',
			caption: 'Filter',
			dbIndex: true,
		},
		{
			field: 'filterName',
			type: 'code',
			kind: 'editable',
			caption: 'Field',
		},
		{
			field: 'selectedFilterOptions',
			type: 'code[]',
			kind: 'editable',
			caption: 'Selected Filter Option',
		},
	],
	joins: [
		{
			referenceField: parentField,
			table: 'sys_grid_filter',
			fields: [
				'createdBy',
				'gridName',
				'userId',
			],
		},
	],
});
