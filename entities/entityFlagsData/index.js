const extend = require('../extend.js');
const standardConfig = require('../standard-config.js');

module.exports = extend(standardConfig, {
	db: 'default',
	table: 'sys_entity_flags_data',
	search: false, // system table, used for reporting but not searching
	entity: {
		base: 'sys',
		name: 'entityFlagsData',
	},
	caption: 'Entity Flag Data',
	captionPlural: 'Entity Flags Data',
	addCaption: 'Add Entity Flag Data', // shouldn't be required as table isn't exposed to ui
	newCaption: 'New Entity Flag Data', // shouldn't be required as table isn't exposed to ui
	allowMetaDataHardDelete: true,
	fields: [
		{
			field: 'entityName',
			type: 'textbox',
			caption: 'Entity Name',
			kind: 'system',
		},
		{
			field: 'entityRecordId',
			type: 'code',
			caption: 'Entity Record Id',
			kind: 'system',
		},
		{
			field: 'flagId',
			type: 'code',
			caption: 'Flag Id',
			kind: 'system',
		},
	],
});
