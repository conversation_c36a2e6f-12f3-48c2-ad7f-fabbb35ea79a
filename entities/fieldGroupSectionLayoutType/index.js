const _ = require('lodash');
const extend = require('../extend.js');
const layoutEnt = require('../layout/index.js');

module.exports = extend(layoutEnt, {
	createTable: false,
	reuseEsIndex: {
		index: 'sys_layout',
		typeField: 'layoutType',
	},
	layoutType: 'field-group-section',
	entity: {
		base: 'sys',
		name: 'field_group_section_layout_type',
	},
	caption: 'Field Group Section',
	captionPlural: 'Field Group Sections',
	addCaption: 'Add Field Group Section',
	newCaption: 'New Field Group Section',
	importTransformer: 'field_group_section_layout_type',
	exportTransformer: 'field_group_section_layout_type',
	customDependencies: entityService => [
		..._.map(entityService.getRootLayoutEntities(), 'entityCanon'), 'isight/entity', 'isight/dynamic_entity',
	],
	validation: require('./validation.js'),
	model() {
		return require('../../public/models/layout-field-group-section-model.js');
	},
});
