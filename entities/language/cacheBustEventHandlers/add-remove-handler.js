/* global $appData */

const $ = require('jquery');
const sharedUtils = require('../../../shared/utils.js')();
const BackboneEvents = require('../../../public/lib/backbone-events.js');

const appDataCategory = 'languages';

function sendBackboneEvent(opts) {
	BackboneEvents.trigger('languages-updated', opts);
}

module.exports = function languagesUpdateHandler(opts) {
	const utils = require('../../../public/lib/utils.js');
	const { patchData } = opts;
	if (patchData) {
		utils.patch({
			appDataCategory,
			data: patchData,
			identifierProperties: ['id'],
		});
		sendBackboneEvent(opts);
	} else {
		$.ajax({
			url: `${$appData.globalConfig.apiRoot}/languages`,
			method: 'GET',
			dataType: 'json',
			contentType: 'application/json',
		}).done((data) => {
			sharedUtils.updateObject($appData[appDataCategory], data);
			sendBackboneEvent(opts);
		});
	}
};
