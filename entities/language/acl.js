const permHelper = require('../../lib/core/permission-helper.js');

module.exports = permHelper.initialize()
	.required({
		name: 'View Languages',
		roles: ['view_language'],
		actions: ['load', 'list', 'save_existing', 'remove'],
		conditions: [],
	})
	.required({
		name: 'Create Language',
		roles: ['create_language'],
		actions: ['save_new'],
		conditions: [{
			attributes: {
				id: null,
			},
		}],
	})
	// Users should not be granted the `edit_language` role since we don't support editing languages
	.required({
		name: 'Edit Languages',
		roles: ['edit_language'],
		actions: ['save_existing'],
		conditions: [{
			attributes: {
				'!id': null,
			},
		}],
	})
	.required({
		name: 'Remove Languages',
		roles: ['remove_language'],
		actions: ['remove'],
		conditions: [],
	})
	.value();
