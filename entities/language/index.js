const extend = require('../extend.js');
const standardConfig = require('../standard-config.js');

module.exports = extend(standardConfig, {
	db: 'default',
	table: 'sys_language',
	entity: {
		base: 'sys',
		name: 'language',
	},
	caption: 'Language',
	captionPlural: 'Languages',
	addCaption: 'Add Language',
	newCaption: 'New Language',
	audit: require('./audit.js'),
	acl: require('./acl.js'),
	aclHierarchy: require('./acl-hierarchy.js'),
	grids: require('./grids.js'),
	validation: require('./validation.js'),
	historyNav: require('./history-nav.js'),
	cacheBustGroups: require('./cache-bust-groups.js'),
	report: false,
	excludeFromAggregation: true,
	configurationExport: true,
	configurationImport: true,
	importTransformer: 'language',
	model() {
		return require('../../public/models/language-model.js');
	},
	collection() {
		return require('../../public/collections/languages-collection.js');
	},
	view() {
		return require('../../public/views/settings/system/languages/language-details-view.js');
	},
	fields: [
		{
			field: 'value',
			type: 'picklist',
			typeOptions: {
				picklistName: 'supported_languages',
			},
			caption: 'Language',
			kind: 'system',
		},
	],
});
