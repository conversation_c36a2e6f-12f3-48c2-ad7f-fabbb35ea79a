const permHelper = require('../../lib/core/permission-helper.js');

module.exports = [{
	type: 'group',
	parentPermission: 'view_system_settings',
	caption: 'Options',
	permission: 'option',
	options: [
		{
			permission: 'view_option',
			caption: 'View',
			tooltip: 'View Options',
			sequence: 2,
		},
		{
			permission: 'create_option',
			caption: 'Create',
			sequence: 1,
			disabled: true,
		},
		{
			type: 'group',
			permission: permHelper.getEditGroupPermissionCode('option'),
			caption: 'Edit',
			sequence: 4,
			dependencies: ['view_option'],
			options: [{
				permission: 'edit_option',
				caption: 'Save',
				tooltip: 'Edit Options',
				dependencies: ['view_option'],
			}],
		},
		{
			permission: 'edit_static_option',
			caption: 'Edit Static',
			sequence: 5,
			disabled: true,
		},
		{
			permission: 'remove_option',
			caption: 'Remove',
			sequence: 6,
			disabled: true,
		},
	],
}];
