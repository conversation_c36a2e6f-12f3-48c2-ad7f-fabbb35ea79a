const validationUtil = require('../../plugins/entity-validator/utils');

const defaultFieldDef = { type: 'textbox' };

module.exports = {
	dependentMandatory$: [
		{
			condition: 'isSSOEnabled',
			// option keys
			fields: [
				'ssoProtocol',
				'ssoCert',
				'ssoSPEntityId',
				'ssoIdPUrl',
				'ssoIdentifierMapping',
				'ssoIdentifierDataMapping',
				'ssoUrl',
			],
		},
		{
			condition: 'isSSOAutoUserProvisionEnabled',
			fields: ['ssoAutoUserProvisionUserRoleFallback'],
		},
		{
			condition: 'isCaseNumberUsingISELEnabled',
			fields: ['caseNumberExpression'],
		},
	],
	maxLength$: [
		{
			name: 'value',
			value(option) {
				return validationUtil.getCharMaxLength(option.fieldDef ?? defaultFieldDef);
			},
		},
	],
	minLength$: [
		{
			name: 'value',
			value(option) {
				return validationUtil.getCharMinLength(option.fieldDef ?? defaultFieldDef);
			},
		},
	],
	pattern$: [
		{
			fields: ['caseNumberPrefix', 'caseNumberSeparator'],
			code: 'noWhitespacesAllowed$',
			pattern: '^\\S*$',
		},
	],
	expression$: [
		{
			field: 'caseNumberExpression',
			entityCanon: 'sys/case',
			thisContextProperty: 'caseNumber',
		},
	],
};
