const permHelper = require('../../lib/core/permission-helper.js');

module.exports = permHelper.initialize()
	.required({
		name: 'View Options',
		roles: ['view_option'],
		actions: ['load', 'list', 'save_existing', 'remove'],
		conditions: [],
	})
	.required({
		name: 'Create Options',
		roles: ['create_option'],
		actions: ['save_new'],
		conditions: [{
			attributes: {
				id: null,
			},
		}],
	})
	.required({
		name: 'Edit Options',
		roles: ['edit_option'],
		actions: ['save_new'],
		conditions: [{
			attributes: {
				'!id': null,
			},
		}],
	})
	.required({
		name: 'Edit Static Options',
		roles: ['edit_static_option'],
		actions: ['save_new'],
		conditions: [{
			attributes: {
				staticFlag: true,
			},
		}],
	})
	.required({
		name: 'Remove Options',
		roles: ['remove_option'],
		actions: ['remove'],
		conditions: [],
	})
	.value();
