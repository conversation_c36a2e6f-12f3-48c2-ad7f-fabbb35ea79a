const extend = require('../extend.js');
const standardConfig = require('../standard-config.js');

module.exports = extend(standardConfig, {
	db: 'default',
	table: 'sys_shared_link_receipt',
	entity: {
		base: 'sys',
		name: 'shared_link_receipt',
	},
	caption: 'Shared Link Receipt',
	captionPlural: 'Shared Links Receipt',
	addCaption: 'Add Shared Link Receipt',
	newCaption: 'New Shared Link Receipt',
	acl: require('./acl.js'),
	grids: require('./grids.js'),
	allowPurge: true,
	dataExport: true,
	includeInDataDictionary: true,
	api: {
		useGenericApi: true,
		mainFileField: 'file',
	},
	features: ['fileShare'],
	model() {
		return require('../../public/models/shared-link-receipt-model.js');
	},
	collection() {
		return require('../../public/collections/shared-link-receipt-collection.js');
	},
	parents: [
		{
			entity: {
				base: 'sys',
				name: 'shared_link',
			},
			field: 'sharedLinkId',
		},
	],
	fields: [
		{
			field: 'sharedLinkId',
			type: 'id',
			caption: 'Shared Link ID',
			kind: 'editable',
			excludeFromRedact: true,
		},
		{
			field: 'attachmentId',
			type: 'id',
			caption: 'Attachment ID',
			kind: 'editable',
			excludeFromRedact: true,
		},
		{
			field: 'file',
			type: 'file[]',
			caption: 'File',
			kind: 'editable',
		},
		{
			field: 'recipient',
			type: 'email',
			caption: 'Recipient',
			kind: 'editable',
		},
		{
			field: 'receipt',
			type: 'picklist',
			typeOptions: {
				picklistName: 'read_receipt_options',
			},
			caption: 'Receipt',
			kind: 'editable',
			cellTemplate(fs) {
				return fs.readFileSync(
					`${__dirname}/cell-templates/shared-link-receipt-cell-tmpl.dust`,
					'utf8',
				);
			},
		},
		{
			field: 'readOn',
			type: 'datetime',
			caption: 'Last Read On',
			kind: 'editable',
		},
		{
			field: 'ipAddress',
			type: 'loginIp',
			caption: 'IP Address',
			kind: 'editable',
		},
		{
			field: 'originallyReadOn',
			type: 'datetime',
			caption: 'Originally Read On',
			kind: 'system',
		},
	],
	joins: [
		{
			referenceField: 'sharedLinkId',
			table: 'sys_shared_link',
			fields: [
				'status',
			],
		},
	],
});
