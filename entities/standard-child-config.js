var _ = require('lodash');
var extend = require('./extend.js');
var standardConfig = require('./standard-config.js');

var reference = {
	displayFields: function(data, auditModel) {
		return auditModel.hideCaseItemNumberReference() !== true ? ['childNumber'] : [];
	},
};

module.exports = extend(standardConfig, {
	// TODO use computed field once developed
	autoGenerateNumber: true,
	allowAdvancedSearch: true,
	allowQuickSearch: true,
	allowPurge: true,
	dataExport: true,
	customForm: true,
	report: true,
	allowRecordLinking: true,
	dataImportConfig: {
		parentMappingGrid: 'data-import-mappings-parent',
		editableGridFields: ['parentRecordTarget', 'source'],
	},
	gridDescriptorField: 'childNumber',
	audit: {
		child: true,
		parentType: {
			base: 'sys',
			name: 'case',
		},
		parentFieldId: 'caseId',
		allowNavigateTo: true,
		cmd: {
			load: {
				'viewed': {
					options: {
						reference,
					},
				},
			},
			save: {
				'created': {
					options: {
						reference,
					},
				},
				'updated': {
					options: {
						changes: {
							excludeFields: ['id'],
						},
						reference,
					},
				},
			},
			remove: {
				'deleted': {
					options: {
						reference,
					},
				},
			},
		},
	},
	esDefaultFilters: function(){
		var query = this.query('es');
		return query.and([
			query.is_not('caseId__canceled', true),
			query.is_not('sysActive', false),
			query.is_not('sysSubmitted', false),
		]).toQuery();
	},
	historyNav: [
		{ from: [], to: '/case/{caseId}/activity/forms' },
		{ from: [], to: '/forms' },
	],
	computeFunctions: {
		childNumber: function (seneca, rows, callback) {
			var caseIds = _.map(rows, 'caseId');
			var caseEnt = seneca.delegate({
				user$: {id: 0},
				perm$: null,
				noAudit$: true,
				showInactiveRecords: true,
			}).make$('sys/case');
			caseEnt.list$({
				ids: caseIds,
			}, function(err, caseList){
				if (err) return callback(err);
				var childNumbers = _.map(rows, function(row){
					var parentCase = _.filter(caseList, { id: row.caseId })[0];
					var number = parseInt(row.number, 10);
					var childNumber;
					if(parentCase){
						var caseNumber = parentCase.caseNumber;
						if (_.isString(caseNumber) && _.isNumber(number)) {
							childNumber = caseNumber + ' / ' + number;
						}
					}else if(number && _.isNumber(number) && !_.isNaN(number)){
						childNumber = number;
					}else{
						childNumber = null;
					}
					return childNumber;
				});
				callback(null, childNumbers);
			});
		},
	},
	rowTemplates: {
		small: function(){
			return require('../public/templates/forms/row-small.dust');
		},
		medium: function(){
			return require('../public/templates/forms/row-medium.dust');
		},
	},
	parents: [
		{
			entity: {
				base: 'sys',
				name: 'case',
			},
			reportable: true,
			field: 'caseId',
		},
	],
	fields: [
		{
			field: 'caseId',
			type: 'case',
			caption: 'Case',
			kind: 'editable-external',
			kindOptions: {
				useInDynamicJoins: true,
			},
			excludeFromRedact: true,
			excludeFromAutofill: true,
			cellTemplate: function (fs) {
				return fs.readFileSync(`${__dirname}/../public/templates/cell-templates/child-case-id-cell-tmpl.dust`, 'utf8');
			},
			showOnPortal: true,
			showOnHotline: true,
			dataImportMappableOverride: false,
		},
		{
			field: 'number',
			type: 'number',
			kind: 'system',
			kindOptions: {
				useInDynamicJoins: true,
			},
			caption: '#',
			excludeFromSaveAndCopy: true,
			excludeFromRedact: true,
			excludeFromAutofill: true,
			cellTemplate: function (fs) {
				return fs.readFileSync(`${__dirname}/../public/templates/cell-templates/child-standard-number-cell-tmpl.dust`, 'utf8');
			},
			showOnPortal: true,
			showOnHotline: true,
		},
		{
			field: 'childNumber',
			type: 'sequenceNumber',
			caption: 'Item Number',
			kind: 'computed-search',
			excludeFromRedact: true,
			excludeFromAutofill: true,
			kindOptions: {
				computeFunction: 'childNumber',
			},
			showOnPortal: true,
			showOnHotline: true,
			cellTemplate: function (fs) {
				return fs.readFileSync(`${__dirname}/../public/templates/cell-templates/child-number-cell-tmpl.dust`, 'utf8');
			},
			gridWidth: 190,
		},
	],
	queryFilter: function({query, args}){
		return query.and([
			args.showInactiveRecords !== true && query.is('sysActive', true),
			args.showCanceledCases$ !== true && query.is_not('caseId__canceled', true),
		]);
	},
});
