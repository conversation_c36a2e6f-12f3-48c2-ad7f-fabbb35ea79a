const getSupportedFileExtensionsForTableOfContentsTemplates = require(
	'../plugins/packets/getSupportedFileExtensionsForTableOfContentsTemplates',
);


module.exports = {
	fields: [
		{
			field: 'name',
			type: 'textbox',
			caption: 'Packet Name',
			kind: 'editable',
		},
		{
			field: 'shouldAddPageNumbers',
			type: 'checkbox',
			caption: 'Apply page numbering',
			default: false,
			kind: 'editable',
		},
		{
			field: 'shouldAddTableOfContents',
			type: 'checkbox',
			caption: 'Generate a Table of Contents (ToC)',
			default: false,
			kind: 'editable',
		},
		{
			field: 'tableOfContentsTemplateKind',
			caption: 'ToC Template',
			type: 'radio',
			default: null,
			typeOptions: {
				picklistName: 'packet_table_of_contents_template_kinds',
				orientation: 'horizontal',
			},
			kind: 'editable',
		},
		{
			field: 'tableOfContentsTemplate',
			caption: 'Template file',
			type: 'file[]',
			default: null,
			excludeFromSaveAndCopy: true,
			kind: 'editable',
			typeOptions: {
				supportedFormats: getSupportedFileExtensionsForTableOfContentsTemplates(),
				maxFileCount: 1,
			},
		},
		{
			field: 'caseFilesInclusionKind',
			caption: 'Include Files attached to Case',
			type: 'radio',
			default: 'no',
			typeOptions: {
				picklistName: 'case_file_inclusion_kinds',
				orientation: 'horizontal',
			},
			subText: 'value',
			kind: 'editable',
		},
		{
			field: 'documentGenerationMethod',
			type: 'picklistSelectize',
			typeOptions: {
				picklistName: 'packet_generation_methods',
			},
			caption: 'Document generation method',
			default: 'always_new',
			kind: 'editable',
		},
	],
};
