var extend = require('../extend.js');
var standardConfig = require('../standard-config.js');

module.exports = extend(standardConfig, {
	db: 'default',
	search: false,
	audit: false,
	table: 'sys_passwordhistory',
	entity: {
		base: 'sys',
		name: 'passwordhistory',
	},
	caption: 'Password History',
	captionPlural: 'Password History Items',
	addCaption: 'Add Password History',
	newCaption: 'New Password History',
	acl: require('./acl.js'),
	fields: [
		{
			field: 'userId',
			type: 'user',
			kind: 'system',
			caption: 'User',
		},
		{
			field: 'salt',
			type: 'textbox',
			kind: 'hidden',
			caption: 'Salt',
		},
		{
			field: 'password',
			type: 'textbox',
			kind: 'hidden',
			caption: 'Password',
		},
		{
			field: 'iterations',
			type: 'number',
			caption: 'Password Iterations',
			kind: 'hidden',
		},
		{
			field: 'keyLength',
			type: 'number',
			caption: 'Password Key Length',
			kind: 'hidden',
		},
		{
			field: 'digest',
			type: 'textbox',
			caption: 'Password Digest',
			kind: 'hidden',
		},
	],
});
