var permHelper = require('../../lib/core/permission-helper.js');

module.exports = permHelper.initialize()
	.required({
		name: 'View Password History',
		roles: ['view_password_history'],
		actions: ['load', 'list', 'save_existing', 'remove'],
		conditions: [],
	})
	.required({
		name: 'Create Password History',
		roles: ['create_password_history'],
		actions: ['save_new'],
		conditions: [{
			attributes: {
				id: null,
			},
		}],
	})
	.required({
		name: 'Edit Password History',
		roles: ['edit_password_history'],
		actions: ['save_new'],
		conditions: [{
			attributes: {
				'!id': null,
			},
		}],
	})
	.required({
		name: 'Remove Password History',
		roles: ['remove_password_history'],
		actions: ['remove'],
		conditions: [],
	})
	.value();