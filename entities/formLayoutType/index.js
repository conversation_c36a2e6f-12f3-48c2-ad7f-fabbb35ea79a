const extend = require('../extend.js');
const layoutEnt = require('../layout');

module.exports = extend(layoutEnt, {
	createTable: false,
	reuseEsIndex: {
		index: 'sys_layout',
		typeField: 'layoutType',
	},
	search: true,
	rootLayoutType: true,
	layoutType: 'form',
	entity: {
		base: 'sys',
		name: 'form_layout_type',
	},
	caption: 'Form',
	captionPlural: 'Forms',
	addCaption: 'Add Form',
	newCaption: 'New Form',
	configurationExport: true,
	importTransformer: 'form_layout_type',
	exportTransformer: 'form_layout_type',
	validation: require('./validation.js'),
	rules: require('./rules.js'),
	cacheBustGroups: require('./cache-bust-groups.js'),
	model() {
		return require('../../public/models/layout-form-model.js');
	},
});
