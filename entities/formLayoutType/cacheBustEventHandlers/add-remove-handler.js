/* global $appData */
const $ = require('jquery');
const sharedUtils = require('../../../shared/utils.js')();
const BackboneEvents = require('../../../public/lib/backbone-events.js');

const appDataCategory = 'layouts';

function setLayouts(callback) {
	$.ajax({
		url: `${$appData.globalConfig.apiRoot}/active_layouts`,
		method: 'GET',
		dataType: 'json',
		contentType: 'application/json',
	})
		.done((data) => {
			sharedUtils.updateArray($appData[appDataCategory], data);
			callback();
		});
}

module.exports = function handler(opts) {
	const utils = require('../../../public/lib/utils.js');
	const { patchData } = opts;
	// use patch data to patch $appData
	if (patchData) {
		utils.patch({
			appDataCategory,
			data: patchData,
			identifierProperties: ['id', 'name'],
		});
		BackboneEvents.trigger('form-layout-updated', opts);
	} else {
		// otherwise, $appData have to be loaded via requests
		setLayouts(() => BackboneEvents.trigger('form-layout-updated', opts));
	}
};
