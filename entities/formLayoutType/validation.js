module.exports = {
	mandatory$: [
		'caption',
		'captionPlural',
		'entityType',
	],
	pattern$: [{
		fields: ['caption'],
		pattern: '[a-zA-Z0-9]',
		code: 'atLeastOneLetterOrNumber$',
	}],
	numberRange$: [
		{
			number: 'sequence',
			minimumNumber: {
				number: 1,
			},
		},
	],
	dependentMandatory$: [
		{
			condition: 'isFormLevelLookup',
			fields: [
				'lookupMethod',
				'mappingMode',
			],
		},
		{
			condition: 'isHttpRequestMethod',
			fields: [
				'endpoint',
				'httpMethod',
				'authenticationMethod',
			],
		},
		{
			condition: 'isBasicAuthentication',
			fields: [
				'username',
				'password',
			],
		},
		{
			condition: 'isExpressionMode',
			fields: ['iselExpression'],
		},
		{
			condition: 'isOAuthAuthentication',
			fields: [
				'grantType',
				'accessTokenEndpoint',
				'clientId',
				'clientSecret',
				'clientAuthenticationOption',
			],
		},
	],
};
