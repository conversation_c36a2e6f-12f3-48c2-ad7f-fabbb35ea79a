const PendingRequest = Symbol.for('PendingRequest');

module.exports = {
	isLocked(data) {
		return data.locked === true;
	},
	isShowOnIntake(data) {
		return data.hideOnIntake === false && !data[PendingRequest];
	},
	isShowOnPortal(data) {
		return data.showOnPortal === true;
	},
	isShowOnHotline(data) {
		return data.showOnHotline === true;
	},
	canShowOnIntake(data) {
		return data.allowShowOnIntake !== false && data.entityType !== 'data';
	},
	isPartyForm(data){
		return data.entityName === 'sys/party';
	},
	isCaseForm(data){
		return data.entityName === 'sys/case';
	},
	isDataFormType(data) {
		return data.entityType === 'data';
	},
	isCustomFormType(data) {
		return data.entityType === 'custom';
	},
	isResponseFormType(data) {
		return data.entityType === 'response';
	},
	isProfileForm(data) {
		return data.entityName === 'sys/person';
	},
	isFormLevelLookup(data) {
		return data.lookup === true;
	},
	isHttpRequestMethod(data){
		return data.lookupMethod === 'http_request';
	},
	isPutPostMethod(data){
		return data.httpMethod === 'put' || data.httpMethod === 'post';
	},
	isBasicAuthentication(data){
		return data.authenticationMethod === 'basic';
	},
	isExpressionMode(data) {
		return data.mappingMode === 'expression';
	},
	isOAuthAuthentication(data) {
		return data.authenticationMethod === 'oAuth';
	},
	isSimpleMode(data) {
		return data.mappingMode === 'simple';
	},
};
