const permHelper = require('../../lib/core/permission-helper.js');

module.exports = permHelper.initialize()
	.filterMarkForPurge()
	.filterPurge()
	.required({
		name: 'View Rules',
		roles: ['view_rule'],
		actions: ['load', 'list'],
		conditions: [],
	})
	.required({
		name: 'Create Rule',
		roles: ['create_rule'],
		actions: ['save_new'],
		conditions: [{
			attributes: {
				id: null,
			},
		}],
	})
	.required({
		name: 'Create Rule',
		roles: ['create_rule'],
		actions: ['save_existing'],
		conditions: [{
			attributes: {
				'!id': null,
				sysSubmitted: false,
			},
		}],
	})
	.required({
		name: 'Edit Rule',
		roles: ['edit_rule'],
		actions: ['save_existing'],
		conditions: [{
			attributes: {
				'!id': null,
				sysSubmitted: true,
			},
		}],
	})
	.required({
		name: 'Remove Rule',
		roles: ['remove_rule'],
		actions: ['remove'],
		conditions: [],
	})
	.value();
