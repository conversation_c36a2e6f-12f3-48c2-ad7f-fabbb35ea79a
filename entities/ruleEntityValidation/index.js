const extend = require('../extend.js');
const standardConfig = require('../standard-config.js');

module.exports = extend(standardConfig, {
	db: 'default',
	table: 'sys_rule_entity_validation',
	entity: {
		base: 'sys',
		name: 'rule_entity_validation',
	},
	api: {
		useGenericApi: true,
	},
	historyNav: [
		{ from: [], to: '/settings/workflow/case-submission-rules' },
	],
	caption: 'Rule Entity Validation',
	captionPlural: 'Rule Entity Validation',
	addCaption: 'Add Rule Entity Validation',
	newCaption: 'New Rule Entity Validation',
	gridDescriptorField: 'name',
	audit: require('./audit.js'),
	acl: require('./acl.js'),
	validation: require('./validation.js'),
	rules: require('./rules.js'),
	model() { return require('./model.js'); },
	grids: require('./grids.js'),
	collection() { return require('./collection.js'); },
	exportCaption: 'Case Submission Rules',
	configurationExport: true,
	importTransformer: 'rule_entity_validation',
	fields: [
		{
			field: 'criteriaId',
			type: 'id',
			caption: 'Criteria ID',
			kind: 'hidden-editable',
			excludeFromSaveAndCopy: true,
		},
		{
			field: 'validationId',
			type: 'id',
			caption: 'Validation',
			kind: 'hidden-editable',
			excludeFromSaveAndCopy: true,
		},
		{
			field: 'entityId',
			caption: 'Entity Id',
			type: 'id',
			kind: 'hidden-editable',
		},
		{
			field: 'submissionType',
			caption: 'Submission Type',
			type: 'picklist',
			typeOptions: {
				picklistName: 'rule_entity_submission_types',
			},
			kind: 'editable',
		},
		{
			field: 'name',
			caption: 'Name',
			type: 'textbox',
			kind: 'editable',
		},
	],
	joins: [
		{
			referenceField: 'entityId',
			table: 'isight_entity',
			fields: [
				'base',
				'name',
			],
		},
	],
});
