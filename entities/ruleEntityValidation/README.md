### Rule Entity Validation
This entity is used to validate records of a specific entity. It holds the information that the validation engine `RuleEntityValidationEngine` will use.

### Case Submission Rule Functionality
This feature is used to validate `sys/case`'s records on submission.

Rules can be created under workflow's settings. See below for [rule's structure](#rule-structure).

### Case Submission Rule Implementation
When a case is submitted the API `api-entity-validate.js` is called to validate the record.

The API will then use the engine `RuleEntityValidationEngine` to run the validation.

If the validation passes the case will be able to be sumbitted, otherwise the user will be warned on the missing requirements of the rule. This is handled by the views mentioned in [files](#files).

There is a case workflow in place to validate the records when being submitted as well.

### Validation Implementation

`RuleEntityValidationEngine` will run the validation.

The engine works with the `entity-validator.js` plugin.

The validation will run as follow:

- All rules in the system that target the record's entity will be loaded.
- Each rule will be validated at a time.
- If the rule's submission type doesn't match the record's type, then it will not be validated.
- The rule's criteria's condition, if any exists, will be evaluated first. If the criteria are not met the validation will not be evaluated.
- After the criteria was evaluated the validation's condition will be evaluated.
- The result from the validation will be an array of results of each rule.

Example of results:

```js
[
	{
		critieraMet: true,
		validationId: '67f5f8d2-1bd6-4463-8d63-983433ee6bfc',
		validationPasses: false,
		ruleId: '0a590e72-234a-455f-b394-04576ee0e232',
		conditionResponse: [
			{
				id: '5891f27c-035c-42b7-8a5f-c7d2980101eb',
				conditionType: 'caseChildFormsMustExist',
				failed: true,
				additionalData: {
					'sys/party': false
				}
			}
		]
	},
	{
		critieraMet: false,
		validationId: '67f5f8d2-1bd6-4463-8d63-983433ee6bfc',
		validationPasses: true,
		ruleId: '0a590e72-234a-455f-b394-04576ee0e232',
		conditionResponse: {}
	}
]
```

`conditionResponse` is the result from the plugin [`ConditionEvaluator.js#evaluate`](https://github.com/i-Sight/isight_main_v5_beta/blob/develop/plugins/condition/ConditionEvaluator.js#L17).


### Rule Structure
`entityId` - Id of entity to be validated - mandatory.

`validationId` - Condition id to be evaluated for record's validation - mandatory.

`criteriaId` - Condition id that's required to be met for record to be validated - optional.

`submissionType` - The type of records to be validated - mandatory.

`name` - Name of the rule - mandatory.

### Files

`plugins/entity-validator/validation-engines/rule-entity-validation/RuleEntityValidationEngine.js`

`test/api/rule-entity-validation/test.rule-entity-validation.js`

`test/plugins/entity-validator/rule-entity-validation/test.RuleEntityValidationEngine.js`

`test/acl/test.rule-entity-validation.js`

`test/api/export-import/test.export-import-rule-entity-validation.js`


### Case Submission Rule Files

`public/views/case/case-submission-rule/case-submit-context-rule-view.js`

`public/views/case/case-submission-rule/case-submit-modal-view.js`

`public/views/case/case-submission-rule/case-submit-rule-view.js`

`lib/services/business-logic/case/workflow/case-submission-rule-validation.js`