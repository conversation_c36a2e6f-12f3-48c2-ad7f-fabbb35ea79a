const reference = {
	displayFields: (data, auditModel) => {
		const fields = [];
		fields.push('recordType');
		if (data?.partyId) fields.push('partyId');
		return fields;
	},
};

module.exports = {
	child: true,
	parentType: {
		base: 'sys',
		name: 'case',
	},
	parentFieldId: 'caseId',
	allowNavigateTo: false,
	cmd: {
		save: {
			created: {
				options: {
					reference,
				},
			},
			updated: {
				options: {
					changes: {
						excludeFields: ['id'],
					},
					reference,
				},
			},
		},
	},
};
