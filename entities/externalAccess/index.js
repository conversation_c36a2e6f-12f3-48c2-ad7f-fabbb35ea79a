const extend = require('../extend.js');
const standardChildConfig = require('../standard-child-config.js');

module.exports = extend(standardChildConfig, {
	db: 'default',
	table: 'sys_external_access',
	entity: {
		base: 'sys',
		name: 'external_access',
	},
	customForm: false,
	ruleEvents: true,
	caption: 'External Access',
	captionPlural: 'External Access',
	addCaption: 'Grant External Access',
	newCaption: 'Grant External Access',
	acl: require('./acl.js'),
	validation: require('./validation.js'),
	grids: require('./grids.js'),
	audit: require('./audit.js'),
	historyNav: [],
	features: ['externalCaseAccess'],
	model() {
		return require('../../public/models/external-access-model.js');
	},
	collection() {
		return require('../../public/collections/external-access-collection.js');
	},
	fields: [
		{
			field: 'caseId',
			caption: 'Case',
			type: 'case',
			kind: 'submit-only',
		},
		{
			field: 'recordType',
			caption: 'Record Type',
			type: 'code',
			kind: 'submit-only',
		},
		{
			field: 'recordId',
			caption: 'Record Id',
			type: 'id',
			kind: 'submit-only',
		},
		{
			field: 'userId',
			caption: 'User',
			type: 'user',
			kind: 'submit-only',
			typeOptions: {
				includeHidden: true,
			},
		},
		{
			field: 'partyId',
			caption: 'Party',
			type: 'party',
			kind: 'submit-only',
		},
		{
			field: 'grantType',
			caption: 'Grant Type',
			type: 'picklist',
			kind: 'editable',
			typeOptions: {
				picklistName: 'external_access_grant_types',
			},
		},
		{
			field: 'grantedDate',
			caption: 'Granted Date',
			type: 'datetime',
			kind: 'system',
		},
		{
			field: 'grantedBy',
			caption: 'Granted By',
			type: 'user',
			kind: 'system',
		},
		{
			field: 'accessExpiry',
			caption: 'Access Expiry',
			type: 'datetime',
			kind: 'editable',
		},
		{
			field: 'revokedDate',
			caption: 'Revoked Date',
			type: 'datetime',
			kind: 'system',
		},
		{
			field: 'revokedBy',
			caption: 'Revoked By',
			type: 'user',
			kind: 'system',
		},
		{
			field: 'sharedRecordCount',
			caption: 'Shared Record Count',
			type: 'number',
			kind: 'system',
		},
	],
	joins: [
		{
			referenceField: 'partyId',
			table: 'sys_party',
			fields: ['partyType'],
		},
	],
});
