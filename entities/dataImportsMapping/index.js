const extend = require('../extend.js');
const standardConfig = require('../standard-config.js');

module.exports = extend(standardConfig, {
	db: 'default',
	table: 'sys_data_import_mapping',
	grids: require('./grids.js'),
	entity: {
		base: 'sys',
		name: 'data_import_mapping',
	},
	joins: [{
		referenceField: 'dataImportId',
		table: 'sys_data_import',
		fields: [
			'name',
			'importEntity',
		],
	}],
	api: {
		useGenericApi: false,
	},
	caption: 'Data Import Mapping',
	captionPlural: 'Data Import Mappings',
	addCaption: 'Add Data Import Mapping',
	newCaption: 'New Data Import Mapping',
	customDependencies: ['sys/data_import'],
	model() {
		return require('../../public/models/data-import-mapping-model.js');
	},
	validation: require('./validation'),
	view() { return require('../../public/views/settings/system/data-imports/data-imports-mapping-view.js'); },
	collection(){
		return require('../../public/collections/data-import-mapping-collection.js');
	},
	fields: [
		{
			field: 'dataImportId',
			type: 'id',
			kind: 'editable',
			caption: 'Data Import Id',
			dbIndex: true,
			excludeFromRedact: true,
		},
		{
			field: 'source',
			caption: 'Source',
			type: 'textbox',
			kind: 'editable',
			features: ['dataImport'],
		},
		{
			field: 'target',
			caption: 'Target',
			type: 'fieldPicklist',
			typeOptions: {
				entityNameField: 'dataImportId__importEntity',
				filterFlag: ['apiWritable', 'formVisible'],
				skipFilterFlag: 'dataImportMappableOverride',
				dropdownParent: 'body',
				captionFieldTypeFilter: ['workflowStatus'],
				skipNullifyIfDeleted: true,
			},
			features: ['dataImport'],
			kind: 'editable',
		},
		{
			field: 'isRequired',
			caption: 'Required',
			type: 'checkbox',
			kind: 'editable',
			features: ['dataImport'],
		},
		{
			field: 'uniqueIdentifier',
			caption: 'Unique Identifier',
			type: 'checkbox',
			kind: 'editable',
			features: ['dataImport'],
		},
		{
			field: 'generatedByFileUpload',
			caption: 'Generated By File Upload',
			type: 'yesno',
			kind: 'hidden-editable',
			features: ['dataImport'],
		},
		{
			field: 'parentRecordType',
			caption: 'Reference Record Type',
			type: 'entityPicklist',
			kind: 'editable',
			features: ['dataImport'],
			typeOptions: {
				valueField: 'canon',
				canCreate: false,
				entityCategory: 0,
				renderWithIcons: true,
			},
		},
		{
			field: 'parentRecordTarget',
			caption: 'Reference Record Target',
			type: 'fieldPicklist',
			typeOptions: {
				entityNameField: 'parentRecordType',
				filterFlag: ['apiWritable', 'formVisible'],
				skipFilterFlag: 'dataImportMappableOverride',
				dropdownParent: 'body',
			},
			features: ['dataImport'],
			kind: 'editable',
		},
		{
			field: 'isParent',
			caption: 'Is Parent',
			type: 'yesno',
			kind: 'hidden-editable',
			features: ['dataImport'],
		},
		{
			field: 'isAttachmentMapping',
			caption: 'Is Attachment Mapping',
			type: 'yesno',
			kind: 'hidden-editable',
			features: ['dataImport'],
		},
	],
});
