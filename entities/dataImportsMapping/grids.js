module.exports = {
	'data-import-mappings': {
		sortColumn: 'source',
		sortOrder: 'asc',
		excludeColumns: ['datePurged', 'pendingPurgeDate', 'purgeReason', 'excludeFromSuggestedLinks'],
		columns: [
			{ field: 'source' },
			{ field: 'target' },
			{ field: 'isRequired' },
		],
		defaultDynamicDataFilters: ['source', 'isRequired'],
	},
	'data-import-mappings-parent': {
		sortColumn: 'source',
		sortOrder: 'asc',
		excludeColumns: ['datePurged', 'pendingPurgeDate', 'purgeReason', 'excludeFromSuggestedLinks'],
		columns: [
			{field: 'source'},
			{field: 'parentRecordType'},
			{field: 'parentRecordTarget'},
			{field: 'isRequired'},
		],
		defaultDynamicDataFilters: ['source', 'isRequired'],
	},
	'data-import-mappings-exclude-source': {
		sortColumn: 'parentRecordType',
		sortOrder: 'asc',
		excludeColumns: ['datePurged', 'pendingPurgeDate', 'purgeReason', 'excludeFromSuggestedLinks'],
		columns: [
			{ field: 'parentRecordType' },
			{ field: 'parentRecordTarget' },
		],
	},
};
