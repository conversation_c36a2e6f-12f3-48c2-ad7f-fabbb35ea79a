var permHelper = require('../../lib/core/permission-helper.js');

module.exports = permHelper.initialize()
	.required({
		name: 'View Holiday Entries',
		roles: ['view_holiday_entry'],
		actions: ['load', 'list', 'save_existing', 'remove'],
		conditions: [],
	})
	.required({
		name: 'Create Holiday Entries',
		roles: ['create_holiday_entry'],
		actions: ['save_new'],
		conditions: [{
			attributes: {
				id: null,
			},
		}],
	})
	.required({
		name: 'Edit Holiday Entries',
		roles: ['edit_holiday_entry'],
		actions: ['save_new'],
		conditions: [{
			attributes: {
				'!id': null,
			},
		}],
	})
	.required({
		name: 'Remove Holiday Entries',
		roles: ['remove_holiday_entry'],
		actions: ['remove'],
		conditions: [],
	})
	.value();