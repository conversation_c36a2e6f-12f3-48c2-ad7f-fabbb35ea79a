const permHelper = require('../../lib/core/permission-helper.js');

module.exports = [{
	type: 'group',
	permission: 'view_holiday_entry_settings',
	caption: 'Holidays',
	parentPermission: 'view_data_settings',
	options: [
		{
			caption: 'View',
			tooltip: 'View holiday calendar rules',
			sequence: 2,
			disabled: true,
			permission: 'view_holiday_entry',
		},
		{
			permission: 'create_holiday_entry',
			caption: 'Create',
			tooltip: 'Create holiday calendar rules',
			sequence: 1,
		},
		{
			type: 'group',
			permission: permHelper.getEditGroupPermissionCode('holiday_entry'),
			caption: 'Edit',
			sequence: 3,
			options: [{
				permission: 'edit_holiday_entry',
				caption: 'Save',
				tooltip: 'Edit holiday calendar rules',
			}],
		},
		{
			permission: 'remove_holiday_entry',
			caption: 'Remove',
			tooltip: 'Delete holiday calendar rules',
			sequence: 4,
		},
	],
}];
