var extend = require('../extend.js');
var standardConfig = require('../standard-config.js');

module.exports = extend(standardConfig, {
	db: 'default',
	table: 'sys_holiday_entry',
	entity: {
		base: 'sys',
		name: 'holiday_entry',
	},
	caption: 'Holiday',
	captionPlural: 'Holidays',
	addCaption: 'Add Holiday Calendar',
	newCaption: 'New Holiday Calendar',
	gridDescriptorField: 'name',
	acl: require('./acl.js'),
	aclHierarchy: require('./acl-hierarchy.js'),
	configurationExport: true,
	configurationImport: true,
	importTransformer: 'holiday_entry',
	grids: require('./grids.js'),
	validation: require('./validation.js'),
	historyNav: require('./history-nav.js'),
	icon: 'fa-calendar-day',
	usageStatistics: require('./usage-statistics.js'),
	audit: require('./audit.js'),
	model: function () {
		return require('../../public/models/holiday-calendar-model.js');
	},
	collection: function () {
		return require('../../public/collections/holiday-calendar-collection.js');
	},
	view: function () {
		return require('../../public/views/settings/data/holiday-calendar/holiday-calendar-view.js');
	},
	calendarView() {
		return require('../../public/views/settings/data/holiday-calendar/holiday-calendar-event-view.js');
	},
	api: {
		useGenericApi: true,
		writableFields: [
			'intersect',
			'reduceBy',
			'excludeDays',
		],
	},
	calendarMapping: {
		title: 'name',
		startDate: 'dateFrom',
		endDate: 'dateTo',
		allDay: true,
	},
	fields: [
		{
			field: 'dateFrom',
			type: 'date',
			kind: 'editable',
			caption: 'Start Date',
			dbIndex: true,
		},
		{
			field: 'dateTo',
			type: 'date',
			kind: 'editable',
			caption: 'End Date',
		},
		{
			field: 'name',
			type: 'textbox',
			kind: 'editable',
			caption: 'Holiday Name',
		},
	],
});
