module.exports = [
	{
		cacheKey: 'addRemoveDynamicEntity',
		triggerOnSaveRemove: false,
		condition: () => true,
		cacheBustEventHandler: require('./cacheBustEventHandlers/add-remove-entity-handler.js'),
		query: null,
	},
	{
		cacheKey: 'addRemoveDynamicEntityTranslations',
		triggerOnSaveRemove: false,
		condition: () => true,
		cacheBustEventHandler: require('../isightEntity/cacheBustEventHandlers/add-remove-translations-handler.js'),
		query: null,
	},
];
