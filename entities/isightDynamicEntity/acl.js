const permHelper = require('../../lib/core/permission-helper.js');

module.exports = permHelper.initialize()
	.required({
		name: 'View caseiq non-data dynamic entities',
		roles: ['form_builder'],
		actions: ['load', 'list', 'save_existing', 'remove'],
		conditions: [{
			attributes: {
				'!type': 'data',
			},
		}],
	})
	.required({
		name: 'Create caseiq dynamic entities',
		roles: ['form_builder'],
		actions: ['save_new'],
		conditions: [{
			attributes: {
				id: null,
			},
		}],
	})
	// no users can be granted this permission
	.filter({
		name: 'Edit Dynamic Entity Flag',
		roles: ['edit_dynamic_entity_flag'],
		actions: ['save_new', 'save_existing'],
		conditions: [{
			attributes: {
				'!id': null,
				dynamic: true,
			},
		}],
		filters: {
			dynamic: false,
		},
	})
	.required({
		name: 'Edit caseiq dynamic entities',
		roles: ['form_builder'],
		actions: ['save_new'],
		conditions: [{
			attributes: {
				'!id': null,
			},
		}],
	})
	.required({
		name: 'Remove caseiq dynamic entities',
		roles: ['form_builder'],
		actions: ['remove'],
		conditions: [],
	})
	.value();
