const _ = require('lodash');
const extend = require('../extend.js');
const isightEntity = require('../isightEntity');

module.exports = extend(isightEntity, {
	db: 'default',
	search: true,
	table: 'isight_dynamic_entity',
	entity: {
		base: 'isight',
		name: 'dynamic_entity',
	},
	acl: require('./acl.js'),
	cacheBustGroups: require('./cache-bust-groups.js'),
	importTransformer: 'dynamic_entity',
	exportTransformer: 'dynamic_entity',
	// Note: We make dynamic entities depend on root layouts even though it's actually a two-way
	// association (i.e. each references the other). The reason for this is so that root layouts,
	// which are the entity that have the status, get imported before dynamic entities.
	customDependencies: entityService => _.map(entityService.getRootLayoutEntities(), 'entityCanon'),
	customExportColumns: ['layoutId__originalId', 'copiedLayoutId__originalId'],
	usageStatistics: require('./usage-statistics.js'),
	model(){
		return require('../../public/models/entity-dynamic-model.js');
	},
	collection(){
		return require('../../public/collections/entity-collection.js');
	},
	fields: [
		{
			field: 'activationInprogress',
			caption: 'Activation Inprogress',
			type: 'yesno',
			kind: 'system',
		},
		{
			field: 'deactivationInprogress',
			caption: 'Deactivation Inprogress',
			type: 'yesno',
			kind: 'system',
		},
		{
			field: 'originalId',
			type: 'id',
			caption: 'Original Id',
			kind: 'hidden',
		},
		{
			field: 'allowOnPortal',
			caption: 'Allow on Portal',
			type: 'yesno',
			kind: 'editable',
		},
		{
			field: 'allowOnHotline',
			caption: 'Allow on Hotline',
			type: 'yesno',
			kind: 'editable',
		},
		{
			field: 'enableTranslation',
			caption: 'Automatic Translation',
			type: 'yesno',
			kind: 'editable',
			features: ['intakeTranslation'],
		},
	],
	queryFilter(opts) {
		const { query, enabledFeatures } = opts;
		const subQueries = [isightEntity.queryFilter(opts)];
		if (!enabledFeatures.includes('nonUserCollaboration')) {
			subQueries.push(query.is_not('type', 'response'));
		}
		return query.and(subQueries);
	},
});
