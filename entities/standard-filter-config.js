const standardConfig = require('./standard-config');
const extend = require('./extend.js');
const fieldKindDefinitions = require('../config/options.field-kind-definitions');

module.exports = extend(standardConfig, {
	db: 'default',
	search: false,
	api: {
		writableFields: [
			'ruleData',
			'entityName',
		],
	},
	fields: [
		{
			field: 'entityId',
			type: 'code',
			caption: 'Entity Id',
			kind: 'editable',
		},
		{
			field: 'entityName',
			type: 'textbox',
			caption: 'Entity Name',
			kind: 'editable',
		},
		{
			field: 'isDynamicEntity',
			type: 'yesno',
			caption: 'Is Dynamic Entity',
			kind: 'system',
		},
		// Denormalized filter children into a field, operator, values list for faster access
		{
			field: 'filterDenormalized',
			type: 'json[]',
			caption: 'Filter',
			kind: 'custom',
			kindOptions: {
				flags: Object.assign({}, fieldKindDefinitions.editable, { audit: false }),
			},
		},
		// TODO: Maybe think of a better name
		{
			field: 'mustMatchAll',
			type: 'yesno',
			caption: 'Must match all filter items',
			kind: 'editable',
		},
		{
			field: 'originalId',
			type: 'id',
			caption: 'Original Id',
			kind: 'hidden',
		},
		{
			field: 'restorable',
			caption: 'Restorable',
			type: 'yesno',
			kind: 'hidden',
		},
	],
});
