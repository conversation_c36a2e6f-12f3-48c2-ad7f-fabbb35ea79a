const _ = require('lodash');
const extend = require('../extend.js');
const layoutEnt = require('../layout');

module.exports = extend(layoutEnt, {
	createTable: false,
	reuseEsIndex: {
		index: 'sys_layout',
		typeField: 'layoutType',
	},
	layoutType: 'field',
	includeInElementCount: true,
	entity: {
		base: 'sys',
		name: 'field_layout_type',
	},
	validation: require('./validation.js'),
	caption: 'Field',
	captionPlural: 'Fields',
	addCaption: 'Add Field',
	newCaption: 'New Field',
	importTransformer: 'field_layout_type',
	exportTransformer: 'field_layout_type',
	customExportColumns: ['fieldId__originalId'],
	customDependencies: (entityService) => {
		const rootLayoutTypes = _.map(entityService.getRootLayoutEntities(), 'entityCanon');
		return [
			'isight/field',
			'isight/dynamic_entity_field',
			...rootLayoutTypes,
		];
	},
	model() {
		return require('../../public/models/layout-field-model.js');
	},
});
