var extend = require('../extend.js');
var standardConfig = require('../standard-config.js');

module.exports = extend(standardConfig, {
	db: 'default',
	table: 'sys_document',
	entity: {
		base: 'sys',
		name: 'document',
	},
	api: {
		useGenericApi: true,
		mainFileField: 'file',
	},
	caption: 'Document',
	captionPlural: 'Documents',
	addCaption: 'Add Document',
	newCaption: 'New Document',
	gridDescriptorField: 'fileName',
	acl: require('./acl.js'),
	exportTransformer: 'document',
	configurationExport: true,
	exportCaption: 'Library',
	historyNav: require('./history-nav.js'),
	aclHierarchy: require('./acl-hierarchy.js'),
	grids: require('./grids.js'),
	validation: require('./validation.js'),
	computeFunctions: require('./compute-functions.js'),
	audit: require('./audit.js'),
	usageStatistics: require('./usage-statistics.js'),
	model: function(){
		return require('../../public/models/document-model.js');
	},
	collection: function(){
		return require('../../public/collections/documents-collection.js');
	},
	view: function(){
		return require('../../public/views/settings/data/documents-settings/documents-settings-details-view.js');
	},
	fields: [
		{
			field: 'file',
			type: 'file[]',
			kind: 'editable',
			caption: 'File',
			alwaysInApiSelectedFields: true,
			typeOptions: { vectorize: true },
		},
		{
			field: 'fileName',
			type: 'textbox',
			kind: 'computed',
			kindOptions: { computeFunction: 'fileName' },
			caption: 'File Name',
		},
		{
			field: 'description',
			type: 'textarea',
			kind: 'computed',
			kindOptions: { computeFunction: 'description' },
			caption: 'Description',
		},
	],
});
