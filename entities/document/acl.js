var permHelper = require('../../lib/core/permission-helper.js');

module.exports = permHelper.initialize()
	.required({
		name: 'View Documents',
		roles: ['view_document', 'documents'],
		actions: ['load', 'list', 'save_existing', 'remove'],
		conditions: [],
	})
	.required({
		name: 'Create Documents',
		roles: ['create_document'],
		actions: ['save_new'],
		conditions: [{
			attributes: {
				id: null,
			},
		}],
	})
	.required({
		name: 'Edit Documents',
		roles: ['edit_document'],
		actions: ['save_new'],
		conditions: [{
			attributes: {
				'!id': null,
			},
		}],
	})
	.required({
		name: 'Remove Documents',
		roles: ['remove_document'],
		actions: ['remove'],
		conditions: [],
	})
	.value();
