const permHelper = require('../../lib/core/permission-helper.js');

module.exports = [
	{
		type: 'group',
		caption: 'Library',
		permission: 'library',
		options: [
			{
				caption: 'View',
				tooltip: 'View the Library',
				permission: 'documents',
			},
		],
	},
	{
		type: 'group',
		caption: 'Library',
		parentPermission: 'view_data_settings',
		permission: 'view_document_settings',
		options: [
			{
				permission: 'view_document',
				caption: 'View',
				tooltip: 'View library documents',
				sequence: 1,
			},
			{
				permission: 'create_document',
				caption: 'Create',
				tooltip: 'Add documents to library',
				sequence: 2,
				dependencies: ['view_document'],
			},
			{
				type: 'group',
				permission: permHelper.getEditGroupPermissionCode('document'),
				caption: 'Edit',
				sequence: 3,
				dependencies: ['view_document'],
				options: [{
					permission: 'edit_document',
					caption: 'Save',
					tooltip: 'Edit library documents',
					dependencies: ['view_document'],
				}],
			},
			{
				permission: 'remove_document',
				caption: 'Remove',
				tooltip: 'Delete library documents',
				sequence: 4,
				dependencies: ['view_document'],
			},
		],
	},
];
