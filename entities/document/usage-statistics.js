const statHelper = require('../../shared/stat-helper.js')('sys_document');
const sharedUtils = require('../../shared/utils.js')();

module.exports = [
	{
		category: 'settings_data',
		key: 'totalDocuments',
		query: statHelper.countActiveEntity(),
	},
	{
		category: 'storage',
		key: 'sizeOfLibraryDocuments',
		query(knex, options, callback) {
			const summary = {
				totalSize: 0,
			};
			const {
				pipeline,
				BulkTransformStream,
				DrainStream,
			} = options;
			const listStream = knex
				.select('sys_document.file')
				.from('sys_document')
				.where('sys_document.sys_active', true)
				.whereNotNull('sys_document.file')
				.stream();

			const accumulatorStream = sharedUtils.getStreamToCalculateTotalSize(BulkTransformStream, summary, 'file');
			const drainStream = new DrainStream();

			return pipeline(
				listStream,
				accumulatorStream,
				drainStream,
				err => callback(err, summary.totalSize),
			);
		},
	},
];
