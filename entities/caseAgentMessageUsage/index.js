const extend = require('../extend.js');
const standardPurgeConfig = require('../standard-purge-config.js');

module.exports = extend(standardPurgeConfig, {
	features: ['caseAssistant'],
	db: 'default',
	table: 'sys_case_agent_message_usage',
	entity: {
		base: 'sys',
		name: 'caseAgentMessageUsage',
	},
	audit: {
		allowNavigateTo: false,
		cmd: {
			purge: {
				purged: {
					options: {
						reference: {
							displayFields: ['createdDate'],
						},
					},
				},
			},
		},
	},
	allowAdvancedSearch: false,
	allowQuickSearch: false,
	dataExport: false,
	customForm: false,
	report: false,
	excludeFromAggregation: true,
	includeInDataDictionary: false,
	caption: 'Case Agent Message Usage',
	captionPlural: 'Case Agent Message Usages',
	addCaption: 'Add Case Agent Message Usage',
	newCaption: 'New Case Agent Message Usage',
	fields: [
		{
			field: 'messageId',
			type: 'id',
			caption: 'Message',
			kind: 'system',
		},
		{
			field: 'totalTokens',
			type: 'number',
			caption: 'Total Tokens',
			kind: 'system',
		},
		{
			field: 'promptTokens',
			type: 'number',
			caption: 'Prompt Tokens',
			kind: 'system',
		},
		{
			field: 'completionTokens',
			type: 'number',
			caption: 'Completion Tokens',
			kind: 'system',
		},
		{
			field: 'encodingName',
			caption: 'Encoding Name',
			type: 'textbox',
			kind: 'system',
		},
		{
			field: 'modelName',
			caption: 'Model Name',
			type: 'textbox',
			kind: 'system',
		},
	],
	parents: [
		{
			entity: {
				base: 'sys',
				name: 'caseAgentMessage',
			},
			field: 'messageId',
		},
	],
	joins: [
		{
			referenceField: 'messageId',
			table: 'sys_case_agent_message',
			fields: ['content', 'type', 'conversationId'],
		},
	],
});
