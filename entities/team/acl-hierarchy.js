const permHelper = require('../../lib/core/permission-helper.js');

module.exports = [{
	type: 'group',
	caption: 'Team',
	parentPermission: 'view_access_settings',
	permission: 'view_team_settings',
	options: [
		{
			caption: 'Create',
			permission: 'create_team',
			tooltip: 'Create Team',
			sequence: 1,
		},
		{
			caption: 'View',
			permission: 'view_team',
			tooltip: 'View Team',
			sequence: 2,
		},
		{
			type: 'group',
			caption: 'Edit',
			permission: permHelper.getEditGroupPermissionCode('team'),
			tooltip: 'Edit Team',
			sequence: 3,
			dependencies: ['view_team'],
			options: [
				{
					caption: 'Details',
					permission: 'edit_details',
					tooltip: 'Edit Details',
					dependencies: ['view_team'],
					sequence: 1,
				},
				{
					caption: 'Add/Remove Members',
					permission: 'add_remove_members',
					tooltip: 'Add/Remove Members',
					dependencies: ['view_team'],
					sequence: 2,
				},
			],
		},
		{
			caption: 'Remove',
			permission: 'remove_team',
			tooltip: 'Delete Team',
			sequence: 4,
			dependencies: ['view_team'],
		},
	],
}];
