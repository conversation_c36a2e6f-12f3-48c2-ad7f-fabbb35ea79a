const extend = require('../extend.js');
const standardConfig = require('../standard-config.js');

module.exports = extend(standardConfig, {
	db: 'default',
	table: 'sys_team',
	entity: {
		base: 'sys',
		name: 'team',
	},
	api: {
		useGenericApi: true,
	},
	caption: 'Team',
	captionPlural: 'Teams',
	addCaption: 'Add Team',
	newCaption: 'New Team',
	gridDescriptorField: 'name',
	configurationExport: true,
	importTransformer: 'team',
	historyNav: require('./history-nav.js'),
	grids: require('./grids.js'),
	rules: require('./rules.js'),
	acl: require('./acl.js'),
	aclHierarchy: require('./acl-hierarchy.js'),
	validation: require('./validation.js'),
	computeFunctions: require('./compute-functions.js'),
	audit: require('./audit.js'),
	usageStatistics: require('./usage-statistics.js'),
	model() {
		return require('../../public/models/team-model.js');
	},
	collection() {
		return require('../../public/collections/teams-collection.js');
	},
	gridFilterExcludeFields: ['summary'],
	fields: [
		{
			field: 'name',
			type: 'textbox',
			caption: 'Name',
			kind: 'editable',
			typeOptions: {
				charMaxTextbox: 64,
			},
		},
		{
			field: 'description',
			type: 'textarea',
			caption: 'Description',
			kind: 'editable',
			typeOptions: {
				charMaxTextArea: 250,
			},
		},
		{
			field: 'summary',
			type: 'textbox',
			caption: 'Summary',
			kind: 'computed',
			kindOptions: {
				computeFunction: 'teamSummary',
			},
		},
		{
			field: 'members',
			type: 'user[]',
			caption: 'Members',
			kind: 'editable',
			cellTemplate(fs) {
				return fs.readFileSync(
					`${__dirname}/cell-templates/team-members-cell-tmpl.dust`,
					'utf8',
				);
			},
		},
		{
			field: 'sequence',
			type: 'number',
			caption: 'Sequence',
			kind: 'editable',
		},
	],
});
