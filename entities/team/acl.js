const permHelper = require('../../lib/core/permission-helper.js');

module.exports = permHelper.initialize()
	.filterMarkForPurge()
	.filterPurge()
	.required({
		name: 'View Teams',
		roles: ['view_team'],
		actions: ['load', 'list', 'save_existing', 'remove'],
		conditions: [],
	})
	.required({
		name: 'Create Teams',
		roles: ['create_team'],
		actions: ['save_new'],
		conditions: [{
			attributes: {
				id: null,
			},
		}],
	})
	.required({
		name: 'Edit Teams',
		roles: [permHelper.getEditGroupPermissionCode('team')],
		actions: ['save_existing'],
		conditions: [{
			attributes: {
				'!id': null,
			},
		}],
	})
	.required({
		name: 'Remove Teams',
		roles: ['remove_team'],
		actions: ['remove'],
		conditions: [],
	})
	.filter({
		name: 'Edit Details',
		roles: ['edit_details'],
		actions: ['save_existing'],
		conditions: [{
			attributes: {
				'!id': null,
			},
		}],
		filters: {
			name: false,
			description: false,
		},
	})
	.filter({
		name: 'Add/Remove Member',
		roles: ['add_remove_members'],
		actions: ['save_existing'],
		conditions: [{
			attributes: {
				'!id': null,
			},
		}],
		filters: {
			members: false,
		},
	})
	.value();
