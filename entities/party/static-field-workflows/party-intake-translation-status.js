module.exports = {
	name: 'party-intake-translation-status',
	field: 'intakeTranslationStatus',
	entity: {
		zone: undefined,
		base: 'sys',
		name: 'party',
	},
	values: [
		{
			value: 'complete',
			indicator: 'success',
		},
		{
			value: 'pending',
			indicator: 'warning',
		},
		{
			value: 'error',
			indicator: 'danger',
		},
		{
			value: null,
		},
	],
	strict: true,
	transitions: [
		{
			id: 'party-intake-translation-status-blank',
			from: [null, undefined],
			to: [null, undefined],
		},
		{
			id: 'party-intake-translation-status-initial',
			from: [null, undefined],
			to: 'pending',
		},
		{
			id: 'party-intake-translation-status-complete',
			from: 'pending',
			to: 'complete',
		},
		{
			id: 'party-intake-translation-status-error',
			from: 'pending',
			to: 'error',
		},
		{
			id: 'party-intake-translation-status-pending',
			from: ['complete', 'error'],
			to: 'pending',
		},
	],
	conditions: [],
	displayRule: 'shouldShowTranslationStatusWorkflow',
};
