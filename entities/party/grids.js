const defaultDynamicDataFilters = ['partyType', 'createdDate', 'lastUpdatedDate'];
module.exports = {
	'main-parties': {
		sortColumn: 'createdDate',
		sortOrder: 'desc',
		columns: [
			{ field: 'childNumber' },
			{ field: 'partyType' },
			{ field: 'partyName' },
			{ field: 'createdDate' },
			{ field: 'primaryEntity' },
		],
		defaultDynamicDataFilters,
	},
	'main-parties-external': {
		sortColumn: 'createdDate',
		sortOrder: 'desc',
		columns: [
			{ field: 'childNumber' },
			{ field: 'createdDate' },
			{ field: 'partyName' },
			{ field: 'partyType' },
		],
	},
	'case-parties': {
		sortColumn: 'createdDate',
		sortOrder: 'desc',
		columns: [
			{ field: 'number' },
			{ field: 'partyType' },
			{ field: 'partyName' },
			{ field: 'createdDate' },
			{ field: 'primaryEntity' },
		],
		defaultDynamicDataFilters,
		default: true,
	},
	'case-parties-external': {
		sortColumn: 'createdDate',
		sortOrder: 'desc',
		columns: [
			{ field: 'number' },
			{ field: 'createdDate' },
			{ field: 'partyName' },
			{ field: 'partyType' },
		],
	},
	'case-capture-parties': {
		sortColumn: 'partyName',
		sortOrder: 'asc',
		columns: [
			{ field: 'partyType' },
			{ field: 'partyName' },
			{ field: 'primaryEntity' },
		],
	},
	'case-grant-access-party-search': {
		sortColumn: 'number',
		sortOrder: 'asc',
		columns: [
			{ field: 'number' },
			{ field: 'partyName' },
			{ field: 'partyType' },
			{ field: 'primaryEntity' },
		],
	},
	'advanced-search-result-parties': {
		sortColumn: 'createdDate',
		sortOrder: 'desc',
		columns: [
			{ field: 'childNumber' },
			{ field: 'partyType' },
			{ field: 'partyName' },
			{ field: 'createdDate' },
			{ field: 'primaryEntity' },
		],
	},
	'search-result-parties-schedule-purge': {
		sortColumn: 'createdDate',
		sortOrder: 'desc',
		columns: [
			{ field: 'childNumber' },
			{ field: 'partyType' },
			{ field: 'partyName' },
			{ field: 'createdDate' },
			{ field: 'primaryEntity' },
		],
	},
	'search-result-parties-purge': {
		sortColumn: 'createdDate',
		sortOrder: 'desc',
		columns: [
			{ field: 'childNumber' },
			{ field: 'partyType' },
			{ field: 'partyName' },
			{ field: 'createdDate' },
			{ field: 'pendingPurgeDate' },
			{ field: 'primaryEntity' },
		],
	},
	'hotline-parties': {
		sortColumn: 'firstName',
		sortOrder: 'asc',
		columns: [
			{ field: 'firstName' },
			{ field: 'lastName' },
			{ field: 'partyType' },
		],
	},
	'party-search': {
		sortColumn: 'createdDate',
		sortOrder: 'desc',
		columns: [
			{ field: 'number' },
			{ field: 'partyType' },
			{ field: 'partyName' },
			{ field: 'createdDate' },
			{ field: 'primaryEntity' },
		],
		defaultDynamicDataFilters,
	},
	'portal-parties': {
		sortColumn: 'firstName',
		sortOrder: 'asc',
		columns: [
			{ field: 'firstName' },
			{ field: 'lastName' },
			{ field: 'partyType' },
		],
	},
};
