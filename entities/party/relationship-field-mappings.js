const basePartyFieldMappings = {
	or: [
		{
			is: {
				firstName: ['firstName'],
				lastName: ['lastName'],
			},
		},
		{ is: { address: ['address'] } },
		{ is: { homePhone: ['homePhone'] } },
		{ is: { homePhone: ['workPhone'] } },
		{ is: { workPhone: ['workPhone'] } },
		{ is: { workPhone: ['homePhone'] } },
		{ is: { emailAddress: ['emailAddress'] } },
	],
};

const primaryPartyFieldMappings = {
	'sys/party': {
		and: [
			{
				is: {
					destinationField: 'primaryEntity',
					destinationEsField: 'primaryEntity__formatted',
					sourceField: 'primaryEntity',
					sourceEsField: 'primaryEntity__formatted',
					rawQuery: true,
					value: 'yes',
				},
			},
			{ ...basePartyFieldMappings },
		],
	},
};

const nonePartyFieldMappings = {
	'sys/note': {
		or: [
			{
				contains: {
					details: ['firstName', 'lastName'],
				},
			},
			{ contains: { details: ['address'] } },
			{ contains: { details: ['emailAddress'] } },
			{ contains: { details: ['homePhone'] } },
			{ contains: { details: ['workPhone'] } },
		],
	},
	'sys/appointment': {
		or: [
			{
				contains: {
					description: ['firstName', 'lastName'],
				},
			},
			{ contains: { description: ['address'] } },
			{ contains: { description: ['emailAddress'] } },
			{ contains: { description: ['homePhone'] } },
			{ contains: { description: ['workPhone'] } },
			{
				contains: {
					subject: ['firstName', 'lastName'],
				},
			},
			{ contains: { subject: ['address'] } },
			{ contains: { subject: ['emailAddress'] } },
			{ contains: { subject: ['homePhone'] } },
			{ contains: { subject: ['workPhone'] } },
		],
	},
	'sys/todo': {
		or: [
			{
				contains: {
					details: ['firstName', 'lastName'],
				},
			},
			{ contains: { details: ['address'] } },
			{ contains: { details: ['emailAddress'] } },
			{ contains: { details: ['homePhone'] } },
			{ contains: { details: ['workPhone'] } },
		],
	},
	'sys/comment': {
		or: [
			{
				contains: {
					details: ['firstName', 'lastName'],
				},
			},
			{ contains: { details: ['address'] } },
			{ contains: { details: ['emailAddress'] } },
			{ contains: { details: ['homePhone'] } },
			{ contains: { details: ['workPhone'] } },
		],
	},
	'sys/attachment': {
		or: [
			{
				contains: {
					description: ['firstName', 'lastName'],
				},
			},
			{ contains: { description: ['address'] } },
			{ contains: { description: ['homePhone'] } },
			{ contains: { description: ['workPhone'] } },
			{ contains: { description: ['emailAddress'] } },
			{
				contains: {
					'files.nameWithoutExtension._english': ['firstName', 'lastName'],
					generateSearchNames: false,
				},
			},
			{
				contains: {
					'files.nameWithoutExtension._english': ['address'],
					generateSearchNames: false,
				},
			},
			{
				contains: {
					'files.nameWithoutExtension._english': ['emailAddress'],
					generateSearchNames: false,
				},
			},
			{
				contains: {
					'files.nameWithoutExtension._english': ['homePhone'],
					generateSearchNames: false,
				},
			},
			{
				contains: {
					'files.nameWithoutExtension._english': ['workPhone'],
					generateSearchNames: false,
				},
			},
			{
				or: [
					{
						contains: {
							'attachment.content': ['firstName', 'lastName'],
							generateSearchNames: false,
						},
					},
					{
						contains: {
							'attachment.content': ['address'],
							generateSearchNames: false,
						},
					},
					{
						contains: {
							'attachment.content': ['emailAddress'],
							generateSearchNames: false,
						},
					},
					{
						contains: {
							'attachment.content': ['homePhone'],
							generateSearchNames: false,
						},
					},
					{
						contains: {
							'attachment.content': ['workPhone'],
							generateSearchNames: false,
						},
					},
				],
			},
		],
	},
	'sys/email': {
		or: [
			{
				contains: {
					body: ['firstName', 'lastName'],
				},
			},
			{
				contains: {
					subject: ['firstName', 'lastName'],
				},
			},
			{ contains: { body: ['address'] } },
			{ contains: { body: ['emailAddress'] } },
			{ contains: { body: ['homePhone'] } },
			{ contains: { body: ['workPhone'] } },
			{ contains: { subject: ['address'] } },
			{ contains: { subject: ['emailAddress'] } },
			{ contains: { subject: ['homePhone'] } },
			{ contains: { subject: ['workPhone'] } },
		],
	},
};

module.exports = [
	{
		features: ['requestCaseAccess'],
		options: [
			{ name: 'caseLinking', value: 'match_on_primary_parties_only' },
		],
		mappings: primaryPartyFieldMappings,
	},
	{
		features: ['requestCaseAccess'],
		options: [
			{ name: 'caseLinking', value: 'match_on_parties_only' },
		],
		mappings: {
			'sys/party': basePartyFieldMappings,
		},
	},
	{
		features: [],
		options: [],
		mappings: {
			'sys/party': basePartyFieldMappings,
			...nonePartyFieldMappings,
		},
	},
];
