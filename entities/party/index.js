const _ = require('lodash');
const extend = require('../extend.js');
const standardChildConfig = require('../standard-child-config.js');
const standardIntakeTranslationConfig = require('../standard-intake-translation-config.js');

const standardIntakeTranslationChildConfig = extend(
	standardIntakeTranslationConfig, standardChildConfig,
);

module.exports = extend(standardIntakeTranslationChildConfig, {
	db: 'default',
	table: 'sys_party',
	entity: {
		base: 'sys',
		name: 'party',
	},
	joins: [
		{
			referenceField: 'sourceJob',
			table: 'sys_event',
			fields: [
				'name',
				'jobId',
			],
		},
	],
	api: {
		useGenericApiExternal: true,
	},
	allowOnPortal: true,
	allowExternalSearch: true,
	normalizeMultiValuePicklistEntries: true,
	allowExternalFlag: true,
	allowOnHotline: true,
	partiallyDynamic: true,
	customForm: false,
	enablePortalUserNotifications: true,
	caption: 'Party',
	captionPlural: 'Parties',
	addCaption: 'Add Party',
	newCaption: 'New Party',
	gridDescriptorFn: (context) => {
		const { formattedData: { partyName, childNumber } } = context;
		if (!_.isEmpty(partyName)) return partyName;
		return childNumber;
	},
	allowDataAggregation: true,
	ruleEvents: true,
	rules: require('./rules.js'),
	acl: require('./acl.js'),
	aclHierarchy: require('./acl-hierarchy.js'),
	audit: require('./audit.js'),
	grids: require('./grids.js'),
	validation: require('./validation.js'),
	computeFunctions: require('./compute-functions.js'),
	historyNav: require('./history-nav.js'),
	icon: 'fa-user',
	includeInDataDictionary: true,
	enableRecordSourceView: true,
	primaryConfig: require('./primary-config.js'),
	usageStatistics: require('./usage-statistics.js'),
	allowInGridViews: true,
	linkSummaryFields: ['partyType', 'primaryEntity'],
	linkSummaryFormatFn: (context) => {
		const {
			formattedData: { partyType, primaryEntity },
			escapedTranslateKey,
		} = context;
		const primaryEntityCaption = escapedTranslateKey({
			groupName: 'sys/party',
			subgroupName: 'fields',
			key: 'primaryentity',
		});
		return `${partyType} (${primaryEntityCaption}: ${primaryEntity})`;
	},
	staticFieldWorkflows: [
		require('../case/static-field-workflows/external-record.js'),
		require('./static-field-workflows/party-intake-translation-status.js'),
	],
	model: function () {
		return require('../../public/models/party-model.js');
	},
	collection: function () {
		return require('../../public/collections/parties-collection.js');
	},
	view: function () {
		return require('../../public/views/party/party-details-view.js');
	},
	rowTemplates: {
		small: function () {
			return require('./row-templates/small.dust');
		},
		medium: function () {
			return require('./row-templates/medium.dust');
		},
		tiny: function () {
			return require('./row-templates/tiny.dust');
		},
		similarSearch: function () {
			return require('./row-templates/similar-search.dust');
		},
	},
	similarSearchConfig: require('./similar-search-config.js'),
	relationshipFieldMappings: require('./relationship-field-mappings.js'),
	includedRecordsForSuggestedLinks: (context) => {
		const {
			knex,
			enabledFeatures,
			entDef,
			optionService,
		} = context;
		if (enabledFeatures.includes('requestCaseAccess')
			&& optionService.get('caseLinking') === 'match_on_primary_parties_only') {
			knex.where(`${entDef.table}.primary_entity`, true);
		}

		return knex.whereNotNull(`${entDef.table}.id`);
	},
	fields: [
		{
			field: 'primaryEntity',
			type: 'yesno',
			caption: 'Primary',
			kind: 'editable',
			default: false,
		},
		{
			field: 'partyType',
			type: 'picklist',
			caption: 'Party Type',
			typeOptions: {
				picklistName: 'party_types',
			},
			kind: 'editable-external',
			showOnPortal: true,
			showOnHotline: true,
		},
		{
			field: 'firstName',
			type: 'textbox',
			caption: 'First Name',
			kind: 'editable-external',
			excludeFromSaveAndCopy: true,
			showOnPortal: true,
			showOnHotline: true,
		},
		{
			field: 'lastName',
			type: 'textbox',
			caption: 'Last Name',
			kind: 'editable-external',
			excludeFromSaveAndCopy: true,
			showOnPortal: true,
			showOnHotline: true,
		},
		{
			field: 'middleInitial',
			type: 'textbox',
			caption: 'Middle Initial',
			kind: 'editable-external',
			excludeFromSaveAndCopy: true,
			showOnPortal: true,
			showOnHotline: true,
		},
		{
			field: 'dateOfBirth',
			type: 'date',
			typeOptions: {
				datePickerEndDate: '0d',
			},
			caption: 'Date of Birth',
			kind: 'editable-external',
			excludeFromSaveAndCopy: true,
			showOnPortal: true,
			showOnHotline: true,
		},
		{
			field: 'partyName',
			type: 'textbox',
			caption: 'Party Name',
			kind: 'computed',
			kindOptions: {
				computeFunction: 'partyName',
			},
			excludeFromSaveAndCopy: true,
			showOnPortal: true,
			showOnHotline: true,
			dataImportMappableOverride: true,
		},
		{
			field: 'address',
			type: 'textbox',
			caption: 'Address',
			kind: 'editable-external',
			showOnPortal: true,
			showOnHotline: true,
		},
		{
			field: 'city',
			type: 'textbox',
			caption: 'City',
			kind: 'editable-external',
			showOnPortal: true,
			showOnHotline: true,
		},
		{
			field: 'stateProvince',
			type: 'picklist',
			caption: 'State/Province',
			typeOptions: {
				picklistName: 'states_and_provinces',
			},
			kind: 'editable-external',
			showOnPortal: true,
			showOnHotline: true,
		},
		{
			field: 'country',
			type: 'country',
			caption: 'Country',
			kind: 'editable-external',
			showOnPortal: true,
			showOnHotline: true,
		},
		{
			field: 'zipCodePostalCode',
			type: 'textbox',
			caption: 'Zip Code/Postal Code',
			kind: 'editable-external',
			showOnPortal: true,
			showOnHotline: true,
		},
		{
			field: 'homePhone',
			type: 'phone-number',
			caption: 'Home Phone #',
			kind: 'editable-external',
			showOnPortal: true,
			showOnHotline: true,
		},
		{
			field: 'workPhone',
			type: 'phone-number',
			caption: 'Work Phone #',
			kind: 'editable-external',
			showOnPortal: true,
			showOnHotline: true,
		},
		{
			field: 'emailAddress',
			type: 'email',
			caption: 'Email address',
			kind: 'editable-external',
			excludeFromSaveAndCopy: true,
			typeOptions: {
				linkWithSystemUser: true,
				disableWhitelist: true,
			},
			showOnPortal: true,
			showOnHotline: true,
		},
		{
			field: 'person',
			type: 'json',
			caption: 'Related Profile',
			kind: 'custom',
			excludeFromAutofill: true,
			kindOptions: {
				flags: {
					audit: false,
					schema: false,
					search: false,
					apiWritable: true,
					apiExternalWritable: true,
					computedOnSave: false,
					computedOnRead: false,
					formVisible: false,
					gridVisible: false,
					gridSortable: false,
					searchVisible: false,
					formattedData: false,
					gridExportable: false,
					reportable: false,
				},
			},
		},
		{
			field: 'linkWithPersonMethod',
			caption: 'Method of creating Link with Profile',
			type: 'picklist',
			typeOptions: {
				picklistName: 'link_party_with_person_methods',
			},
			kind: 'custom',
			excludeFromAutofill: true,
			kindOptions: {
				flags: {
					audit: false,
					schema: false,
					search: false,
					apiWritable: true,
					apiExternalWritable: true,
					computedOnSave: false,
					computedOnRead: false,
					formVisible: false,
					gridVisible: false,
					gridSortable: false,
					searchVisible: false,
					formattedData: false,
					gridExportable: false,
					reportable: false,
				},
			},
		},
		{
			field: 'portalReporterParty',
			type: 'yesno',
			caption: 'Portal Reporter Party',
			kind: 'editable-external',
			showOnHotline: true,
			dataImportMappableOverride: false,
		},
		{
			field: 'externalRecord',
			type: 'checkbox',
			caption: 'External',
			kind: 'editable',
			typeOptions: {
				allowNull: false,
			},
			kindOptions: {
				useInDynamicJoins: true,
			},
			showOnPortal: true,
			showOnHotline: true,
			excludeFromAutofill: true,
			dataImportMappableOverride: false,
		},
	],
});
