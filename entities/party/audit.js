const refFields = [
	'partyName',
];
const reference = {
	displayFields(data, auditModel) {
		return auditModel.hideCaseItemNumberReference() !== true
			? ['childNumber'].concat(refFields)
			: refFields;
	},
};
module.exports = {
	child: true,
	parentType: {
		base: 'sys',
		name: 'case',
	},
	parentFieldId: 'caseId',
	allowNavigateTo: true,
	cmd: {
		load: {
			'viewed': {
				options: {
					reference,
				},
			},
		},
		save: {
			'created': {
				options: {

					reference,
				},
			},
			'updated': {
				options: {
					changes: {
						excludeFields: [
							'createdBy',
							'caseId',
							'id',
						],
					},
					reference,
				},
			},
			'party_cloned_from': {
				condition: {
					composite: 'all',
					rules: [{
						path: 'diff.id',
						comparator: 'nexists',
					}, {
						path: 'diff.caseId__caseNumber',
						comparator: 'exists',
					}, {
						path: 'diff.caseId__caseNumber.updatedValue',
						comparator: 'matches',
						value: '/\d{4}-\d{2}-\d{8}-\d*$/',
					}],
				},
				options: {

				},
			},
		},
		remove: {
			'deleted': {
				options: {
					reference,
				},
			},
		},
		clone: {
			'party_cloned_to': {

			},
		},
		translate_record: {
			status: {
				condition: {
					composite: 'all',
					rules: [
						{
							path: 'entity.intakeTranslationStatus',
							comparator: 'exists',
						},
					],
				},
				options: {
					changes: {
						displayFields: ['intakeTranslationStatus'],
					},
					reference,
				},
			},
		},
	},
};
