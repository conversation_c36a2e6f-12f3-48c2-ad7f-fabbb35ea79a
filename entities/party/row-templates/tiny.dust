<div class="card-tiny{?model.datePurged} purged{:else}{?model.pendingPurgeDate} scheduled-purge{/model.pendingPurgeDate}{/model.datePurged}">
    {@entityLink entity=entity$ context=model target="_blank"}
        <div class="card-header">
            {@entityIcon entity=entity$/}
            <div class="card-title" data-toggle="tooltip" data-placement="top"
                 title='{formattedData.childNumber|s}'>
                {@resource groupName="sys/party" subgroupName="general" key="name"/}:
            </div>
            <div class="card-label">
                {formattedData.partyName|s}
            </div>
            <i class="fa fa-external-link external-link-icon" aria-hidden="true"></i>
            <br/>
        </div>
    {/entityLink}
</div>

