<td class="card-similar-search{?model.datePurged} purged{:else}{?model.pendingPurgeDate} scheduled-purge{/model.pendingPurgeDate}{/model.datePurged}"
	id="card-similar-search-{model.id}">
	<div class="card-left">
		{@entityIcon entity=entity$/}
	</div>
	<div class="card-right">
		<div class="card-header">
			<div class="card-title" title="{formattedData.partyName|s}">
				{formattedData.partyName|s}
			</div>
			<div class="row-actions pull-right">
			</div>
		</div>
		<div class='card-body'>
			<div class="highlight-section">
				<div class="full-highlight">
					<span class="highlight-item">
						<span class="field-name">{@resource groupName="sys/case" subgroupName="fields" key="caseNumber" /}:</span>
						&nbsp;
						{@find key="fieldName" value="caseId__caseNumber" array=highlightedFields.highlightedFields}
							{highlight|s}
						{:else}
							{@eqLowerCase value1=model.caseId value2=formModel.caseId }
								<span class="highlight-result">{formattedData.caseId|s}</span>
							{:else}
								{formattedData.caseId|s}
							{/eqLowerCase}
						{/find}
					</span>
					<span class="highlight-item">
						<span class="field-name">{@resource groupName=entity$ subgroupName="fields" key="firstName" /}:</span>
						&nbsp;
						{@find key="fieldName" value="firstName" array=highlightedFields.highlightedFields}
							{highlight|s}
						{:else}
							{@eqLowerCase value1=model.firstName value2=formModel.firstName }
								<span class="highlight-result">{formattedData.firstName|s}</span>
							{:else}
								{formattedData.firstName|s}
							{/eqLowerCase}
						{/find}
					</span>
					<span class="highlight-item">
						<span class="field-name">{@resource groupName=entity$ subgroupName="fields" key="lastName" /}:</span>
						&nbsp;
						{@find key="fieldName" value="lastName" array=highlightedFields.highlightedFields}
							{highlight|s}
						{:else}
							{@eqLowerCase value1=model.lastName value2=formModel.lastName }
								<span class="highlight-result">{formattedData.lastName|s}</span>
							{:else}
								{formattedData.lastName|s}
							{/eqLowerCase}
						{/find}
					</span>
					<span class="highlight-item">
						<span class="field-name">{@resource groupName=entity$ subgroupName="fields" key="address" /}:</span>
						&nbsp;
						{@find key="fieldName" value="address" array=highlightedFields.highlightedFields}
							{highlight|s}
						{:else}
							{@eqLowerCase value1=model.address value2=formModel.address }
								<span class="highlight-result">{formattedData.address|s}</span>
							{:else}
								{formattedData.address|s}
							{/eqLowerCase}
						{/find}
					</span>
	
					{#highlightedFields.highlightedFields}
						{@notInArray value=fieldName array="firstName,lastName,address,caseId"}					
							<span class="highlight-item">
							<span class="field-name">{@resource groupName=entity$ subgroupName="fields" key=fieldName /}:</span>
							&nbsp;
							{#highlight}
								{.|s}{@sep}&nbsp;<b>[&#8230;]</b>{/sep}
							{/highlight}
							</span>
						{/notInArray}
					{/highlightedFields.highlightedFields}
				</div>
			</div>
		</div>
	</div>
</td>