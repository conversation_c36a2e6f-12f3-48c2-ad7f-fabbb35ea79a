module.exports = {
	mandatory$: [
		'partyType',
	],
	dependentMandatory$: [
		// Bypass `caseId` validation when the party is external and the `bypassCaseIdValidation`
		// flag is set to true
		{
			condition: '!isPortalReporterParty || !bypassCaseIdValidation',
			fields: ['caseId'],
		},
		{
			condition: '!isExternalApi',
			fields: ['primaryEntity'],
		},
		{
			condition: 'isPortalReporterParty',
			fields: [
				'firstName',
				'lastName',
			],
		},
	],
	phoneNumber$: [
		'homePhone',
		'workPhone',
	],
};
