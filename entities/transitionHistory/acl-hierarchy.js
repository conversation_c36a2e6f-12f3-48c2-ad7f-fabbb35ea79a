const permHelper = require('../../lib/core/permission-helper.js');

module.exports = [{
	type: 'group',
	caption: 'Workflow History',
	permission: 'transition_history',
	disabled: true,
	options: [
		{
			permission: 'view_transition_history',
			caption: 'View',
			tooltip: 'View Transition History',
			disabled: true,
			sequence: 2,
		},
		{
			permission: 'create_transition_history',
			caption: 'Create',
			tooltip: 'Add Transition History',
			disabled: true,
			sequence: 1,
		},
		{
			type: 'group',
			permission: permHelper.getEditGroupPermissionCode('transition_history'),
			caption: 'Edit',
			sequence: 3,
			dependencies: ['view_transition_history'],
			options: [{
				permission: 'edit_transition_history',
				caption: 'Save',
				tooltip: 'Edit Transition History',
				disabled: true,
				dependencies: ['view_transition_history'],
			}],
		},
		{
			permission: 'remove_transition_history',
			caption: 'Remove',
			tooltip: 'Delete Transition History',
			sequence: 4,
			disabled: true,
			dependencies: ['view_transition_history'],
		},
	],
}];
