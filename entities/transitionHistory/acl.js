const permHelper = require('../../lib/core/permission-helper.js');

module.exports = permHelper.initialize()
	.filterMarkForPurge()
	.filterPurge()
	.required({
		name: 'View Transition History',
		roles: ['view_transition_history'],
		actions: ['load', 'list', 'save_existing', 'remove'],
		conditions: [],
	})
	.required({
		name: 'Create Transition History',
		roles: ['create_transition_history'],
		actions: ['save_new'],
		conditions: [{
			attributes: {
				id: null,
			},
		}],
	})
	.required({
		name: 'Edit Transition History',
		roles: ['edit_transition_history'],
		actions: ['save_new'],
		conditions: [{
			attributes: {
				'!id': null,
			},
		}],
	})
	.required({
		name: 'Remove Transition History',
		roles: ['remove_transition_history'],
		actions: ['remove'],
		conditions: [],
	})
	.required({
		name: 'Inherit ACL of Case to which the transition was done',
		roles: ['bypass_inherited_acl'],
		actions: ['load', 'list', 'save_new', 'save_existing', 'remove'],
		conditions: [{
			attributes: {
				objectType: 'sys/case',
			},
		}, 'sys/case::{objectId}::load'],
	})
	.required({
		name: 'Access Transition History of transitions done to non-Case entities',
		roles: ['view_non_case_transition_history'],
		actions: ['load', 'list', 'save_new', 'save_existing', 'remove'],
		conditions: [{
			attributes: {
				'!objectType': 'sys/case',
			},
		}],
	})
	.value();
