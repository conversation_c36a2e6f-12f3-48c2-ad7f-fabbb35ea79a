const extend = require('../extend.js');
const standardConfig = require('../standard-config.js');

/**
 * Entity to store transition history
 */

module.exports = extend(standardConfig, {
	db: 'default',
	table: 'sys_transition_history',
	entity: {
		base: 'sys',
		name: 'transition_history',
	},
	caption: 'Transition History',
	captionPlural: 'Transition History Items',
	addCaption: 'Add Transition History',
	newCaption: 'New Transition History',
	allowPurge: true,
	gridDescriptorField: 'createdDate',
	report: true,
	reportOptions: {
		joins: [
			{ parent: 'id', child: 'object_id' },
			{ parent: 'context_yellowfin_username', child: 'context_yellowfin_username' },
		],
	},
	audit: false,
	acl: require('./acl.js'),
	aclHierarchy: require('./acl-hierarchy.js'),
	grids: require('./grids.js'),
	model() {
		return require('../../public/models/transition-history-model.js');
	},
	collection() {
		return require('../../public/collections/transition-history-collection.js');
	},
	fields: [
		{
			field: 'user',
			type: 'user',
			caption: 'User',
			kind: 'system',
		},
		{
			field: 'transition',
			type: 'workflowTransition',
			caption: 'Step',
			kind: 'system',
			typeOptions: {
				entity: 'sys/transition',
				textField: 'name',
			},
		},
		{
			field: 'transitionName',
			type: 'textbox',
			caption: 'Original Step',
			kind: 'system',
		},
		{
			field: 'stateFrom',
			type: 'workflowState',
			caption: 'Status From',
			kind: 'system',
			typeOptions: {
				entity: 'sys/state',
				textField: 'name',
			},
		},
		{
			field: 'stateFromName',
			type: 'textbox',
			caption: 'Status From Name',
			kind: 'system',
		},
		{
			field: 'stateFromDescription',
			type: 'textbox',
			caption: 'Status From Description',
			kind: 'system',
		},
		{
			field: 'stateFromPrimaryState',
			type: 'picklist',
			typeOptions: {
				picklistName: 'primary_state_types',
			},
			caption: 'Status From Status Type',
			kind: 'system',
		},
		{
			field: 'stateFromDefault',
			type: 'checkbox',
			caption: 'Status From Default Status',
			kind: 'system',
		},
		{
			field: 'stateTo',
			type: 'workflowState',
			caption: 'Status To',
			kind: 'system',
			typeOptions: {
				entity: 'sys/state',
				textField: 'name',
			},
		},
		{
			field: 'stateToName',
			type: 'textbox',
			caption: 'Status To Name',
			kind: 'system',
		},
		{
			field: 'stateToDescription',
			type: 'textbox',
			caption: 'Status To Description',
			kind: 'system',
		},
		{
			field: 'stateToPrimaryState',
			type: 'picklist',
			typeOptions: {
				picklistName: 'primary_state_types',
			},
			caption: 'Status To Status Type',
			kind: 'system',
		},
		{
			field: 'stateToDefault',
			type: 'checkbox',
			caption: 'Status To Default Status',
			kind: 'system',
		},
		{
			field: 'dateOfTransition',
			type: 'datetime',
			caption: 'Start Date',
			kind: 'system',
			excludeFromRedact: true,
		},
		{
			field: 'endDate',
			type: 'datetime',
			caption: 'End Date',
			kind: 'system',
		},
		{
			field: 'durationCalendarDays',
			type: 'textbox',
			caption: 'Duration Calendar Days',
			kind: 'system',
		},
		{
			field: 'transitionReason',
			type: 'textbox',
			caption: 'Reason',
			kind: 'system',
		},
		{
			field: 'objectType',
			type: 'code',
			kind: 'system',
			caption: 'Object Type',
			excludeFromRedact: true,
		},
		{
			field: 'objectId',
			type: 'id',
			kind: 'hidden-editable',
			caption: 'Object ID',
			excludeFromRedact: true,
		},
		{
			field: 'workflowId',
			type: 'workflowName',
			caption: 'Workflow Name',
			kind: 'system',
		},
		{
			field: 'workflowName',
			type: 'textbox',
			caption: 'Original Workflow Name',
			kind: 'system',
		},
		{
			field: 'durationBusinessDays',
			type: 'textbox',
			caption: 'Duration Business Days',
			kind: 'system',
		},
		{
			field: 'createdFiscalYear',
			type: 'number',
			caption: 'Start Fiscal Year',
			kind: 'system',
			typeOptions: {
				format: '0000',
			},
		},
		{
			field: 'createdFiscalMonth',
			type: 'number',
			caption: 'Start Fiscal Month',
			kind: 'system',
		},
		{
			field: 'createdFiscalWeek',
			type: 'number',
			caption: 'Start Fiscal Week',
			kind: 'system',
		},
		{
			field: 'createdFiscalQuarter',
			type: 'number',
			caption: 'Start Fiscal Quarter',
			kind: 'system',
		},
		{
			field: 'closedFiscalYear',
			type: 'number',
			caption: 'End Fiscal Year',
			kind: 'system',
			typeOptions: {
				format: '0000',
			},
		},
		{
			field: 'closedFiscalMonth',
			type: 'number',
			caption: 'End Fiscal Month',
			kind: 'system',
		},
		{
			field: 'closedFiscalWeek',
			type: 'number',
			caption: 'End Fiscal Week',
			kind: 'system',
		},
		{
			field: 'closedFiscalQuarter',
			type: 'number',
			caption: 'End Fiscal Quarter',
			kind: 'system',
		},
	],
});
