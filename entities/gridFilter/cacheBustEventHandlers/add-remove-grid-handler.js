/* global $appData */
/**
 * When a grid is deactivated all user configurations for it are removed.
 * When it is re-activated, only the default configuration shared by all
 * users is restored.
 *
 * This handler will update the default configuration accordingly, and
 * remove any user configurations for the grids.
 */
const _ = require('lodash');
const $ = require('jquery');
const sharedUtils = require('../../../shared/utils.js')();
const BackboneEvents = require('../../../public/lib/backbone-events.js');

// $appData categories
const defaultConfigCategory = 'gridsDefaultFilters';
const userConfigCategory = 'gridsFilters';

/**
 * When we fallback to requests to update our cache we recieve
 * all the default configurations in the application.
 *
 * Since we do not know if a configuration was removed, we filter
 * out any user configurations that no longer have a corresponding default
 * config (user configurations cannot be created for a grid with no default).
 */
function getConfigs(callback) {
	$.ajax({
		url: `${$appData.globalConfig.apiRoot}/grids/defaults`,
		method: 'GET',
		dataType: 'json',
		contnetType: 'application/json',
	}).done((defaultConfigs) => {
		sharedUtils.updateArray($appData[defaultConfigCategory], defaultConfigs);

		// Remove any user configs from $appData that no longer have a default configuration.
		const defaultConfigNames = new Set();
		_.each(defaultConfigs, (config) => {
			defaultConfigNames.add(config.gridName);
		});
		$appData[userConfigCategory] = _.filter($appData[userConfigCategory], (config) => {
			return defaultConfigNames.has(config.gridName);
		});
		callback();
	});
}

module.exports = function handler(opts) {
	const { patchData } = opts;
	if (patchData) {
		const utils = require('../../../public/lib/utils.js');
		// Update the default config with the changes
		utils.patch({
			appDataCategory: defaultConfigCategory,
			data: patchData,
			identifierProperties: ['gridName'],
		});

		// Clear any user configuration for the grid if one exists.
		// New / reactivated grid -> ensures no stale config is stored in appData.
		// Deactivated grid -> user config is removed, clean it up.
		const userConfigPatchData = {};
		_.forEach(patchData, (config, gridName) => {
			userConfigPatchData[gridName] = null;
		});
		utils.patch({
			appDataCategory: userConfigCategory,
			data: userConfigPatchData,
			identifierProperties: ['gridName'],
		});
	} else {
		getConfigs(() => {
			BackboneEvents.trigger('add-remove-grid', opts);
		});
	}
};
