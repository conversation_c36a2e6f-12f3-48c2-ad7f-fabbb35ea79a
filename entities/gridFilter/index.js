var extend = require('../extend.js');
var standardConfig = require('../standard-config.js');

module.exports = extend(standardConfig, {
	db: 'default',
	table: 'sys_grid_filter',
	entity: {
		base: 'sys',
		name: 'gridFilter',
	},
	api: {
		writableFields: [
			'columns',
			'dataFilters',
		],
	},
	cacheBustGroups: require('./cache-bust-groups.js'),
	caption: 'Grid',
	captionPlural: 'Grids',
	addCaption: 'Add Grid',
	newCaption: 'New Grid',
	gridDescriptorField: 'name',
	search: true,
	acl: require('./acl.js'),
	aclHierarchy: require('./acl-hierarchy.js'),
	validation: require('./validation.js'),
	configurationExport: true,
	configurationImport: true,
	importTransformer: 'gridfilter',
	model: function(){
		return require('../../public/models/grid-filter-model.js');
	},
	collection: function(){
		return require('../../public/collections/grid-filters-collection.js');
	},
	fields: [
		{
			field: 'userId',
			type: 'user',
			kind: 'editable',
			caption: 'User',
		},
		{
			field: 'gridName',
			type: 'code',
			kind: 'editable',
			caption: 'Grid Name',
			dbIndex: true,
		},
		{
			field: 'selected',
			type: 'checkbox',
			kind: 'editable',
			caption: 'Selected',
		},
		{
			field: 'sortColumn',
			type: 'code',
			kind: 'editable',
			caption: 'Sort Column',
		},
		{
			field: 'sortOrder',
			type: 'code',
			kind: 'editable',
			caption: 'Sort Order',
		},
		{
			field: 'excludeColumns',
			type: 'code[]',
			kind: 'system',
			caption: 'Exclude Columns',
		},
		{
			field: 'gridViewId',
			type: 'id',
			kind: 'hidden',
			caption: 'View',
			dbIndex: true,
		},
		{
			field: 'dynamic',
			type: 'yesno',
			kind: 'system',
			caption: 'Dynamically Created Grid',
		},
		{
			field: 'features',
			type: 'textbox[]',
			caption: 'Features',
			kind: 'hidden-searchable',
		},
	],
});
