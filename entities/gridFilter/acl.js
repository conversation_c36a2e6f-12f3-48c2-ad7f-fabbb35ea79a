var permHelper = require('../../lib/core/permission-helper.js');

var conditionForOwn = {
	attributes: {
		userId: '{user.id}',
	},
};
var conditionForDefault = {
	attributes: {
		userId: '0',
	},
};

module.exports = permHelper.initialize()
	// View
	.required({
		name: 'View Grid Filters',
		roles: ['view_grid_filter'],
		actions: ['load', 'list', 'save_existing', 'remove'],
		conditions: [],
	})
	.required({
		name: 'View Own Grid Filters',
		roles: ['view_own_grid_filter'],
		actions: ['load', 'list', 'save_existing', 'remove'],
		conditions: [conditionForOwn],
	})
	.required({
		name: 'View Default Grid Filters',
		roles: ['view_default_grid_filter'],
		actions: ['load', 'list', 'save_existing', 'remove'],
		conditions: [conditionForDefault],
	})
	.required({
		name: 'View Others Grid Filters',
		roles: ['view_others_grid_filter'],
		actions: ['load', 'list', 'save_existing', 'remove'],
		conditions: [{
			attributes: {
				'!userId': '0',
			},
		}, {
			attributes: {
				'!userId': '{user.id}',
			},
		}],
	})
	// Create
	.required({
		name: 'Create Grid Filters',
		roles: ['create_grid_filter'],
		actions: ['save_new'],
		conditions: [{
			attributes: {
				id: null,
			},
		}],
	})
	.required({
		name: 'Create Own Grid Filters',
		roles: ['create_own_grid_filter'],
		actions: ['save_new'],
		conditions: [conditionForOwn, {
			attributes: {
				id: null,
			},
		}],
	})
	.required({
		name: 'Create Default Grid Filters',
		roles: ['create_default_grid_filter'],
		actions: ['save_new'],
		conditions: [conditionForDefault, {
			attributes: {
				id: null,
			},
		}],
	})
	.required({
		name: 'Create Others Grid Filters',
		roles: ['create_others_grid_filter'],
		actions: ['save_new'],
		conditions: [{
			attributes: {
				id: null,
			},
		}, {
			attributes: {
				'!userId': '0',
			},
		}, {
			attributes: {
				'!userId': '{user.id}',
			},
		}],
	})
	// Edit
	.required({
		name: 'Edit Grid Filters',
		roles: ['edit_grid_filter'],
		actions: ['save_new', 'save_existing'],
		conditions: [{
			attributes: {
				'!id': null,
			},
		}],
	})
	.required({
		name: 'Edit Own Grid Filters',
		roles: ['edit_own_grid_filter'],
		actions: ['save_new', 'save_existing'],
		conditions: [conditionForOwn, {
			attributes: {
				'!id': null,
			},
		}],
	})
	.required({
		name: 'Edit Default Grid Filters',
		roles: ['edit_default_grid_filter'],
		actions: ['save_new', 'save_existing'],
		conditions: [conditionForDefault, {
			attributes: {
				'!id': null,
			},
		}],
	})
	.required({
		name: 'Edit Others Grid Filters',
		roles: ['edit_others_grid_filter'],
		actions: ['save_new', 'save_existing'],
		conditions: [{
			attributes: {
				'!id': null,
			},
		}, {
			attributes: {
				'!userId': '0',
			},
		}, {
			attributes: {
				'!userId': '{user.id}',
			},
		}],
	})
	// Remove
	.required({
		name: 'Remove Grid Filters',
		roles: ['remove_grid_filter'],
		actions: ['remove'],
		conditions: [],
	})
	.required({
		name: 'Remove Own Grid Filters',
		roles: ['remove_own_grid_filter'],
		actions: ['remove'],
		conditions: [conditionForOwn],
	})
	.required({
		name: 'Remove Default Grid Filters',
		roles: ['remove_default_grid_filter'],
		actions: ['remove'],
		conditions: [conditionForDefault],
	})
	.required({
		name: 'Remove Others Grid Filters',
		roles: ['remove_others_grid_filter'],
		actions: ['remove'],
		conditions: [{
			attributes: {
				'!userId': '0',
			},
		}, {
			attributes: {
				'!userId': '{user.id}',
			},
		}],
	})
	.required({
		name: 'Inherit Grid View ACL',
		roles: ['bypass_inherited_acl'],
		actions: ['load', 'list', 'save_new', 'save_existing', 'remove'],
		conditions: [{
			attributes: {
				'!gridViewId': null,
			},
		}, 'sys/grid_view::{gridViewId}'],
	})
	.value();
