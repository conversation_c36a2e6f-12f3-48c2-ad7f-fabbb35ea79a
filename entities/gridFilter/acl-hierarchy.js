const permHelper = require('../../lib/core/permission-helper.js');

module.exports = [{
	type: 'group',
	caption: 'Grids',
	permission: 'view_grid_filter_settings',
	options: [
		{
			caption: 'View Default Grids',
			sequence: 2,
			disabled: true,
			permission: 'view_default_grid_filter',
		},
		{
			caption: 'Create Default Grids',
			sequence: 1,
			disabled: true,
			permission: 'create_grid_filter',
		},
		{
			type: 'group',
			permission: permHelper.getEditGroupPermissionCode('grid_filter'),
			sequence: 3,
			caption: 'Edit Default Grids',
			options: [{
				permission: 'edit_default_grid_filter',
				caption: 'Save',
				tooltip: 'Configure the default column headers of grids',
			}],
		},
		{
			caption: 'Remove Default Grids',
			sequence: 4,
			disabled: true,
			permission: 'remove_grid_filter',
		},
		{
			caption: 'Export Grid Data',
			sequence: 5,
			disabled: true,
			permission: 'export_grid',
		},
	],
}];
