var permHelper = require('../../lib/core/permission-helper.js');

module.exports = permHelper.initialize()
	.required({
		name: 'View Message',
		roles: ['view_message'],
		actions: ['load', 'list', 'save_existing', 'remove'],
		conditions: [],
	})
	.required({
		name: 'View Own Message',
		roles: ['view_own_message'],
		actions: ['load', 'list', 'save_existing', 'remove'],
		conditions: [{
			attributes: {
				userId: '{user.id}',
			},
		}],
	})
	.required({
		name: 'View Others Message',
		roles: ['view_others_message'],
		actions: ['load', 'list', 'save_existing', 'remove'],
		conditions: [{
			attributes: {
				'!userId': '{user.id}',
			},
		}],
	})
	.required({
		name: 'Create Message',
		roles: ['create_message'],
		actions: ['save_new'],
		conditions: [{
			attributes: {
				id: null,
			},
		}],
	})
	.required({
		name: 'Edit Message',
		roles: ['edit_message'],
		actions: ['save_new'],
		conditions: [{
			attributes: {
				'!id': null,
			},
		}],
	})
	.required({
		name: 'Remove Message',
		roles: ['remove_message'],
		actions: ['remove'],
		conditions: [],
	})
	.value();