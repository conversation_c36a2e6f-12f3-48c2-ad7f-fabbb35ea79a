var extend = require('../extend.js');
var standardConfig = require('../standard-config.js');

module.exports = extend(standardConfig, {
	db: 'default',
	table: 'sys_log',
	entity: {
		base: 'sys',
		name: 'log',
	},
	search: false,
	caption: 'Log',
	captionPlural: 'Logs',
	addCaption: 'Add Log',
	newCaption: 'New Log',
	acl: require('./acl.js'),
	model: function(){
		return require('../../public/models/log-model.js');
	},
	fields: [
		{
			field: 'msg',
			type: 'textbox',
			kind: 'editable',
			caption: 'Message',
		},
		{
			field: 'details',
			type: 'textarea',
			kind: 'editable',
			caption: 'Details',
		},
		{
			field: 'type',
			type: 'code',
			kind: 'editable',
			caption: 'Type',
		},
		{
			field: 'subtype',
			type: 'textbox',
			kind: 'editable',
			caption: 'Subtype',
		},
		{
			field: 'origin',
			type: 'textbox',
			kind: 'editable',
			caption: 'Origin',
		},
		{
			field: 'userId',
			type: 'user',
			kind: 'system',
			caption: 'User',
		},
		{
			field: 'loginid',
			type: 'textbox',
			kind: 'editable',
			caption: 'Login ID',
		},
	],
});