const _ = require('lodash');
const extend = require('../extend.js');
const layoutEnt = require('../layout');

module.exports = extend(layoutEnt, {
	createTable: false,
	reuseEsIndex: {
		index: 'sys_layout',
		typeField: 'layoutType',
	},
	layoutType: 'tab',
	entity: {
		base: 'sys',
		name: 'tab_layout_type',
	},
	caption: 'Tab',
	captionPlural: 'Tabs',
	addCaption: 'Add Tab',
	newCaption: 'New Tab',
	importTransformer: 'tab_layout_type',
	exportTransformer: 'tab_layout_type',
	customDependencies: entityService => _.map(entityService.getRootLayoutEntities(), 'entityCanon'),
	validation: require('./validation.js'),
	rules: require('./rules.js'),
	model() {
		return require('../../public/models/layout-tab-model.js');
	},
});
