/**
 *	Openlayers layer configuration file. Allows you to set any number of layers & layer groups.
 *  Required: URLs for all tile/style requests to be made.
 *
 *  Exports an array of layer option objects. Each object is a layer to be added to the map.
 *
 * @param {string=} type - Needs to specify if it's a 'raster' or 'vector' layer being added.
 *
 * @param {string=} layerName - Name to be used for the layer.
 * 					This will be displayed when selecting layers.
 *
 * @param {string=} url - URL for the vector tile requests.
 * 					This can be a local proxied request with the following format:
 * 						get_map_tile/:tileset_id/:format/:ac/:z/:x/:y
 * 						example: /api/1.0/get_map_tile/mapbox.satellite/png/{a-c}/{z}/{x}/{y}
 *
 * 					or a direct URL call to a tile service for a non-proxied request.
 * 					A direct call may require you to provide the token directly here.
 * 					Token should restrict access based on the URL it is being used from.
 *
 * @param {string=} styleUrl - URL that will load the style for a vector layer.
 * 					This option is required when requesting vector layers.
 *
 * @param {boolean=} opts.baseLayer - Boolean indicating if this is to be used as a base layer.
 * 					A base layer is when a tile consists of a single layer only.
 * 					If set <groupName> will be ignored
 *
 * @param {string=} groupName -	A string that indicates which groupName this layer should be added.
 * 					Matching group names will be requested together in a tile request.
 * 					<baseLayer> is automatically set true for each unique <groupName>
 */
const config = require('./');

const attribution = '<span>© <a href="https://www.mapbox.com/about/maps" target="_blank">Mapbox</a></span>'
	+ ' <span>© <a href="https://www.openstreetmap.org/copyright" target="_blank">OpenStreetMap</a></span>'
	+ ' <span><a href="https://www.mapbox.com/map-feedback/" target="_blank">Improve this map</a></span>';


function createTileUrl(opts) {
	return config.enableGeoMappingProxyMode
		? `/api/1.0/get_map_tile/${opts.tileset}/${opts.format}/{a-c}/{z}/{x}/{y}`
		: `https://api.mapbox.com/v4/${opts.tileset}/{z}/{x}/{y}.${opts.format}?access_token=${config.mapboxAccessToken}`;
}

function createStyleUrl(opts) {
	return config.enableGeoMappingProxyMode
		? `/api/1.0/get_map_style/${opts.styleUrl}`
		: `https://api.mapbox.com/styles/v1/${opts.styleUrl}?access_token=${config.mapboxAccessToken}`;
}

const streetOpts = {
	tileset: 'mapbox.mapbox-streets-v8,mapbox.mapbox-terrain-v2',
	format: 'vector.pbf',
	styleUrl: 'isightadmin/cl1m54l1x00ah14lqjpeedaa1',
};

const streetLayer = {
	type: 'vector',
	layerName: 'Streets',
	url: createTileUrl(streetOpts),
	styleUrl: createStyleUrl(streetOpts),
	baseLayer: true,
	attribution,
};

const satelliteOpts = {
	tileset: 'mapbox.satellite',
	format: 'png',
};

const satelliteGroup = {
	type: 'raster',
	layerName: 'satelliteGroup',
	url: createTileUrl(satelliteOpts),
	groupName: 'Satellite Roads',
	attribution,
};

const vectorOpts = {
	tileset: 'mapbox.mapbox-streets-v8',
	format: 'vector.pbf',
	styleUrl: 'isightadmin/cl1m550sp001614m89gtrqrgh',
};

const vectorGroup = {
	type: 'vector',
	layerName: 'streetGroup',
	url: createTileUrl(vectorOpts),
	styleUrl: createStyleUrl(vectorOpts),
	groupName: 'Satellite Roads',
	attribution,
};

const options = [
	streetLayer,
	satelliteGroup,
	vectorGroup,
];

module.exports = options;
