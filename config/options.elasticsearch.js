//ES handles a maximum of 32766 bytes limit per field which is approximatly 10922 UTF-8 characters.
var IGNORE_ABOVE_CHAR = process.env.IGNORE_ABOVE_CHAR
	? parseInt(process.env.IGNORE_ABOVE_CHAR, 10)
	: 10000;

/* eslint-disable import/no-dynamic-require */
module.exports = {
	entities: [],
	refreshOnSave: true,
	fetchEntitiesFromDB: true,
	defaultBulkRequestSize: 2500,
	defaultLowPriorityBulkRequestSize: 50,
	analysis: {
		normalizer: {
			lowercase_normalizer: {
				type: 'custom',
				char_filter: [],
				filter: ['lowercase'],
			},
		},
		analyzer: {
			'exact_lowercase': {
				'tokenizer': 'keyword',
				'type': 'custom',
				'filter': ['max_length_truncate_filter', 'lowercase'],
			},
			'formatted': {
				'tokenizer': 'standard',
				'filter': ['lowercase'],
			},
			'code_analyzer': {
				'type': 'custom',
				'tokenizer': 'code_tokenizer',
				'filter': ['lowercase'],
			},
			'typeahead': {
				'tokenizer': 'standard',
				'filter': [
					'lowercase',
					'edgeNGram',
				],
			},
			'email_typeahead': {
				'tokenizer': 'uax_url_email',
				'filter': [
					'lowercase',
					'edgeNGramEmail',
				],
			},
			'email': {
				'type': 'custom',
				'tokenizer': 'uax_url_email',
				'filter': ['lowercase', 'stop'],
			},
			'email_split': {
				'type': 'custom',
				'tokenizer': 'uax_url_email',
				'filter': ['email_split_pattern', 'lowercase'],
			},
			'sequence_number': {
				'tokenizer': 'keyword',
				'filter': [
					'lowercase',
					'edgeNGramSequenceNumber',
					'reverse',
					'edgeNGramSequenceNumber',
					'reverse',
					'unique',
				],
				'char_filter': [
					'remove_spaces',
				],
			},
			'search_sequence_number': {
				'tokenizer': 'keyword',
				'filter': [
					'lowercase',
				],
				'char_filter': [
					'remove_spaces',
				],
			},
			'html': {
				'tokenizer': 'standard',
				'char_filter': ['html'],
			},
			'phone_number_analyzer': {
				'tokenizer': 'phone_number_tokenizer',
			},
			'rich_text': {
				'tokenizer': 'standard',
				'filter': ['lowercase'],
				'char_filter': ['newline'],
			},
			'arabic': {
				'tokenizer': 'standard',
				'filter': [
					'lowercase',
					'arabic_stop',
					'arabic_normalization',
					'arabic_keywords',
					'arabic_stemmer',
				],
			},
			'armenian': {
				'tokenizer': 'standard',
				'filter': [
					'lowercase',
					'armenian_stop',
					'armenian_keywords',
					'armenian_stemmer',
				],
			},
			'brazilian': {
				'tokenizer': 'standard',
				'filter': [
					'lowercase',
					'brazilian_stop',
					'brazilian_keywords',
					'brazilian_stemmer',
				],
			},
			'bulgarian': {
				'tokenizer': 'standard',
				'filter': [
					'lowercase',
					'bulgarian_stop',
					'bulgarian_keywords',
					'bulgarian_stemmer',
				],
			},
			'catalan': {
				'tokenizer': 'standard',
				'filter': [
					'catalan_elision',
					'lowercase',
					'catalan_stop',
					'catalan_keywords',
					'catalan_stemmer',
				],
			},
			'cjk': {
				'tokenizer': 'standard',
				'filter': [
					'cjk_width',
					'lowercase',
					'cjk_bigram',
					'english_stop',
				],
			},
			'czech': {
				'tokenizer': 'standard',
				'filter': [
					'lowercase',
					'czech_stop',
					'czech_keywords',
					'czech_stemmer',
				],
			},
			'danish': {
				'tokenizer': 'standard',
				'filter': [
					'lowercase',
					'danish_stop',
					'danish_keywords',
					'danish_stemmer',
				],
			},
			'dutch': {
				'tokenizer': 'standard',
				'filter': [
					'lowercase',
					'dutch_stop',
					'dutch_keywords',
					'dutch_override',
					'dutch_stemmer',
				],
			},
			'english': {
				'tokenizer': 'standard',
				'filter': [
					'english_possessive_stemmer',
					'lowercase',
					'english_stop',
					'english_stemmer',
				],
			},
			'english_html': {
				'tokenizer': 'standard',
				'filter': [
					'english_possessive_stemmer',
					'lowercase',
					'english_stop',
					'english_stemmer',
				],
				'char_filter': ['html'],
			},
			'finnish': {
				'tokenizer': 'standard',
				'filter': [
					'lowercase',
					'finnish_stop',
					'finnish_keywords',
					'finnish_stemmer',
				],
			},
			'french': {
				'tokenizer': 'standard',
				'filter': [
					'french_elision',
					'lowercase',
					'french_stop',
					'french_keywords',
					'french_stemmer',
				],
			},
			'french_html': {
				'tokenizer': 'standard',
				'filter': [
					'french_elision',
					'lowercase',
					'french_stop',
					'french_keywords',
					'french_stemmer',
				],
				'char_filter': ['html'],
			},
			'german': {
				'tokenizer': 'standard',
				'filter': [
					'lowercase',
					'german_stop',
					'german_keywords',
					'german_normalization',
					'german_stemmer',
				],
			},
			'greek': {
				'tokenizer': 'standard',
				'filter': [
					'greek_lowercase',
					'greek_stop',
					'greek_keywords',
					'greek_stemmer',
				],
			},
			'hindi': {
				'tokenizer': 'standard',
				'filter': [
					'lowercase',
					'indic_normalization',
					'hindi_normalization',
					'hindi_stop',
					'hindi_keywords',
					'hindi_stemmer',
				],
			},
			'hungarian': {
				'tokenizer': 'standard',
				'filter': [
					'lowercase',
					'hungarian_stop',
					'hungarian_keywords',
					'hungarian_stemmer',
				],
			},
			'indonesian': {
				'tokenizer': 'standard',
				'filter': [
					'lowercase',
					'indonesian_stop',
					'indonesian_keywords',
					'indonesian_stemmer',
				],
			},
			'irish': {
				'tokenizer': 'standard',
				'filter': [
					'irish_stop',
					'irish_elision',
					'irish_lowercase',
					'irish_keywords',
					'irish_stemmer',
				],
			},
			'italian': {
				'tokenizer': 'standard',
				'filter': [
					'italian_elision',
					'lowercase',
					'italian_stop',
					'italian_keywords',
					'italian_stemmer',
				],
			},
			'latvian': {
				'tokenizer': 'standard',
				'filter': [
					'lowercase',
					'latvian_stop',
					'latvian_keywords',
					'latvian_stemmer',
				],
			},
			'lithuanian': {
				'tokenizer': 'standard',
				'filter': [
					'lowercase',
					'lithuanian_stop',
					'lithuanian_keywords',
					'lithuanian_stemmer',
				],
			},
			'norwegian': {
				'tokenizer': 'standard',
				'filter': [
					'lowercase',
					'norwegian_stop',
					'norwegian_keywords',
					'norwegian_stemmer',
				],
			},
			'persian': {
				'tokenizer': 'standard',
				'char_filter': [ 'zero_width_spaces' ],
				'filter': [
					'lowercase',
					'arabic_normalization',
					'persian_normalization',
					'persian_stop',
				],
			},
			'romanian': {
				'tokenizer': 'standard',
				'filter': [
					'lowercase',
					'romanian_stop',
					'romanian_keywords',
					'romanian_stemmer',
				],
			},
			'russian': {
				'tokenizer': 'standard',
				'filter': [
					'lowercase',
					'russian_stop',
					'russian_keywords',
					'russian_stemmer',
				],
			},
			'sorani': {
				'tokenizer': 'standard',
				'filter': [
					'sorani_normalization',
					'lowercase',
					'sorani_stop',
					'sorani_keywords',
					'sorani_stemmer',
				],
			},
			'spanish': {
				'tokenizer': 'standard',
				'filter': [
					'lowercase',
					'spanish_stop',
					'spanish_keywords',
					'spanish_stemmer',
				],
			},
			'swedish': {
				'tokenizer': 'standard',
				'filter': [
					'lowercase',
					'swedish_stop',
					'swedish_keywords',
					'swedish_stemmer',
				],
			},
			'thai': {
				'tokenizer': 'thai',
				'filter': [
					'lowercase',
					'thai_stop',
				],
			},
			'turkish': {
				'tokenizer': 'standard',
				'filter': [
					'apostrophe',
					'turkish_lowercase',
					'turkish_stop',
					'turkish_keywords',
					'turkish_stemmer',
				],
			},
		},
		tokenizer: {
			'code_tokenizer': {
				'type': 'ngram',
				'min_gram': 1,
				'max_gram': 12,
				'token_chars': [
					'letter',
					'digit',
				],
			},
			'phone_number_tokenizer': {
				'type': 'ngram',
				'min_gram': 2,
				'max_gram': 15,
			},
		},
		filter: {
			'edgeNGram': {
				'type': 'edge_ngram',
				'min_gram': 1,
				'max_gram': 15,
			},
			'edgeNGramEmail': {
				'type': 'edge_ngram',
				'min_gram': 1,
				// Higher max ngram length due to emails
				'max_gram': 20,
			},
			'edgeNGramSequenceNumber': {
				'type': 'edge_ngram',
				'min_gram': 1,
				'max_gram': 15,
				'token_chars': [
					'letter',
					'digit',
				],
			},
			'english_stop': {
				'type': 'stop',
				'stopwords': '_english_',
			},
			'english_stemmer': {
				'type': 'stemmer',
				'language': 'english',
			},
			'english_possessive_stemmer': {
				'type': 'stemmer',
				'language': 'possessive_english',
			},
			'max_length_truncate_filter': {
				type: 'truncate',
				length: IGNORE_ABOVE_CHAR,
			},
			'phone_number_filter': {
				'type': 'ngram',
				'min_gram': 2,
				'max_gram': 15,
			},
			'french_elision': {
				'type': 'elision',
				'articles_case': true,
				'articles': [
					'l', 'm', 't', 'qu', 'n', 's',
					'j', 'd', 'c', 'jusqu', 'quoiqu',
					'lorsqu', 'puisqu',
				],
			},
			'french_stop': {
				'type': 'stop',
				'stopwords': '_french_',
			},
			'french_keywords': {
				'type': 'keyword_marker',
				'keywords': ['Exemple'],
			},
			'french_stemmer': {
				'type': 'stemmer',
				'language': 'light_french',
			},
			'arabic_stop': {
				'type': 'stop',
				'stopwords': '_arabic_',
			},
			'arabic_keywords': {
				'type': 'keyword_marker',
				'keywords': ['مثال'],
			},
			'arabic_stemmer': {
				'type': 'stemmer',
				'language': 'arabic',
			},
			'armenian_stop': {
				'type': 'stop',
				'stopwords': '_armenian_',
			},
			'armenian_keywords': {
				'type': 'keyword_marker',
				'keywords': ['օրինակ'],
			},
			'armenian_stemmer': {
				'type': 'stemmer',
				'language': 'armenian',
			},
			'brazilian_stop': {
				'type': 'stop',
				'stopwords': '_brazilian_',
			},
			'brazilian_keywords': {
				'type': 'keyword_marker',
				'keywords': ['exemplo'],
			},
			'brazilian_stemmer': {
				'type': 'stemmer',
				'language': 'brazilian',
			},
			'bulgarian_stop': {
				'type': 'stop',
				'stopwords': '_bulgarian_',
			},
			'bulgarian_keywords': {
				'type': 'keyword_marker',
				'keywords': ['пример'],
			},
			'bulgarian_stemmer': {
				'type': 'stemmer',
				'language': 'bulgarian',
			},
			'catalan_elision': {
				'type': 'elision',
				'articles': [ 'd', 'l', 'm', 'n', 's', 't'],
				'articles_case': true,
			},
			'catalan_stop': {
				'type': 'stop',
				'stopwords': '_catalan_',
			},
			'catalan_keywords': {
				'type': 'keyword_marker',
				'keywords': ['exemple'],
			},
			'catalan_stemmer': {
				'type': 'stemmer',
				'language': 'catalan',
			},
			'czech_stop': {
				'type': 'stop',
				'stopwords': '_czech_',
			},
			'czech_keywords': {
				'type': 'keyword_marker',
				'keywords': ['příklad'],
			},
			'czech_stemmer': {
				'type': 'stemmer',
				'language': 'czech',
			},
			'danish_stop': {
				'type': 'stop',
				'stopwords': '_danish_',
			},
			'danish_keywords': {
				'type': 'keyword_marker',
				'keywords': ['eksempel'],
			},
			'danish_stemmer': {
				'type': 'stemmer',
				'language': 'danish',
			},
			'dutch_stop': {
				'type': 'stop',
				'stopwords': '_dutch_',
			},
			'dutch_keywords': {
				'type': 'keyword_marker',
				'keywords': ['voorbeeld'],
			},
			'dutch_stemmer': {
				'type': 'stemmer',
				'language': 'dutch',
			},
			'dutch_override': {
				'type': 'stemmer_override',
				'rules': [
					'fiets=>fiets',
					'bromfiets=>bromfiets',
					'ei=>eier',
					'kind=>kinder',
				],
			},
			'finnish_stop': {
				'type': 'stop',
				'stopwords': '_finnish_',
			},
			'finnish_keywords': {
				'type': 'keyword_marker',
				'keywords': ['esimerkki'],
			},
			'finnish_stemmer': {
				'type': 'stemmer',
				'language': 'finnish',
			},
			'german_stop': {
				'type': 'stop',
				'stopwords': '_german_',
			},
			'german_keywords': {
				'type': 'keyword_marker',
				'keywords': ['Beispiel'],
			},
			'german_stemmer': {
				'type': 'stemmer',
				'language': 'light_german',
			},
			'greek_stop': {
				'type': 'stop',
				'stopwords': '_greek_',
			},
			'greek_lowercase': {
				'type': 'lowercase',
				'language': 'greek',
			},
			'greek_keywords': {
				'type': 'keyword_marker',
				'keywords': ['παράδειγμα'],
			},
			'greek_stemmer': {
				'type': 'stemmer',
				'language': 'greek',
			},
			'hindi_stop': {
				'type': 'stop',
				'stopwords': '_hindi_',
			},
			'hindi_keywords': {
				'type': 'keyword_marker',
				'keywords': ['उदाहरण'],
			},
			'hindi_stemmer': {
				'type': 'stemmer',
				'language': 'hindi',
			},
			'hungarian_stop': {
				'type': 'stop',
				'stopwords': '_hungarian_',
			},
			'hungarian_keywords': {
				'type': 'keyword_marker',
				'keywords': ['példa'],
			},
			'hungarian_stemmer': {
				'type': 'stemmer',
				'language': 'hungarian',
			},
			'indonesian_stop': {
				'type': 'stop',
				'stopwords': '_indonesian_',
			},
			'indonesian_keywords': {
				'type': 'keyword_marker',
				'keywords': ['contoh'],
			},
			'indonesian_stemmer': {
				'type': 'stemmer',
				'language': 'indonesian',
			},
			'irish_elision': {
				'type': 'elision',
				'articles': [ 'h', 'n', 't' ],
			},
			'irish_stop': {
				'type': 'stop',
				'stopwords': '_irish_',
			},
			'irish_lowercase': {
				'type': 'lowercase',
				'language': 'irish',
			},
			'irish_keywords': {
				'type': 'keyword_marker',
				'keywords': ['sampla'],
			},
			'irish_stemmer': {
				'type': 'stemmer',
				'language': 'irish',
			},
			'italian_elision': {
				'type': 'elision',
				'articles': [
					'c', 'l', 'all', 'dall', 'dell',
					'nell', 'sull', 'coll', 'pell',
					'gl', 'agl', 'dagl', 'degl', 'negl',
					'sugl', 'un', 'm', 't', 's', 'v', 'd',
				],
				'articles_case': true,
			},
			'italian_stop': {
				'type': 'stop',
				'stopwords': '_italian_',
			},
			'italian_keywords': {
				'type': 'keyword_marker',
				'keywords': ['esempio'],
			},
			'italian_stemmer': {
				'type': 'stemmer',
				'language': 'light_italian',
			},
			'latvian_stop': {
				'type': 'stop',
				'stopwords': '_latvian_',
			},
			'latvian_keywords': {
				'type': 'keyword_marker',
				'keywords': ['piemērs'],
			},
			'latvian_stemmer': {
				'type': 'stemmer',
				'language': 'latvian',
			},
			'lithuanian_stop': {
				'type': 'stop',
				'stopwords': '_lithuanian_',
			},
			'lithuanian_keywords': {
				'type': 'keyword_marker',
				'keywords': ['pavyzdys'],
			},
			'lithuanian_stemmer': {
				'type': 'stemmer',
				'language': 'lithuanian',
			},
			'norwegian_stop': {
				'type': 'stop',
				'stopwords': '_norwegian_',
			},
			'norwegian_keywords': {
				'type': 'keyword_marker',
				'keywords': ['eksempel'],
			},
			'norwegian_stemmer': {
				'type': 'stemmer',
				'language': 'norwegian',
			},
			'persian_stop': {
				'type': 'stop',
				'stopwords': '_persian_',
			},
			'romanian_stop': {
				'type': 'stop',
				'stopwords': '_romanian_',
			},
			'romanian_keywords': {
				'type': 'keyword_marker',
				'keywords': ['exemplu'],
			},
			'romanian_stemmer': {
				'type': 'stemmer',
				'language': 'romanian',
			},
			'russian_stop': {
				'type': 'stop',
				'stopwords': '_russian_',
			},
			'russian_keywords': {
				'type': 'keyword_marker',
				'keywords': ['пример'],
			},
			'russian_stemmer': {
				'type': 'stemmer',
				'language': 'russian',
			},
			'sorani_stop': {
				'type': 'stop',
				'stopwords': '_sorani_',
			},
			'sorani_keywords': {
				'type': 'keyword_marker',
				'keywords': ['mînak'],
			},
			'sorani_stemmer': {
				'type': 'stemmer',
				'language': 'sorani',
			},
			'spanish_stop': {
				'type': 'stop',
				'stopwords': '_spanish_',
			},
			'spanish_keywords': {
				'type': 'keyword_marker',
				'keywords': ['ejemplo'],
			},
			'spanish_stemmer': {
				'type': 'stemmer',
				'language': 'light_spanish',
			},
			'swedish_stop': {
				'type': 'stop',
				'stopwords': '_swedish_',
			},
			'swedish_keywords': {
				'type': 'keyword_marker',
				'keywords': ['exempel'],
			},
			'swedish_stemmer': {
				'type': 'stemmer',
				'language': 'swedish',
			},
			'thai_stop': {
				'type': 'stop',
				'stopwords': '_thai_',
			},
			'turkish_stop': {
				'type': 'stop',
				'stopwords': '_turkish_',
			},
			'turkish_lowercase': {
				'type': 'lowercase',
				'language': 'turkish',
			},
			'turkish_keywords': {
				'type': 'keyword_marker',
				'keywords': ['örnek'],
			},
			'turkish_stemmer': {
				'type': 'stemmer',
				'language': 'turkish',
			},
			'email_split_pattern': {
				'type': 'pattern_capture',
				'preserve_original': true,
				'patterns': [
					'([^@]+)', // Matches everything before @
					'(\\p{L}+)', // Matches sequences of letters (Unicode letters)
					'(\\d+)', // Matches sequences of digits
					'@(.+)', // Matches everything after
				],
			},
		},
		char_filter: {
			'html': {
				'type': 'html_strip',
			},
			'remove_spaces': {
				'type': 'pattern_replace',
				'pattern': '\\s+',
				'replacement': '',
			},
			'newline': {
				'type': 'mapping',
				'mappings': [
					'<br />\\n => \\u0020',
				],
			},
			'zero_width_spaces': {
				'type': 'mapping',
				'mappings': [ '\\u200C=> '],
			},
		},
	},
	localisedAnalyzers: {
		ar: 'arabic',
		hy: 'armenian',
		bg: 'bulgarian',
		ca: 'catalan',
		zh: 'cjk',
		cs: 'czech',
		da: 'danish',
		nl: 'dutch',
		fi: 'finnish',
		fr: 'french',
		de: 'german',
		el: 'greek',
		hi: 'hindi',
		hu: 'hungarian',
		id: 'indonesian',
		ga: 'irish',
		it: 'italian',
		ja: 'cjk',
		ko: 'cjk',
		ku: 'sorani',
		lv: 'latvian',
		lt: 'lithuanian',
		nb: 'norwegian',
		fa: 'persian',
		pt: 'brazilian',
		ro: 'romanian',
		ru: 'russian',
		es: 'spanish',
		sv: 'swedish',
		th: 'thai',
		tr: 'turkish',
	},
};
// export all entities in the folder
function attachEntity(file) {
	if (file.match(/\.js$/) !== null && file !== 'index.js') {
		var entMapping = require('./elasticsearch-entities/' + file);
		module.exports.entities.push(entMapping);
	}
}
require('fs')
	.readdirSync(__dirname + '/elasticsearch-entities')
	.forEach(attachEntity);
