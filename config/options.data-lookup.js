const DATA_LOOKUP_ALLOWED_FIELD_TYPES = [
	'textbox',
	'textarea',
	'texteditor',
	'datetime',
	'checkbox',
	'radio',
	'picklist',
	'picklist[]',
	'email',
	'decimal',
	'money',
	'number',
	'yesno',
];

const FILTER_KIND = ['editable', 'editable-external', 'dynamic'];
const EXCLUDED_FIELDS = [
	'purgeReason', 'excludeFromPurge',
	'caseId', 'externalRecord', 'portalReporterParty'];

module.exports = {
	dataLookupAllowedFieldTypes: DATA_LOOKUP_ALLOWED_FIELD_TYPES,
	maxDefaultMappingRows: process.env.MAX_DEFAULT_MAPPING_ROWS || 5,
	filterKind: FILTER_KIND,
	excludedFields: EXCLUDED_FIELDS,
};
