/* global module */
/** *
*
*   Default options for YellowFin configuration. This can be override by the project config.
*
*/

/**
 *	functionTypeCode tells YF where to show the link of a "function" that permissions govern
 *	(more info here https://community.yellowfinbi.com/topic/type-code-of-roless-permission)
 */
module.exports = {
	orgConfiguration: {
		MULTILANGUAGE: 'true',
		VIEWDATAPREVIEWDEFAULT: 'NONE',
	},
	permissionsMapping: [
		{
			yfPermission: 'MIREPORT',
			isightPermission: {
				C: 'create_private_reports',
				R: 'view_private_reports',
				U: 'edit_private_reports',
				D: 'remove_private_reports',
			},
			functionTypeCode: 'TN',
		},
		{
			yfPermission: 'REPORTCORPORATE',
			isightPermission: {
				C: 'create_public_reports',
				R: 'view_public_reports',
				U: 'edit_public_reports',
				D: 'remove_public_reports',
			},
		},
		{
			yfPermission: 'ADHOCREPORTS',
			isightPermission: 'ad_hoc_reports',
		},
		{
			yfPermission: 'SUBQUERIES',
			isightPermission: 'report_sub_queries',
		},
		{
			yfPermission: 'DRILLTHROUGH',
			isightPermission: 'related_reports',
		},
		{
			yfPermission: 'REPORTDASHBOARD',
			isightPermission: {
				C: 'create_personal_report_dashboard',
				R: 'view_personal_report_dashboard',
				U: 'edit_personal_report_dashboard',
				D: 'remove_personal_report_dashboard',
			},
		},
		{
			yfPermission: 'DASHBOARDCODEMODE',
			isightPermission: 'edit_personal_report_dashboard',
		},
		{
			yfPermission: 'DASHPUBLIC',
			isightPermission: {
				C: 'create_public_report_dashboard',
				R: 'view_public_report_dashboard',
				U: 'edit_public_report_dashboard',
				D: 'remove_public_report_dashboard',
			},
		},
		{
			yfPermission: 'THEMES',
			isightPermission: {
				C: 'create_report_themes',
				R: 'view_report_themes',
				U: 'edit_report_themes',
				D: 'remove_report_themes',
			},
		},
		{
			yfPermission: 'STORY',
			isightPermission: {
				C: 'create_report_stories',
				R: 'view_report_stories',
				U: 'edit_report_stories',
				D: 'remove_report_stories',
			},
		},
		{
			yfPermission: 'REPORTDATADISCOVERY',
			isightPermission: 'assisted_discovery',
		},
		{
			yfPermission: 'ACTIVITYSTREAM',
			isightPermission: 'instant_insight',
		},
		{
			yfPermission: 'INSTANTINSIGHTUSE',
			isightPermission: 'instant_insight',
		},
		{
			yfPermission: 'BROADCAST',
			isightPermission: 'broadcast_report',
		},
		{
			yfPermission: 'MULTICAST',
			isightPermission: 'multicast_report',
		},
		{
			yfPermission: 'MULTICASTSRCFILTER',
			isightPermission: 'multicast_report',
		},
		{
			yfPermission: 'DASHBOARDBROADCAST',
			isightPermission: 'dashboards_report',
		},
		{
			yfPermission: 'EXPORTCSV',
			isightPermission: 'export_reports_to_csv',
		},
		{
			yfPermission: 'EXPORTDOC',
			isightPermission: 'export_reports_to_doc',
		},
		{
			yfPermission: 'EXPORTPDF',
			isightPermission: 'export_reports_to_pdf',
		},
		{
			yfPermission: 'EXPORTEXCEL',
			isightPermission: 'export_reports_to_excel',
		},
		{
			yfPermission: 'CONTENTACCESS',
			isightPermission: 'report_folder_access',
			functionTypeCode: 'CP',
		},
		{
			yfPermission: 'BOOKMARKS',
			isightPermission: 'bookmark_reports',
		},
		{
			yfPermission: 'ACCESSGROUP',
			isightPermission: 'report_group_management',
			functionTypeCode: 'CP',
		},
		{
			yfPermission: 'USERPROFILE',
			isightPermission: 'view_private_reports',
		},
		{
			yfPermission: 'HIDEPASSWORD',
			isightPermission: 'view_private_reports',
		},
		{
			yfPermission: 'HIDEUSERID',
			isightPermission: 'view_private_reports',
		},
		{
			yfPermission: 'HIDEEMAIL',
			isightPermission: 'view_private_reports',
		},
	],
	// Password generator options.
	// This will need to be updated if the Yellowfin default length (4 to 20 characters) is updated.
	passwordOpts: {
		length: 12,
		memorable: false,
		pattern: /[a-zA-Z0-9]/,
	},
};
