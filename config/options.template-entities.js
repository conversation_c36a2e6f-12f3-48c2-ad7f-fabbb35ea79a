/* eslint-disable max-len */
const moment = require('moment-timezone');
const _ = require('lodash');
const async = require('async');
const OptUtil = require('../lib/common/OptUtil.js');
const entitiesHelper = require('../lib/core/entities-helper');
const entityHelper = require('../shared/entity-helper.js');
const entities = require('../entities');
const config = require('./index.js');

const knexEntity = require('../plugins/knex-entity');

const globalConfig = OptUtil.getOptionsFile('options.global.js');
const optionService = require('../services/option.js');

const blacklistedUserAttributes = OptUtil.getOptionsFile('options.blacklisted-user-attributes.js').attributes;

function removeDisabledFeatureFields({entDef, entData, enabledFeatures}, callback) {
	entDef.withSyncFields((err) => {
		if (err) return callback(err);
		const disabledFeatureFields = entDef.filterDisabledFeaturesFields(
			entDef.fields(),
			enabledFeatures,
		);
		if (disabledFeatureFields.length === 0) return callback();
		_.each(disabledFeatureFields, (fieldDef) => {
			delete entData[fieldDef.field];
		});
		callback();
	});
}

const templateEntities = {
	emailRowTemplate: `{?currentValue}
		<li>
			<span class="field-label">{label}:</span>
			{currentValue}
		</li>
		{/currentValue}`,
	emailModifiedRowTemplate: `{?updatedValue}
		<li>
			<span class="field-label">{label}:</span>
			<mark class="original-value">&nbsp;{originalValue}&nbsp;</mark>
			<mark class="updated-value">&nbsp;{updatedValue}&nbsp;</mark>
		</li>
		{/updatedValue}`,
	splitMessageKey: 'write_above_this_line_to_post_a_reply',
	virtualEntities: {
		UserProfile: {
			// Function gets called by template engine that consumes the config
			getProperties(opts, ent, caseId, user, callback) {
				async.waterfall([
					function removeUserDisabledFeatureFields(callback) {
						removeDisabledFeatureFields({
							entDef: entities.get('sys/user'),
							entData: user,
							enabledFeatures: opts?.enabledFeatures,
						}, callback);
					},
					function formatUser(callback) {
						entitiesHelper.formatObject({
							data: user,
							locale: user?.locale,
							entity: 'sys/user',
							html: false,
						}, callback);
					},
				], (err, formattedUser) => {
					if (err) return callback(err);
					// Remove sensitive user fields before passing on (password, keylength, permissions etc).
					const cleanedUser = [_.omit(formattedUser, [...blacklistedUserAttributes, 'perm'])];
					return callback(null, cleanedUser);
				});
			},
		},
		linkedCase: {
			getProperties(opts, ent, caseId, user, callback) {
				const {attachmentParams} = opts;
				const {recordEvent, recordEventData} = attachmentParams;
				const {linkedRecordId, linkedRecordEntity} = recordEventData || {};
				if (recordEvent !== 'case-linked' || !linkedRecordId || linkedRecordEntity !== 'sys/case') {
					return callback(null, null);
				}
				const caseEntDef = entities.get('sys/case');
				const caseEnt = knexEntity(caseEntDef);
				return caseEnt.load({
					q: {id: linkedRecordId},
				}, (err, linkedCase) => {
					if (err) return callback(err);
					if (_.isEmpty(linkedCase)) return callback(new Error('could not load linked case'));
					async.waterfall([
						function removeCaseDisabledFeatureFields(callback) {
							removeDisabledFeatureFields({
								entDef: caseEntDef,
								entData: linkedCase,
								enabledFeatures: opts?.enabledFeatures,
							}, callback);
						},
						function formatCase(callback) {
							entitiesHelper.formatObject({
								data: linkedCase,
								locale: user?.locale,
								entity: 'sys/case',
								html: false,
							}, callback);
						},
					], (err, formattedLinkedCase) => {
						if (err) return callback(err);
						return callback(null, formattedLinkedCase);
					});
				});
			},
		},
		caseLinks: {
			getProperties(opts, ent, caseId, user, callback) {
				const linkCanon = 'sys/link';
				const linkDef = entities.get(linkCanon);
				const linkEnt = knexEntity(linkDef);
				const limit = globalConfig.caseLinkColMaxSize;
				return linkEnt.list({
					q: (knex) => {
						knex
							.where(`${linkDef.table}.entity_1_id`, caseId)
							.orWhere(`${linkDef.table}.entity_2_id`, caseId);
					},
					limit,
					sort: [
						{
							field: 'createdDate',
							direction: 'desc',
						},
					],
				}, (err, links) => {
					if (err) return callback(err);
					links = _.map(links, (link) => {
						const {
							entity1Id, entity2Id, entity1Type, entity2Type,
						} = link;
						let otherEntityType;
						if (caseId === entity1Id) {
							otherEntityType = entity2Type;
						}
						if (caseId === entity2Id) {
							otherEntityType = entity1Type;
						}
						otherEntityType = entityHelper.getEntityName(otherEntityType);
						return _.assign(link, {otherEntityType});
					});
					// Virtual entities need to call their own formatObjects
					return entitiesHelper.formatObjects({
						data: links,
						locale: user?.locale,
						entity: linkCanon,
						html: false,
					}, (err, formattedLinks) => {
						if (err) return callback(err);
						return callback(null, formattedLinks);
					});
				});
			},
		},
		primaryParty: {
			getProperties({ seneca }, _caseData, caseId, user, callback) {
				return async.waterfall(
					[
						cb => seneca
							.make$('sys/party')
						// ideally there should be one primary party per case id
						// but in case more than 1 primary party found, use list$ and limit$ 1
							.list$({
								caseId,
								primaryEntity: true,
								limit$: 1,
							}, cb),
						(records, cb) => entitiesHelper.formatObjects({
							data: records,
							locale: user?.locale,
							entity: 'sys/party',
							html: false,
						}, cb),
					],
					callback,
				);
			},
		},
	},
	getSystemFields: function getSystemFields(timezone = config.systemTimeZone) {
		const self = this().tz(timezone);
		const dateFormat = optionService.get('dateFormat') || globalConfig.defaultDateFormat;
		const dateTimeFormat = optionService.get('dateTimeFormat') || globalConfig.defaultDateTimeFormat;
		const timeFormat = optionService.get('timeFormat') || globalConfig.defaultTimeFormat;
		const systemFields = {
			CurrentDateTime: self.format(globalConfig.defaultDateTimeLongFormat), // March 2, 2018 12:30 PM
			CurrentDateTimeShort: self.format(dateTimeFormat), // 02-Mar-2018 12:30 PM /  Admin selected format
			CurrentDate: self.format(globalConfig.defaultDateLongFormat), // March 2, 2018
			CurrentDateShort: self.format(dateFormat), // 02-Mar-2018 // Admin selected format
			CurrentTime: self.format(timeFormat), // 12:30 PM // / Admin selected format
			CurrentWeekday: self.format('dddd'), // Friday
			CurrentMonth: self.format('MMMM'), // March
			CurrentMonthShort: self.format('MM'), // 03
			CurrentDay: self.format('Do'), // 2nd
			CurrentDayShort: self.format('DD'), // 02
			CurrentYear: self.format('YYYY'), // 2018
			CurrentYearShort: self.format('YY'), // 18
		};
		systemFields.Now = systemFields.CurrentDateTime; // Alias for CurrentDateTime
		systemFields.Today = systemFields.CurrentDate; // Alias for CurrentDate
		return systemFields;
	}.bind(moment),
};

module.exports = templateEntities;
