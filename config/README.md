# Global Configuration

## System Settings

The System Settings are found in the application under the Settings -> System page. The settings themselves are found in the first dropdown on the System page. Each setting can be selected which will load a sub-view to allow the user to manage those settings.

The configuration file for defining the default system settings are found in the platform application at `/config/options.system-settings.js`. The format of the config file is as follows (note, that the actual values may change):

```
module.exports = [
	{
		caption: {
			groupName: 'settings',
			subgroupName: 'system',
			key: 'about',
		},
		navigateTo: 'about',
	},
	{
		caption: {
			groupName: 'settings',
			subgroupName: 'system',
			key: 'unassigned_incoming_mail',
		},
		navigateTo: 'incoming-mail',
		showForRole: 'view_incoming_email_unassigned',
	},
	{
		caption: {
			groupName: 'settings',
			subgroupName: 'system',
			key: 'integration_log',
		},
		navigateTo: 'integration-log',
		showForRole: 'view_event',
	},
	{
		caption: {
			groupName: 'settings',
			subgroupName: 'system',
			key: 'canceled_cases',
		},
		navigateTo: 'cancelled-cases',
		showForRole: 'view_canceled_cases',
	},
	{
		caption: {
			groupName: 'settings',
			subgroupName: 'system',
			key: 'configuration_export_import',
		},
		navigateTo: 'export/configuration',
		showForRole: 'view_configuration_export_import_settings',
	},
	{
		caption: {
			groupName: 'settings',
			subgroupName: 'system',
			key: 'data_export',
		},
		navigateTo: 'export/data',
		showForRole: 'export_data',
	},
	{
		caption: {
			groupName: 'settings',
			subgroupName: 'system',
			key: 'dialing_instructions',
		},
		navigateTo: 'dialing-instructions',
		showForRole: 'view_dialing_instruction_settings',
	},
	{
		caption: {
			groupName: 'settings',
			subgroupName: 'system',
			key: 'purge_records',
		},
		navigateTo: 'purge',
		showForRole: 'schedule_or_purge',
	},
];
```

Some of the settings has been moved under Settings -> Data page. Each setting can be selected which will load a sub-view to allow the user to manage those settings

The `.caption` is a translation key which is handled on the front-end and `.navigateTo` is the name of the route to navigate to when the system setting is chosen.

`.showForRole` is used to determine if the system setting should be shown for the given role or not and will check against the ACL.

#### Config Application
The config application can add its own, custom System Settings by adding `options.system-settings.js` file in it's `/config` folder (the same structure as the platform application) and adding an array of objects as done in the platform application. For example:

```
module.exports = [
	{
		showForRole: 'location',
		navigateTo: 'location',
		caption: {
			groupName: 'settings',
			subgroupName: 'system',
			key: 'location'
		}
	}
];
```

This will concatenate the `location` system settings to the end of the default settings.

## Core Workflows

This allows for monitoring state changes with transitions, enforce access control between transitions, set variables between states and during transitions.

NOTE: The file name of the config file must be unique. It cna be anywhere in sub folders, but all files must have a unique name. This is for the UI, you can refer to a workflow by name (file name).

Workflow config files are located under `/config/workflows`. When a file is added to this folder, the system automatically picks it up and makes it part of the core workflows.

List of reserved variables for states -> onSet, states -> onUnset & transitions -> set:  
- **{now}** Replace with current time on execution.
- **{user.id}** Replace with current user id on execution.

### List of config attributes
  
* **field** - Use this attribute for setting which field of an entity the workflow revolves around.
* **entity** - Use to identify which entity the field relates to. ex:

```
entity: {
	zone: undefined,  
	base: 'sys',  
	name: 'test'  
}
```

* **states** - This is an optional array of states for the workflow. This is a list of explicit valid values for the given field. The system won't accept other vlaues than the ones defined. Available state properties are:  
	* **state** - The value of the state the field would have.  
	* **onSet** - Object of values to set when the workflow enters the state.  
	* **onUnset** - Object of values to set when the workflow leaves the state.
* **strict** - Use this to ensure a transition is given each time the value changes.
* **transitions** - Array of transitions, a set of from and to values the states change. Available transition properties:
	* **id** - The unique identifyer for the transition.  
	* **from** - The originalValue from the diff to evaluate against.  
	* **to** - The updatedValue from the diff to evaluate against.  
	* **roles** - Array of roles. The user should have at least ONE of them in order to make this transition.  
	* **set** - List of properties to set when transition is active.
* **conditions** - Array of conditions for the workflow. List of required options to create a condition:  
 * **name** - The name the condition is to be referenced by.  
 * **message** - The message the condition will give out when it fails.  
 * **affectsUi** - Weather this condition should be considered in the UI or not.  
 * **evaluate** - (optional) Function accepting the model as the first argument. Returns true or false on weather the condition passed or not.  
 * **attributes** - (optional) Plain object identifying what the attributes must equal for the condition to pass.  
 	* Reserved values: `*!empty`: Anything but empty, `{user.id}`: match current user's id.

NOTE: `evaluate` or `attributes` must have one of them defined.

## Custom Forms

Custom forms are objects / entities / data that differs per configuration. They are accessed under the (+) of the main navigation, the forms tab on the left side navigation, the (+) in the case details and within case printing. The platform is dynamic enough to have a few places to add code to have the system aware of them.

In standard-child-config, **customForm** is the property that is`true` by default and differentiates between standard child docs and custom child docs.
For instance, party/index.js will have **customForm** property set to `false`

* **view** - will require the Backbone View to which custom form relates to [`view(){return require('./view.js');},`]. This will require the view defined in the same directory as index.js of custom form
* **model** - will require the Backbone Model to which custom form relates to [`model(){return require('./model.js');},`]. This will require the model defined in the same directory as index.js of custom form
