/**
 * Custom export queries that can be defined for the various entities that get exported during
 * the Configuration Export & Import.
 *
 * The top-level keys for the `options` object belong to the entities that can be selected by the
 * user from the Configuration Export and Import page. Then, for each entity, export functions can
 * be defined for any associated entities (either children or those that are manually exported in
 * the post-export actions) that get exported during the process.
 */

const _ = require('lodash');
const entities = require('../entities');
const dynamicLayoutService = require('../services/dynamic-layout.js');

const options = {
	sys_workflow: {
		sys_rule: {
			query: (knex) => {
				knex.whereNotNull('sys_rule.workflow_id');
			},
		},
	},
	sys_grid_filter: {
		query: (knex) => {
			knex.whereNot('dynamic', true);
		},
	},
};

// Add the root layout types
_.forEach(dynamicLayoutService.getRootLayoutTypes(), (rootLayoutEntity, layoutType) => {
	const rootEntDef = entities.get(rootLayoutEntity);
	const exportName = rootEntDef.entityType;
	options[exportName] = {
		query: (knex) => {
			const { table } = rootEntDef;
			knex
				.where(`${table}.layout_type`, layoutType)
				.whereNot(`${table}.status`, 'pending_changes');
		},
	};
});

module.exports = options;
