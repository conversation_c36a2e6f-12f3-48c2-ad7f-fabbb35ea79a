const myOptions = require('./');

const {disableSecureCookies, sameSite} = myOptions;
const disableProxy = process.env.DISABLE_PROXY === 'true';
// max age of backend session, recommended to be under 45min
const sessionExpiryTime = process.env.LOGIN_SESSION_EXPIRY
	? Math.ceil(Number(process.env.LOGIN_SESSION_EXPIRY) / 1000) // convert to seconds
	: 45 * 60;
const secret = process.env.SESSION_SECRET || 'keyboard dog';
const logger = require('../plugins/log').file('config/options.sessions.js');

if (process.env.NODE_ENV === 'production' && !process.env.SESSION_SECRET) {
	logger.warn('SESSION_SECRET was not provided, using generic one instead');
}

module.exports = {
	secret,
	resave: false,
	saveUninitialized: false,
	cookie: {
		path: '/',
		httpOnly: true,
		secure: !disableSecureCookies,
		sameSite,
	},
	// ITPL-9498: Trust proxy must be set for secure cookies w/ a proxy
	proxy: !disableProxy,
	rolling: true,
	// Case IQ specific
	nonRollingUrls: [
		'/sse-channelid',
		'/api/1.0/banner',
		'/api/1.0/pulse',
		'/api/1.0/search/sys_message',
		'/api/1.0/grid_search/sys_message',
		'/api/1.0/message/count/unread',
		'/api/1.0/teams',
		'/api/1.0/fetch_cache_bust_info',
		'/api/1.0/feature_flags',
	],
	redisSessionKeyPrefix: 'sess:',
	sessionExpiryTime,
};
