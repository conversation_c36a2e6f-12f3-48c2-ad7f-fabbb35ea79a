/* eslint-disable import/no-dynamic-require */
const path = require('path');
const fs = require('fs');
const _ = require('lodash');
const config = require('../index');

const platformPath = __dirname;
const configPath = path.join(config.appConfigPath, 'config', 'dynamic-strategy-configs');

function getConfig(folderPath, name) {
	const filePath = path.join(folderPath, `${name}-config.js`);
	if (fs.existsSync(filePath)) {
		// always require uncached
		delete require.cache[require.resolve(filePath)];
		const file = require(filePath);
		file.dynamic = true;
		return file;
	}
	return null;
}

function getConfigsInFolder(folderPath) {
	if (fs.existsSync(folderPath)) {
		const files = fs.readdirSync(folderPath);
		const configFiles = [];
		_.each(files, (file) => {
			if (/config.js$/.test(file)) {
				// always require uncached
				delete require.cache[require.resolve(path.join(folderPath, file))];
				const configFile = require(path.join(folderPath, file));
				configFile.dynamic = true;
				configFiles.push(configFile);
			}
		});
		return configFiles;
	}
	return [];
}

module.exports = {
	get(name) {
		const configFile = getConfig(configPath, name);
		if (configFile) return configFile;
		return getConfig(platformPath, name);
	},
	getAll() {
		const configFiles = getConfigsInFolder(configPath);
		const platformFiles = getConfigsInFolder(platformPath);
		return _.unionBy(configFiles, platformFiles, 'name');
	},
};
