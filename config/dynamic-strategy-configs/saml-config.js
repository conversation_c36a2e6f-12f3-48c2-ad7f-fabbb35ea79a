const optionService = require('../../services/option.js');
const PassportSaml = require('../../lib/auth/dynamic-passport-strategy/strategies/PassportSaml.js');

const strategyName = 'saml';

module.exports = {
	name: strategyName,
	features: ['SSO'],
	enabled: process.env.SSO_SAML_DYNAMIC_OVERRIDE
		? process.env.SSO_SAML_DYNAMIC_OVERRIDE === 'true'
		: optionService.get('ssoEnabled') && optionService.get('ssoProtocol') === strategyName,
	Strategy: PassportSaml,
	successRedirect: '/',
	failureRedirect: process.env.SSO_FAILURE_REDIRECT || '/login?failure=true',
	identifierMapping: optionService.get('ssoIdentifierMapping'),
	dataMapping: {
		identifier: optionService.get('ssoIdentifierDataMapping'),
	},
	passportOptions: {
		idpCert: optionService.get('ssoCert'),
		issuer: optionService.get('ssoSPEntityId'),
		entryPoint: optionService.get('ssoIdPUrl'),
		callbackUrl: optionService.get('ssoUrl'),
		identifierFormat: optionService.get('ssoIdentifierFormat'),
		authnContext: optionService.get('ssoAuthnContext'),
		disableRequestedAuthnContext: !optionService.get('ssoEnableRequestedAuthnContext'),
		wantAssertionsSigned: optionService.get('ssoWantAssertionsSigned') ?? false,
		wantAuthnResponseSigned: optionService.get('ssoWantAuthnResponseSigned') ?? false,
	},
	// Used for metadata upload mapping
	passportOptionsMap: {
		identifierFormat: 'ssoIdentifierFormat',
		entryPoint: 'ssoIdPUrl',
		idpCert: 'ssoCert',
	},
};
