module.exports = {
	standardPermissionsForNewUserRoles: [
		'agent',
		'upload_file',
		'view_holiday_entry',
		'view_translation',
		'create_audit_log',
		'view_picklist_item',
		'view_internal_picklist_item',
		'view_external_picklist_item',
		'view_standard_response',
		'view_dialing_instruction',
		'view_template',
		'view_user',
		'edit_user',
		'edit_own_user_profile',
		'view_grid_filter',
		'view_default_grid_filter',
		'view_own_grid_filter',
		'create_grid_filter',
		'create_own_grid_filter',
		'edit_grid_filter',
		'edit_own_grid_filter',
		'remove_grid_filter',
		'remove_own_grid_filter',
		'view_permission',
		'export_grid',
		'view_field',
		'view_isight_entity',
		'view_notification',
		'view_default_notification',
		'view_own_notification',
		'edit_notification',
		'edit_own_notification',
		'remove_notification',
		'remove_own_notification',
		'create_notification',
		'create_own_notification',
		'view_message',
		'view_own_message',
		'view_searches',
		'view_own_searches',
		'create_searches',
		'create_own_searches',
		'edit_searches',
		'edit_own_searches',
		'remove_searches',
		'remove_own_searches',
		'view_user_role',
		'view_audit_log',
		'view_theme',
		'view_team_settings',
		'view_own_user_list',
		'create_own_user_list',
		'edit_own_user_list',
		'remove_own_user_list',
		'view_transition_history',
		'view_action_history',
		'view_non_case_action_history',
		'view_packet',
		'view_non_portal_fields',
		'save_non_hotline_fields',
		'view_flag_records',
		'view_tag_records',
		'view_dynamic_workflow_records',
		'view_static_data_form_entry_fields',
		'view_access_request',
		'create_access_request',
		'view_relationship_type',
	],
	blacklist: [
		'remove_default_system_language',
		'remove_user',
		'access_system_users',
		'access_static_service_accounts',
		'access_service_accounts',
		'remove_default_grid_filter',
		'edit_audit_log',
		'remove_audit_log',
		'remove_case',
		'bypass_case_manual_blacklist',
		'bypass_case_system_blacklist',
		'bypass_user_role_filter',
		'bypass_create',
		'own_locked_notification',
		'others_notification',
		'can_assign_appointment_replies_to_case',
		'create_others_user_list',
		'view_others_user_list',
		'edit_others_user_list',
		'remove_others_user_list',
		'bypass_dynamic_form_acl',
		'view_other_portal_user',
		'access_others_draft_children',
		'access_others_grid_views',
		'bypass_inherited_acl',
	],
};
