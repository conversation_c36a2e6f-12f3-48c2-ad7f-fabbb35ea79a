/**
 * Certain app configurations are discouraged in production. This file contains a list
 * of the variables that need to be validated and the list of values that are discouraged.
 */

const _ = require('lodash');
const config = require('./index');

module.exports = {
	discouragedVariableValues: {
		disableSecureCookies: [true],
		integrationPathFiles: [false, null, '', undefined],
		integrationPathFilesProcessed: [false, null, '', undefined],
		dataImportMaxFileSize: [false, null, '', undefined],
		dataImportMaxRecordCount: [false, null, '', undefined],
		customerCode: [false, null, '', undefined],
		sameSite: [false],
	},
	isProd() {
		return process.env.NODE_ENV === 'production';
	},
	validateProdConfig() {
		if (!this.isProd) return [];

		const variablesToValidate = this.discouragedVariableValues;
		const cautionedVariables = [];

		_.each(variablesToValidate, (undesirableValues, name) => {
			const prodValue = config[name];
			if (_.includes(undesirableValues, prodValue)) {
				cautionedVariables.push(name);
			}
		});

		return cautionedVariables;
	},
};
