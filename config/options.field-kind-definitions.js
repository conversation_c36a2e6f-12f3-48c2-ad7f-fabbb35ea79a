/**
 * Defines field kinds.
 *
 * See documentation on kind options [../entities/README.md#kind-options]
 *
 * A computedOnSave flag set to true should always be combined with search: true and/or schema: true
 *
 * computed-search fields are saved in the DB as an array but indexed in ES as separate fields.
 * All computed-search fields are flattened in the the API response.
 *
 * NOTE: Combining some flags would sometimes not make sense and results in NOOP. For example:
 *  {
 *  search: true,
 *  schema: false,
 *  computedOnRead: true
 *  }
 *
 */

const _ = require('lodash');
const fieldKindDefinitions = {};

fieldKindDefinitions.editable = {
	audit: true,
	schema: true,
	search: true,
	readable: true,
	apiWritable: true,
	apiExternalWritable: false,
	computedOnSave: false,
	computedOnRead: false,
	iselComputedOnSave: false,
	aggregateField: false,
	submitOnly: false,
	formVisible: true,
	gridVisible: true,
	gridSortable: true,
	searchVisible: true,
	formattedData: true,
	gridExportable: true,
	reportable: true,
};
fieldKindDefinitions['submit-only'] = {
	audit: true,
	schema: true,
	search: true,
	readable: true,
	apiWritable: true,
	apiExternalWritable: true,
	computedOnSave: false,
	computedOnRead: false,
	iselComputedOnSave: false,
	aggregateField: false,
	submitOnly: true,
	formVisible: true,
	gridVisible: true,
	gridSortable: true,
	searchVisible: true,
	formattedData: true,
	gridExportable: true,
	reportable: true,
};
fieldKindDefinitions['editable-external'] = {
	audit: true,
	schema: true,
	search: true,
	readable: true,
	apiWritable: true,
	apiExternalWritable: true,
	computedOnSave: false,
	computedOnRead: false,
	iselComputedOnSave: false,
	aggregateField: false,
	submitOnly: false,
	formVisible: true,
	gridVisible: true,
	gridSortable: true,
	searchVisible: true,
	formattedData: true,
	gridExportable: true,
	reportable: true,
};
fieldKindDefinitions.system = {
	audit: true,
	schema: true,
	search: true,
	readable: true,
	apiWritable: false,
	apiExternalWritable: false,
	computedOnSave: false,
	computedOnRead: false,
	iselComputedOnSave: false,
	aggregateField: false,
	submitOnly: false,
	formVisible: true,
	gridVisible: true,
	gridSortable: true,
	searchVisible: true,
	formattedData: true,
	gridExportable: true,
	reportable: true,
};
fieldKindDefinitions.hidden = {
	audit: false,
	schema: true,
	search: false,
	readable: true,
	apiWritable: false,
	apiExternalWritable: false,
	computedOnSave: false,
	computedOnRead: false,
	iselComputedOnSave: false,
	aggregateField: false,
	submitOnly: false,
	formVisible: false,
	gridVisible: false,
	gridSortable: false,
	searchVisible: false,
	formattedData: false,
	gridExportable: false,
	reportable: false,
};
fieldKindDefinitions['hidden-searchable'] = {
	audit: false,
	schema: true,
	search: true,
	readable: true,
	apiWritable: false,
	apiExternalWritable: false,
	computedOnSave: false,
	computedOnRead: false,
	iselComputedOnSave: false,
	aggregateField: false,
	submitOnly: false,
	formVisible: false,
	gridVisible: false,
	gridSortable: false,
	searchVisible: false,
	formattedData: true,
	gridExportable: false,
	reportable: false,
};
fieldKindDefinitions.computed = {
	audit: true,
	schema: true,
	search: true,
	readable: true,
	apiWritable: false,
	apiExternalWritable: false,
	computedOnSave: true,
	computedOnRead: false,
	iselComputedOnSave: false,
	aggregateField: false,
	submitOnly: false,
	formVisible: true,
	gridVisible: true,
	gridSortable: true,
	searchVisible: true,
	formattedData: true,
	gridExportable: true,
	reportable: true,
};
fieldKindDefinitions['computed-search'] = {
	audit: true,
	schema: false,
	search: true,
	readable: true,
	apiWritable: false,
	apiExternalWritable: false,
	computedOnSave: true,
	computedOnRead: false,
	iselComputedOnSave: false,
	aggregateField: false,
	submitOnly: false,
	formVisible: true,
	gridVisible: true,
	gridSortable: true,
	searchVisible: true,
	formattedData: true,
	gridExportable: true,
	reportable: false,
};
fieldKindDefinitions['computed-read'] = {
	audit: true,
	schema: false,
	search: false,
	readable: true,
	apiWritable: false,
	apiExternalWritable: false,
	computedOnSave: false,
	computedOnRead: true,
	iselComputedOnSave: false,
	aggregateField: false,
	submitOnly: false,
	formVisible: true,
	gridVisible: true,
	gridSortable: false,
	searchVisible: false,
	formattedData: false,
	gridExportable: true,
	reportable: false,
};
fieldKindDefinitions.join = {
	audit: false,
	schema: false,
	search: false,
	readable: true,
	apiWritable: false,
	apiExternalWritable: false,
	computedOnSave: false,
	computedOnRead: false,
	iselComputedOnSave: false,
	aggregateField: false,
	submitOnly: false,
	formVisible: false,
	gridVisible: false,
	gridSortable: false,
	searchVisible: false,
	formattedData: false,
	gridExportable: false,
	reportable: false,
};
fieldKindDefinitions.virtual = {
	audit: false,
	schema: false,
	search: false,
	readable: false,
	apiWritable: false,
	apiExternalWritable: false,
	computedOnSave: false,
	computedOnRead: false,
	iselComputedOnSave: false,
	aggregateField: false,
	submitOnly: false,
	formVisible: false,
	gridVisible: false,
	gridSortable: false,
	searchVisible: false,
	formattedData: false,
	gridExportable: false,
	reportable: false,
};
fieldKindDefinitions['virtual-external'] = {
	audit: false,
	schema: true,
	search: false,
	readable: true,
	apiWritable: false,
	apiExternalWritable: true,
	computedOnSave: false,
	computedOnRead: false,
	iselComputedOnSave: false,
	aggregateField: false,
	submitOnly: false,
	formVisible: false,
	gridVisible: false,
	gridSortable: false,
	searchVisible: false,
	formattedData: false,
	gridExportable: false,
	reportable: false,
};
// TODO: Create formal definitions for this (dynamic-editable) (ITPL-16409)
fieldKindDefinitions.dynamic = {
	audit: true,
	schema: false,
	search: true,
	readable: true,
	apiWritable: true,
	apiExternalWritable: false,
	computedOnSave: false,
	computedOnRead: false,
	iselComputedOnSave: false,
	aggregateField: false,
	submitOnly: false,
	// TODO: Need to evaluate below properties to see if they fit
	formVisible: true,
	gridVisible: true,
	gridSortable: true,
	searchVisible: true,
	formattedData: true,
	gridExportable: true,
	reportable: true,
	toJSONB: true,
};
fieldKindDefinitions['dynamic-external'] = _.extend({}, fieldKindDefinitions.dynamic, {
	apiExternalWritable: true,
});
fieldKindDefinitions['hidden-editable'] = {
	audit: false,
	schema: true,
	search: true,
	readable: true,
	apiWritable: true,
	apiExternalWritable: false,
	computedOnSave: false,
	computedOnRead: false,
	iselComputedOnSave: false,
	aggregateField: false,
	submitOnly: false,
	formVisible: false,
	gridVisible: false,
	gridSortable: false,
	searchVisible: false,
	formattedData: true,
	gridExportable: false,
	reportable: false,
};
fieldKindDefinitions['form-only'] = {
	audit: false,
	schema: false,
	search: false,
	readable: true,
	apiWritable: false,
	apiExternalWritable: false,
	computedOnSave: false,
	computedOnRead: false,
	iselComputedOnSave: false,
	aggregateField: false,
	submitOnly: false,
	formVisible: true,
	gridVisible: false,
	gridSortable: false,
	searchVisible: false,
	formattedData: false,
	gridExportable: false,
	reportable: false,
};

module.exports = fieldKindDefinitions;
