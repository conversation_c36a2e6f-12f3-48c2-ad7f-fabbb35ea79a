module.exports = {
	layoutType: 'form',
	layoutOpts: {
		type: 'custom',
		caption: 'Workplace Violence Report',
		captionPlural: 'Workplace Violence Reports',
		hideOnIntake: false,
	},
	children: [
		{
			layoutType: 'field',
			layoutOpts: {
				hideOnIntake: false,
				fieldDef: {
					type: 'datetime',
					caption: 'Incident date, time',
				},
			},
		},
		{
			layoutType: 'field',
			layoutOpts: {
				hideOnIntake: false,
				fieldDef: {
					type: 'textbox',
					caption: 'Location',
				},
			},
		},
		{
			layoutType: 'field',
			layoutOpts: {
				hideOnIntake: false,
				fieldDef: {
					type: 'textbox',
					caption: 'Workplace Violence Type',
				},
			},
		},
		{
			layoutType: 'field',
			layoutOpts: {
				hideOnIntake: false,
				fieldDef: {
					type: 'textbox',
					caption: 'Classification of who committed the violence',
				},
			},
		},
		{
			layoutType: 'field',
			layoutOpts: {
				hideOnIntake: false,
				fieldDef: {
					type: 'textarea',
					caption: 'The circumstances at the time of the incident',
				},
			},
		},
		{
			layoutType: 'field',
			layoutOpts: {
				hideOnIntake: false,
				fieldDef: {
					type: 'textbox',
					typeOptions: {
						subText: 'Including specific incident characteristics (e.g., physical attack, weapon involvement, threats, sexual assault, animal incident, or other events)',
					},
					caption: 'Detailed description of the incident',
					formCaption: 'Detailed description of the incident, including specific incident characteristics',
				},
			},
		},
		{
			layoutType: 'field',
			layoutOpts: {
				hideOnIntake: false,
				fieldDef: {
					type: 'textarea',
					typeOptions: {
						subText: 'Including involvement of law enforcement',
					},
					caption: 'Consequences of the incident',
					formCaption: 'Consequences of the incident, including involvement of law enforcement',
				},
			},
		},
		{
			layoutType: 'field',
			layoutOpts: {
				hideOnIntake: false,
				fieldDef: {
					type: 'textarea',
					caption: 'Steps to protect employees from threats or hazards',
					formCaption: 'Steps taken to protect employees from further threat or hazards',
				},
			},
		},
		{
			layoutType: 'section',
			layoutOpts: {
				caption: 'Person who filled in the log and the date it was completed',
				hideOnIntake: false,
			},
			children: [
				{
					layoutType: 'field',
					layoutOpts: {
						hideOnIntake: false,
						fieldDef: {
							type: 'textbox',
							caption: 'Name',
						},
					},
				},
				{
					layoutType: 'field',
					layoutOpts: {
						hideOnIntake: false,
						fieldDef: {
							type: 'textbox',
							caption: 'Job Title',
						},
					},
				},
			],
		},
	],
};
