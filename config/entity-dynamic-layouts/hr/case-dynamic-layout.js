module.exports = {
	layoutType: 'form',
	layoutOpts: {
		type: 'standard',
		entity: {
			base: 'sys',
			name: 'case',
		},
	},
	children: [
		{
			layoutType: 'insertionPoint',
			layoutOpts: {
				name: 'insertion-point-1',
			},
			children: [
				{
					layoutType: 'section',
					layoutOpts: {
						caption: 'Case Details',
						hideOnIntake: false,
					},
					children: [
						{
							layoutType: 'field',
							layoutOpts: {
								fieldDef: {
									type: 'picklist',
									caption: 'Case Category',
									mandatory: true,
									typeOptions: {
										picklistDependencies: ['caseType'],
										directPicklistDependency: ['case_types'],
									},
									picklistOptions: {
										name: 'case_categories',
										parents: ['case_types'],
									},
								},
								hideOnIntake: false,
								picklistItems: [
									{
										parents: ['Investigation'],
										value: 'Discrimination',
									},
									{
										parents: ['Investigation'],
										value: 'Harassment',
									},
									{
										parents: ['Investigation'],
										value: 'Health and safety',
									},
									{
										parents: ['Investigation'],
										value: 'Misconduct',
									},
									{
										parents: ['Accommodation'],
										value: 'Educational and professional development',
									},
									{
										parents: ['Accommodation'],
										value: 'Family and caregiving',
									},
									{
										parents: ['Accommodation'],
										value: 'Flexible work arrangments',
									},
									{
										parents: ['Accommodation'],
										value: 'Health and disability',
									},
									{
										parents: ['Accommodation'],
										value: 'Leave of absence',
									},
									{
										parents: ['Accommodation'],
										value: 'Mental health',
									},
									{
										parents: ['Accommodation'],
										value: 'Religious',
									},
									{
										parents: ['Accommodation'],
										value: 'Work-Life balance',
									},
									{
										parents: ['Inquiry'],
										value: 'Career development',
									},
									{
										parents: ['Inquiry'],
										value: 'Company policies and procedures',
									},
									{
										parents: ['Inquiry'],
										value: 'Compensation and payroll',
									},
									{
										parents: ['Inquiry'],
										value: 'Employee relations',
									},
									{
										parents: ['Inquiry'],
										value: 'Employment policies',
									},
									{
										parents: ['Inquiry'],
										value: 'General',
									},
									{
										parents: ['Inquiry'],
										value: 'Leave and time off',
									},
									{
										parents: ['Inquiry'],
										value: 'Onboarding and offboarding',
									},
									{
										parents: ['Performance'],
										value: 'Adaptability and learning',
									},
									{
										parents: ['Performance'],
										value: 'Behavior',
									},
									{
										parents: ['Performance'],
										value: 'Communication and collaboration',
									},
									{
										parents: ['Performance'],
										value: 'Motivation and engagement',
									},
									{
										parents: ['Performance'],
										value: 'Quality and accuracy',
									},
									{
										parents: ['Performance'],
										value: 'Work performance',
									},
								],
							},
						},
						{
							layoutType: 'field',
							layoutOpts: {
								fieldDef: {
									type: 'picklist',
									caption: 'Case Sub-Category',
									mandatory: true,
									typeOptions: {
										picklistDependencies: ['caseType', 'caseCategory'],
										directPicklistDependency: ['case_categories'],
									},
									picklistOptions: {
										name: 'case_sub_categories',
										parents: ['case_types', 'case_categories'],
									},
								},
								hideOnIntake: false,
								picklistItems: [
									{
										parents: ['Investigation', 'Discrimination'],
										value: 'Age',
									},
									{
										parents: ['Investigation', 'Discrimination'],
										value: 'Disability',
									},
									{
										parents: ['Investigation', 'Discrimination'],
										value: 'Gender',
									},
									{
										parents: ['Investigation', 'Discrimination'],
										value: 'Marital status',
									},
									{
										parents: ['Investigation', 'Discrimination'],
										value: 'Military status',
									},
									{
										parents: ['Investigation', 'Discrimination'],
										value: 'National origin',
									},
									{
										parents: ['Investigation', 'Discrimination'],
										value: 'Other',
									},
									{
										parents: ['Investigation', 'Discrimination'],
										value: 'Pregnancy',
									},
									{
										parents: ['Investigation', 'Discrimination'],
										value: 'Racial',
									},
									{
										parents: ['Investigation', 'Discrimination'],
										value: 'Religious',
									},
									{
										parents: ['Investigation', 'Discrimination'],
										value: 'Parental status',
									},
									{
										parents: ['Investigation', 'Discrimination'],
										value: 'Sexual orientation',
									},
									{
										parents: ['Investigation', 'Discrimination'],
										value: 'Weight',
									},
									{
										parents: ['Investigation', 'Harassment'],
										value: 'Age',
									},
									{
										parents: ['Investigation', 'Harassment'],
										value: 'Disability',
									},
									{
										parents: ['Investigation', 'Harassment'],
										value: 'Gender',
									},
									{
										parents: ['Investigation', 'Harassment'],
										value: 'Harassment based on marital status',
									},
									{
										parents: ['Investigation', 'Harassment'],
										value: 'Bullying',
									},
									{
										parents: ['Investigation', 'Harassment'],
										value: 'Sexual',
									},
									{
										parents: ['Investigation', 'Harassment'],
										value: 'Racial',
									},
									{
										parents: ['Investigation', 'Harassment'],
										value: 'Religious',
									},
									{
										parents: ['Investigation', 'Harassment'],
										value: 'National origin',
									},
									{
										parents: ['Investigation', 'Harassment'],
										value: 'Sexual orientation',
									},
									{
										parents: ['Investigation', 'Harassment'],
										value: 'Cyber',
									},
									{
										parents: ['Investigation', 'Health and safety'],
										value: 'Slip and fall',
									},
									{
										parents: ['Investigation', 'Health and safety'],
										value: 'Unsafe working conditions',
									},
									{
										parents: ['Investigation', 'Health and safety'],
										value: 'Non-compliance with fire safety and evacuation procedures',
									},
									{
										parents: ['Investigation', 'Health and safety'],
										value: 'Improper handling of hazardous materials',
									},
									{
										parents: ['Investigation', 'Health and safety'],
										value: 'Failure to use Personal Protective Equipment (PPE)',
									},
									{
										parents: ['Investigation', 'Health and safety'],
										value: 'Unsafe operation of machinery and equipment',
									},
									{
										parents: ['Investigation', 'Health and safety'],
										value: 'Ignoring ergonomics and workstation safety',
									},
									{
										parents: ['Investigation', 'Health and safety'],
										value: 'Hostile work environment',
									},
									{
										parents: ['Investigation', 'Health and safety'],
										value: 'Substance abuse violation',
									},
									{
										parents: ['Investigation', 'Health and safety'],
										value: 'Environmental hazards',
									},
									{
										parents: ['Investigation', 'Misconduct'],
										value: 'Insubordination',
									},
									{
										parents: ['Investigation', 'Misconduct'],
										value: 'Theft',
									},
									{
										parents: ['Investigation', 'Misconduct'],
										value: 'Falsification of records',
									},
									{
										parents: ['Investigation', 'Misconduct'],
										value: 'Workplace violence',
									},
									{
										parents: ['Investigation', 'Misconduct'],
										value: 'Absenteeism and tardiness',
									},
									{
										parents: ['Investigation', 'Misconduct'],
										value: 'Unprofessional conduct',
									},
									{
										parents: ['Investigation', 'Misconduct'],
										value: 'Conflict of interest',
									},
									{
										parents: ['Investigation', 'Misconduct'],
										value: 'Misuse of company resources',
									},
									{
										parents: ['Investigation', 'Misconduct'],
										value: 'Confidentiality breach',
									},
									{
										parents: ['Investigation', 'Misconduct'],
										value: 'Negligence',
									},
									{
										parents: ['Investigation', 'Misconduct'],
										value: 'Retaliation',
									},
									{
										parents: ['Investigation', 'Misconduct'],
										value: 'Damage to property',
									},
									{
										parents: ['Accommodation', 'Educational and professional development'],
										value: 'Tuition reimbursement',
									},
									{
										parents: ['Accommodation', 'Educational and professional development'],
										value: 'Study leave',
									},
									{
										parents: ['Accommodation', 'Educational and professional development'],
										value: 'Training programs',
									},
									{
										parents: ['Accommodation', 'Educational and professional development'],
										value: 'Mentorship programs',
									},
									{
										parents: ['Accommodation', 'Family and caregiving'],
										value: 'On-site childcare',
									},
									{
										parents: ['Accommodation', 'Family and caregiving'],
										value: 'Elder care support',
									},
									{
										parents: ['Accommodation', 'Family and caregiving'],
										value: 'Dependent care leave',
									},
									{
										parents: ['Accommodation', 'Flexible work arrangments'],
										value: 'Remote work',
									},
									{
										parents: ['Accommodation', 'Flexible work arrangments'],
										value: 'Flexible hours',
									},
									{
										parents: ['Accommodation', 'Flexible work arrangments'],
										value: 'Compressed work week',
									},
									{
										parents: ['Accommodation', 'Flexible work arrangments'],
										value: 'Job sharing',
									},
									{
										parents: ['Accommodation', 'Health and disability'],
										value: 'Ergonomic adjustments',
									},
									{
										parents: ['Accommodation', 'Health and disability'],
										value: 'Assistive technology',
									},
									{
										parents: ['Accommodation', 'Health and disability'],
										value: 'Modified duties',
									},
									{
										parents: ['Accommodation', 'Health and disability'],
										value: 'Workplace modifications',
									},
									{
										parents: ['Accommodation', 'Leave of absence'],
										value: 'Long-term',
									},
									{
										parents: ['Accommodation', 'Leave of absence'],
										value: 'Short-term',
									},
									{
										parents: ['Accommodation', 'Leave of absence'],
										value: 'Parental',
									},
									{
										parents: ['Accommodation', 'Leave of absence'],
										value: 'Bereavement',
									},
									{
										parents: ['Accommodation', 'Leave of absence'],
										value: 'Sabbatical',
									},
									{
										parents: ['Accommodation', 'Mental health'],
										value: 'Counseling services',
									},
									{
										parents: ['Accommodation', 'Mental health'],
										value: 'Stress management programs',
									},
									{
										parents: ['Accommodation', 'Mental health'],
										value: 'Mental health days',
									},
									{
										parents: ['Accommodation', 'Mental health'],
										value: 'Flexible workload',
									},
									{
										parents: ['Accommodation', 'Religious'],
										value: 'Flexible scheduling',
									},
									{
										parents: ['Accommodation', 'Religious'],
										value: 'Prayer and meditation space',
									},
									{
										parents: ['Accommodation', 'Religious'],
										value: 'Dress code modifications',
									},
									{
										parents: ['Accommodation', 'Religious'],
										value: 'Religious leave',
									},
									{
										parents: ['Accommodation', 'Work-Life balance'],
										value: 'Paid time off (PTO)',
									},
									{
										parents: ['Accommodation', 'Work-Life balance'],
										value: 'Wellness programs',
									},
									{
										parents: ['Accommodation', 'Work-Life balance'],
										value: 'Family events',
									},
									{
										parents: ['Accommodation', 'Work-Life balance'],
										value: 'Volunteer leave',
									},
									{
										parents: ['Inquiry', 'Career development'],
										value: 'Training programs',
									},
									{
										parents: ['Inquiry', 'Career development'],
										value: 'Performance reviews',
									},
									{
										parents: ['Inquiry', 'Career development'],
										value: 'Career advancement',
									},
									{
										parents: ['Inquiry', 'Career development'],
										value: 'Mentorship opportunities',
									},
									{
										parents: ['Inquiry', 'Career development'],
										value: 'Skill development',
									},
									{
										parents: ['Inquiry', 'Company policies and procedures'],
										value: 'IT and technology use',
									},
									{
										parents: ['Inquiry', 'Company policies and procedures'],
										value: 'Data privacy',
									},
									{
										parents: ['Inquiry', 'Company policies and procedures'],
										value: 'Health and safety',
									},
									{
										parents: ['Inquiry', 'Company policies and procedures'],
										value: 'Compliance and legal',
									},
									{
										parents: ['Inquiry', 'Company policies and procedures'],
										value: 'Travel policies',
									},
									{
										parents: ['Inquiry', 'Compensation and payroll'],
										value: 'Paychecks',
									},
									{
										parents: ['Inquiry', 'Compensation and payroll'],
										value: 'Overtime policies',
									},
									{
										parents: ['Inquiry', 'Compensation and payroll'],
										value: 'Tax forms',
									},
									{
										parents: ['Inquiry', 'Compensation and payroll'],
										value: 'Expense reimbursement',
									},
									{
										parents: ['Inquiry', 'Compensation and payroll'],
										value: 'Payroll deductions',
									},
									{
										parents: ['Inquiry', 'Compensation and payroll'],
										value: 'Paid time off (PTO)',
									},
									{
										parents: ['Inquiry', 'Compensation and payroll'],
										value: 'Salary information',
									},
									{
										parents: ['Inquiry', 'Employee relations'],
										value: 'Conflict resolution',
									},
									{
										parents: ['Inquiry', 'Employee relations'],
										value: 'Harassment and discrimination',
									},
									{
										parents: ['Inquiry', 'Employee relations'],
										value: 'Employee feedback',
									},
									{
										parents: ['Inquiry', 'Employee relations'],
										value: 'Workplace accommodations',
									},
									{
										parents: ['Inquiry', 'Employee relations'],
										value: 'Exit interviews',
									},
									{
										parents: ['Inquiry', 'Employment policies'],
										value: 'Work hours and schedules',
									},
									{
										parents: ['Inquiry', 'Employment policies'],
										value: 'Attendance and punctuality',
									},
									{
										parents: ['Inquiry', 'Employment policies'],
										value: 'Dress code',
									},
									{
										parents: ['Inquiry', 'Employment policies'],
										value: 'Code of conduct',
									},
									{
										parents: ['Inquiry', 'Employment policies'],
										value: 'Remote work policies',
									},
									{
										parents: ['Inquiry', 'General'],
										value: 'General inquiry',
									},
									{
										parents: ['Inquiry', 'Leave and time off'],
										value: 'Parental',
									},
									{
										parents: ['Inquiry', 'Leave and time off'],
										value: 'Family and medical',
									},
									{
										parents: ['Inquiry', 'Leave and time off'],
										value: 'Bereavement',
									},
									{
										parents: ['Inquiry', 'Leave and time off'],
										value: 'Sabbatical',
									},
									{
										parents: ['Inquiry', 'Leave and time off'],
										value: 'Holiday schedule',
									},
									{
										parents: ['Inquiry', 'Onboarding and offboarding'],
										value: 'New hire orientation',
									},
									{
										parents: ['Inquiry', 'Onboarding and offboarding'],
										value: 'Probationary period',
									},
									{
										parents: ['Inquiry', 'Onboarding and offboarding'],
										value: 'Separation procedures',
									},
									{
										parents: ['Inquiry', 'Onboarding and offboarding'],
										value: 'Exit process',
									},
									{
										parents: ['Inquiry', 'Onboarding and offboarding'],
										value: 'Rehire policies',
									},
									{
										parents: ['Performance', 'Adaptability and learning'],
										value: 'Resistance to change or new processes',
									},
									{
										parents: ['Performance', 'Adaptability and learning'],
										value: 'Difficulty adapting to new technologies or systems',
									},
									{
										parents: ['Performance', 'Adaptability and learning'],
										value: 'Inadequate training or knowledge gaps',
									},
									{
										parents: ['Performance', 'Behavior'],
										value: 'Inappropriate behavior or conduct in the workplace',
									},
									{
										parents: ['Performance', 'Behavior'],
										value: 'Attendance issues',
									},
									{
										parents: ['Performance', 'Communication and collaboration'],
										value: 'Difficulty working in teams or collaborating with others',
									},
									{
										parents: ['Performance', 'Communication and collaboration'],
										value: 'Communication problems',
									},
									{
										parents: ['Performance', 'Communication and collaboration'],
										value: 'Conflict with colleagues or supervisors',
									},
									{
										parents: ['Performance', 'Communication and collaboration'],
										value: 'Resistance to feedback or coaching',
									},
									{
										parents: ['Performance', 'Motivation and engagement'],
										value: 'Lack of initiative or motivation',
									},
									{
										parents: ['Performance', 'Motivation and engagement'],
										value: 'Lack of engagement or interest in work tasks',
									},
									{
										parents: ['Performance', 'Motivation and engagement'],
										value: 'Negative attitude or morale issues',
									},
									{
										parents: ['Performance', 'Quality and accuracy'],
										value: 'Poor quality of work',
									},
									{
										parents: ['Performance', 'Quality and accuracy'],
										value: 'Lack of attention to detail',
									},
									{
										parents: ['Performance', 'Quality and accuracy'],
										value: 'Limited problem-solving skills',
									},
									{
										parents: ['Performance', 'Work performance'],
										value: 'Low productivity or output levels',
									},
									{
										parents: ['Performance', 'Work performance'],
										value: 'Missed deadlines or frequent delays',
									},
									{
										parents: ['Performance', 'Work performance'],
										value: 'Poor time management skills',
									},
									{
										parents: ['Performance', 'Work performance'],
										value: 'Inconsistent performance',
									},
									{
										parents: ['Performance', 'Work performance'],
										value: 'Inability to meet performance targets or goals',
									},
								],
							},
						},
						{
							layoutType: 'field',
							layoutOpts: {
								fieldDef: {
									type: 'picklist',
									caption: 'Case Details Intake Method',
									formCaption: 'Intake Method',
								},
								hideOnIntake: false,
								picklistItems: [
									{ value: 'Email' },
									{ value: 'Hotline' },
									{ value: 'In person' },
									{ value: 'Phone' },
									{ value: 'Portal' },
								],
							},
						},
						{
							layoutType: 'field',
							layoutOpts: {
								fieldDef: {
									type: 'radio',
									typeOptions: {
										options: [
											'Low',
											'Medium',
											'High',
										],
									},
									caption: 'Priority',
								},
								hideOnIntake: false,
							},
						},
						{
							layoutType: 'field',
							layoutOpts: {
								fieldDef: {
									type: 'textarea',
									caption: 'Case Details',
								},
								hideOnIntake: false,
							},
						},
						{
							layoutType: 'field',
							layoutOpts: {
								fieldDef: {
									type: 'picklist',
									caption: 'Department',
								},
								picklistItems: [
									{ value: 'Administration' },
									{ value: 'Customer Service' },
									{ value: 'Finance' },
									{ value: 'Human Resources' },
									{ value: 'Information Technology' },
									{ value: 'Marketing' },
									{ value: 'Operations' },
									{ value: 'Sales' },
								],
								hideOnIntake: false,
							},
						},
					],
				},
				{
					layoutType: 'section',
					layoutOpts: {
						caption: 'Dates',
						hideOnIntake: false,
					},
					children: [
						{
							layoutType: 'field',
							layoutOpts: {
								fieldDef: {
									type: 'date',
									caption: 'Reported Date',
								},
								hideOnIntake: false,
							},
						},
						{
							layoutType: 'field',
							layoutOpts: {
								fieldDef: {
									type: 'datetime',
									caption: 'Date and time of incident',
								},
								hideOnIntake: false,
							},
						},
						{
							layoutType: 'field',
							layoutOpts: {
								fieldDef: {
									type: 'date',
									caption: 'Date Incident Started',
								},
							},
						},
						{
							layoutType: 'field',
							layoutOpts: {
								fieldDef: {
									type: 'radio',
									typeOptions: {
										options: [
											'Yes',
											'No',
											'Undetermined',
										],
									},
									caption: 'Is the issue ongoing?',
								},
							},
						},
						{
							layoutType: 'field',
							layoutOpts: {
								fieldDef: {
									type: 'radio',
									typeOptions: {
										options: [
											'Yes',
											'No',
											'Undetermined',
										],
									},
									caption: 'Has the issue been reported to a supervisor in the past?',
								},
							},
						},
						{
							layoutType: 'field',
							layoutOpts: {
								fieldDef: {
									type: 'date',
									caption: 'Date Due',
								},
							},
						},
					],
				},
				{
					layoutType: 'section',
					layoutOpts: {
						caption: 'Location',
						hideOnIntake: false,
					},
					children: [
						{
							layoutType: 'field',
							layoutOpts: {
								fieldDef: {
									type: 'picklist',
									caption: 'Location of incident',
								},
								picklistItems: [
									{ value: 'Customer site' },
									{ value: 'Company or third party event' },
									{ value: 'On site' },
									{ value: 'Phone' },
									{ value: 'Office' },
									{ value: 'Virtual Conference' },
									{ value: 'Other' },
								],
								hideOnIntake: false,
							},
						},
						{
							layoutType: 'field',
							layoutOpts: {
								fieldDef: {
									type: 'textbox',
									caption: 'Additional or other information about the location',
								},
							},
						},
						{
							layoutType: 'field',
							layoutOpts: {
								fieldDef: {
									type: 'textbox',
									caption: 'Address',
								},
								hideOnIntake: false,
							},
						},
						{
							layoutType: 'field',
							layoutOpts: {
								fieldDef: {
									type: 'textbox',
									caption: 'Location State Province',
									formCaption: 'State/Province',
								},
								hideOnIntake: false,
							},
						},
						{
							layoutType: 'field',
							layoutOpts: {
								fieldDef: {
									type: 'textbox',
									caption: 'Location Country',
									formCaption: 'Country',
								},
								hideOnIntake: false,
							},
						},
					],
				},
				{
					layoutType: 'section',
					layoutOpts: {
						caption: 'People Involved',
					},
					children: [
						{
							layoutType: 'entity-grid',
							layoutOpts: {
								gridEntity: 'sys/party',
								caption: 'Parties',
							},
						},
						{
							layoutType: 'field',
							layoutOpts: {
								fieldDef: {
									type: 'radio',
									typeOptions: {
										options: [
											'Yes',
											'No',
										],
									},
									caption: 'Remain anonymous?',
								},
							},
						},
						{
							layoutType: 'field',
							layoutOpts: {
								fieldDef: {
									type: 'textbox',
									caption: 'Reporter\'s name',
								},
							},
						},
						{
							layoutType: 'field',
							layoutOpts: {
								fieldDef: {
									type: 'textbox',
									caption: 'Reporter\'s email',
								},
							},
						},
						{
							layoutType: 'field',
							layoutOpts: {
								fieldDef: {
									type: 'picklist',
									caption: 'Relationship to organization',
								},
								picklistItems: [
									{ value: 'Contractor' },
									{ value: 'Customer' },
									{ value: 'Employee' },
									{ value: 'Former employee' },
									{ value: 'Law enforcement' },
									{ value: 'Non-employee' },
									{ value: 'Vendor' },
								],
								hideOnIntake: false,
							},
						},
					],
				},
			],
		},
		{
			layoutType: 'tab',
			layoutOpts: {
				caption: 'Assessment',
			},
			children: [
				{
					layoutType: 'section',
					layoutOpts: {
						caption: 'Medical or Professional Documentation',
					},
					children: [
						{
							layoutType: 'field',
							layoutOpts: {
								fieldDef: {
									type: 'radio',
									typeOptions: {
										options: [
											'Yes',
											'No',
										],
									},
									caption: 'Has documentation been attached to this case?',
								},
							},
						},
						{
							layoutType: 'field',
							layoutOpts: {
								fieldDef: {
									type: 'textbox',
									typeOptions: {
										subText: 'E.g., Medical reports, doctor\'s notes, letters of accommodation',
									},
									caption: 'Type of documentation provided',
								},
							},
						},
						{
							layoutType: 'field',
							layoutOpts: {
								fieldDef: {
									type: 'textbox',
									caption: 'Summary of key points from documentation',
								},
							},
						},
						{
							layoutType: 'field',
							layoutOpts: {
								fieldDef: {
									type: 'date',
									caption: 'Date of documentation',
								},
							},
						},
					],
				},
				{
					layoutType: 'section',
					layoutOpts: {
						caption: 'Job Information',
					},
					children: [
						{
							layoutType: 'field',
							layoutOpts: {
								fieldDef: {
									type: 'textbox',
									caption: 'Essential functions of the employee\'s job',
								},
							},
						},
						{
							layoutType: 'field',
							layoutOpts: {
								fieldDef: {
									type: 'textbox',
									caption: 'What specific accommodation is the requestor requesting?',
								},
							},
						},
						{
							layoutType: 'field',
							layoutOpts: {
								fieldDef: {
									type: 'textbox',
									caption: 'Describe how the request relates to these functions',
								},
							},
						},
					],
				},
				{

					layoutType: 'section',
					layoutOpts: {
						caption: 'Previous Accommodations',
					},
					children: [
						{
							layoutType: 'field',
							layoutOpts: {
								fieldDef: {
									type: 'radio',
									typeOptions: {
										options: [
											'Yes',
											'No',
											'Unknown',
										],
									},
									caption: 'Has the requestor made similar requests in the past?',
								},
							},
						},
						{
							layoutType: 'field',
							layoutOpts: {
								fieldDef: {
									type: 'textbox',
									caption: 'Details of past accommodations provided to employee',
								},
							},
						},
						{
							layoutType: 'field',
							layoutOpts: {
								fieldDef: {
									type: 'textbox',
									caption: 'Describe the effectiveness of previous accommodations',
								},
							},
						},
					],
				},
				{

					layoutType: 'section',
					layoutOpts: {
						caption: 'Initial Assessment',
					},
					children: [
						{
							layoutType: 'field',
							layoutOpts: {
								fieldDef: {
									type: 'textarea',
									caption: 'Preliminary observation on the request',
								},
							},
						},
						{
							layoutType: 'field',
							layoutOpts: {
								fieldDef: {
									type: 'textarea',
									caption: 'Initial thoughts on potential accommodations',
								},
							},
						},
					],
				},
			],
		},
		{
			layoutType: 'tab',
			layoutOpts: {
				caption: 'Planning',
			},
			children: [
				{
					layoutType: 'section',
					layoutOpts: {
						caption: 'To-Do',
					},
					children: [
						{
							layoutType: 'entity-grid',
							layoutOpts: {
								gridEntity: 'sys/todo',
								caption: 'Action Items',
							},
						},
					],
				},
				{
					layoutType: 'section',
					layoutOpts: {
						caption: 'Initial Risk Factor Review',
					},
					children: [
						{
							layoutType: 'field',
							layoutOpts: {
								fieldDef: {
									type: 'radio',
									typeOptions: {
										options: [
											'Yes',
											'No',
											'Undetermined',
										],
									},
									caption: 'Safety and wellbeing of employees at risk?',
								},
							},
						},
						{
							layoutType: 'field',
							layoutOpts: {
								fieldDef: {
									type: 'radio',
									typeOptions: {
										options: [
											'Yes',
											'No',
											'Undetermined',
										],
									},
									caption: 'Potentially significant financial impact?',
								},
							},
						},
						{
							layoutType: 'field',
							layoutOpts: {
								fieldDef: {
									type: 'radio',
									typeOptions: {
										options: [
											'Yes',
											'No',
											'Undetermined',
										],
									},
									caption: 'Any high profile or VIP directly involved in the case?',
								},
							},
						},
						{
							layoutType: 'field',
							layoutOpts: {
								fieldDef: {
									type: 'radio',
									typeOptions: {
										options: [
											'Yes',
											'No',
											'Undetermined',
										],
									},
									caption: 'Does the subject have any prior case history?',
								},
							},
						},
						{
							layoutType: 'field',
							layoutOpts: {
								fieldDef: {
									type: 'radio',
									typeOptions: {
										options: [
											'Yes',
											'No',
											'Undetermined',
										],
									},
									caption: 'Any direct customer impact?',
								},
							},
						},
						{
							layoutType: 'field',
							layoutOpts: {
								fieldDef: {
									type: 'radio',
									typeOptions: {
										options: [
											'Yes',
											'No',
											'Undetermined',
										],
									},
									caption: 'Legal risks?',
								},
							},
						},
						{
							layoutType: 'field',
							layoutOpts: {
								fieldDef: {
									type: 'radio',
									typeOptions: {
										options: [
											'Yes',
											'No',
											'Undetermined',
										],
										subText: 'E.g., Police, Media, or Government Agency',
									},
									caption: 'Has this been referred to anyone outside the organization?',
								},
							},
						},
						{
							layoutType: 'field',
							layoutOpts: {
								fieldDef: {
									type: 'textarea',
									caption: 'Additional risk factors?',
								},
							},
						},
					],
				},
				{

					layoutType: 'section',
					layoutOpts: {
						caption: 'Initial Planning Assessment',
					},
					children: [
						{
							layoutType: 'field',
							layoutOpts: {
								fieldDef: {
									type: 'picklist[]',
									caption: 'Immediate actions required',
								},
								picklistItems: [
									{ value: 'No action required' },
									{ value: 'Contact law enforcement' },
									{ value: 'Drug and alcohol test' },
									{ value: 'Employee suspension' },
									{ value: 'Employee termination' },
									{ value: 'Notify executive team (CEO, regional VP)' },
									{ value: 'Notify legal' },
									{ value: 'Reassign to other team' },
								],
								hideOnIntake: false,
							},
						},
						{
							layoutType: 'field',
							layoutOpts: {
								fieldDef: {
									type: 'textbox',
									caption: 'Scope of the investigation',
								},
							},
						},
						{
							layoutType: 'field',
							layoutOpts: {
								fieldDef: {
									type: 'textarea',
									caption: 'Critical timelines',
								},
							},
						},
					],
				},
			],
		},
		{
			layoutType: 'tab',
			layoutOpts: {
				caption: 'Evaluation',
			},
			children: [
				{
					layoutType: 'section',
					layoutOpts: {
						caption: 'Evaluation Action',
					},
					children: [
						{
							layoutType: 'field',
							layoutOpts: {
								fieldDef: {
									type: 'textbox',
									caption: 'Resources required for an effective investigation',
								},
							},
						},
						{
							layoutType: 'field',
							layoutOpts: {
								fieldDef: {
									type: 'textbox',
									caption: 'Describe the communication steps and how to execute them',
								},
							},
						},
						{
							layoutType: 'field',
							layoutOpts: {
								fieldDef: {
									type: 'textbox',
									caption: 'Identify important deadlines or time contraints',
								},
							},
						},
						{
							layoutType: 'field',
							layoutOpts: {
								fieldDef: {
									type: 'picklist',
									caption: 'Identify any groups involved in the investigation',
									typeOptions: {
										subText: 'E.g., departments, teams or external agencies',
									},
								},
								picklistItems: [
									{ value: 'Associate relations' },
									{ value: 'Accounting' },
									{ value: 'Compliance' },
									{ value: 'IT' },
									{ value: 'Law enforcement' },
									{ value: 'Legal' },
									{ value: 'Operations' },
									{ value: 'Security' },
									{ value: 'None' },
								],
							},
						},
					],
				},
				{
					layoutType: 'section',
					layoutOpts: {
						caption: 'Legal Compliance',
					},
					children: [
						{
							layoutType: 'field',
							layoutOpts: {
								fieldDef: {
									type: 'radio',
									typeOptions: {
										options: [
											'Yes',
											'No',
										],
									},
									caption: 'ADA related',
								},
							},
						},
						{
							layoutType: 'field',
							layoutOpts: {
								fieldDef: {
									type: 'textbox',
									caption: 'Legal consultations',
								},
							},
						},
						{
							layoutType: 'field',
							layoutOpts: {
								fieldDef: {
									type: 'radio',
									typeOptions: {
										options: [
											'Yes',
											'No',
											'Undetermined',
										],
									},
									caption: 'Legal Hold?',
								},
							},
						},
					],
				},
				{
					layoutType: 'section',
					layoutOpts: {
						caption: 'Evaluation Dates',
					},
					children: [
						{
							layoutType: 'field',
							layoutOpts: {
								fieldDef: {
									type: 'date',
									caption: 'Investigation start date',
								},
							},
						},
						{
							layoutType: 'field',
							layoutOpts: {
								fieldDef: {
									type: 'date',
									caption: 'Date investigator initially contacted',
								},
							},
						},
					],
				},
			],
		},
		{
			layoutType: 'tab',
			layoutOpts: {
				caption: 'Investigation',
			},
			children: [
				{
					layoutType: 'section',
					layoutOpts: {
						caption: 'Investigation',
					},
					children: [
						{
							layoutType: 'field',
							layoutOpts: {
								fieldDef: {
									type: 'user',
									caption: 'Primary Investigator',
								},
							},
						},
						{
							layoutType: 'field',
							layoutOpts: {
								fieldDef: {
									type: 'user',
									caption: 'Secondary Investigator',
								},
							},
						},
					],
				},
				{
					layoutType: 'section',
					layoutOpts: {
						caption: 'Legal',
					},
					children: [
						{
							layoutType: 'field',
							layoutOpts: {
								fieldDef: {
									type: 'radio',
									typeOptions: {
										options: [
											'Yes',
											'No',
											'Maybe',
										],
									},
									caption: 'Legal involvement?',
								},
							},
						},
						{
							layoutType: 'field',
							layoutOpts: {
								fieldDef: {
									type: 'radio',
									typeOptions: {
										options: [
											'Yes',
											'No',
											'Maybe',
										],
									},
									caption: 'Legally privileged?',
								},
							},
						},
					],
				},
				{
					layoutType: 'section',
					layoutOpts: {
						caption: 'Case Milestones',
					},
					children: [
						{
							layoutType: 'field',
							layoutOpts: {
								fieldDef: {
									type: 'date',
									caption: 'Date assigned to investigator',
								},
							},
						},
						{
							layoutType: 'field',
							layoutOpts: {
								fieldDef: {
									type: 'date',
									caption: 'Investigation due date',
								},
							},
						},
						{
							layoutType: 'field',
							layoutOpts: {
								fieldDef: {
									type: 'date',
									caption: 'Interview(s) start date',
								},
							},
						},
						{
							layoutType: 'field',
							layoutOpts: {
								fieldDef: {
									type: 'date',
									caption: 'Interview(s) end date',
								},
							},
						},
						{
							layoutType: 'field',
							layoutOpts: {
								fieldDef: {
									type: 'date',
									caption: 'Corrective action determination date',
								},
							},
						},
						{
							layoutType: 'field',
							layoutOpts: {
								fieldDef: {
									type: 'date',
									caption: 'Case close date',
								},
							},
						},
					],
				},
				{
					layoutType: 'section',
					layoutOpts: {
						caption: 'Investigative Details',
					},
					children: [
						{
							layoutType: 'field',
							layoutOpts: {
								fieldDef: {
									type: 'radio',
									typeOptions: {
										options: [
											'Yes',
											'No',
										],
									},
									caption: 'Has all supporting documentation been received?',
								},
							},
						},
						{
							layoutType: 'field',
							layoutOpts: {
								fieldDef: {
									type: 'radio',
									typeOptions: {
										options: [
											'Yes',
											'No',
										],
									},
									caption: 'Have all necessary interviews been completed?',
								},
							},
						},
						{
							layoutType: 'field',
							layoutOpts: {
								fieldDef: {
									type: 'radio',
									typeOptions: {
										options: [
											'Yes',
											'No',
										],
									},
									caption: 'Have you partnered with an HR manager?',
								},
							},
						},
					],
				},
			],
		},
		{
			layoutType: 'tab',
			layoutOpts: {
				caption: 'Outcome',
			},
			children: [
				{
					layoutType: 'section',
					layoutOpts: {
						caption: 'Decision',
					},
					children: [
						{
							layoutType: 'field',
							layoutOpts: {
								fieldDef: {
									type: 'picklist',
									caption: 'Accommodation decision',
								},
								picklistItems: [
									{ value: 'Approved' },
									{ value: 'Closed (no action)' },
									{ value: 'Denied' },
								],
							},
						},
						{
							layoutType: 'field',
							layoutOpts: {
								fieldDef: {
									type: 'textbox',
									caption: 'Reason for decision',
								},
							},
						},
						{
							layoutType: 'field',
							layoutOpts: {
								fieldDef: {
									type: 'picklist',
									caption: 'Accommodation closure reason',
								},
								picklistItems: [
									{ value: 'Continuous pattern' },
									{ value: 'Does not qualify' },
									{ value: 'Employee resignation' },
									{ value: 'Employee retirement' },
									{ value: 'Employee termination' },
									{ value: 'Employee withdrew request' },
									{ value: 'Failure to cooperate' },
									{ value: 'Resolved' },
									{ value: 'Withdrawal' },
								],
							},
						},
						{
							layoutType: 'field',
							layoutOpts: {
								fieldDef: {
									type: 'picklist',
									caption: 'Reason for denial',
								},
								picklistItems: [
									{ value: 'Causing a lack of necessary staffing' },
									{ value: 'Costing more than a minimal amount' },
									{ value: 'Jeopardizing security or health' },
									{ value: 'Violating seniority system' },
									{ value: 'Accommodation ineffective' },
									{ value: 'Accommodation would cause undue hardship' },
									{ value: 'Medical documentation inadequate' },
								],
							},
						},
						{
							layoutType: 'field',
							layoutOpts: {
								fieldDef: {
									type: 'textarea',
									caption: 'Additional comments',
								},
							},
						},
						{
							layoutType: 'field',
							layoutOpts: {
								fieldDef: {
									type: 'textbox',
									caption: 'Decision',
								},
							},
						},
						{
							layoutType: 'field',
							layoutOpts: {
								fieldDef: {
									type: 'textbox',
									caption: 'Any conditions or limitations',
								},
							},
						},
						{
							layoutType: 'field',
							layoutOpts: {
								fieldDef: {
									type: 'date',
									caption: 'Date of decision',
								},
							},
						},
					],
				},
				{
					layoutType: 'section',
					layoutOpts: {
						caption: 'Implementation Information',
					},
					children: [
						{
							layoutType: 'field',
							layoutOpts: {
								fieldDef: {
									type: 'picklist',
									caption: 'Implementation',
								},
								picklistItems: [
									{ value: 'Accessibility' },
									{ value: 'Acquiring or modifying equipment' },
									{ value: 'Dress and grooming' },
									{ value: 'Job reassignment' },
									{ value: 'Job restructuring' },
									{ value: 'Modifications to training materials/practices/policies' },
									{ value: 'Religious observance or practice' },
									{ value: 'Scheduling changes' },
								],
							},
						},
						{
							layoutType: 'field',
							layoutOpts: {
								fieldDef: {
									type: 'date',
									caption: 'Implementation date',
								},
							},
						},
						{
							layoutType: 'field',
							layoutOpts: {
								fieldDef: {
									type: 'textarea',
									caption: 'Steps taken for implementation',
								},
							},
						},
						{
							layoutType: 'field',
							layoutOpts: {
								fieldDef: {
									type: 'textbox',
									caption: 'Costs',
								},
							},
						},
					],
				},
				{
					layoutType: 'section',
					layoutOpts: {
						caption: 'Follow-up and Effectiveness',
					},
					children: [
						{
							layoutType: 'field',
							layoutOpts: {
								fieldDef: {
									type: 'date',
									caption: 'Follow-up date',
								},
							},
						},
						{
							layoutType: 'field',
							layoutOpts: {
								fieldDef: {
									type: 'textarea',
									caption: 'Employee feedback post-implementation',
								},
							},
						},
						{
							layoutType: 'field',
							layoutOpts: {
								fieldDef: {
									type: 'picklist',
									caption: 'Effectiveness assessment',
								},
								picklistItems: [
									{ value: 'Not satisfied'},
									{ value: 'Satisfied' },
									{ value: 'Very satisfied' },
								],
							},
						},
						{
							layoutType: 'field',
							layoutOpts: {
								fieldDef: {
									type: 'textbox',
									typeOptions: {
										subText: 'Were any adjustments made?',
									},
									caption: 'Adjustments or modifications to the accommodation',
								},
							},
						},
					],
				},
				{
					layoutType: 'section',
					layoutOpts: {
						caption: 'Summary Outcome',
					},
					children: [
						{
							layoutType: 'field',
							layoutOpts: {
								fieldDef: {
									type: 'textarea',
									caption: 'Description',
								},
							},
						},
						{
							layoutType: 'field',
							layoutOpts: {
								fieldDef: {
									type: 'date',
									caption: 'Date of response',
								},
							},
						},
					],
				},
			],
		},
		{
			layoutType: 'tab',
			layoutOpts: {
				caption: 'Resolution',
			},
			children: [
				{
					layoutType: 'section',
					layoutOpts: {
						caption: 'Summary Resolution',
					},
					children: [
						{
							layoutType: 'field',
							layoutOpts: {
								fieldDef: {
									type: 'picklist',
									caption: 'Resolution Disposition',
									formCaption: 'Disposition',
								},
								picklistItems: [
									{ value: 'Substantiated' },
									{ value: 'Unsubstantiated' },
									{ value: 'Withdrawn' },
									{ value: 'Referred/Transferred' },
									{ value: 'Partially Substantiated' },
								],
							},
						},
						{
							layoutType: 'field',
							layoutOpts: {
								fieldDef: {
									type: 'textarea',
									caption: 'Disposition details',
								},
							},
						},
						{
							layoutType: 'field',
							layoutOpts: {
								fieldDef: {
									type: 'date',
									caption: 'Date of disposition determination',
								},
							},
						},
						{
							layoutType: 'field',
							layoutOpts: {
								fieldDef: {
									type: 'picklist[]',
									caption: 'Associated policies',
								},
								picklistItems: [
									{ value: 'Anti-Discrimination and Harassment Policy' },
									{ value: 'Code of Conduct' },
									{ value: 'Compliance with Laws and Regulations' },
									{ value: 'Conflicts of Interest Policy' },
									{ value: 'Data Protection and Confidentiality Policy' },
									{ value: 'Disciplinary and Grievance Procedures' },
									{ value: 'Gifts and Entertainment Policy' },
									{ value: 'Social Media Policy' },
									{ value: 'Substnace Abuse Policy' },
									{ value: 'Whistleblower Protection Policy' },
								],
							},
						},
						{
							layoutType: 'field',
							layoutOpts: {
								fieldDef: {
									type: 'radio',
									typeOptions: {
										options: [
											'Yes',
											'No',
										],
									},
									caption: 'Is there a corrective action?',
								},
							},
						},
						{
							layoutType: 'field',
							layoutOpts: {
								fieldDef: {
									type: 'radio',
									typeOptions: {
										options: [
											'Yes',
											'No',
										],
									},
									caption: 'Is there a preventative action?',
								},
							},
						},
					],
				},
				{
					layoutType: 'section',
					layoutOpts: {
						caption: 'Corrective Action',
					},
					children: [
						{
							layoutType: 'field',
							layoutOpts: {
								fieldDef: {
									type: 'textarea',
									caption: 'Corrective action notes',
								},
							},
						},
						{
							layoutType: 'field',
							layoutOpts: {
								fieldDef: {
									type: 'picklist[]',
									caption: 'Recommended corrective action',
								},
								picklistItems: [
									{ value: 'Coaching' },
									{ value: 'Demotion / Reassignment' },
									{ value: 'Financial penalties' },
									{ value: 'Mediated conversation' },
									{ value: 'No action' },
									{ value: 'Not applicable' },
									{ value: 'Termination' },
									{ value: 'Training' },
									{ value: 'Vendor terminated' },
									{ value: 'Warning' },
								],
							},
						},
						{
							layoutType: 'field',
							layoutOpts: {
								fieldDef: {
									type: 'picklist[]',
									caption: 'Final corrective action',
								},
								picklistItems: [
									{ value: 'Coaching' },
									{ value: 'Demotion / Reassignment' },
									{ value: 'Financial penalties' },
									{ value: 'Mediated conversation' },
									{ value: 'No action' },
									{ value: 'Not applicable' },
									{ value: 'Termination' },
									{ value: 'Training' },
									{ value: 'Vendor terminated' },
									{ value: 'Warning' },
								],
							},
						},
					],
				},
				{
					layoutType: 'section',
					layoutOpts: {
						caption: 'Preventative Action',
					},
					children: [
						{
							layoutType: 'field',
							layoutOpts: {
								fieldDef: {
									type: 'picklist',
									caption: 'Root cause',
								},
								picklistItems: [
									{ value: 'Policy and procedure violations' },
									{ value: 'Training and development issues' },
									{ value: 'Communication breakdown' },
									{ value: 'Work environment factors' },
									{ value: 'Management practices' },
									{ value: 'Employee behavior and attitudes' },
									{ value: 'System and process failures' },
									{ value: 'Resource limitations' },
									{ value: 'External factors' },
									{ value: 'Organizational structure' },
									{ value: 'Cultural and social influences ' },
								],
							},
						},
						{
							layoutType: 'field',
							layoutOpts: {
								fieldDef: {
									type: 'textarea',
									caption: 'Root cause details',
								},
							},
						},
						{
							layoutType: 'field',
							layoutOpts: {
								fieldDef: {
									type: 'textarea',
									caption: 'Preventative action notes',
								},
							},
						},
						{
							layoutType: 'field',
							layoutOpts: {
								fieldDef: {
									type: 'picklist[]',
									caption: 'Recommended preventative action',
								},
								picklistItems: [
									{ value: 'Coaching' },
									{ value: 'Employee resignation' },
									{ value: 'Internal audit conducted' },
									{ value: 'Leave of absence' },
									{ value: 'Management coaching' },
									{ value: 'Mediation/Facilitated conversation' },
									{ value: 'Mutual separation' },
									{ value: 'New policy created' },
									{ value: 'No action required' },
									{ value: 'Not applicable' },
									{ value: 'Other' },
									{ value: 'Performance Improvement Plan (PIP)' },
									{ value: 'Policy updated' },
									{ value: 'Procedure update' },
									{ value: 'Survey initiated' },
									{ value: 'Suspension' },
									{ value: 'Termination' },
									{ value: 'Training' },
									{ value: 'Training required' },
									{ value: 'Warning' },
								],
							},
						},
						{
							layoutType: 'field',
							layoutOpts: {
								fieldDef: {
									type: 'picklist[]',
									caption: 'Final preventative action',
								},
								picklistItems: [
									{ value: 'Coaching' },
									{ value: 'Employee resignation' },
									{ value: 'Internal audit conducted' },
									{ value: 'Leave of absence' },
									{ value: 'Management coaching' },
									{ value: 'Mediation/Facilitated conversation' },
									{ value: 'Mutual separation' },
									{ value: 'New policy created' },
									{ value: 'No action required' },
									{ value: 'Not applicable' },
									{ value: 'Other' },
									{ value: 'Performance Improvement Plan (PIP)' },
									{ value: 'Policy updated' },
									{ value: 'Procedure update' },
									{ value: 'Survey initiated' },
									{ value: 'Suspension' },
									{ value: 'Termination' },
									{ value: 'Training' },
									{ value: 'Training required' },
									{ value: 'Warning' },
								],
							},
						},
					],
				},
			],
		},
	],
};
