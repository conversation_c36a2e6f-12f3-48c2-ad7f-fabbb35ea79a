module.exports = {
	layoutType: 'form',
	layoutOpts: {
		type: 'custom',
		caption: 'Interview',
		captionPlural: 'Interviews',
		hideOnIntake: false,
	},
	children: [
		{
			layoutType: 'section',
			layoutOpts: {
				caption: 'Interview Details',
				hideOnIntake: false,
			},
			children: [
				{
					layoutType: 'field',
					layoutOpts: {
						hideOnIntake: false,
						fieldDef: {
							type: 'party',
							caption: 'Person interviewed',
						},
					},
				},
				{
					layoutType: 'field',
					layoutOpts: {
						hideOnIntake: false,
						fieldDef: {
							type: 'user',
							caption: 'Primary interviewer',
						},
					},
				},
				{
					layoutType: 'field',
					layoutOpts: {
						hideOnIntake: false,
						fieldDef: {
							type: 'picklist',
							caption: 'Interview Type',
						},
						picklistItems: [
							{ value: 'Email' },
							{ value: 'In person' },
							{ value: 'Phone' },
							{ value: 'Web conference' },
						],
					},
				},
				{
					layoutType: 'field',
					layoutOpts: {
						hideOnIntake: false,
						fieldDef: {
							type: 'radio',
							typeOptions: {
								options: [
									'Yes',
									'No',
								],
							},
							caption: 'Interview recorded',
						},
					},
				},
				{
					layoutType: 'field',
					layoutOpts: {
						hideOnIntake: false,
						fieldDef: {
							type: 'date',
							caption: 'Interview date',
						},
					},
				},
				{
					layoutType: 'field',
					layoutOpts: {
						hideOnIntake: false,
						fieldDef: {
							type: 'textbox',
							caption: 'Individuals present',
						},
					},
				},
				{
					layoutType: 'field',
					layoutOpts: {
						hideOnIntake: false,
						fieldDef: {
							type: 'textbox',
							caption: 'Interview location',
						},
					},
				},
			],
		},
		{
			layoutType: 'section',
			layoutOpts: {
				hideOnIntake: false,
				caption: 'Statements',
			},
			children: [
				{
					layoutType: 'field',
					layoutOpts: {
						hideOnIntake: false,
						fieldDef: {
							type: 'checkbox',
							caption: 'Welcome the employee and introduce yourself',
							typeOptions: {
								subText: 'Explain your role in the employee relations department.',
							},
						},
					},
				},
				{
					layoutType: 'field',
					layoutOpts: {
						hideOnIntake: false,
						fieldDef: {
							type: 'checkbox',
							caption: 'Describe the purpose of the interview',
							typeOptions: {
								subText: 'Emphasize the goal is to gather accurate information.',
							},
						},
					},
				},
				{
					layoutType: 'field',
					layoutOpts: {
						hideOnIntake: false,
						fieldDef: {
							type: 'checkbox',
							caption: 'Assure confidentiality to the extent possible',
							typeOptions: {
								subText: 'Information may be disclosed on a need-to-know basis.',
							},
						},
					},
				},
			],
		},
		{
			layoutType: 'section',
			layoutOpts: {
				hideOnIntake: false,
				caption: 'Notes',
			},
			children: [
				{
					layoutType: 'field',
					layoutOpts: {
						hideOnIntake: false,
						fieldDef: {
							type: 'textarea',
							caption: 'Interview Notes',
							typeOptions: {
								subText: 'Ask open-ended questions.',
							},
						},
					},
				},
				{
					layoutType: 'field',
					layoutOpts: {
						hideOnIntake: false,
						fieldDef: {
							type: 'textarea',
							caption: 'Inconsistencies',
							typeOptions: {
								subText: 'If there are any inconsistencies, ask for clarification.',
							},
						},
					},
				},
				{
					layoutType: 'field',
					layoutOpts: {
						hideOnIntake: false,
						fieldDef: {
							type: 'textarea',
							caption: 'Additional information',
							typeOptions: {
								subText: 'Ask for any additional information important to the case.',
							},
						},
					},
				},
			],
		},
	],
};
