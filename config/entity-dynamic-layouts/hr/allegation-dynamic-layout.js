module.exports = {
	layoutType: 'form',
	layoutOpts: {
		type: 'custom',
		caption: 'Allegation',
		captionPlural: 'Allegations',
		hideOnIntake: false,
	},
	children: [
		{
			layoutType: 'field',
			layoutOpts: {
				hideOnIntake: false,
				fieldDef: {
					type: 'party',
					caption: 'Reported party',
					mandatory: true,
				},
			},
		},
		{
			layoutType: 'field',
			layoutOpts: {
				fieldDef: {
					type: 'picklist',
					caption: 'Allegation type',
				},
				picklistItems: [
					{ value: 'Discrimination' },
					{ value: 'Harassment' },
					{ value: 'Health and Safety' },
					{ value: 'Misconduct' },
				],
			},
		},
		{
			layoutType: 'field',
			layoutOpts: {
				fieldDef: {
					type: 'picklist',
					caption: 'Discrimination type',
				},
				picklistItems: [
					{ value: 'Age' },
					{ value: 'Disability' },
					{ value: 'Gender' },
					{ value: 'Marital status' },
					{ value: 'Military status' },
					{ value: 'National origin' },
					{ value: 'Other' },
					{ value: 'Preganancy' },
					{ value: 'Racial' },
					{ value: 'Religious' },
					{ value: 'Parental status' },
					{ value: 'Sexual orientation' },
					{ value: 'Weight' },
				],
			},
		},
		{
			layoutType: 'field',
			layoutOpts: {
				fieldDef: {
					type: 'picklist',
					caption: 'Harassment type',
				},
				picklistItems: [
					{ value: 'Age' },
					{ value: 'Disability' },
					{ value: 'Gender' },
					{ value: 'Marital status' },
					{ value: 'Bullying' },
					{ value: 'Sexual' },
					{ value: 'Racial' },
					{ value: 'Religious' },
					{ value: 'National origin' },
					{ value: 'Sexual orientation' },
					{ value: 'Cyber' },
				],
			},
		},
		{
			layoutType: 'field',
			layoutOpts: {
				fieldDef: {
					type: 'picklist',
					caption: 'Health and safety type',
				},
				picklistItems: [
					{ value: 'Slip and fall' },
					{ value: 'Unsafe working conditions' },
					{ value: 'Non-compliance with fire safety and evacuation procedures' },
					{ value: 'Improper handling of hazardous materials' },
					{ value: 'Failure to use Personal Protective Equipment (PPE)' },
					{ value: 'Unsafe operation of machinery and equipment' },
					{ value: 'Ignoring ergonomics and workstation safety' },
					{ value: 'Hostile work environment' },
					{ value: 'Substance abuse violation' },
					{ value: 'Environmental hazards' },
				],
			},
		},
		{
			layoutType: 'field',
			layoutOpts: {
				fieldDef: {
					type: 'picklist',
					caption: 'Misconduct',
				},
				picklistItems: [
					{ value: 'Insubordination' },
					{ value: 'Theft' },
					{ value: 'Falsification of records' },
					{ value: 'Workplace violence' },
					{ value: 'Absenteeism and tardiness' },
					{ value: 'Unprofessional conduct' },
					{ value: 'Conflict of interest' },
					{ value: 'Misuse of company resources' },
					{ value: 'Confidentiality breach' },
					{ value: 'Negligence' },
					{ value: 'Retaliation' },
					{ value: 'Damage to property' },
				],
			},
		},
		{
			layoutType: 'field',
			layoutOpts: {
				hideOnIntake: false,
				fieldDef: {
					type: 'picklist',
					caption: 'Case category',
					mandatory: true,
				},
				picklistItems: [
					{ value: 'Discrimination' },
					{ value: 'Harassment' },
					{ value: 'Health and Safety' },
					{ value: 'Misconduct' },
				],
			},
		},
		{
			layoutType: 'field',
			layoutOpts: {
				hideOnIntake: false,
				fieldDef: {
					type: 'picklist',
					caption: 'Case sub-category',
					mandatory: true,
				},
				picklistItems: [
					{ value: 'Age' },
					{ value: 'Disability' },
					{ value: 'Gender' },
					{ value: 'Marital status' },
					{ value: 'Military status' },
					{ value: 'National origin' },
					{ value: 'Other' },
					{ value: 'Pregnancy' },
					{ value: 'Racial' },
					{ value: 'Religious' },
					{ value: 'Parental status' },
					{ value: 'Sexual orientation' },
					{ value: 'Weight' },
					{ value: 'Bullying' },
					{ value: 'Sexual' },
					{ value: 'Cyber' },
					{ value: 'Slip and fall' },
					{ value: 'Unsafe working conditions' },
					{ value: 'Non-compliance with fire safety and evacuation procedures' },
					{ value: 'Improper handling of hazardous materials' },
					{ value: 'Failure to use Personal Protective Equipment (PPE)' },
					{ value: 'Unsafe operation of machinery and equipment' },
					{ value: 'Ignoring ergonomics and workstation safety' },
					{ value: 'Hostile work environment' },
					{ value: 'Substance abuse violation' },
					{ value: 'Environmental hazards' },
					{ value: 'Insubordination' },
					{ value: 'Theft' },
					{ value: 'Falsification of records' },
					{ value: 'Workplace violence' },
					{ value: 'Absenteeism and tardiness' },
					{ value: 'Unprofessional conduct' },
					{ value: 'Conflict of interest' },
					{ value: 'Misuse of company resources' },
					{ value: 'Confidentiality breach' },
					{ value: 'Negligence' },
					{ value: 'Retaliation' },
					{ value: 'Damage to property' },
				],
			},
		},
		{
			layoutType: 'section',
			layoutOpts: {
				caption: 'Additional Allegations',
				hideOnIntake: false,
			},
			children: [
				{
					layoutType: 'field',
					layoutOpts: {
						fieldDef: {
							type: 'textarea',
							caption: 'Allegation description',
						},
						hideOnIntake: false,
					},
				},
				{
					layoutType: 'field',
					layoutOpts: {
						hideOnIntake: false,
						fieldDef: {
							type: 'radio',
							typeOptions: {
								options: [
									'Yes',
									'No',
								],
							},
							caption: 'Substantiated?',
						},
					},
				},
				{
					layoutType: 'field',
					layoutOpts: {
						hideOnIntake: false,
						fieldDef: {
							type: 'picklist',
							caption: 'Corrective action taken',
						},
						picklistItems: [
							{ value: 'Coaching' },
							{ value: 'Demotion / Reassignment' },
							{ value: 'Financial penalties' },
							{ value: 'Mediated conversation' },
							{ value: 'No action' },
							{ value: 'Not applicable' },
							{ value: 'Termination' },
							{ value: 'Training' },
							{ value: 'Vendor terminated' },
							{ value: 'Warning' },
						],
					},
				},
				{
					layoutType: 'field',
					layoutOpts: {
						hideOnIntake: false,
						fieldDef: {
							type: 'textarea',
							caption: 'Comments',
						},
					},
				},
			],
		},
	],
};
