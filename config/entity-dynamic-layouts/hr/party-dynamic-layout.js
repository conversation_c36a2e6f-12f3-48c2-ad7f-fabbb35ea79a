module.exports = {
	layoutType: 'form',
	layoutOpts: {
		type: 'standard',
		entity: {
			base: 'sys',
			name: 'party',
		},
	},
	children: [
		{
			layoutType: 'insertionPoint',
			layoutOpts: {
				name: 'insertion-point-1',
			},
			children: [
				{
					layoutType: 'field',
					layoutOpts: {
						hideOnIntake: false,
						fieldDef: {
							type: 'radio',
							typeOptions: {
								options: [
									'Yes',
									'No',
								],
							},
							caption: 'Reported the case?',
						},
					},
				},
				{
					layoutType: 'section',
					layoutOpts: {
						hideOnIntake: false,
						caption: 'Job Information',
					},
					children: [
						{
							layoutType: 'field',
							layoutOpts: {
								hideOnIntake: false,
								fieldDef: {
									type: 'picklist',
									caption: 'Relationship to the organization',
								},
								picklistItems: [
									{ value: 'Contractor' },
									{ value: 'Customer' },
									{ value: 'Employee' },
									{ value: 'Former employee' },
									{ value: 'Law enforcement' },
									{ value: 'Non-employee' },
									{ value: 'Vendor' },
								],
							},
						},
						{
							layoutType: 'field',
							layoutOpts: {
								hideOnIntake: false,
								fieldDef: {
									type: 'textbox',
									caption: 'Identification number',
								},
							},
						},
						{
							layoutType: 'field',
							layoutOpts: {
								hideOnIntake: false,
								fieldDef: {
									type: 'textbox',
									caption: 'Job title',
								},
							},
						},
						{
							layoutType: 'field',
							layoutOpts: {
								hideOnIntake: false,
								fieldDef: {
									type: 'picklist',
									caption: 'Business unit',
								},
								picklistItems: [
									{ value: 'Business development' },
									{ value: 'Business operations' },
									{ value: 'Communications' },
									{ value: 'Corporate development' },
									{ value: 'Engineering' },
									{ value: 'Marketing' },
									{ value: 'Finance' },
									{ value: 'N/A' },
									{ value: 'Other' },
								],
							},
						},
					],
				},
			],
		},
	],
};
