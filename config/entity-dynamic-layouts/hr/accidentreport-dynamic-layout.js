module.exports = {
	layoutType: 'form',
	layoutOpts: {
		type: 'custom',
		caption: 'Accident Report',
		captionPlural: 'Accident Reports',
		hideOnIntake: false,
	},
	children: [
		{
			layoutType: 'section',
			layoutOpts: {
				caption: 'Accident details',
				hideOnIntake: false,
			},
			children: [
				{
					layoutType: 'field',
					layoutOpts: {
						fieldDef: {
							type: 'picklist',
							caption: 'Accident Type',
						},
						hideOnIntake: false,
						picklistItems: [
							{ value: 'Slips, trips, and falls' },
							{ value: 'Equipment malfunction' },
							{ value: 'Vehicle accidents' },
							{ value: 'Chemical spills' },
							{ value: 'Cuts and lacerations' },
							{ value: 'Burns (thermal, chemical, electrical)' },
							{ value: 'Strains and sprains (due to lifting, overexertion, etc.)' },
							{ value: 'Falling objects' },
							{ value: 'Exposure to harmful substances (inhalation, skin contact, etc.)' },
							{ value: 'Repetitive strain injuries' },
							{ value: 'Crush injuries (due to machinery or heavy objects)' },
							{ value: 'Electric shock' },
							{ value: 'Fire-related accidents' },
							{ value: 'Contact with moving machinery or equipment' },
							{ value: 'Animal bites or stings (if applicable)' },
							{ value: 'Workplace violence' },
							{ value: 'Confined space incidents' },
							{ value: 'Exposure to extreme temperatures (heat stress or hypothermia)' },
							{ value: 'Radiation exposure (if applicable)' },
							{ value: 'Noise-induced hearing loss' },
						],
					},
				},
				{
					layoutType: 'field',
					layoutOpts: {
						fieldDef: {
							type: 'date',
							caption: 'Accident Date',
						},
						hideOnIntake: false,
					},
				},
				{
					layoutType: 'field',
					layoutOpts: {
						fieldDef: {
							type: 'textbox',
							caption: 'Accident Details',
						},
						hideOnIntake: false,
					},
				},
			],
		},
		{
			layoutType: 'section',
			layoutOpts: {
				caption: 'Victim of Accident',
				hideOnIntake: false,
			},
			children: [
				{
					layoutType: 'field',
					layoutOpts: {
						fieldDef: {
							type: 'textbox',
							caption: 'Name',
						},
						hideOnIntake: false,
					},
				},
				{
					layoutType: 'field',
					layoutOpts: {
						fieldDef: {
							type: 'textbox',
							caption: 'Email',
						},
						hideOnIntake: false,
					},
				},
				{
					layoutType: 'field',
					layoutOpts: {
						fieldDef: {
							type: 'phone-number',
							caption: 'Work Phone',
						},
						hideOnIntake: false,
					},
				},
				{
					layoutType: 'field',
					layoutOpts: {
						fieldDef: {
							type: 'picklist',
							caption: 'Status',
						},
						picklistItems: [
							{ value: 'Uninjured' },
							{ value: 'Minor injury (treated on-site)' },
							{ value: 'Minor injury (treated off-site)' },
							{ value: 'Major injury (requiring hospitalization)' },
							{ value: 'Critical condition' },
							{ value: 'Fatality' },
							{ value: 'Recovered and returned to work' },
							{ value: 'Recovered but on modified duty' },
							{ value: 'Temporarily disabled' },
							{ value: 'Permanently disabled' },
							{ value: 'On medical leave' },
							{ value: 'On workers\' compensation' },
							{ value: 'Under investigation' },
							{ value: 'Cleared for full duty' },
							{ value: 'Cleared for light duty' },
							{ value: 'Pending medical evaluation' },
							{ value: 'Pending workers\' compensation approval' },
							{ value: 'Pending legal action' },
							{ value: 'Undergoing rehabilitation' },
							{ value: 'Retired (due to injury)' },
						],
						hideOnIntake: false,
					},
				},
				{
					layoutType: 'field',
					layoutOpts: {
						fieldDef: {
							type: 'textbox',
							caption: 'Job Code',
						},
						hideOnIntake: false,
					},
				},
				{
					layoutType: 'field',
					layoutOpts: {
						fieldDef: {
							type: 'date',
							caption: 'Hire Date',
						},
						hideOnIntake: false,
					},
				},
				{
					layoutType: 'field',
					layoutOpts: {
						fieldDef: {
							type: 'textbox',
							caption: 'Location',
						},
						hideOnIntake: false,
					},
				},
				{
					layoutType: 'field',
					layoutOpts: {
						fieldDef: {
							type: 'textbox',
							caption: 'Employee ID',
						},
						hideOnIntake: false,
					},
				},
				{
					layoutType: 'field',
					layoutOpts: {
						fieldDef: {
							type: 'textbox',
							caption: 'Department ID',
						},
						hideOnIntake: false,
					},
				},
				{
					layoutType: 'field',
					layoutOpts: {
						fieldDef: {
							type: 'textbox',
							caption: 'Supervisor',
						},
						hideOnIntake: false,
					},
				},
			],
		},
	],
};
