module.exports = {
	layoutType: 'form',
	layoutOpts: {
		type: 'standard',
		entity: {
			base: 'sys',
			name: 'case',
		},
	},
	children: [
		{
			layoutType: 'insertionPoint',
			layoutOpts: {
				name: 'insertion-point-1',
			},
			children: [
				{
					layoutType: 'section',
					layoutOpts: {
						caption: 'General',
						hideOnIntake: false,
					},
					children: [
						{
							layoutType: 'field',
							layoutOpts: {
								fieldDef: {
									type: 'picklist',
									caption: 'Case Category',
									mandatory: true,
								},
								hideOnIntake: false,
								picklistItems: [
									{ value: 'Accommodation - Ergonomic / working space' },
									{ value: 'Accommodation - Health / disability' },
									{ value: 'Accommodation - Leave of absence; Bereavement' },
									{ value: 'Accommodation - Leave of absence; Disability - long term' },
									{ value: 'Accommodation - Leave of absence; Disability - short term' },
									{ value: 'Accommodation - Leave of absence; Parental leave' },
									{ value: 'Accommodation - Leave of absence; Other' },
									{ value: 'Accommodation - Pregnancy and parental'},
									{ value: 'Accommodation - Religious' },
									{ value: 'Workplace Safety - Discrimination; Age' },
									{ value: 'Workplace Safety - Discrimination; Disability (physical or mental)' },
									{ value: 'Workplace Safety - Discrimination; Gender' },
									{ value: 'Workplace Safety - Discrimination; Marital status' },
									{ value: 'Workplace Safety - Discrimination; Military status' },
									{ value: 'Workplace Safety - Discrimination; National origin' },
									{ value: 'Workplace Safety - Discrimination; Other' },
									{ value: 'Workplace Safety - Discrimination; Pregnancy' },
									{ value: 'Workplace Safety - Discrimination; Race' },
									{ value: 'Workplace Safety - Discrimination; Religion' },
									{ value: 'Workplace Safety - Discrimination; Sexual orientation' },
									{ value: 'Workplace Safety - Discrimination; Bullying' },
									{ value: 'Workplace Safety - Discrimination; Harassment' },
									{ value: 'Workplace Safety - Discrimination; Drug / alcohol abuse' },
									{ value: 'Workplace Safety - Discrimination; Environmental hazards' },
									{ value: 'Workplace Safety - Discrimination; Health and safety' },
									{ value: 'Workplace Safety - Discrimination; Hostile work environment' },
									{ value: 'Workplace Safety - Discrimination; Misconduct' },
									{ value: 'Workplace Safety - Discrimination; Unsafe working conditions or processes' },
									{ value: 'Question - Benefits' },
									{ value: 'Question - General question / issue' },
									{ value: 'Question - Payroll' },
									{ value: 'Question - Promotion' },
									{ value: 'Question - Staffing' },
									{ value: 'Performance Management and Coaching - Behavior' },
									{ value: 'Performance Management and Coaching - Performance' },
									{ value: 'Performance Management and Coaching - Relationship conflict' },
									{ value: 'Performance Management and Coaching - Supplemental training' },
								],
							},
						},
						{
							layoutType: 'field',
							layoutOpts: {
								fieldDef: {
									type: 'textarea',
									caption: 'Description',
								},
								hideOnIntake: false,
							},
						},
						{
							layoutType: 'field',
							layoutOpts: {
								fieldDef: {
									type: 'picklist',
									caption: 'Priority',
								},
								picklistItems: [
									{ value: 'Low' },
									{ value: 'Medium' },
									{ value: 'High' },
								],
								hideOnIntake: false,
							},
						},
						{
							layoutType: 'field',
							layoutOpts: {
								fieldDef: {
									type: 'picklist[]',
									caption: 'Describe any immediate actions required',
								},
								picklistItems: [
									{ value: 'Contact law enforcement' },
									{ value: 'Drug and alcohol test' },
									{ value: 'Employee suspension' },
									{ value: 'Employee termination' },
									{ value: 'No action required' },
									{ value: 'Notify executive team (CEO, regional VP)' },
									{ value: 'Notify legal' },
									{ value: 'Reassign to other team' },
								],
								hideOnIntake: false,
							},
						},
					],
				},
			],
		},
		{
			layoutType: 'tab',
			layoutOpts: {
				caption: 'Assessment',
			},
			children: [
				{
					layoutType: 'section',
					layoutOpts: {
						caption: 'Initial Risk Factor Review',
						helpText: 'Required for the Workplace Safety case type.',
					},
					children: [
						{
							layoutType: 'field',
							layoutOpts: {
								fieldDef: {
									type: 'radio',
									typeOptions: {
										options: [
											'Yes',
											'No',
											'Undetermined',
										],
									},
									caption: 'Safety and wellbeing of employees at risk?',
								},
							},
						},
						{
							layoutType: 'field',
							layoutOpts: {
								fieldDef: {
									type: 'radio',
									typeOptions: {
										options: [
											'Yes',
											'No',
											'Undetermined',
										],
									},
									caption: 'Potentially significant financial impact?',
								},
							},
						},
						{
							layoutType: 'field',
							layoutOpts: {
								fieldDef: {
									type: 'radio',
									typeOptions: {
										options: [
											'Yes',
											'No',
											'Undetermined',
										],
									},
									caption: 'Any executive leadership members involved?',
								},
							},
						},
						{
							layoutType: 'field',
							layoutOpts: {
								fieldDef: {
									type: 'radio',
									typeOptions: {
										options: [
											'Yes',
											'No',
											'Undetermined',
										],
									},
									caption: 'Does the subject have any prior case history?',
								},
							},
						},
						{
							layoutType: 'field',
							layoutOpts: {
								fieldDef: {
									type: 'radio',
									typeOptions: {
										options: [
											'Yes',
											'No',
											'Undetermined',
										],
									},
									caption: 'Legal risks?',
								},
							},
						},
						{
							layoutType: 'field',
							layoutOpts: {
								fieldDef: {
									type: 'radio',
									typeOptions: {
										options: [
											'Yes',
											'No',
											'Undetermined',
										],
										subText: 'E.g., Police, Media, or Government Agency',
									},
									caption: 'Has this been referred to anyone outside the organization?',
								},
							},
						},
						{
							layoutType: 'field',
							layoutOpts: {
								fieldDef: {
									type: 'textbox',
									caption: 'Additional risk factors',
								},
							},
						},
					],
				},
				{
					layoutType: 'section',
					layoutOpts: {
						caption: 'Initial Case Analysis',
					},
					children: [
						{
							layoutType: 'field',
							layoutOpts: {
								fieldDef: {
									type: 'textarea',
									caption: 'Initial case analysis',
								},
							},
						},
					],
				},
				{
					layoutType: 'section',
					layoutOpts: {
						caption: 'To-Do',
					},
					children: [
						{
							layoutType: 'entity-grid',
							layoutOpts: {
								gridEntity: 'sys/todo',
								caption: 'Action Items',
							},
						},
					],
				},
				{
					layoutType: 'section',
					layoutOpts: {
						caption: 'Medical or Professional Documentation',
						helpText: 'Required for the Accommodation case type.',
					},
					children: [
						{
							layoutType: 'field',
							layoutOpts: {
								fieldDef: {
									type: 'radio',
									typeOptions: {
										options: [
											'Yes',
											'No',
										],
									},
									caption: 'Has documentation been attached to this case?',
								},
							},
						},
						{
							layoutType: 'field',
							layoutOpts: {
								fieldDef: {
									type: 'textbox',
									typeOptions: {
										subText: 'E.g., Medical reports, doctor\'s notes, letters of accommodation',
									},
									caption: 'Type of documentation provided',
								},
							},
						},
						{
							layoutType: 'field',
							layoutOpts: {
								fieldDef: {
									type: 'textbox',
									caption: 'Summary of key points from documentation',
								},
							},
						},
						{
							layoutType: 'field',
							layoutOpts: {
								fieldDef: {
									type: 'date',
									caption: 'Date of documentation',
								},
							},
						},
					],
				},
				{
					layoutType: 'section',
					layoutOpts: {
						caption: 'Job Information',
						helpText: 'Required for the Accommodation case type.',
					},
					children: [
						{
							layoutType: 'field',
							layoutOpts: {
								fieldDef: {
									type: 'textbox',
									caption: 'Essential functions of the employee\'s job',
								},
							},
						},
						{
							layoutType: 'field',
							layoutOpts: {
								fieldDef: {
									type: 'textbox',
									caption: 'What specific accommodation is the requestor requesting?',
								},
							},
						},
						{
							layoutType: 'field',
							layoutOpts: {
								fieldDef: {
									type: 'textbox',
									caption: 'Describe how the request relates to these functions',
								},
							},
						},
					],
				},
				{

					layoutType: 'section',
					layoutOpts: {
						caption: 'Previous Accommodations',
						helpText: 'Required for the Accommodation case type.',
					},
					children: [
						{
							layoutType: 'field',
							layoutOpts: {
								fieldDef: {
									type: 'radio',
									typeOptions: {
										options: [
											'Yes',
											'No',
											'Unknown',
										],
									},
									caption: 'Has the requestor made similar requests in the past?',
								},
							},
						},
						{
							layoutType: 'field',
							layoutOpts: {
								fieldDef: {
									type: 'textbox',
									caption: 'Details of past accommodations provided to employee',
								},
							},
						},
						{
							layoutType: 'field',
							layoutOpts: {
								fieldDef: {
									type: 'textbox',
									caption: 'Describe the effectiveness of previous accommodations',
								},
							},
						},
					],
				},
				{

					layoutType: 'section',
					layoutOpts: {
						caption: 'Initial Assessment',
						helpText: 'Required for the Accommodation case type.',
					},
					children: [
						{
							layoutType: 'field',
							layoutOpts: {
								fieldDef: {
									type: 'textarea',
									caption: 'Preliminary observation on the request',
								},
							},
						},
						{
							layoutType: 'field',
							layoutOpts: {
								fieldDef: {
									type: 'textarea',
									caption: 'Any immediate concerns or considerations',
								},
							},
						},
						{
							layoutType: 'field',
							layoutOpts: {
								fieldDef: {
									type: 'textarea',
									caption: 'Initial thoughts on potential accommodations',
								},
							},
						},
					],
				},
			],
		},
	],
};
