
const fs = require('fs');
const _ = require('lodash');
const path = require('path');

const entityDynamicLayouts = {};

function addLayoutsInFolder(folderPath) {
	const files = fs.readdirSync(folderPath);
	const folderName = path.basename(folderPath);
	_.each(files, (file) => {
		const filePath = path.join(folderPath, file);
		const fileStats = fs.statSync(filePath);
		if (fileStats.isDirectory()) return addLayoutsInFolder(filePath);
		if (/dynamic-layout.js$/.test(file)) {
			if (!entityDynamicLayouts[folderName]) entityDynamicLayouts[folderName] = [];
			// eslint-disable-next-line import/no-dynamic-require
			const layout = require(filePath);
			entityDynamicLayouts[folderName].push(layout);
		}
	});
}

addLayoutsInFolder(__dirname);
module.exports = entityDynamicLayouts;
