const path = require('path');


const numConversionsBeforeConverterRestart = Number(
	process.env.PDF_NUM_CONVERSIONS_BEFORE_CONVERTER_RESTART || '20',
);

if (typeof numConversionsBeforeConverterRestart !== 'number'
	|| Number.isNaN(numConversionsBeforeConverterRestart)) {
	throw new Error('PDF_NUM_CONVERSIONS_BEFORE_CONVERTER_RESTART must be a valid number');
}


const minTemporaryFileLifetimeInSeconds = Number(
	process.env.PDF_MIN_TEMPORARY_FILE_LIFETIME_IN_SECONDS || '600',
);

if (typeof minTemporaryFileLifetimeInSeconds !== 'number'
	|| Number.isNaN(minTemporaryFileLifetimeInSeconds)) {
	throw new Error('PDF_MIN_TEMPORARY_FILE_LIFETIME_IN_SECONDS must be a valid number');
}


const maxTimeAllottedPerConversionInSeconds = Number(
	process.env.PDF_MAX_TIME_ALLOTTED_PER_CONVERSION_IN_SECONDS || '40',
);

if (typeof maxTimeAllottedPerConversionInSeconds !== 'number'
	|| Number.isNaN(maxTimeAllottedPerConversionInSeconds)) {
	throw new Error('PDF_MAX_TIME_ALLOTTED_PER_CONVERSION_IN_SECONDS must be a valid number');
}


const tmpDirPath = path.join(__dirname, '../plugins/pdftron/tmp');


module.exports = {
	numConversionsBeforeConverterRestart,
	minTemporaryFileLifetimeInSeconds,
	maxTimeAllottedPerConversionInSeconds,
	tmpDirPath,
};
