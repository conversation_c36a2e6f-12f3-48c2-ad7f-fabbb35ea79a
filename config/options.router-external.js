/* eslint-disable import/no-dynamic-require */
const fs = require('fs');
const _ = require('lodash');
const options = require('./');

let routes = [
	// Root
	'/external',

	// New case children
	'/external/case/:caseId/note/new',
	'/external/case/:caseId/file/new',
	'/external/case/:caseId/party/new',

	// Case
	'/external/cases',
	'/external/case/:id/edit',
	'/external/case/:id/print',
	'/external/case/:id/:tab/:subcategory',
	'/external/case/:id/:tab',
	'/external/case/:id',

	// Note
	'/external/notes',
	'/external/note/new',
	'/external/note/:id/edit',
	'/external/note/:id',
	'/external/note/:id/new',

	// File
	'/external/files',
	'/external/file/new',
	'/external/file/permission_error',
	'/external/file/:id/edit',
	'/external/file/:id',
	'/external/file/:id/new',

	// Party
	'/external/parties',
	'/external/party/new',
	'/external/party/:id/edit',
	'/external/party/:id',
	'/external/party/:id/new',

	// Profile
	'/external/profile',
	'/external/profile/edit',
	'/external/change-password',

	// Forms
	'/external/form/:formName/:id',
];

let configAppRoutes = {};

// Combine any routes defined in the config with the routes defined in this file
const routerFilePath = `${options.appConfigPath}/config/options.routes-external.js`;
if (fs.existsSync(routerFilePath)) {
	configAppRoutes = require(routerFilePath);
	routes = _.union(routes, configAppRoutes.routes);
}

module.exports = {
	entityMap: _.assign({}, {
		sys_attachment: '/external/file/{id}',
		sys_case: '/external/case/{id}',
		sys_file: '/external/file/{id}',
		sys_shared_link: '/external/case/{caseId}/files/shared',
		sys_note: '/external/note/{id}',
		sys_party: '/external/party/{id}',

		'-/sys/attachment': '/external/file/{id}',
		'-/sys/case': '/external/case/{id}',
		'-/sys/file': '/external/file/{id}',
		'-/sys/shared_link': '/external/case/{caseId}/files/shared',
		'-/sys/note': '/external/note/{id}',
		'-/sys/party': '/external/party/{id}',
	}, configAppRoutes.entityMap),
	routes,
	redirectRoutes: _.assign({
		change_password: 'external/change-password',
	}, configAppRoutes.redirectRoutes),
};
