module.exports = {
	unreusablePasswords: 6,
	initialPassFromComplexity: true,
	denyInactives: process.env.LOGIN_DENY_INACTIVES === 'true'
		|| process.env.LOGIN_DENY_INACTIVES === '1',
	internal: {
		inactiveUnits: process.env.LOGIN_INACTIVE_UNITS || 'days',
		inactivePeriod: process.env.LOGIN_INACTIVE_PERIOD
			? parseInt(process.env.LOGIN_INACTIVE_PERIOD, 10) : 90,
		remindUnits: process.env.LOGIN_REMIND_UNITS_INTERNAL || 'days',
		remindBefore: process.env.LOGIN_REMIND_BEFORE_INTERNAL
			? parseInt(process.env.LOGIN_REMIND_BEFORE_INTERNAL, 10) : 14,
	},
	external: {
		inactiveUnits: process.env.LOGIN_INACTIVE_UNITS_EXTERNAL || 'days',
		inactivePeriod: process.env.LOGIN_INACTIVE_PERIOD_EXTERNAL
			? parseInt(process.env.LOGIN_INACTIVE_PERIOD_EXTERNAL, 10) : 90,
		remindUnits: process.env.LOGIN_REMIND_UNITS_EXTERNAL || 'days',
		remindBefore: process.env.LOGIN_REMIND_BEFORE_EXTERNAL
			? parseInt(process.env.LOGIN_REMIND_BEFORE_EXTERNAL, 10) : 14,
	},
};
