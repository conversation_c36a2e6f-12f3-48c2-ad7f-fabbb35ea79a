module.exports = {
	name: 'file-details',
	entity: {
		base: 'sys',
		name: 'attachment',
	},
	elements: [
		{
			type: 'information-box',
			caption: 'some_files_not_included_for_packet_due_to_large_size',
			informationBoxType: 'warning',
			displayRule: '(isPacket || isCustomPacket) && didSomeFilesExceedMaxSize',
		},
		{
			type: 'information-box',
			caption: 'some_files_for_packet_were_truncated',
			informationBoxType: 'warning',
			displayRule: '(isPacket || isCustomPacket) && wereSomeFilesTruncated',
		},
		{
			field: 'caseId',
			editRule: 'isNew && !isAddingToSpecificCase',
		},
		{
			readOnly: true,
			field: 'createdDate',
			displayRule: 'isNotNew && isActive',
		},
		{
			readOnly: true,
			field: 'createdBy',
			displayRule: 'isNotNew && isActive',
		},
		{
			field: 'kind',
			editRule: 'isNew',
			typeOptions: {
				filter: 'filterOutPacketKindsIfNecessary',
				refreshOnFields: ['caseId'],
			},
		},
		{
			field: 'externalRecord',
			caption: 'allow_reporter_access',
			displayRule: 'canSetExternal',
			clearOnHide: false,
			helpBlock: 'reporter_access_help_block',
		},
		{
			field: 'templateLocale',
			displayRule: 'isGeneratedTemplate',
			editRule: '!hasActiveShare && !isActive',
		},
		{
			field: 'generatedFileType',
			displayRule: 'isGeneratedTemplate',
			editRule: '!hasActiveShare && !isActive',
		},
		{
			field: 'packetId',
			displayRule: 'isPacket && !isActive',
			editRule: '!hasActiveShare && !isActive',
			clearOnHide: false,
		},
		{
			field: 'name',
			caption: 'Packet',
			displayRule: 'isPacket && isActive',
			editRule: 'isNew',
		},
		{
			field: 'templateId',
			displayRule: 'isGeneratedTemplate',
			editRule: '!hasActiveShare && !isActive',
		},
		{
			field: 'useTranslatedText',
			type: 'checkbox',
			displayRule: '(isNew || isDraft) && (isGeneratedTemplate || isPacket || isCustomPacket)',
			caption: 'use_translated_text',
			features: ['intakeTranslation'],
		},
		{
			field: 'description',
		},
		{
			field: 'url',
			type: 'url',
			displayRule: 'isUrl',
		},
		{
			field: 'shouldAddPageNumbers',
			displayRule: 'isCustomPacket || (isPacket && isPacketDenormalized && isActive)',
			editRule: 'isCustomPacket && !isActive',
		},
		{
			field: 'shouldAddTableOfContents',
			displayRule: 'isCustomPacket || (isPacket && isPacketDenormalized && isActive)',
			editRule: 'isCustomPacket && !isActive',
		},
		{
			field: 'caseFilesInclusionKind',
			displayRule: 'isCustomPacket || (isPacket && isActive)',
			editRule: 'isCustomPacket && !isActive',
		},
		{
			type: 'section',
			displayRule: '(isCustomPacket && isManualSelectionOfCaseFilesForCustomPacketRequired) || (isPacket && isActive && wasManualSelectionOfCaseFilesForPacketRequired) || (isPacket && !isActive && isManualSelectionOfCaseFilesForPacketRequired)',
			editRule: 'isCustomPacket && !isActive',
			width: 'col-xs-12 col-sm-12',
			elements: [
				{
					type: 'inline-form',
					config: {
						expandedByDefault: false,
						dataContainerOptions: {
							configName: 'selected-file-for-packet-grid',
							pluginOptions: {
								enableBatchExport: false,
								enableBatchDelete: {
									displayRule: 'isNew',
								},
							},
						},
						enableFilesDefaultRowActions: true,
						parentRules: {
							canCreate: 'canCreateAndDeleteSelectedFilesForPacket',
							canDelete: 'canCreateAndDeleteSelectedFilesForPacket',
							canEdit: 'editingOfSelectedFilesForPacketIsDisabled',
						},
						minimizeOnParentFieldChange: ['caseId'],
						forceSaveParentModel: true,
						entities: [
							{
								formName: 'selected-file-for-packet-inline-form',
								entityName: 'sys/selected_file_for_packet',
								aclRoles: {
									createACLRole: 'agent',
									editACLRole: 'agent',
									deleteACLRole: 'agent',
								},
							},
						],
					},
				},
			],
		},
		{
			field: 'documentGenerationMethod',
			displayRule: 'isCustomPacket || (isPacket && isActive)',
			editRule: 'isCustomPacket && !isActive',
		},
		{
			field: 'bulkTags',
			displayRule: 'isFileUpload',
			caption: 'tags',
			features: ['fileEnhancement'],
		},
		{
			field: 'files',
			displayRule: 'isFileUpload || (isGeneratedTemplate && isNotNew) || (isPacket && isActive) || (isCustomPacket && isActive)',
			editRule: '!hasActiveShare && !isCustomPacket && !isGeneratedTemplate && !isPacket',
		},
		{
			displayRule: 'isCustomPacket || (isPacket && isActive)',
			editRule: 'isCustomPacket && !isActive',
			type: 'section',
			elements: [
				{
					type: 'inline-form',
					config: {
						expandedByDefault: false,

						dataContainerOptions: {
							configName: 'packet-template-grid',
						},

						parentRules: {
							canCreate: 'canModifyCustomPacketTemplates',
							canEdit: 'canModifyCustomPacketTemplates',
							canDelete: 'canModifyCustomPacketTemplates',
						},

						entities: [
							{
								formName: 'packet-template-inline-form',
								entityName: 'sys/packet_template',
								aclRoles: {
									createACLRole: 'agent',
									editACLRole: 'agent',
									deleteACLRole: 'agent',
								},
							},
						],
					},
				},
			],
		},
	],
};
