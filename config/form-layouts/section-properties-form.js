module.exports = {
	name: 'section-properties-form',
	entity: {
		base: 'sys',
		name: 'section_layout_type',
	},
	defaultFieldsLayout: {
		labelOnTop: true,
		width: 'col-sm-12',
	},
	elements: [
		{
			field: 'caption',
			required: true,
			typeOptions: {
				charMaxTextbox: 60,
				placeholder: false,
			},
		},
		{
			type: 'section',
			defaultFieldsLayout: {
				labelOnTop: false,
			},
			elements: [
				{
					field: 'hideOnIntake',
					disableRule: 'hideOnIntakeDisabled',
					labelWidth: 'col-sm-12 col-sm-8',
					helpBlock: 'show_section_on_intake_subtext',
					helpBlockOffset: 'col-sm-offset-0',
					helpBlockWidth: 'col-sm-12',
					displayRule: '!isDataForm',
				},
				{
					field: 'showOnPortal',
					labelWidth: 'col-sm-12 col-sm-8',
					disableRule: 'showOnPortalDisabled',
					displayRule: 'portalEnabledForEntity && !showOnPortalDisabled',
					helpBlock: 'show_section_on_portal_subtext',
					helpBlockOffset: 'col-sm-offset-0',
					helpBlockWidth: 'col-sm-12',
				},
				{
					field: 'showOnHotline',
					labelWidth: 'col-sm-12 col-sm-8',
					disableRule: 'showOnHotlineDisabled',
					displayRule: 'hotlineEnabledForEntity && !isDataForm',
					helpBlock: 'show_section_on_hotline_subtext',
					helpBlockOffset: 'col-sm-offset-0',
					helpBlockWidth: 'col-sm-12',
				},
			],
		},
		{
			field: 'helpText',
			helpBlock: 'help_text_help_block',
		},
	],
};
