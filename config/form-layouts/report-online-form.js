module.exports = {
	name: 'report-online',
	partiallyDynamic: true,
	partiallyDynamicFormOptions: {
		editableInFormBuilder: false,
	},
	entity: {
		base: 'sys',
		name: 'case',
	},
	defaultFieldsLayout: {
		labelWidth: 'col-xs-12 col-sm-3',
		helpBlockOffset: 'col-xs-offset-0 col-sm-offset-3',
	},
	elements: [
		{
			type: 'section',
			caption: 'incident_details',
			displayRule: 'showIncidentDetails',
			elements: [
				{
					type: 'insertionPoint',
					name: 'insertion-point-1',
				},
				{
					type: 'insertionPoint',
					name: 'case-dynamic-tab-contents',
				},
			],
		},
	],
};
