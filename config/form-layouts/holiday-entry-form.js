module.exports = {
	name: 'holiday-entry-form',
	entity: {
		base: 'sys',
		name: 'holiday_entry',
	},
	elements: [
		{
			type: 'section',
			width: 'row',
			elements: [
				{
					type: 'section',
					width: 'col-sm-3',
					defaultFieldsLayout: {
						labelOnTop: true,
					},
					elements: [
						{
							field: 'name',
							width: 'col-sm-12',
						},
					],
				},
				{
					type: 'section',
					width: 'col-sm-2',
					defaultFieldsLayout: {
						labelOnTop: true,
					},
					elements: [
						{
							field: 'dateFrom',
							width: 'col-sm-12',
						},
					],
				},
				{
					type: 'section',
					width: 'col-sm-2',
					defaultFieldsLayout: {
						labelOnTop: true,
					},
					elements: [
						{
							field: 'dateTo',
							width: 'col-sm-12',
						},
					],
				},
			],
		},
	],
};
