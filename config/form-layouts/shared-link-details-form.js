module.exports = {
	name: 'shared-link-details',
	entity: {
		base: 'sys',
		name: 'shared_link_access',
	},
	elements: [
		{
			field: 'recipient',
			caption: 'to',
			labelWidth: 'col-xs-1 col-sm-1',
		},
		{
			field: 'sharedLinkId__expiryDate',
			labelWidth: 'col-xs-1 col-sm-1',
		},
		{
			field: 'sharedLinkId__subject',
			labelWidth: 'col-xs-1 col-sm-1',
		},
		{
			field: 'sharedLinkId__message',
			labelWidth: 'col-xs-1 col-sm-1',
		},
		{
			field: 'sharedLinkId__filesShared',
			downloadUrl: 'downloadUrl',
			hideCaption: true,
			downloadRule: 'canDownloadSharedFile',
			hideCaptionOffset: 'col-xs-offset-0 col-sm-offset-0',
			labelWidth: 'col-xs-1 col-sm-1',
			width: 'col-xs-5 col-sm-5',
		},
	],
};
