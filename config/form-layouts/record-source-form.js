const fs = require('fs');
const path = require('path');

module.exports = {
	name: 'record-source',
	elements: [
		{
			type: 'section',
			with: 'col-xs-12',
			elements: [
				{
					type: 'html-text',
					caption: {
						groupName: 'record-source-view',
						subgroupName: 'modal',
						key: 'subtitle',
					},
				},
			],
		},
		{
			type: 'section',
			defaultFieldsLayout: {
				size: 'large',
			},
			with: 'row',
			elements: [
				{
					type: 'section',
					width: 'col-xs-12 col-sm-6',
					elements: [
						{field: 'recordSource'},
						{field: 'createdBy'},
						{
							field: 'intakeMethod',
							displayRule: 'hasIntakeMethod',
						},
						{
							field: 'externalRecord',
							displayRule: 'hasExternalRecord',
						},
						{
							type: 'section',
							elements: [
								{
									type: 'raw',
									template: fs.readFileSync(
										path.join(__dirname,
											'../../public/templates/record-source/source-job-link-tmpl.dust'),
										'utf8',
									),
									displayRule: 'hasSourceJob',
								},
							],
						},
					],
				},
				{
					type: 'section',
					width: 'col-xs-12 col-sm-6',
					elements: [
						{field: 'createdDate'},
						{field: 'dateSubmitted'},
						{field: 'apiType'},
						{field: 'lastUpdatedDate'},
					],
				},
				{
					type: 'section',
					width: 'col-xs-12',
					caption: {
						groupName: 'record-source-view',
						subgroupName: 'modal',
						key: 'source_data_section_title',
					},
					helpText: {
						groupName: 'record-source-view',
						subgroupName: 'modal',
						key: 'source_data_help_text',
					},
					displayRule: 'hasViewSourceDataRole && hasSourceData',
					elements: [
						{
							type: 'html-text',
							width: 'col-xs-12',
							caption: {
								groupName: 'record-source-view',
								subgroupName: 'modal',
								key: 'source_data_help_text',
							},
						},
						{
							type: 'section',
							width: 'col-xs-12 col-sm-6',
							elements: [
								{ field: 'sourceId' },
							],
						},
						{
							type: 'section',
							width: 'col-xs-12 col-sm-6',
							elements: [
								{
									field: 'sourceTag',
								},
							],
						},
						{
							type: 'section',
							width: 'col-xs-12 col-sm-12',
							elements: [
								{
									field: 'sourceData',
									displayRule: 'hasSourceData',
								},
							],
						},
					],
				},
			],
		},
	],
};
