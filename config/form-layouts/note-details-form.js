module.exports = {
	name: 'note-details',
	entity: {
		base: 'sys',
		name: 'note',
	},
	elements: [
		{
			field: 'caseId',
			editRule: 'isNew && !isAddingToSpecificCase',
		},
		{ field: 'noteType' },
		{
			field: 'externalRecord',
			caption: 'allow_reporter_access',
			displayRule: 'canSetExternal',
			clearOnHide: false,
			helpBlock: 'reporter_access_help_block',
		},
		{
			field: 'createdDate',
			readOnly: true,
			displayRule: 'isNotNew',
		},
		{
			field: 'createdBy',
			readOnly: true,
			displayRule: 'isNotNew',
		},
		{
			field: 'details',
			editRule: 'isUntranslatedNote',
		},
	],
};
