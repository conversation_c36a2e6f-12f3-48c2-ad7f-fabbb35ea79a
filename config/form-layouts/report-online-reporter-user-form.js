const fs = require('fs');
const path = require('path');

module.exports = {
	name: 'report-online-reporter-user',
	entity: {
		base: 'sys',
		name: 'user',
	},
	defaultFieldsLayout: {
		labelWidth: 'col-xs-12 col-sm-3',
		width: 'col-xs-12 col-sm-3',
		helpBlockOffset: 'col-xs-offset-0 col-sm-offset-3',
	},
	elements: [
		{
			field: 'isReturningPortalUser',
			editRule: '!portalUserAuthenticatedDuringSubmission',
			displayRule: '!isPortalUserAuthenticated && (!anonymousPortalUsersEnabled || (portalUserRemainAnonymous || portalUserNotRemainAnonymous))',
		},
		{
			type: 'raw',
			template: fs.readFileSync(
				path.join(__dirname, '../../public-portal/templates/portal/subviews/report-online-returning-user-login-message-tmpl.dust'),
				'utf8',
			),
			displayRule: 'isReturningPortalUser && !isPortalUserAuthenticated',
			className: 'reporter-user-login-message-form-group',
		},
		{
			field: 'portalLoginUsernameEmail',
			displayRule: 'isReturningPortalUser && !portalUserAuthenticatedDuringSubmission && !isPortalUserAuthenticated',
			clearOnHide: false,
		},
		{
			field: 'portalLoginPassword',
			displayRule: 'isReturningPortalUser && !portalUserAuthenticatedDuringSubmission && !isPortalUserAuthenticated',
		},
		{
			type: 'raw',
			template: fs.readFileSync(
				path.join(__dirname, '../../public-portal/templates/portal/subviews/report-online-returning-user-login-tmpl.dust'),
				'utf8',
			),
			displayRule: 'isReturningPortalUser && !portalUserAuthenticatedDuringSubmission && !isPortalUserAuthenticated',
		},
		{
			field: 'createPortalUserAccount',
			displayRule: 'isNotReturningPortalUser && !isPortalUserAuthenticated && !isPortalUpdatesMandatory',
			helpBlock: {
				groupName: 'portal',
				subgroupName: 'report_online',
				key: 'would_you_like_to_receive_updates_sub_text',
			},
		},
		{
			field: 'portalUserProvideEmail',
			displayRule: '(createPortalUserAccount && portalUserRemainAnonymous && anonymousPortalUsersEnabled && !isPortalUserAuthenticated) || (isPortalUpdatesMandatory && isNotReturningPortalUser && !isPortalUserAuthenticated && anonymousPortalUsersEnabled)',
			helpBlock: {
				groupName: 'portal',
				subgroupName: 'report_online',
				key: 'would_you_like_to_confidentially_provide_email_sub_text',
			},
		},
		{
			field: 'portalUserEmail',
			typeOptions: {
				linkWithSystemUser: false,
			},
			displayRule: '(portalUserProvideEmail || (createPortalUserAccount && !anonymousPortalUsersEnabled) || (createPortalUserAccount && portalUserNotRemainAnonymous)) || (isPortalUpdatesMandatory && isNotReturningPortalUser && !anonymousPortalUsersEnabled) && !isPortalUserAuthenticated',
		},
		{
			field: 'portalUserUsername',
			displayRule: 'portalUserNotProvideEmail && !isPortalUserAuthenticated',
		},
		{
			field: 'portalUserNewPassword',
			displayRule: '(portalUserProvideEmail || portalUserNotProvideEmail || (createPortalUserAccount && !anonymousPortalUsersEnabled) || (createPortalUserAccount && portalUserNotRemainAnonymous)) || (isPortalUpdatesMandatory && isNotReturningPortalUser && !anonymousPortalUsersEnabled) && !isPortalUserAuthenticated',
		},
		{
			type: 'raw',
			template: fs.readFileSync(
				path.join(__dirname, '../../public-portal/templates/portal/subviews/report-online-password-validation-tmpl.dust'),
				'utf8',
			),
			displayRule: '(portalUserProvideEmail || portalUserNotProvideEmail || (createPortalUserAccount && !anonymousPortalUsersEnabled) || (createPortalUserAccount && portalUserNotRemainAnonymous)) || (isPortalUpdatesMandatory && isNotReturningPortalUser && !anonymousPortalUsersEnabled) && !isPortalUserAuthenticated',
		},
		{
			field: 'portalUserConfirmedPassword',
			displayRule: '(portalUserProvideEmail || portalUserNotProvideEmail || (createPortalUserAccount && !anonymousPortalUsersEnabled) || (createPortalUserAccount && portalUserNotRemainAnonymous)) || (isPortalUpdatesMandatory && isNotReturningPortalUser && !anonymousPortalUsersEnabled) && !isPortalUserAuthenticated',
		},
		{
			type: 'raw',
			template: fs.readFileSync(
				path.join(__dirname, '../../public-portal/templates/portal/subviews/report-online-password-matcher-tmpl.dust'),
				'utf8',
			),
			displayRule: '(portalUserProvideEmail || portalUserNotProvideEmail || (createPortalUserAccount && !anonymousPortalUsersEnabled) || (createPortalUserAccount && portalUserNotRemainAnonymous)) || (isPortalUpdatesMandatory && isNotReturningPortalUser && !anonymousPortalUsersEnabled) && !isPortalUserAuthenticated',
		},
	],
};
