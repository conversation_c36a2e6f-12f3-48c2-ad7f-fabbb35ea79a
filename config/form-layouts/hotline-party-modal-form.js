module.exports = {
	name: 'hotline-party-modal',
	partiallyDynamic: true,
	partiallyDynamicFormOptions: {
		editableInFormBuilder: false,
	},
	entity: {
		base: 'sys',
		name: 'party',
	},
	defaultFieldsLayout: {
		labelWidth: 'col-xs-12 col-sm-3',
		width: 'col-xs-12 col-sm-6',
		hideCaptionOffset: 'col-xs-offset-0 col-sm-offset-3',
		helpBlockOffset: 'col-xs-offset-0 col-sm-offset-3',
	},
	elements: [
		{
			field: 'partyType',
			size: 'large',
		},
		{
			field: 'firstName',
			size: 'large',
		},
		{
			field: 'lastName',
			size: 'large',
		},
		{
			field: 'middleInitial',
			size: 'large',
		},
		{
			type: 'insertionPoint',
			name: 'insertion-point-1',
			fieldOptions: {
				size: 'large',
			},
		},
		{
			field: 'dateOfBirth',
			size: 'large',
		},
		{
			field: 'address',
			size: 'large',
		},
		{
			field: 'city',
			size: 'large',
		},
		{
			field: 'stateProvince',
			size: 'large',
		},
		{
			field: 'country',
			size: 'large',
		},
		{
			field: 'zipCodePostalCode',
			size: 'large',
		},
		{
			field: 'homePhone',
			size: 'large',
		},
		{
			field: 'workPhone',
			size: 'large',
		},
		{
			field: 'emailAddress',
			typeOptions: {
				linkWithSystemUser: false,
			},
			size: 'large',
		},
		{
			type: 'insertionPoint',
			name: 'insertion-point-2',
			fieldOptions: {
				size: 'large',
			},
		},
	],
};
