module.exports = {
	name: 'share-file',
	entity: {
		base: 'sys',
		name: 'shared_link',
	},
	defaultFieldsLayout: {
		width: 'col-xs-12 col-sm-6',
		labelWidth: 'col-xs-12 col-sm-3',
		helpBlockOffset: 'col-xs-offset-0 col-sm-offset-3',
	},
	elements: [
		{
			field: 'expiryDate',
			helpBlock: 'maximum_validity_up_to_one_year',
		},
		{
			field: 'provideDownloadLink',
			helpBlock: 'file_preview_supported_formats',
		},
		{
			type: 'section',
			elements: [
				{
					field: 'sharedWith',
					caption: 'share_with',
					typeOptions: {
						trailingButton: {
							id: 'file-party-search',
							ariaLabel: 'party_search',
							icon: 'fa fa-address-book-o',
						},
					},
				},
				{
					field: 'subject',
				},
				{
					field: 'standardResponse',
					caption: 'standard_response',
					type: 'picklistApi',
					typeOptions: {
						picklistDependencies: ['locale'],
						picklistName: 'email_templates',
					},
				},
				{
					field: 'message',
					type: 'texteditor',
					width: 'col-xs-12 col-sm-9',
				},
			],
		},
	],
};
