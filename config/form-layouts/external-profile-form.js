const fs = require('fs');
const path = require('path');

module.exports = {
	name: 'external-profile-details',
	entity: {
		base: 'sys',
		name: 'user',
	},
	elements: [
		{ field: 'nick', readOnly: true },
		{ field: 'email', readOnly: true },
		{ field: 'locale' },
		{ field: 'themeId' },
		{
			type: 'raw',
			template: fs.readFileSync(
				path.join(__dirname, '../../public/templates/common/profile-timezone-field-tmpl.dust'),
				'utf8',
			),
		},
	],
};
