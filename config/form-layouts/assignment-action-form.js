module.exports = {
	name: 'assignment-action',
	entity: {
		base: 'sys',
		name: 'assignment_action',
	},
	elements: [
		{
			type: 'section',
			caption: 'triggers',
			elements: [
				{ field: 'triggerType' },
				{ field: 'delay', displayRule: 'isTriggerTypeDelayed' },
			],
		},
		{
			field: 'assignmentField',
		},
		{
			field: 'userType',
		},
		{
			field: 'contextUsers',
			displayRule: 'isTypeContextUser',
			typeOptions: {
				dependentMaxItemField: 'assignmentField',
				maxItemFunction: 'isMultiAssignment',
				entityNameField: 'targetEntity',
				filterType: ['user', 'user[]'],
				filter: 'filterAssignmentField',
				refreshOnFields: ['assignmentField', 'targetEntity'],
			},
		},
		{
			field: 'systemUsers',
			displayRule: 'isTypeSystemUser',
			typeOptions: {
				dependentMaxItemField: 'assignmentField',
				maxItemFunction: 'isMultiAssignment',
			},
		},
	],
};
