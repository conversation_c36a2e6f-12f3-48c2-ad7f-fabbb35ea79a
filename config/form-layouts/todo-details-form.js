module.exports = {
	name: 'todo-details',
	entity: {
		base: 'sys',
		name: 'todo',
	},
	elements: [
		{
			field: 'caseId',
			editRule: 'isNew && !isAddingToSpecificCase',
		},
		{ field: 'todoType' },
		{
			field: 'responsible',
			typeOptions: {
				enableSelfAssign: 'enabled',
			},
		},
		{
			field: 'due',
			typeOptions: {
				datePickerStartDate: '0d',
			},
		}, {
			caption: 'send_email_reminder',
			field: 'emailReminder',
			helpBlock: 'days_before_due_date',
			displayRule: 'isDueDateSet',
		},
		{ field: 'details' },
		{
			field: 'status',
			readOnly: true,
			displayRule: 'isNotNew',
			clearOnHide: false,
		}, {
			field: 'createdBy',
			readOnly: true,
			displayRule: 'isNotNew',
		}, {
			field: 'assignedBy',
			readOnly: true,
			displayRule: 'isNotNew',
		}, {
			field: 'createdDate',
			readOnly: true,
			displayRule: 'isNotNew',
		}, {
			field: 'dateCompleted',
			readOnly: true,
			displayRule: 'isClosed',
		},
		{
			field: 'closedBy',
			readOnly: true,
			displayRule: 'isClosed',
		},
	],
};
