# Form Layouts

Form generation is used to build forms for views based on configuration files rather than writing HTML. Each form configuration file is responsible for generating a single form.



### Location of Form Configs and Loading by the Server

The form configurations are located in the `/config/form-layouts/` folder. They are generally named after the view that uses them. So `case-overview-view.js` has a related form config called `case-overview-form.js`.

When the application is loaded, the `/controllers/index.js` loads all of the form configs from the `form-layouts` folder, pulls them together into an array of objects and sets them to the `appData.formLayouts` property which can then be consumed by the client.



### Client Side Form Config Consumption

When the client is loaded and a view is to be rendered, the view (via the custom base Backbone view) will check if it has a `formConfigName` property set on the view, if so, it's value will be looked up in the appData.formLayouts array to see if a `name` property can be found in any of the form layout objects. If a form layout object is found, it will be processed and used to render HTML components into a placeholder in the view's template.



### Config Structure

The form config structure consists of a JavaScript Object with two properties; `name` and `elements`.

`.name` is the name of the form config and will be used to associate the form config to Backbone views based on the views ti match a form to a config object. In the case below, the loss detail view will have `formConfigName` property with a value of `'loss-detail'`.

The `.elements` property contains an array of objects that describe each element to be auto generated for the view.

For more detailed information on the form configuration properties see the comments in the `form-layout-helper.js` file in the project.

Below is an example of a form configuration:

```
/* global module */

/***************************************************************************************************
*
*		Defines the HTML field components used by the
*		Loss Detail view. This configuration will
*		provide the front-end with what it needs to generate the form
*		for this view.
*
*		Elements will be displayed on the form in the order
*		they appear here.
*/

module.exports = {
	name: 'loss-detail',
	elements: [
		{
			field: 'caseNumber'
		},
		{
			field: 'storeOrFacilityOrDepartmentNumber'
		},
		{
			field: 'lossType'
		},
		{
			field: 'other'
		},
		{
			field: 'upc'
		},
		{
			field: 'itemDescription'
		},
		{
			field: 'itemQuantity'
		},
		{
			field: 'itemDollarAmount'
		},
		{
			field: 'identifiedRisk'
		},
		{
			field: 'avertedDollars'
		},
		{
			field: 'recoveryDollars'
		},
		{
			field: 'valueOrEstimatedLoss'
		},
		{
			field: 'actualLoss'
		},
		{
			field: 'avertedLoss'
		}
	]
};
```

# Partially Dynamic Form layouts
Setting `partiallyDynamic: true` on a form config will populate that form as a "Dynamic Form" and allow users to add fields and other layout elements to it through the form builder. The `partiallyDynamic` flag must also be set on the corresponding entity definition.

To sync your changes run `make sync-dynamic-forms`.

### Configuring Child Entities
**Each child entity can support 1 partiallyDynamic form config.** For example if you wanted to populate the party details form you would configure `config/form-layouts/party-details-form.js` with the following values:

```
module.exports = {
	name: 'party-details',
	caption: 'Party',
	captionPlural: 'Parties',
	entity: {
		base: 'sys',
		name: 'party',
	},
	partiallyDynamic: true,
	elements: [...],
};
```

### Configuring Case Entity
The Case entity will allow each "Details" tab to be populated as `partiallyDynamic`. This will require having the case details tabs setup on the `sys/case` entity definition (see `entities/case/README.md` for details).

For example if you had case details tabs configured like: 
```
caseDetailsTabs: [
	{
		id: 'overview',
		caption: 'overview',
		default: true,
		formConfigName: 'case-overview',
		...
	},
	{
		id: 'test',
		caption: 'test_tab',
		formConfigName: 'case-test',
		...
	},
	...
]
```
You would need to set `partiallyDynamic` flag on each tab's form config (`case-overview` and `case-test`).

### Insertion Points

Insertion points are layout element types where users are able to add dynamic fields from the form-builder. When you add an insertion point to a partiallyDynamic form config, any elements added to it through the form-builder will show up in that location on the form.

Properties:
* `name` unique identifier for the insertion point. The value must be unique across all the form configs for that entity.
* `aboveContextHint` (optional) translation key to communicate the form context above the insertion point to the user.
* `belowContextHint` (optional) translation key to communicate the form context below the insertion point to the user.
* `showOnIntake` (optional) set to `true` to allow users to toggle "Show in Intake" in the form-builder. Default is `false`.

```
{
    type: 'insertionPoint',
    name: 'insertion-point-1',
    aboveContextHint: 'above_context_translation_key',
    belowContextHint: 'below_context_translation_key',
    showOnIntake: true,
},
```

There can be multiple insertion points per form config, but each name has to be unique. The name attribute will act as the unique identifier and once data has been added to the insertion point it can't be changed. Where ever you move the insertion point on the form config, the dynamic fields will move with it.

### Re-using existing Insertion Points

Even though only 1 form per entity can be populated and editable through the Form Builder, you re-use the insertion points from that form on any number of additional forms for that entity. 

For example here we have a party form that is editable through the Form Builder:
```
module.exports = {
    name: 'party-details',
    caption: 'Party',
    captionPlural: 'Parties',
    entity: {
        base: 'sys',
        name: 'party',
    },
    partiallyDynamic: true,
    elements: [{
        type: 'insertionPoint',
        name: 'insertion-point-1',
        aboveContextHint: 'above_context_translation_key',
        belowContextHint: 'below_context_translation_key',
        showOnIntake: true,
    }],
};
```
We can now re-use `insertion-point-1` on a party portal form and set `editableInFormBuilder: false`:
```
module.exports = {
    name: 'report-online-details',
    entity: {
        base: 'sys',
        name: 'case',
    },
    partiallyDynamic: true,
    partiallyDynamicFormOptions: {
        editableInFormBuilder: false,
    },
    elements: [{
        type: 'insertionPoint',
        name: 'insertion-point-1',
    }],
};
```

### Case Insertion Points
`partiallyDynamicIntake` flag is Deprecated since v8.3.0 - [See "Re-using existing Insertion Points" instead.](#re-using-existing-insertion-points)

Setting `partiallyDynamicIntake: true` on the case capture form config will allow insertion points from the case's `partiallyDynamic` forms to be added. 

Add an insertion point with a matching name to an existing insertion point, and the dynamic fields from the exising insertion point will be displayed in that location. We currently don't support adding new insertion points to the case capture.


### Case Tab Data Reserved Insertion Point
An insertion with reserved name `case-dynamic-tab-contents` can be added to the case capture and all dynamic fields from the case's dynamic tabs will populate it. The dynamic fields will display in the same order as the tabs.

```
{
    type: 'insertionPoint',
    name: 'case-dynamic-tab-contents',
},
```

### Display Rules
Display rules can be used to conditionally show form elements. When a display rule evaluates to true, the form element is displayed.

To set a default display rule on a form element, use `defaultDisplayRule`. This default display rule will be applied to the element and all children as long as they don't define their own display rule. To prevent a child element from inheriting the default display rule, use `displayRule: null`.

In the example below, `field 1` will inherit the default display rule `isNew`, whereas `field2` and `field3` will not (since `field2` defines its own display rule and `field3` explicitly states not to inherit the default display rule).

```
module.exports = {
    name: 'my-test-form',
    entity: {
        base: 'sys',
        name: 'test',
    },
    defaultDisplayRule: 'isNew',
    elements: [{
        field: 'field1',
    }, {
        field: 'field2',
        displayRule: '!isNew',
    }, {
        field: 'field3',
        displayRule: null,
    }],
};
```
