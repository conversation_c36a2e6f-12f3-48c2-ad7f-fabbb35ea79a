const fs = require('fs');
const path = require('path');

module.exports = {
	name: 'data-lookup-section-properties-form',
	entity: {
		base: 'sys',
		name: 'data_lookup_section_layout_type',
	},
	defaultFieldsLayout: {
		labelOnTop: true,
		width: 'col-sm-12',
	},
	elements: [
		{
			field: 'caption',
			required: true,
			typeOptions: {
				charMaxTextbox: 60,
				placeholder: false,
			},
		},
		{
			type: 'section',
			width: 'row col-sm-12',
			elements: [
				{
					field: 'mappingMode',
					labelWidth: 'col-sm-12 col-sm-8',
					displayRule: null,
				},
				{
					field: 'iselExpression',
					width: 'col-xs-12',
					helpBlock: 'expression_subtext',
					labelOnTop: true,
					displayRule: 'isExpressionMode',
				},
			],
		},
		{
			type: 'section',
			defaultFieldsLayout: {
				labelOnTop: false,
			},
			elements: [
				{
					field: 'initMapping',
					labelWidth: 'col-xs-12 col-sm-8',
					helpBlock: 'initial_auto_mapping_help',
					helpBlockOffset: 'col-sm-offset-0',
					helpBlockWidth: 'col-sm-12',
					displayRule: 'isSimpleMode',
				},

			],
		},
		{
			field: 'lookupMethod',
		},
		{
			type: 'section',
			displayRule: 'isHttpRequestMethod',
			elements: [
				{
					field: 'endpoint',
					helpBlock: 'endpoint_help',
					required: true,
				},
				{
					field: 'httpMethod',
					required: true,
				},
				{
					field: 'httpBody',
					displayRule: 'isPutPostMethod',
					helpBlock: 'http_body_help',
				},
				{
					field: 'authenticationMethod',
				},
				{
					type: 'section',
					displayRule: 'isBasicAuthentication',
					elements: [
						{
							field: 'username',
						},
						{
							field: 'password',
						},
					],
				},
				{
					type: 'section',
					displayRule: 'isOAuthAuthentication',
					elements: [
						{field: 'grantType'},
						{field: 'accessTokenEndpoint'},
						{field: 'clientId'},
						{field: 'clientSecret'},
						{field: 'clientAuthenticationOption'},
						{field: 'scope'},
					],
				},
				{
					type: 'raw',
					displayRule: 'isOAuthAuthentication || isBasicAuthentication',
					template: fs.readFileSync(
						path.join(__dirname,
							'../../public/templates/settings/forms/custom-forms/btn-test-authentication-tmpl.dust'),
						'utf8',
					),
				},
				{
					field: 'collectionProperty',
				},
			],
		},
		{
			type: 'section',
			defaultFieldsLayout: {
				labelOnTop: false,
			},
			elements: [
				{
					field: 'hideOnIntake',
					disableRule: 'hideOnIntakeDisabled',
					labelWidth: 'col-sm-12 col-sm-8',
					helpBlock: 'show_data_form_section_on_intake_subtext',
					helpBlockOffset: 'col-sm-offset-0',
					helpBlockWidth: 'col-sm-12',
				},
				{
					field: 'showOnHotline',
					labelWidth: 'col-sm-12 col-sm-8',
					disableRule: 'showOnHotlineDisabled',
					displayRule: 'hotlineEnabledForEntity',
					helpBlock: 'show_data_form_section_on_hotline_subtext',
					helpBlockOffset: 'col-sm-offset-0',
					helpBlockWidth: 'col-sm-12',
				},
			],
		},
		{
			field: 'helpText',
			helpBlock: 'help_text_help_block',
		},
	],
};
