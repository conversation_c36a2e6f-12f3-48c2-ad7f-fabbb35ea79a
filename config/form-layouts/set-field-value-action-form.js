const fs = require('fs');
const path = require('path');

module.exports = {
	name: 'set-field-value-action',
	entity: {
		base: 'sys',
		name: 'field_value_action',
	},
	elements: [
		{
			type: 'section',
			caption: 'triggers',
			elements: [
				{ field: 'triggerType' },
				{ field: 'delay', displayRule: 'isTriggerTypeDelayed' },
			],
		},
		{ field: 'fieldName' },
		{
			field: 'isExpression',
			displayRule: 'isFieldNameSelected && (!isExpressionDisabled || isExpression)',
		},
		{
			type: 'raw',
			displayRule: 'isFieldNameSelected && isFieldValue',
			template: fs.readFileSync(
				path.join(__dirname, '../../public/templates/settings/workflow/rule/set-field-value-container-placeholder.dust'),
				'utf8',
			),
		},
		{
			field: 'expression',
			displayRule: 'isExpression',
			editRule: '!isExpressionDisabled',
			helpBlock: 'expression_subtext',
			labelOnTop: false,
		},
	],
};
