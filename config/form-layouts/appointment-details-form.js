const fs = require('fs');
const path = require('path');

module.exports = {
	name: 'appointment-details',
	entity: {
		base: 'sys',
		name: 'appointment',
	},
	elements: [
		{
			field: 'caseId',
			width: 'col-xs-12 col-sm-8 col-xl-7',
			editRule: 'isNew && !isAddingToSpecificCase',
		},
		{
			field: 'invitees',
			width: 'col-xs-12 col-sm-8 col-xl-7',
			disablePeopleSearchRule: '!hasParent',
			typeOptions: {
				trailingButton: {
					id: 'appointment-party-search',
					ariaLabel: 'party_search',
					icon: 'fa fa-address-book-o',
				},
			},
		},
		{
			field: 'subject',
			width: 'col-xs-12 col-sm-8 col-xl-7',
		},
		{
			field: 'location',
			width: 'col-xs-12 col-sm-8 col-xl-7',
		},
		{
			type: 'section',
			width: 'row',
			elements: [
				{
					type: 'section',
					width: 'col-xs-12 col-md-6',
					indent: false,
					elements: [
						{
							field: 'startDate',
							labelWidth: 'col-xs-12 col-sm-2 col-md-4',
							width: 'col-xs-12 col-sm-8 col-md-8',
							editTimeRule: 'isAllDayEvent',
						},
					],
				},
				{
					type: 'section',
					width: 'col-xs-12 col-md-4',
					indent: false,
					elements: [
						{
							field: 'allDayEvent',
							labelWidth: 'col-xs-12 col-sm-2 col-md-5',
							width: 'col-xs-12 col-sm-8 col-md-7',
						},
					],
				},
			],
		},
		{
			type: 'section',
			width: 'row',
			elements: [
				{
					type: 'section',
					width: 'col-xs-12 col-md-6',
					indent: false,
					elements: [
						{
							field: 'endDate',
							labelWidth: 'col-xs-12 col-sm-2 col-md-4',
							width: 'col-xs-12 col-sm-8 col-md-8',
							editTimeRule: 'isAllDayEvent',
						},
					],
				},
				{
					type: 'section',
					width: 'col-xs-12 col-md-4',
					indent: false,
					elements: [
						{
							field: 'duration',
							readOnly: true,
							labelWidth: 'col-xs-12 col-sm-2 col-md-5',
							width: 'col-xs-12 col-sm-8 col-md-7',
						},
					],
				},
			],
		},
		{
			type: 'section',
			width: 'row',
			elements: [
				{
					type: 'section',
					width: 'col-xs-12 col-md-2 col-md-offset-2',
					indent: false,
					elements: [],
				},
				{
					type: 'section',
					width: 'col-xs-12 col-md-4 col-md-offset-2',
					indent: false,
					elements: [
						{
							field: 'standardResponse',
							caption: 'standard_response',
							type: 'picklistApi',
							typeOptions: {
								picklistDependencies: ['locale'],
								picklistName: 'standard_response',
							},
							kind: 'editable',
							labelWidth: 'col-xs-12 col-sm-2 col-md-5',
							width: 'col-xs-12 col-sm-5 col-md-7',
						},
					],
				},
			],
		},
		{
			field: 'description',
			width: 'col-xs-12 col-sm-8 col-xl-7',
		},
		{
			type: 'section',
			width: 'row',
			elements: [
				{
					type: 'section',
					width: 'col-xs-12 col-sm-8 col-xl-7 col-sm-offset-2',
					elements: [
						{
							type: 'raw',
							template: fs.readFileSync(
								path.join(__dirname,
									'../../public/templates/email/email-signature-button.dust'),
								'utf8',
							),
						},
					],
				},
			],
		},
	],
};
