const fs = require('fs');
const path = require('path');

module.exports = {
	name: 'multi-file-upload-modal',
	caption: 'Multi File Upload',
	features: ['fileEnhancement'],
	entity: {
		base: 'sys',
		name: 'attachment',
	},
	defaultFieldsLayout: {
		labelWidth: 'col-xs-12 col-sm-3',
		width: 'col-xs-12 col-sm-9',
	},
	elements: [
		{
			type: 'raw',
			template: fs.readFileSync(
				path.join(__dirname, '../../public/templates/common/multi-file-upload/multi-file-upload-container-tmpl.dust'),
				'utf8',
			),
			displayRule: 'showUploadStep',
		},
		{
			field: 'bulkTags',
			displayRule: '!showUploadStep',
			clearOnHide: false,
			features: ['fileEnhancement'],
			typeOptions: {
				allowCreate: true,
			},
		},
		{
			field: 'description',
			displayRule: '!showUploadStep',
			clearOnHide: false,
			features: ['fileEnhancement'],
		},
	],
};
