/** ************************************************************************************************
 *
 *		Defines the HTML field components used by the
 *		Person Details view. This configuration will
 *		provide the front-end with what it needs to generate the form
 *		for this view.
 *
 *		Elements will be displayed on the form in the order
 *		they appear here.
 */

module.exports = {
	name: 'person-details',
	entity: {
		base: 'sys',
		name: 'person',
	},
	caption: 'Profile',
	captionPlural: 'Profiles',
	partiallyDynamic: true,
	partiallyDynamicFormOptions: {
		initialStatus: 'active',
	},
	elements: [
		{
			type: 'data-lookup-section',
			name: 'form-level-lookup',
			displayRule: 'isFormLevelLookup',
		},
		{ field: 'firstName' },
		{ field: 'lastName' },
		{ field: 'middleInitial'},
		{
			type: 'insertionPoint',
			name: 'insertion-point-1',
			showOnIntake: true,
			aboveContextHint: 'person_insertion_point_1_above_context_hint',
			belowContextHint: 'person_insertion_point_1_below_context_hint',
		},
		{ field: 'emailAddress' },
		{ field: 'address' },
		{ field: 'city' },
		{ field: 'stateProvince' },
		{ field: 'country' },
		{ field: 'zipCodePostalCode' },
		{ field: 'homePhone' },
		{ field: 'workPhone' },
		{ field: 'dateOfBirth' },
		{
			type: 'insertionPoint',
			name: 'insertion-point-2',
			showOnIntake: true,
			aboveContextHint: 'person_insertion_point_2_above_context_hint',
			belowContextHint: 'person_insertion_point_2_below_context_hint',
		},
		{
			readOnly: true,
			field: 'createdDate',
			displayRule: 'isNotNew',
		},
		{
			readOnly: true,
			field: 'createdBy',
			displayRule: 'isNotNew',
		},
		{ field: 'picture' },
	],
};
