const fs = require('fs');
const path = require('path');

module.exports = {
	name: 'workflow-form',
	defaultFieldsLayout: {
		labelOnTop: true,
	},
	entity: {
		base: 'sys',
		name: 'workflow',
	},
	elements: [
		{
			field: 'name',
			hideWhenReadOnly: true,
			typeOptions: {
				charMaxTextbox: 36,
			},
		},
		{
			field: 'description',
			width: 'col-xs-12 col-sm-6 col-xl-7',
		},
		{
			field: 'visibleExternally',
			displayRule: 'isExternalWorkflowFeatureEnabled',
			helpBlock: {
				groupName: 'sys/workflow',
				subgroupName: 'general',
				key: 'visible_externally_help_text',
			},
		},
		{
			type: 'section',
			width: 'row',
			defaultFieldsLayout: {
				width: 'col-xs-12 col-sm-6',
				labelOnTop: true,
			},
			elements: [
				{
					type: 'section',
					width: 'col-xs-12',
					elements: [
						{
							type: 'raw',
							template: fs.readFileSync(
								path.join(__dirname, '../../public/templates/settings/workflow/custom-workflow/workflow-criteria-placeholder-tmpl.dust'),
								'utf8',
							),
						},
					],
				},
			],
		},
	],
};
