const fs = require('fs');
const path = require('path');
const config = require('../../config');

module.exports = {
	name: 'rule-details-form',
	entity: {
		base: 'sys',
		name: 'rule',
	},
	elements: [
		{
			field: 'name',
		},
		{
			field: 'type',
			readOnly: true,
		},
		{
			type: 'section',
			displayRule: 'isRecordEvent',
			elements: [
				{
					field: 'entityId',
					readOnly: true,
				},
				{
					field: 'recordEvent',
					readOnly: true,
				},
			],
		},
		{
			type: 'section',
			displayRule: 'isWorkflowStep',
			elements: [
				{
					field: 'workflowId',
					readOnly: true,
				},
				{
					field: 'transitionId',
				},
			],
		},
		{
			type: 'section',
			displayRule: 'isSchedule',
			elements: [
				{
					field: 'entityId',
					readOnly: true,
				},
				{
					field: 'scheduleField',
				},
				{
					field: 'scheduleDelay',
				},
				{
					field: 'scheduleOccurrence',
				},
			],
		},
		{
			type: 'section',
			displayRule: 'isScheduleEvent',
			elements: [
				{
					field: 'frequency',
					displayRule: 'isScheduleEvent',
				},
				{
					field: 'weekDays',
					displayRule: 'isWeekly',
				},
				{
					field: 'monthDays',
					displayRule: 'isMonthly',
				},
				{
					field: 'triggerTime',
					displayRule: ['isScheduleEvent', 'isNotCalendarQuarterly', 'isNotFiscalQuarterly'],
					typeOptions: {
						helpText: {
							icon: 'fa fa-address-book-o',
							groupName: 'sys/rule',
							subgroupName: 'general',
							key: 'trigger_time_tooltip_text',
							context: {
								systemTimeZone: config.systemTimeZone,
							},
						},
					},
				},
			],
		},
		{
			type: 'raw',
			template: fs.readFileSync(
				path.join(__dirname, '../../public/templates/settings/workflow/custom-workflow/conditions-container-placeholder-tmpl.dust'),
				'utf8',
			),
		},
	],
};
