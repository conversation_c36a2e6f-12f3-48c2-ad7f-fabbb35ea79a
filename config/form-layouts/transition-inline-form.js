const fs = require('fs');
const path = require('path');

module.exports = {
	name: 'transition-inline-form',
	defaultFieldsLayout: {
		labelOnTop: true,
		width: 'col-xs-12 col-sm-12',
	},
	entity: {
		base: 'sys',
		name: 'transition',
	},
	elements: [
		{
			type: 'section',
			width: 'row',
			elements: [
				{
					type: 'section',
					width: 'col-sm-4',
					elements: [
						{field: 'name'},
					],
				},
				{
					type: 'section',
					width: 'col-sm-6',
					elements: [
						{
							readOnlyWidth: 'col-sm-12',
							field: 'description',
						},
					],
				},
			],
		},
		{
			type: 'section',
			width: 'row',
			elements: [
				{
					type: 'section',
					width: 'col-sm-4',
					elements: [
						{field: 'stateFromId'},
					],
				},
				{
					type: 'section',
					width: 'col-sm-4',
					elements: [
						{field: 'stateToId'},
					],
				},
				{
					type: 'section',
					width: 'col-sm-4',
					elements: [
						{field: 'sequence'},
					],
				},
			],
		},
		{
			type: 'section',
			width: 'row',
			elements: [
				{
					type: 'section',
					width: 'col-sm-4',
					elements: [
						{field: 'reasons'},
					],
				},
				{
					type: 'section',
					width: 'col-sm-4',
					elements: [
						{field: 'mandatoryFields'},
					],
				},
			],
		},
		{
			type: 'raw',
			template: fs.readFileSync(
				path.join(__dirname, '../../public/templates/settings/workflow/custom-workflow/conditions-container-placeholder-tmpl.dust'),
				'utf8',
			),
		},
	],
};
