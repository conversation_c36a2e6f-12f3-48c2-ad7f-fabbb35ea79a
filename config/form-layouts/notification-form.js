module.exports = {
	name: 'notification-form',
	entity: {
		base: 'sys',
		name: 'notification',
	},
	elements: [
		{
			type: 'section',
			caption: {
				groupName: 'sys/notification',
				subgroupName: 'general',
				key: 'settings_section',
			},
			helpText: {
				groupName: 'sys/notification',
				subgroupName: 'general',
				key: 'settings_section_help_text',
			},
			elements: [
				{ field: 'disabled' },
				{ field: 'methods' },
				{ field: 'locked' },
				{
					field: 'saveToInbox',
					helpBlock: 'save_to_inbox',
					displayRule: 'isNotExternalNotification',
				},
			],
		},
		{
			type: 'section',
			caption: {
				groupName: 'sys/notification',
				subgroupName: 'general',
				key: 'content_section',
			},
			helpText: {
				groupName: 'sys/notification',
				subgroupName: 'general',
				key: 'content_section_help_text',
			},
			elements: [
				{
					type: 'raw',
					template: '<div class="language-selector-container"></div>',
				},
				{
					field: 'subject',
				},
				{
					field: 'html',
				},
				{
					field: 'text',
				},
			],
		},
	],
};
