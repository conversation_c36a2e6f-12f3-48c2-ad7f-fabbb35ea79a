module.exports = {
	name: 'hotline-intake',
	partiallyDynamic: true,
	partiallyDynamicFormOptions: {
		editableInFormBuilder: false,
	},
	entity: {
		base: 'sys',
		name: 'case',
	},
	defaultFieldsLayout: {
		labelWidth: 'col-xs-12 col-sm-3',
		helpBlockOffset: 'col-xs-offset-0 col-sm-offset-3',
	},
	elements: [
		// About the caller
		{
			type: 'section',
			caption: {
				groupName: 'hotline',
				subgroupName: 'intake',
				key: 'about_the_caller_section_header',
			},
			customClassName: 'hotline-case-fields',
			elements: [
				{
					type: 'information-box',
					caption: {
						groupName: 'hotline',
						subgroupName: 'intake',
						key: 'about_the_caller_information_box_anonymous_reporting_enabled',
					},
					informationBoxType: 'info',
					displayRule: 'isAnonymousReportingEnabled',
				},
				{
					type: 'information-box',
					caption: {
						groupName: 'hotline',
						subgroupName: 'intake',
						key: 'about_the_caller_information_box_anonymous_reporting_disabled',
					},
					informationBoxType: 'info',
					displayRule: '!isAnonymousReportingEnabled',
				},
				{
					field: 'reportedAnonymously',
					caption: 'would_you_like_to_remain_anonymous',
					displayRule: 'isAnonymousReportingEnabled',
					clearOnHide: false,
				},
			],
		},
		{
			type: 'section',
			elements: [
				{
					type: 'raw',
					template: '<div class="reporter-party-container"></div>',
				},
			],
		},
		// Case details
		{
			type: 'section',
			caption: {
				groupName: 'hotline',
				subgroupName: 'intake',
				key: 'case_details_section_header',
			},
			customClassName: 'hotline-case-fields',
			elements: [
				{
					type: 'raw',
					template: '<div class="hotline-case-details-container"></div>',
					className: 'hotline-case-details-form-group',
				},
			],
		},
		// People involved
		{
			type: 'section',
			displayRule: '!displayFormsOnHotlineEnabled',
			caption: {
				groupName: 'sys/party',
				subgroupName: 'general',
				key: 'namePlural',
			},
			elements: [
				{
					type: 'information-box',
					caption: {
						groupName: 'hotline',
						subgroupName: 'intake',
						key: 'people_involved_information_box',
					},
					informationBoxType: 'info',
				},
				{
					type: 'raw',
					template: '<div class="parties-container"></div>',
					className: 'hotline-parties-form-group',
				},
			],
		},
		// Custom form
		{
			type: 'section',
			displayRule: 'displayFormsOnHotlineEnabled',
			elements: [
				{
					type: 'raw',
					template: '<div class="hotline-dynamic-forms"></div>',
					className: 'hotline-dynamic-forms-container',
				},
			],
		},
		// Follow up
		{
			type: 'section',
			caption: {
				groupName: 'hotline',
				subgroupName: 'intake',
				key: 'follow_up_section_header',
			},
			elements: [
				{
					type: 'information-box',
					caption: {
						groupName: 'hotline',
						subgroupName: 'intake',
						key: 'follow_up_information_box',
					},
					informationBoxType: 'info',
				},
				{
					type: 'raw',
					template: '<div class="hotline-user-container"></div>',
				},
			],
		},
	],
};
