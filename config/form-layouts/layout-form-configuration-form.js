const fs = require('fs');
const path = require('path');
const config = require('../../config');

module.exports = {
	name: 'form-configuration-form',
	entity: {
		base: 'sys',
		name: 'form_layout_type',
	},
	defaultFieldsLayout: {
		width: 'col-xs-12 col-sm-8',
		labelWidth: 'col-xs-12 col-sm-4',
	},
	elements: [
		{
			field: 'caption',
			editRule: '!isLocked',
			labelOnTop: true,
		},
		{
			field: 'captionPlural',
			editRule: '!isLocked',
			labelOnTop: true,
		},
		{
			field: 'entityType',
			readOnly: true,
		},
		{
			field: 'hideOnIntake',
			helpBlock: 'hide_on_intake_only_subtext',
			helpBlockOffset: 'col-xs-offset-0',
			helpBlockWidth: 'col-xs-12',
			displayRule: 'canShowOnIntake',
		},
		{
			field: 'showOnPortal',
			helpBlock: 'show_data_form_on_portal_subtext',
			helpBlockOffset: 'col-xs-offset-0',
			helpBlockWidth: 'col-xs-12',
			displayRule: 'isDataFormType && portalEnabled',
		},
		{
			field: 'showOnHotline',
			helpBlock: 'show_data_form_on_hotline_subtext',
			helpBlockOffset: 'col-xs-offset-0',
			helpBlockWidth: 'col-xs-12',
			displayRule: 'isDataFormType && hotlineEnabled',
		},
		{
			field: 'showOnPortal',
			helpBlock: 'show_custom_form_on_portal_subtext',
			helpBlockContext: {
				showOnIntakeFormLimit: config.formsOnIntakeLimit,
			},
			helpBlockOffset: 'col-xs-offset-0',
			helpBlockWidth: 'col-xs-12',
			displayRule: 'isCustomFormType && portalEnabled',
		},
		{
			field: 'showOnHotline',
			helpBlock: 'show_custom_form_on_hotline_subtext',
			helpBlockOffset: 'col-xs-offset-0',
			helpBlockWidth: 'col-xs-12',
			displayRule: 'isCustomFormType && hotlineEnabled && displayFormsOnHotlineEnabled',
		},
		{
			type: 'raw',
			template: '<div id="enable-translation-container"></div>',
			displayRule: '(isCustomFormType || isResponseFormType) && intakeTranslationEnabled',
		},
		{
			field: 'showOnPortal',
			caption: 'add_parties_on_portal',
			helpBlock: 'show_party_form_on_portal_subtext',
			helpBlockOffset: 'col-xs-offset-0',
			helpBlockWidth: 'col-xs-12',
			displayRule: 'isPartyForm && portalEnabled',
		},
		{
			field: 'sequence',
			helpBlock: 'sequence_limit_subtext',
			helpBlockContext: {
				showOnIntakeFormLimit: config.formsOnIntakeLimit,
			},
			helpBlockOffset: 'col-xs-offset-0',
			helpBlockWidth: 'col-xs-12',
			displayRule: '(isShowOnIntake || isShowOnHotline || isShowOnPortal) && !isDataFormType && !isCaseForm',
			mask: {
				allowMinus: false,
			},
		},
		{
			field: 'lookup',
			helpBlock: 'form_lookup_description',
			helpBlockOffset: 'col-xs-offset-0',
			helpBlockWidth: 'col-xs-12',
			displayRule: 'isPartyForm || isCustomFormType || isProfileForm',
		},
		{
			field: 'lookupLabel',
			displayRule: 'isFormLevelLookup',
			labelOnTop: true,
			width: 'col-sm-12',
		},
		{
			type: 'section',
			elements: [
				{
					field: 'mappingMode',
					required: true,
					labelWidth: 'col-sm-12 col-sm-8',
					displayRule: 'isFormLevelLookup',
				},
				{
					field: 'iselExpression',
					width: 'col-xs-12',
					helpBlock: 'expression_subtext',
					labelOnTop: true,
					displayRule: 'isExpressionMode',
				},
			],
		},
		{
			type: 'section',
			defaultFieldsLayout: {
				labelOnTop: false,
			},
			elements: [
				{
					field: 'initMapping',
					labelWidth: 'col-xs-12 col-sm-8',
					helpBlock: 'initial_auto_mapping_help',
					helpBlockOffset: 'col-sm-offset-0',
					helpBlockWidth: 'col-sm-12',
					displayRule: 'isSimpleMode && isFormLevelLookup',
				},
				{
					type: 'raw',
					template: '<div class="col-sm-2"><button id="layout-form-add-mapping-btn" class="btn btn-primary ladda-button">{@resource key="add_mapping"/}</button></div>',
					displayRule: 'isSimpleMode && isFormLevelLookup',
				},
			],
		},
		{
			field: 'lookupMethod',
			displayRule: 'isFormLevelLookup',
			labelOnTop: true,
			width: 'col-sm-12',
		},
		{
			type: 'section',
			displayRule: 'isHttpRequestMethod',
			elements: [
				{
					field: 'endpoint',
					helpBlock: 'endpoint_help',
					required: true,
					labelOnTop: true,
					width: 'col-sm-12',
				},
				{
					field: 'httpMethod',
					required: true,
					labelOnTop: true,
					width: 'col-sm-12',
				},
				{
					field: 'httpBody',
					displayRule: 'isPutPostMethod',
					helpBlock: 'http_body_help',
					labelOnTop: true,
					width: 'col-sm-12',
				},
				{
					field: 'authenticationMethod',
					labelOnTop: true,
					width: 'col-sm-12',
				},
				{
					type: 'section',
					displayRule: 'isBasicAuthentication',
					elements: [
						{
							field: 'username',
							labelOnTop: true,
							width: 'col-sm-12',
						},
						{
							field: 'password',
							labelOnTop: true,
							width: 'col-sm-12',
						},
					],
				},
				{
					type: 'section',
					displayRule: 'isOAuthAuthentication',
					elements: [
						{
							field: 'grantType',
							labelOnTop: true,
							width: 'col-sm-12',
						},
						{
							field: 'accessTokenEndpoint',
							labelOnTop: true,
							width: 'col-sm-12',
						},
						{
							field: 'clientId',
							labelOnTop: true,
							width: 'col-sm-12',
						},
						{
							field: 'clientSecret',
							labelOnTop: true,
							width: 'col-sm-12',
						},
						{
							field: 'clientAuthenticationOption',
							labelOnTop: true,
							width: 'col-sm-12',
						},
						{
							field: 'scope',
							labelOnTop: true,
							width: 'col-sm-12',
						},
					],
				},
				{
					type: 'raw',
					displayRule: 'isOAuthAuthentication || isBasicAuthentication',
					template: fs.readFileSync(
						path.join(__dirname,
							'../../public/templates/settings/forms/custom-forms/btn-test-authentication-tmpl.dust'),
						'utf8',
					),
				},
				{
					field: 'collectionProperty',
					labelOnTop: true,
					width: 'col-sm-12',
				},
			],
		},
	],
};
