const config = require('../../config');

module.exports = {
	name: 'new-form-configuration-form',
	entity: {
		base: 'sys',
		name: 'form_layout_type',
	},
	defaultFieldsLayout: {
		width: 'col-xs-12 col-sm-8',
		labelWidth: 'col-xs-12 col-sm-4',
	},
	elements: [
		{
			field: 'caption',
			editRule: '!isLocked',
		},
		{
			field: 'captionPlural',
			editRule: '!isLocked',
		},
		{
			field: 'entityType',
			typeOptions: {
				hiddenValues: ['standard'],
			},
		},
		{
			field: 'hideOnIntake',
			helpBlock: 'hide_on_intake_only_subtext',
			helpBlockOffset: 'col-xs-offset-0',
			helpBlockWidth: 'col-xs-12',
			displayRule: 'canShowOnIntake',
		},
		{
			field: 'showOnPortal',
			helpBlock: 'show_data_form_on_portal_subtext',
			helpBlockOffset: 'col-xs-offset-0',
			helpBlockWidth: 'col-xs-12',
			displayRule: 'isDataFormType && portalEnabled',
		},
		{
			field: 'showOnHotline',
			helpBlock: 'show_data_form_on_hotline_subtext',
			helpBlockOffset: 'col-xs-offset-0',
			helpBlockWidth: 'col-xs-12',
			displayRule: 'isDataFormType && hotlineEnabled',
		},
		{
			field: 'showOnPortal',
			helpBlock: 'show_custom_form_on_portal_subtext',
			helpBlockContext: {
				showOnIntakeFormLimit: config.formsOnIntakeLimit,
			},
			helpBlockOffset: 'col-xs-offset-0',
			helpBlockWidth: 'col-xs-12',
			displayRule: 'isCustomFormType && portalEnabled',
		},
		{
			field: 'showOnHotline',
			helpBlock: 'show_custom_form_on_hotline_subtext',
			helpBlockOffset: 'col-xs-offset-0',
			helpBlockWidth: 'col-xs-12',
			displayRule: 'isCustomFormType && hotlineEnabled && displayFormsOnHotlineEnabled',
		},
		{
			type: 'raw',
			template: '<div id="enable-translation-container"></div>',
			displayRule: '(isCustomFormType || isResponseFormType) && intakeTranslationEnabled',
		},
	],
};
