module.exports = {
	name: 'packet-details',
	entity: {
		base: 'sys',
		name: 'packet',
	},
	elements: [
		{
			field: 'name',
		},
		{
			field: 'shouldAddPageNumbers',
		},
		{
			field: 'shouldAddTableOfContents',
		},
		{
			field: 'tableOfContentsTemplateKind',
			displayRule: 'shouldAddTableOfContents',
		},
		{
			field: 'tableOfContentsTemplate',
			displayRule: 'isCustomTableOfContentsTemplateKindSelected',
			hideCaption: true,
			hideCaptionOffset: 'col-xs-offset-2 col-sm-offset-2',
		},
		{
			field: 'caseFilesInclusionKind',
		},
		{
			field: 'documentGenerationMethod',
		},
		{
			type: 'inline-form',
			config: {
				expandedByDefault: false,
				dataContainerOptions: {
					configName: 'packet-template-grid',
				},
				parentRules: {
					canCreate: 'canModifyPacketTemplates',
					canEdit: 'canModifyPacketTemplates',
					canDelete: 'canModifyPacketTemplates',
				},
				entities: [
					{
						formName: 'packet-template-inline-form',
						entityName: 'sys/packet_template',
						aclRoles: {
							createACLRole: 'agent',
							editACLRole: 'agent',
							deleteACLRole: 'agent',
						},
					},
				],
			},
		},
	],
};
