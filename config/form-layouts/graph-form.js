module.exports = {
	name: 'graph-form',
	formAttributes: {
		class: 'form-horizontal elements col-md-12 col-lg-12',
	},
	elements: [
		{
			type: 'section',
			width: 'col-sm-6',
			defaultFieldsLayout: {
				labelOnTop: true,
			},
			elements: [
				{
					field: 'graphType',
					width: 'col-sm-12',
				},
				{
					field: 'dateFilter',
					width: 'col-sm-12',
				},
				{
					field: 'startDate',
					width: 'col-sm-12',
				},
				{
					field: 'endDate',
					width: 'col-sm-12',
				},
			],
		},
		{
			type: 'section',
			width: 'col-sm-6',
			defaultFieldsLayout: {
				labelOnTop: true,
			},
			elements: [
				{
					field: 'categories',
					width: 'col-sm-12',
				},
				{
					field: 'sets',
					width: 'col-sm-12',
				},
			],
		},
	],
};
