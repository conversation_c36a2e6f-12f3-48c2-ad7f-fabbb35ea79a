const fs = require('fs');
const path = require('path');

module.exports = {
	name: 'data-import-details',
	entity: {
		base: 'sys',
		name: 'data_import',
	},
	elements: [
		{
			type: 'section',
			caption: {
				groupName: 'sys/data_import',
				subgroupName: 'form',
				key: 'data_configure_import',
			},
			elements: [
				{ field: 'name' },
				{ field: 'importEntity', editRule: '!isEntitySelected'},
				{
					field: 'fileFormat',
					displayRule: '!isAttachmentEntity',
					typeOptions: {
						helpText: {
							icon: 'fa fa-address-book-o',
							groupName: 'sys/data_import',
							subgroupName: 'form',
							key: 'select_file_format_help_text',
						},
					},
				},
				{
					field: 'delimiter',
					displayRule: '!fileTypeEqualsXlsx && !isAttachmentEntity',
				},
				{
					field: 'updateMethod',
					displayRule: 'isScheduleBatchProcess && !isAttachmentEntity',
					typeOptions: {
						filter: 'updateMethodsFilter',
					},
				},
				{
					field: 'distributionList',
					typeOptions: {
						helpText: {
							icon: 'fa fa-address-book-o',
							groupName: 'sys/data_import',
							subgroupName: 'form',
							key: 'distribution_list_help_text',
						},
					},
				},
				{
					field: 'userLookupField',
					displayRule: '!isAttachmentEntity',
				},
				{
					field: 'defaultUserRoleId',
					displayRule: 'isUserImport',
				},
			],
		},
		{
			type: 'section-collapsible',
			caption: {
				groupName: 'sys/data_import',
				subgroupName: 'form',
				key: 'advanced_options',
			},
			typeOptions: {
				helpText: {
					icon: 'fa fa-address-book-o',
					groupName: 'sys/data_import',
					subgroupName: 'form',
					key: 'advanced_options_help_text',
				},
			},
			elements: [
				{
					type: 'section',
					caption: {
						groupName: 'sys/data_import',
						subgroupName: 'form',
						key: 'data_import_overrides',
					},
					elements: [
						{
							field: 'dateFormat',
							displayRule: '!isAttachmentEntity',
							typeOptions: {
								helpText: {
									icon: 'fa fa-address-book-o',
									groupName: 'sys/data_import',
									subgroupName: 'form',
									key: 'select_date_format_help_text',
								},
							},
						},
						{
							field: 'encodingOverride',
							displayRule: '!fileTypeEqualsXlsx',
							typeOptions: {
								helpText: {
									icon: 'fa fa-address-book-o',
									groupName: 'sys/data_import',
									subgroupName: 'form',
									key: 'encoding_override_help_text',
								},
							},
						},
						{
							field: 'phoneNumberOverride',
							displayRule: '!isAttachmentEntity',
							typeOptions: {
								helpText: {
									icon: 'fa fa-address-book-o',
									groupName: 'sys/data_import',
									subgroupName: 'form',
									key: 'phone_number_override_help_text',
								},
							},
						},
						{
							field: 'countryCodeOverride',
							displayRule: '!isAttachmentEntity',
							typeOptions: {
								helpText: {
									icon: 'fa fa-address-book-o',
									groupName: 'sys/data_import',
									subgroupName: 'form',
									key: 'country_code_override_help_text',
								},
							},
						},
					],
				},

			],
		},
		{
			type: 'raw',
			template: '<div id="data-import-mapping"></div>',
		},
		{
			type: 'section',
			caption: {
				groupName: 'sys/data_import',
				subgroupName: 'form',
				key: 'import_schedule',
			},
			elements: [
				{
					type: 'raw',
					template: fs.readFileSync(
						path.join(__dirname,
							'../../public/templates/settings/system/data-imports/data-imports-schedule-notification.dust'),
						'utf8',
					),
				},
			],
		},
	],
};
