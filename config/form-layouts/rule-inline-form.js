const fs = require('fs');
const path = require('path');

module.exports = {
	name: 'rule-inline-form',
	entity: {
		base: 'sys',
		name: 'rule',
	},
	elements: [
		{
			field: 'name',
			width: 'col-xs-12 col-sm-8 col-xl-7',
		},
		{
			field: 'transitionId',
			width: 'col-xs-12 col-sm-8 col-xl-7',
		},
		{
			type: 'raw',
			template: fs.readFileSync(
				path.join(__dirname, '../../public/templates/settings/workflow/custom-workflow/conditions-container-placeholder-tmpl.dust'),
				'utf8',
			),
		},
		{
			type: 'raw',
			template: fs.readFileSync(
				path.join(__dirname, '../../public/templates/settings/workflow/custom-workflow/actions-container-placeholder-tmpl.dust'),
				'utf8',
			),
		},
	],
};
