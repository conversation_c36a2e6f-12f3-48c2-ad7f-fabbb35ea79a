module.exports = {
	name: 'hotline-reporter-party',
	partiallyDynamic: true,
	partiallyDynamicFormOptions: {
		editableInFormBuilder: false,
	},
	entity: {
		base: 'sys',
		name: 'party',
	},
	defaultFieldsLayout: {
		labelWidth: 'col-xs-12 col-sm-3',
		width: 'col-xs-12 col-sm-4',
		helpBlockOffset: 'col-xs-offset-0 col-sm-offset-3',
	},
	elements: [
		{
			field: 'firstName',
			displayRule: 'isReporterNotAnonymous',
		},
		{
			field: 'lastName',
			displayRule: 'isReporterNotAnonymous',
		},
		{
			type: 'section',
			displayRule: 'isReporterNotAnonymous',
			elements: [
				{
					type: 'insertionPoint',
					name: 'insertion-point-1',
				},
				{
					type: 'insertionPoint',
					name: 'insertion-point-2',
				},
			],
		},
	],
};
