/***************************************************************************************************
 *
 *		Defines the HTML field components used by the
 *		Party Details view. This configuration will
 *		provide the front-end with what it needs to generate the form
 *		for this view.
 *
 *		Elements will be displayed on the form in the order
 *		they appear here.
 */

module.exports = {
	name: 'party-details',
	entity: {
		base: 'sys',
		name: 'party',
	},
	caption: 'Party',
	captionPlural: 'Parties',
	partiallyDynamic: true,
	partiallyDynamicFormOptions: {
		allowShowOnIntake: true,
		initialShowOnIntake: true,
		allowShowOnPortal: true,
		initialShowOnPortal: true,
		allowShowOnHotline: true,
		initialShowOnHotline: true,
		initialStatus: 'active',
	},
	elements: [
		{
			field: 'caseId',
			editRule: 'isNew && !isAddingToSpecificCase',
		},
		{
			type: 'data-lookup-section',
			name: 'form-level-lookup',
			displayRule: 'isFormLevelLookup',
		},
		{
			field: 'primaryEntity',
			editRule: 'canEditPrimaryParty',
		},
		{ field: 'partyType' },
		{
			field: 'externalRecord',
			caption: 'allow_reporter_access',
			displayRule: 'canSetExternal',
			clearOnHide: false,
			helpBlock: 'reporter_access_help_block',
		},
		{
			field: 'firstName',
			typeOptions: {
				trailingButton: {
					id: 'party-profile-search',
					ariaLabel: 'people_search',
					icon: 'fa fa-address-book-o',
					displayRule: 'canLinkSearchProfile',
				},
			},
		},
		{ field: 'lastName' },
		{ field: 'middleInitial'},
		{
			type: 'insertionPoint',
			name: 'insertion-point-1',
			showOnIntake: true,
			aboveContextHint: 'party_insertion_point_1_above_context_hint',
			belowContextHint: 'party_insertion_point_1_below_context_hint',
		},
		{ field: 'dateOfBirth' },
		{ field: 'address' },
		{ field: 'city' },
		{ field: 'stateProvince' },
		{ field: 'country' },
		{ field: 'zipCodePostalCode' },
		{ field: 'homePhone' },
		{ field: 'workPhone' },
		{ field: 'emailAddress' },
		{
			type: 'insertionPoint',
			name: 'insertion-point-2',
			showOnIntake: true,
			aboveContextHint: 'party_insertion_point_2_context_hint',
		},
	],
};
