/* global module */
/* eslint-disable max-len */
/***************************************************************************************************
*
*		Defines the HTML field components used by the
*		Note Details view. This configuration will
*		provide the front-end with what it needs to generate the form
*		for this view.
*
*		Elements will be displayed on the form in the order
*		they appear here.
*/

module.exports = {
	entity: {
		base: 'isight',
		name: 'field',
	},
	name: 'field-data-form',
	elements: [
		{
			field: 'caption',
			editRule: 'isNew',
		},
		{
			field: 'field',
			displayRule: '!isNew',
		},
		{
			field: 'type',
			editRule: 'isNew',
		},
		{
			field: 'mandatory',
			displayRule: '!hasDependentMandatoryRule',
		},
		{
			field: 'mandatoryWhen',
			displayRule: '!hasAlwaysMandatoryRule',
		},
		{
			field: 'specialNotes',
		},
		{
			field: 'search',
			editRule: 'isNew',
		},
		{
			field: 'excludeFromSaveAndCopy',
			editRule: 'isNew',
		},
		{
			type: 'section',
			caption: 'type_options',
			displayRule: 'hasType',
			elements: [
				{
					field: 'picklistName',
					displayRule: 'isPicklist || isRadio || isPicklistMultiple || isPicklistApi || isPicklistApiMultiple',
					editRule: 'isNew',
				},
				{
					field: 'picklistDependencies',
					displayRule: 'isPicklist || isRadio || isPicklistMultiple || isPicklistApi || isPicklistApiMultiple',
					editRule: 'isNew',
				},
				{
					field: 'allowNull',
					editRule: 'isNew',
					displayRule: 'isBoolean || isCheckbox || isYesNo',
				},
				{
					field: 'linkWithSystemUser',
					displayRule: 'isEmail && !isDataForm',
					editRule: 'isNew',
				},
				{
					field: 'hideCurrent',
					editRule: 'isNew',
					displayRule: 'isUser || isUserMultiple || isEmail || isEmailMultiple',
				},
				{
					field: 'format',
					editRule: 'isNew',
					displayRule: 'isDate || isDatetime || isTime || isNumber || isMoney || isDecimal || isInteger || isNumberRangePicklist',
				},
				{
					field: 'orientation',
					editRule: 'isNew',
					displayRule: 'isRadio',
				},
				{
					field: 'blankText',
					editRule: 'isNew',
					displayRule: 'isPicklist || isPicklistMultiple || isPicklistApi || isPicklistApiMultiple',
				},
				{
					field: 'ignoreParents',
					editRule: 'isNew',
					displayRule: 'isPicklist || isPicklistMultiple || isPicklistApi || isPicklistApiMultiple || isRadio',
				},
				{
					field: 'mask',
					editRule: 'isNew',
					displayRule: 'isDecimal || isMoney || isNumber',
				},
				{
					field: 'startValue',
					editRule: 'isNew',
					displayRule: 'isNumberRangePicklist',
				},
				{
					field: 'endValue',
					editRule: 'isNew',
					displayRule: 'isNumberRangePicklist',
				},
				{
					field: 'entityNameField',
					editRule: 'isNew',
					displayRule: 'isFieldPicklist || isFieldPicklistMultiple',
				},
				{
					field: 'filterFlag',
					editRule: 'isNew',
					displayRule: 'isFieldPicklist || isFieldPicklistMultiple',
				},
				{
					field: 'filterType',
					editRule: 'isNew',
					displayRule: 'isFieldPicklist || isFieldPicklistMultiple',
				},
				{
					field: 'searchableFields',
					editRule: 'isNew',
					displayRule: 'isFieldPicklist || isFieldPicklistMultiple',
				},
				{
					field: 'precision',
					editRule: 'isNew',
					displayRule: 'isDecimal || isMoney',
				},
				{
					field: 'scale',
					editRule: 'isNew',
					displayRule: 'isDecimal || isMoney',
				},
				{
					field: 'maxItems',
					editRule: 'isNew',
					displayRule: 'isEmailMultiple',
				},
				{
					field: 'disableTimezoneTracking',
					editRule: 'isNew',
					displayRule: 'isDatetime',
				},
				{
					field: 'datePickerStartDate',
					displayRule: 'isDate || isDatetime',
					editRule: 'isNew',
				},
			],
		},
	],
};
