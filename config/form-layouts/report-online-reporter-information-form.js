const fs = require('fs');
const path = require('path');

module.exports = {
	name: 'report-online-reporter-information',
	entity: {
		base: 'sys',
		name: 'case',
	},
	defaultFieldsLayout: {
		labelWidth: 'col-xs-12 col-sm-3',
	},
	elements: [
		{
			type: 'section',
			caption: 'reporter',
			helpText: {
				groupName: 'portal',
				subgroupName: 'report_online',
				key: 'reporter_help_text',
			},
			elements: [
				{
					type: 'raw',
					template: fs.readFileSync(
						path.join(__dirname,
							'../../public-portal/templates/portal/subviews/report-online-reporter-case-container-tmpl.dust'),
						'utf8',
					),
					className: 'reporter-case-form-group',
				},
				{
					type: 'raw',
					template: fs.readFileSync(
						path.join(__dirname,
							'../../public-portal/templates/portal/subviews/report-online-reporter-party-container-tmpl.dust'),
						'utf8',
					),
					className: 'reporter-party-form-group',
				},
				{
					type: 'raw',
					displayRule: 'isTwoWayPortalEnabled',
					template: fs.readFileSync(
						path.join(__dirname,
							'../../public-portal/templates/portal/subviews/report-online-reporter-user-container-tmpl.dust'),
						'utf8',
					),
					className: 'reporter-user-form-group',
				},
			],
		},
	],
};
