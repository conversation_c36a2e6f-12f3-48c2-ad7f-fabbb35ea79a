module.exports = {
	name: 'relationship-type-details',
	entity: {
		base: 'sys',
		name: 'relationship_type',
	},
	elements: [
		{
			field: 'primaryVerb',
			editRule: 'isNew',
		},
		{
			field: 'inverseVerb',
			editRule: 'isNew',
			helpBlock: {
				groupName: 'sys/relationship_type',
				subgroupName: 'form',
				key: 'inverse_verb_help_block',
			},
			typeOptions: {
				helpText: {
					groupName: 'sys/relationship_type',
					subgroupName: 'form',
					key: 'inverse_verb_help_text',
				},
			},
		},
		{
			field: 'rank',
		},
	],
};
