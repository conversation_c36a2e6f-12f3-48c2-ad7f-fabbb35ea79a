var fs = require('fs');
var path = require('path');
module.exports = {
	name: 'profile-details',
	entity: {
		base: 'sys',
		name: 'user',
	},
	elements: [
		{ field: 'email', readOnly: true },
		{ field: 'phoneNumber' },
		{ field: 'userRoleId', readOnly: true },
		{ field: 'locale' },
		{ field: 'themeId' },
		{ field: 'caseCaptureRedirect' },
		{ field: 'gridRowCount'},
		{ field: 'signature' },
		{
			type: 'raw',
			template: fs.readFileSync(
				path.join(__dirname, '../../public/templates/common/profile-create-signature-button.dust'),
				'utf8',
			),
		},
		{
			type: 'raw',
			template: fs.readFileSync(
				path.join(__dirname, '../../public/templates/common/profile-timezone-field-tmpl.dust'),
				'utf8',
			),
		},
	],
};