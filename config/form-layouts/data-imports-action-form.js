const fs = require('fs');
const path = require('path');

module.exports = {
	name: 'data-imports-action',
	entity: {
		base: 'sys',
		name: 'data_import_action',
	},
	elements: [
		{
			type: 'raw',
			template: fs.readFileSync(
				path.join(__dirname,
					'../../public/templates/settings/workflow/rule/data-import-actions-notification-tmpl.dust'),
				'utf8',
			),
		},
		{
			field: 'dataImport',
		},
		{
			field: 'keepSourceFiles',
			typeOptions: {
				helpText: {
					groupName: 'sys/data_import',
					subgroupName: 'form',
					key: 'keep_source_files_text',
				},
			},
		},
		{
			field: 'validateOnly',
			displayRule: '!isAttachmentEntity',
			typeOptions: {
				helpText: {
					groupName: 'sys/data_import',
					subgroupName: 'form',
					key: 'validate_only_help_text',
				},
			},
		},
		{
			field: 'uniqueAttachmentFolderName',
			displayRule: 'isAttachmentEntity',
		},
		{
			field: 'uniqueIdentifierFileName',
			displayRule: '!isAttachmentEntity',

		},
		{
			field: 'uniqueFileName',
			readOnly: true,
			displayRule: '!isAttachmentEntity',
		},
	],
};
