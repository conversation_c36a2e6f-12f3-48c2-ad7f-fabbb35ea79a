const fs = require('fs');
const path = require('path');

module.exports = {
	name: 'case-submission-rule-details-form',
	entity: {
		base: 'sys',
		name: 'rule_entity_validation',
	},
	elements: [
		{
			field: 'name',
		},
		{
			field: 'submissionType',
		},
		{
			type: 'raw',
			template: fs.readFileSync(
				path.join(__dirname, '../../public/templates/settings/workflow/case-submission-rule/criteria-container-placeholder-tmpl.dust'),
				'utf8',
			),
		},
		{
			type: 'raw',
			template: fs.readFileSync(
				path.join(__dirname, '../../public/templates/settings/workflow/case-submission-rule/validation-container-placeholder-tmpl.dust'),
				'utf8',
			),
		},
	],
};
