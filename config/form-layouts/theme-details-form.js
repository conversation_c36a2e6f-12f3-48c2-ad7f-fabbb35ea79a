module.exports = {
	name: 'theme-details',
	entity: {
		base: 'sys',
		name: 'theme',
	},
	elements: [
		{
			field: 'name',
			editRule: 'canEditField',
		},
		{
			field: 'description',
			editRule: 'canEditField',
		},
		{
			field: 'default',
			editRule: 'canToggleDefaultField',
		},
		{
			type: 'section',
			caption: 'logo',
			elements: [
				{
					field: 'logo',
					editRule: 'canEditField',
				},
			],
		},
		{
			type: 'section',
			caption: 'colors',
			elements: [
				{
					field: 'primary',
					editRule: 'canEditField',
					colorTooltip: 'primary_color_tooltip',
				},
				{
					field: 'accent',
					editRule: 'canEditField',
					colorTooltip: 'accent_color_tooltip',
				},
				{
					field: 'complementary1',
					editRule: 'canEditField',
					colorTooltip: 'complementary1_color_tooltip',
				},
				{
					field: 'complementary2',
					editRule: 'canEditField',
					colorTooltip: 'complementary2_color_tooltip',
				},
			],
		},
	],
};
