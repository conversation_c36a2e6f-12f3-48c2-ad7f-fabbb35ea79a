module.exports = {
	name: 'todo-event',
	entity: {
		base: 'sys',
		name: 'todo',
	},
	formAttributes: {
		class: 'form-horizontal elements col-md-12 col-lg-12',
	},
	defaultFieldsLayout: {
		labelWidth: 'col-xs-12 col-sm-3',
		width: 'col-xs-12 col-sm-6',
		helpBlockOffset: 'col-xs-offset-0 col-sm-offset-3',
	},
	elements: [
		{
			field: 'caseId',
			editRule: 'isNew && !isAddingToSpecificCase',
		},
		{
			field: 'todoType',
			editRule: 'canEditThisTodo',
		},
		{
			field: 'responsible',
			editRule: 'canReassignThisTodo',
			typeOptions: {
				enableSelfAssign: 'enabled',
			},
		},
		{
			field: 'due',
			editRule: 'canEditThisTodo',
			typeOptions: {
				datePickerStartDate: '0d',
			},
		},
		{
			caption: 'send_email_reminder',
			field: 'emailReminder',
			editRule: 'canEditThisTodo',
			helpBlock: 'days_before_due_date',
			displayRule: 'isDueDateSet',
		},
		{
			field: 'details',
			editRule: 'canEditThisTodo',
			width: 'col-xs-12 col-sm-9',
		},
	],
};
