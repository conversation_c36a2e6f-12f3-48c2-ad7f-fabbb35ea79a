module.exports = {
	name: 'hotline-case-details',
	partiallyDynamic: true,
	partiallyDynamicFormOptions: {
		editableInFormBuilder: false,
	},
	entity: {
		base: 'sys',
		name: 'case',
	},
	defaultFieldsLayout: {
		labelWidth: 'col-xs-12 col-sm-3',
		helpBlockOffset: 'col-xs-offset-0 col-sm-offset-3',
	},
	elements: [
		{
			type: 'information-box',
			caption: {
				groupName: 'hotline',
				subgroupName: 'intake',
				key: 'case_details_information_box',
			},
			informationBoxType: 'info',
		},
		{
			type: 'insertionPoint',
			name: 'insertion-point-1',
		},
		{
			type: 'insertionPoint',
			name: 'case-dynamic-tab-contents',
		},
	],
};
