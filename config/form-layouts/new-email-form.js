var fs = require('fs');
var path = require('path');
module.exports = {
	name: 'new-email',
	entity: {
		base: 'sys',
		name: 'email',
	},
	elements: [
		{
			field: 'caseId',
			displayRule: '!isReply && !isForward',
			editRule: '!isAddingToSpecificCase',
			clearOnHide: false,
			width: 'col-xs-12 col-sm-8 col-xl-7',
		},
		{
			field: 'recipients',
			caption: 'send_to',
			width: 'col-xs-12 col-sm-8 col-xl-7',
			disablePeopleSearchRule: '!hasParent',
			typeOptions: {
				trailingButton: {
					id: 'recipients-party-search',
					ariaLabel: 'party_search',
					icon: 'fa fa-address-book-o',
				},
			},
		},
		// More / Less
		{
			type: 'section',
			displayRule: 'isMore',
			elements: [
				{
					displayRule: 'isCCEmailEnabled',
					field: 'ccRecipients',
					clearOnHide: false,
					width: 'col-xs-12 col-sm-8 col-xl-7',
					disablePeopleSearchRule: '!hasParent',
					typeOptions: {
						trailingButton: {
							id: 'cc-profile-search',
							ariaLabel: 'party_search',
							icon: 'fa fa-address-book-o',
						},
					},
				},
				{
					displayRule: 'isBCCEmailEnabled',
					field: 'bccRecipients',
					clearOnHide: false,
					width: 'col-xs-12 col-sm-8 col-xl-7',
					disablePeopleSearchRule: '!hasParent',
					typeOptions: {
						trailingButton: {
							id: 'bcc-profile-search',
							ariaLabel: 'party_search',
							icon: 'fa fa-address-book-o',
						},
					},
				},
			],
		},
		{
			type: 'raw',
			displayRule: 'isBCCEmailEnabled || isCCEmailEnabled',
			template: fs.readFileSync(
				path.join(__dirname, '../../public/templates/email/more-expand-link-tmpl.dust'),
				'utf8',
			),
		},
		{
			field: 'subject',
			displayRule: '!isReply',
			clearOnHide: false,
			width: 'col-xs-12 col-sm-8 col-xl-7',
		},
		{
			type: 'section',
			width: 'row',
			elements: [
				{
					type: 'section',
					width: 'col-xs-12 col-md-2 col-md-offset-2',
					elements: [
						{
							field: 'highPriority',
							labelWidth: 'col-xs-12 col-sm-2 col-md-5',
							width: 'col-xs-12 col-sm-5 col-md-7',
						},
					],
				},
				{
					type: 'section',
					width: 'col-xs-12 col-md-4 col-md-offset-2',
					elements: [
						{
							field: 'emailTemplate',
							caption: 'standard_response',
							type: 'picklistApi',
							typeOptions: {
								picklistDependencies: ['locale'],
								picklistName: 'email_templates',
							},
							kind: 'editable',
							labelWidth: 'col-xs-12 col-sm-2 col-md-5',
							width: 'col-xs-12 col-sm-5 col-md-7',
						},
					],
				},
			],
		},
		{
			field: 'body',
			width: 'col-xs-12 col-sm-8 col-xl-7',
		},
		{
			type: 'section',
			width: 'row',
			elements: [
				{
					type: 'section',
					width: 'col-xs-12 col-sm-8 col-xl-7 col-sm-offset-2',
					elements: [
						{
							type: 'raw',
							template: fs.readFileSync(
								path.join(__dirname,
									'../../public/templates/email/email-signature-button.dust'),
								'utf8',
							),
						},
					],
				},
			],
		},
		{
			field: 'attachments',
			hideCaptionOffset: 'col-xs-offset-0 col-sm-offset-2',
			width: 'col-xs-12 col-sm-8 col-xl-7',
		},
	],
};
