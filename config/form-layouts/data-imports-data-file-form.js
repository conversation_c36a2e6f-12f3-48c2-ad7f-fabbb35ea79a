const fs = require('fs');
const path = require('path');
const { dataImportMaxFileSize } = require('../../config/index.js');

module.exports = {
	name: 'data-imports-data-file',
	entity: {
		base: 'sys',
		name: 'data_import',
	},
	elements: [
		{
			type: 'section',
			elements: [
				{
					type: 'raw',
					template: fs.readFileSync(
						path.join(
							__dirname,
							'../../public/templates/settings/system/data-imports/csv-file-validation-description-tmpl.dust',
						),
						'utf-8',
					),
				},
			],
		},
		{
			field: 'file',
			typeOptions: {
				maxAttachmentSize: dataImportMaxFileSize,
			},
		},
	],
};
