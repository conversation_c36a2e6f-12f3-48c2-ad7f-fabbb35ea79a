module.exports = {
	name: 'entity-grid-properties-form',
	entity: {
		base: 'sys',
		name: 'entity_grid_layout_type',
	},
	defaultFieldsLayout: {
		labelOnTop: true,
		width: 'col-sm-12',
	},
	elements: [
		{
			field: 'caption',
			required: true,
			typeOptions: {
				placeholder: false,
			},
		},
		{
			field: 'gridEntity',
			required: true,
			labelWidth: 'col-sm-12 col-sm-8',
			helpBlock: 'entity_grid_entity_subtext',
			helpBlockOffset: 'col-sm-offset-0',
			helpBlockWidth: 'col-sm-12',
			editRule: 'isNew',
		},
		{
			type: 'section',
			defaultFieldsLayout: {
				labelOnTop: false,
			},
			elements: [
				{
					field: 'hideOnIntake',
					disableRule: 'hideOnIntakeDisabled',
					labelWidth: 'col-sm-12 col-sm-8',
					helpBlock: 'entity_grid_show_on_intake_subtext',
					helpBlockOffset: 'col-sm-offset-0',
					helpBlockWidth: 'col-sm-12',
				},
			],
		},
		{
			field: 'helpText',
			helpBlock: 'help_text_help_block',
		},
	],
};
