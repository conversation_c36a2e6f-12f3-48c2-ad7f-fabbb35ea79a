var fs = require('fs');
var path = require('path');
module.exports = {
	name: 'email-rule',
	entity: {
		base: 'sys',
		name: 'emailRule',
	},
	elements: [
		{ field: 'ruleType' },
		{
			field: 'emailFrom',
			helpBlock: 'email_from_rule_help_block',
			displayRule: 'isNotOutgoingEmailFilter',
			editRule: '!isAcceptAllSendersChecked',
			width: 'col-xs-12 col-sm-5',
		},
		{
			field: 'validRecipients',
			helpBlock: 'email_recipient_rule_help_block',
			displayRule: 'isOutgoingEmailFilter',
			width: 'col-xs-12 col-sm-5',
		},
		{
			field: 'acceptAllSenders',
			displayRule: 'isAcceptAllSendersEnabled',
		},
		{
			type: 'raw',
			template: fs.readFileSync(path.join(__dirname, '../../public/templates/settings/system/email-rule/email-rule-all-senders-warning-tmpl.dust'), 'utf8'),
		},
		{
			field: 'emailTo',
			helpBlock: 'email_to_rule_help_block',
			displayRule: 'isNotIncomingEmailFilter && isNotOutgoingEmailFilter',
			width: 'col-xs-12 col-sm-5',
		},
		/****
		* Define fields that will be populated on case creation via incoming email.
		* Note, you must add the fields to email-rule-model-ex.js for this to work.
		*{
		*	comment: 'Case Values',
		*	caption: 'case_values',
		*	type: 'section',
		*	displayRule: 'isTypeEmailToCase',
		*	elements: [
		*		{
		*			comment: 'Category Level 1',
		*			field: 'categoryLevel1',
		*			caption: 'category_level1',
		*			type: 'picklist',
		*			picklistData: 'categories_level1'
		*		}
		*	]
		*}
		****/
	],
};
