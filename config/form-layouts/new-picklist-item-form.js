module.exports = {
	name: 'new-picklist-item',
	elements: [
		{
			field: 'picklistName',
			caption: 'picklist',
			type: 'picklistSelectizeApi',
			typeOptions: {
				itemTranslationKey: {
					groupName: 'picklist_type',
					subgroupName: 'caption',
					key: '{name}',
				},
				picklistName: 'picklist_types',
				labelField: 'translation',
				ignoreParents: true,
				disabledItem: 'isItemLocked',
				secondaryCaption: 'getCaptionForLockedItems',
			},
		},
	],
};
