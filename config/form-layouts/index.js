/* eslint-disable import/no-dynamic-require*/
var fs = require('fs');
var _ = require('lodash');
var path = require('path');
var config = require('../');

var formLayouts = [];

function addLayoutsInFolder (folderPath) {
	var files = fs.readdirSync(folderPath);
	_.each(files, function (file) {
		const filePath = path.join(folderPath, file);
		const fileStats = fs.statSync(filePath);
		if (fileStats.isDirectory()) {
			addLayoutsInFolder(filePath);
		} else if (/form.js$/.test(file)) {
			var layout = require(path.join(folderPath, file));
			_.remove(formLayouts, { name: layout.name });
			formLayouts.push(layout);
		}
	});
}

addLayoutsInFolder(__dirname);
addLayoutsInFolder(path.join(config.appConfigPath, 'config', 'form-layouts'));
addLayoutsInFolder(path.join(__dirname, '../../field-types'));
addLayoutsInFolder(path.join(config.appConfigPath, 'field-types'));

module.exports = formLayouts;