const fs = require('fs');
const path = require('path');

module.exports = {
	name: 'workflow-summary-form',
	defaultFieldsLayout: {
		labelOnTop: true,
	},
	entity: {
		base: 'sys',
		name: 'workflow',
	},
	disableMandatoryFields: true,
	elements: [
		{
			type: 'information-box',
			caption: {
				groupName: 'settings',
				subgroupName: 'custom-workflow',
				key: 'summary_msg',
			},
		},
		{
			field: 'description',
			width: 'col-xs-12 col-sm-6 col-xl-7',
		},
		{
			type: 'section',
			width: 'row',
			defaultFieldsLayout: {
				width: 'col-xs-12 col-sm-6',
				labelOnTop: true,
			},
			elements: [
				{
					type: 'section',
					width: 'col-xs-12',
					elements: [
						{
							type: 'raw',
							template: fs.readFileSync(
								path.join(__dirname, '../../public/templates/settings/workflow/custom-workflow/workflow-criteria-placeholder-tmpl.dust'),
								'utf8',
							),
						},
					],
				},
			],
		},
	],
};
