module.exports = {
	name: 'case-number-form',
	elements: [
		{
			field: 'caseNumberPrefix',
			helpBlock: 'case_number_prefix_help',
		},
		{
			field: 'caseNumberSeparator',
			helpBlock: 'case_number_separator_help',
		},
		{
			field: 'caseNumberMonthFormat',
			helpBlock: 'case_number_month_format_help',
		},
		{
			field: 'caseNumberYearFormat',
			helpBlock: 'case_number_year_format_help',
		},
		{
			field: 'caseNumberSequencePeriod',
			helpBlock: 'case_number_sequence_period_help',
		},
		{
			field: 'caseNumberSequenceMinSize',
			helpBlock: 'case_number_sequence_minsize_help',
		},
		{ field: 'caseNumberISELEnabled' },
		{
			field: 'caseNumberExpression',
			helpBlock: 'case_number_expression_help_block',
			displayRule: 'isCaseNumberUsingISELEnabled',
		},
	],
};
