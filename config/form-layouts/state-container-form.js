module.exports = {
	name: 'state-container-form',
	defaultFieldsLayout: {
		labelOnTop: true,
	},
	entity: {
		base: 'sys',
		name: 'workflow',
	},
	elements: [
		{
			type: 'inline-form',
			config: {
				expandedByDefault: true,
				dataContainerOptions: {
					configName: 'states-step-grid',
				},
				parentRules: {
					canCreate: 'canCreateState',
					canEdit: 'canEditState',
					canDelete: 'canDeleteState',
				},
				entities: [
					{
						formName: 'state-inline-form',
						entityName: 'sys/state',
						aclRoles: {
							createACLRole: 'agent',
							editACLRole: 'agent',
							deleteACLRole: 'agent',
						},
					}],
			},
		},
	],
};
