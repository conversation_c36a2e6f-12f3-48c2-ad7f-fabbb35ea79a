module.exports = {
	name: 'link-entities-modal-form',
	entity: {
		base: 'sys',
		name: 'link',
	},
	caption: 'Record Link',
	captionPlural: 'Record Links',
	defaultFieldsLayout: {
		labelWidth: 'col-xs-12 col-sm-3',
		width: 'col-xs-12 col-sm-6',
	},
	elements: [
		{
			field: 'entity1Id',
			displayRule: '!isNew && !isViewingLinkedRecord',
			readOnly: true,
		},
		{
			field: 'entity2Id',
			displayRule: '!isNew && isViewingLinkedRecord',
			readOnly: true,
		},
		{
			field: 'relationshipTypeId',
		},
		{
			field: 'entity2Id',
			displayRule: '!isNew && !isViewingLinkedRecord',
			readOnly: true,
		},
		{
			field: 'entity1Id',
			displayRule: '!isNew && isViewingLinkedRecord',
			readOnly: true,
		},
	],
};
