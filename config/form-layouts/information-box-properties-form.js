module.exports = {
	name: 'information-box-properties-form',
	entity: {
		base: 'sys',
		name: 'information_box_layout_type',
	},
	defaultFieldsLayout: {
		labelOnTop: true,
		width: 'col-sm-12',
	},
	elements: [
		{
			field: 'caption',
			required: true,
		},
		{
			type: 'section',
			defaultFieldsLayout: {
				labelOnTop: false,
			},
			elements: [
				{
					field: 'hideOnIntake',
					disableRule: 'hideOnIntakeDisabled',
					labelWidth: 'col-sm-12 col-sm-8',
					helpBlock: 'show_on_intake_subtext',
					helpBlockOffset: 'col-sm-offset-0',
					helpBlockWidth: 'col-sm-12',
				},
				{
					field: 'showOnPortal',
					labelWidth: 'col-sm-12 col-sm-8',
					disableRule: 'showOnPortalDisabled',
					displayRule: 'portalEnabledForEntity && !showOnPortalDisabled',
					helpBlock: 'show_on_portal_subtext',
					helpBlockOffset: 'col-sm-offset-0',
					helpBlockWidth: 'col-sm-12',
				},
				{
					field: 'showOnHotline',
					labelWidth: 'col-sm-12 col-sm-8',
					disableRule: 'showOnHotlineDisabled',
					displayRule: 'hotlineEnabledForEntity',
					helpBlock: 'show_on_hotline_subtext',
					helpBlockOffset: 'col-sm-offset-0',
					helpBlockWidth: 'col-sm-12',
				},
			],
		},
		{
			field: 'informationBoxType',
			required: true,
		},
	],
};
