/* HACK:
*  This layout gets injected when rendering `caseId` on layouts of entDefs of type "custom"
* see usage of "injectDynamicEntityDataDetailsLayout" in layout-elements/section/component.js
*/
module.exports = {
	name: 'dynamic-entity-data-details-form',
	elements: [
		// It is imperative that caseId remains represented in this layout as injecting the layout
		// is based on the presence of caseId
		{ field: 'caseId' },
		{
			field: 'externalRecord',
			caption: 'allow_reporter_access',
			editRule: 'canSetExternal',
			displayRule: 'canSetExternal',
			clearOnHide: false,
			helpBlock: 'reporter_access_help_block',
		},
	],
};
