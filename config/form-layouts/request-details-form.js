module.exports = {
	name: 'request-details-form',
	formAttributes: {
		class: 'form-horizontal elements col-md-12 col-lg-12',
	},
	entity: {
		base: 'sys',
		name: 'request',
	},
	elements: [
		{
			field: 'caseId',
			editRule: 'isNew && !isAddingToSpecificCase',
		},
		{
			field: 'sendTo',
			helpBlock: 'send_to_help_block',
			displayRule: 'isNew || isDraft',
			typeOptions: {
				searchCaption: 'send_to_search_caption',
				trailingButton: {
					id: 'recipients-party-search',
					ariaLabel: 'party_search',
					icon: 'fa fa-address-book-o',
				},
			},
		},
		{
			field: 'subject',
			width: 'col-xs-12 col-sm-8',
			editRule: 'isNew || isResend',
		},
		{
			field: 'recipient',
			displayRule: '!isNew && !isDraft && !isResend',
			readOnly: true,
		},
		{
			field: 'recipient',
			caption: {
				groupName: 'sys/request',
				subgroupName: 'fields',
				key: 'sendTo',
			},
			displayRule: '!isNew && !isDraft && isResend',
			readOnly: true,
		},
		{
			field: 'dueDate',
			helpBlock: 'due_date_help_block',
			editRule: 'isNew || isResend',
		},
		{
			field: 'lastSentDate',
			displayRule: '!isNew && !isDraft && !isResend',
			readOnly: true,
		},
		{
			field: 'timesSent',
			displayRule: '!isNew && !isDraft && !isResend',
			readOnly: true,
		},
		{
			field: 'emailReminder',
			editRule: 'isNew || isSent || isResend',
			helpBlock: 'email_reminder_help_block',
		},
		{
			field: 'createdBy',
			displayRule: '!isNew && !isDraft && !isResend',
			readOnly: true,
		},
		{
			field: 'createdDate',
			displayRule: '!isNew && !isDraft && !isResend',
			readOnly: true,
		},
		{
			field: 'message',
			editRule: 'isNew || isDraft || isResend',
		},
	],
};
