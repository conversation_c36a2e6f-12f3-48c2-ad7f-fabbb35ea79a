module.exports = {
	name: 'notification-action-details',
	entity: {
		base: 'sys',
		name: 'notification_action',
	},
	elements: [
		{
			type: 'section',
			caption: 'triggers',
			elements: [
				{
					field: 'triggerType',
					typeOptions: {
						filter: 'checkRuleType',
					},
				},
				{ field: 'delay', displayRule: 'isTriggerTypeDelayed' },
				{ field: 'method' },
			],
		},
		{
			type: 'section',
			caption: 'distribution',
			elements: [
				{
					field: 'contextUsers',
					displayRule: '!isUnassignedIncomingMail && !isRuleTypeScheduleProcess',
				},
				{
					field: 'partyType',
					displayRule: 'isRecordTypeCase',
				},
				{
					field: 'systemUserRoles',
				},
				{
					field: 'systemUsers',
				},
				{
					field: 'systemTeams',
				},
				{
					field: 'contextEmails',
					displayRule: 'isMethodEmail && !(isRecordTypeEmail || isRecordTypeAppointment ||isRecordTypeNote || isRecordTypeFile || isRuleTypeScheduleProcess)',
				},
				{
					field: 'otherAddresses',
					width: 'col-xs-12 col-sm-3',
					displayRule: 'isMethodEmail',
				},
				{
					field: 'otherPhoneNumbers',
					displayRule: 'isMethodPhone || isMethodSMS',
				},
			],
		},
		{
			comment: 'Content',
			caption: 'content',
			type: 'section',
			elements: [
				{
					field: 'highPriority',
					displayRule: 'isMethodEmail',
				},
				{
					field: 'subject',
					displayRule: 'isMethodEmail',
				},
				{
					field: 'template',
					displayRule: 'isMethodEmail',
					helpBlock: 'notification_content_warning',
				},
				{
					field: 'templateForSMS',
					displayRule: 'isMethodSMS',
					helpBlock: 'notification_content_warning',
				},
				{
					field: 'templateForPhone',
					displayRule: 'isMethodPhone',
					helpBlock: 'notification_content_warning',
				},
				{
					field: 'fields',
					displayRule: '!isRuleTypeScheduleProcess',
				},
			],
		},
	],
};
