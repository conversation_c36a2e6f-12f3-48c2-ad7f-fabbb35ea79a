module.exports = {
	name: 'automated-todo-action',
	entity: {
		base: 'sys',
		name: 'automated_todo_action',
	},
	elements: [
		{
			type: 'section',
			caption: 'triggers',
			elements: [
				{ field: 'triggerType' },
				{ field: 'delay', displayRule: 'isTriggerTypeDelayed' },
			],
		},
		{
			type: 'section',
			caption: 'assignment',
			elements: [
				{ field: 'responsibleUserType' },
				{
					field: 'contextUser',
					displayRule: 'isResponsibleUserTypeContext',
				},
				{
					field: 'systemUser',
					displayRule: 'isResponsibleUserTypeSystem',
				},
			],
		},
		{
			type: 'section',
			caption: 'content',
			elements: [
				{
					field: 'todoType',
				},
				{
					field: 'details',
				},
				{
					field: 'setDueDate',
				},
				{
					field: 'numberOfDays',
					displayRule: 'isSetDueDate',
				},
				{
					field: 'dateType',
					displayRule: 'isSetDueDate',
				},
				{
					field: 'emailReminder',
					helpBlock: 'days_before_due_date',
					displayRule: 'isSetDueDate',
				},
			],
		},
	],
};
