module.exports = {
	name: 'external-file-details',
	entity: {
		base: 'sys',
		name: 'attachment',
	},
	elements: [
		{
			field: 'caseId',
			editRule: 'isNew && !isAddingToSpecificCase',
		},
		{
			field: 'createdDate',
			readOnly: true,
			displayRule: 'isNotNew && isActive',
		},
		{
			field: 'description',
		},
		{
			field: 'url',
			type: 'url',
			displayRule: 'isUrl',
		},
		{
			field: 'files',
			displayRule: 'isFileUpload || (isGeneratedTemplate && isNotNew) || (isPacket && isActive) || (isCustomPacket && isActive)',
			editRule: '!hasActiveShare && !isCustomPacket && !isGeneratedTemplate && !isPacket',
		},
	],
};
