module.exports = {
	name: 'sso-options',
	elements: [
		{
			type: 'section',
			caption: 'single_sign_on',
			elements: [
				{ field: 'ssoEnabled' },
				{ field: 'ssoProtocol' },
				{
					field: 'ssoAutoUserProvisioningEnabled',
					caption: 'sso_auto_user_provisioning',
					helpBlock: 'sso_auto_user_provisioning_help',
				},
				{
					field: 'ssoAutoUserProvisionUserRoleFallback',
					displayRule: 'isSSOAutoUserProvisionEnabled',
					helpBlock: 'sso_auto_user_provisioning_userrole_fallback_help',
				},
			],
		},
		{
			type: 'section',
			caption: 'identity_provider',
			elements: [
				{
					field: 'ssoIdPUrl',
					typeOptions: {
						trailingButton: {
							id: 'upload-metadata-url',
							ariaLabel: 'upload_metadata',
							tooltip: 'upload_metadata',
							icon: 'fa fa-upload',
							disableRule: 'isIdPUrlEmpty',
						},
					},
				},
				{ field: 'ssoCert' },
			],
		},
		{
			type: 'section',
			caption: 'service_provider',
			elements: [
				{
					field: 'ssoIdentifierMapping',
					helpBlock: 'sso_identifier_mapping_help',
				},
				{
					field: 'ssoIdentifierDataMapping',
					helpBlock: 'sso_identifier_data_mapping_help',
				},
				{
					field: 'ssoIdentifierFormat',
					helpBlock: 'sso_identifier_format_help',
				},
				{
					field: 'ssoWantAssertionsSigned',
					helpBlock: 'sso_want_assertions_signed_context_help',
				},
				{
					field: 'ssoWantAuthnResponseSigned',
					helpBlock: 'sso_want_authn_response_signed_context_help',
				},
				{
					field: 'ssoEnableRequestedAuthnContext',
					helpBlock: 'request_authn_context_help',
				},
				{
					field: 'ssoAuthnContext',
					displayRule: 'isSSOAuthnContextEnabled',
				},
				{
					field: 'ssoSPEntityId',
					readOnly: true,
					helpBlock: 'sso_sp_entity_id_help',
				},
				{
					field: 'ssoUrl',
					readOnly: true,
					helpBlock: 'sso_sp_consumer_url_help',
				},
			],
		},
	],
};
