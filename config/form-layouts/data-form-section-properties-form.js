module.exports = {
	name: 'data-form-section-properties-form',
	entity: {
		base: 'sys',
		name: 'data_form_section_layout_type',
	},
	defaultFieldsLayout: {
		labelOnTop: true,
		width: 'col-sm-12',
	},
	elements: [
		{
			field: 'caption',
			required: true,
			typeOptions: {
				charMaxTextbox: 60,
				placeholder: false,
			},
		},
		{
			field: 'dataForm',
			required: true,
			editRule: 'isNew',
		},
		{
			type: 'section',
			defaultFieldsLayout: {
				labelOnTop: false,
			},
			elements: [
				{
					field: 'hideOnIntake',
					disableRule: 'hideOnIntakeDisabled',
					labelWidth: 'col-sm-12 col-sm-8',
					helpBlock: 'show_data_form_section_on_intake_subtext',
					helpBlockOffset: 'col-sm-offset-0',
					helpBlockWidth: 'col-sm-12',
				},
				{
					field: 'showOnPortal',
					labelWidth: 'col-sm-12 col-sm-8',
					disableRule: 'showOnPortalDisabled',
					displayRule: 'portalEnabledForEntity && !showOnPortalDisabled',
					helpBlock: 'show_data_form_section_on_portal_subtext',
					helpBlockOffset: 'col-sm-offset-0',
					helpBlockWidth: 'col-sm-12',
				},
				{
					field: 'showOnHotline',
					labelWidth: 'col-sm-12 col-sm-8',
					disableRule: 'showOnHotlineDisabled',
					displayRule: 'hotlineEnabledForEntity',
					helpBlock: 'show_data_form_section_on_hotline_subtext',
					helpBlockOffset: 'col-sm-offset-0',
					helpBlockWidth: 'col-sm-12',
				},
			],
		},
		{
			field: 'helpText',
			helpBlock: 'help_text_help_block',
		},
	],
};
