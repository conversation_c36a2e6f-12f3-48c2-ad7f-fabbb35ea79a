module.exports = {
	name: 'party-similar',
	entity: {
		base: 'sys',
		name: 'party',
	},
	partiallyDynamic: true,
	partiallyDynamicFormOptions: {
		editableInFormBuilder: false,
	},
	defaultFieldsLayout: {
		labelWidth: 'col-xs-12 col-sm-3',
		width: 'col-xs-12 col-sm-9',
	},
	elements: [
		{
			field: 'caseId',
		},
		{
			field: 'primaryEntity',
		},
		{ field: 'partyType' },
		{ field: 'firstName' },
		{ field: 'lastName' },
		{ field: 'middleInitial'},
		{
			type: 'insertionPoint',
			name: 'insertion-point-1',
		},
		{ field: 'dateOfBirth'},
		{ field: 'address' },
		{ field: 'city' },
		{ field: 'stateProvince' },
		{ field: 'country' },
		{ field: 'zipCodePostalCode' },
		{ field: 'homePhone' },
		{ field: 'workPhone' },
		{ field: 'emailAddress' },
		{
			type: 'insertionPoint',
			name: 'insertion-point-2',
		},
	],
};
