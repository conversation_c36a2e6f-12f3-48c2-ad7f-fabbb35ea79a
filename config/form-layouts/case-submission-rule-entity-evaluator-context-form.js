module.exports = {
	name: 'case-submission-rule-entity-evaluator-context',
	entity: {
		base: 'sys',
		name: 'entityEvaluatorContext',
	},
	defaultFieldsLayout: {
		width: 'col-xs-12 col-sm-6',
		labelWidth: 'col-xs-12 col-sm-6',
	},
	elements: [
		{
			type: 'section',
			width: 'col-xs-4 col-sm-4',
			defaultFieldsLayout: {
				width: 'col-xs-12 col-sm-8',
				labelWidth: 'col-xs-12 col-sm-4',
			},
			displayRule: 'isTypeCaseChildFormsMustExist',
			elements: [
				{
					field: 'mustExistForms',
					displayRule: 'mustMatchAll',
					typeOptions: {
						filter: 'mustExistFormsFilterCaseSubmissionRule',
						entityCategory: 2,
						childFormsOnly: true,
						canCreate: false,
						parentEntityName: 'sys/case',
					},
				},
				{
					field: 'mustExistForms',
					displayRule: '!mustMatchAll',
					caption: 'at_least_1_of_these_forms_must_be_added',
					typeOptions: {
						filter: 'mustExistFormsFilterCaseSubmissionRule',
						entityCategory: 2,
						childFormsOnly: true,
						canCreate: false,
						parentEntityName: 'sys/case',
					},
				},
				{
					field: 'mustMatchAll',
					displayRule: 'hasMultipleForms',
					hideWhenReadOnly: true,
					hideCaption: true,
					hideCaptionOffset: 'col-xs-offset-0',
				},
			],
		},
	],
};
