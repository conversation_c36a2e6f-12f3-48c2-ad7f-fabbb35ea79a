module.exports = {
	name: 'external-party-details',
	entity: {
		base: 'sys',
		name: 'party',
	},
	caption: 'Party',
	captionPlural: 'Parties',
	partiallyDynamic: true,
	partiallyDynamicFormOptions: {
		editableInFormBuilder: false,
	},
	elements: [
		{
			field: 'caseId',
			editRule: 'isNew && !isAddingToSpecificCase',
		},
		{
			field: 'createdDate',
			readOnly: true,
			displayRule: 'isNotNew',
		},
		{
			field: 'partyType',
		},
		{
			field: 'firstName',
			typeOptions: {
				trailingButton: {
					id: 'party-profile-search',
					ariaLabel: 'people_search',
					icon: 'fa fa-address-book-o',
					displayRule: 'canLinkSearchProfile',
				},
			},
		},
		{
			field: 'lastName',
		},
		{
			field: 'middleInitial',
		},
		{
			type: 'insertionPoint',
			name: 'insertion-point-1',
			showOnIntake: true,
			aboveContextHint: 'party_insertion_point_1_above_context_hint',
			belowContextHint: 'party_insertion_point_1_below_context_hint',
		},
		{
			field: 'dateOfBirth',
		},
		{
			field: 'address',
		},
		{
			field: 'city',
		},
		{
			field: 'stateProvince',
		},
		{
			field: 'country',
		},
		{
			field: 'zipCodePostalCode',
		},
		{
			field: 'homePhone',
		},
		{
			field: 'workPhone',
		},
		{
			field: 'emailAddress',
		},
		{
			type: 'insertionPoint',
			name: 'insertion-point-2',
			showOnIntake: true,
			aboveContextHint: 'party_insertion_point_2_context_hint',
		},
	],
};
