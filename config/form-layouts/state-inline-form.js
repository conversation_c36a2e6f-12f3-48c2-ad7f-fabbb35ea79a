module.exports = {
	name: 'state-inline-form',
	defaultFieldsLayout: {
		labelOnTop: true,
		width: 'col-xs-12 col-sm-12',
	},
	entity: {
		base: 'sys',
		name: 'state',
	},
	elements: [
		{
			type: 'section',
			width: 'row',
			elements: [
				{
					type: 'section',
					width: 'col-sm-3',
					elements: [
						{field: 'name'},
					],
				},
				{
					type: 'section',
					width: 'col-sm-3',
					elements: [
						{field: 'primaryState'},
					],
				},
				{
					type: 'section',
					width: 'col-sm-4',
					elements: [
						{
							readOnlyWidth: 'col-xs-12',
							field: 'description',
						},
					],
				},
				{
					type: 'section',
					width: 'col-sm-2',
					elements: [
						{field: 'default'},
					],
				},
			],
		},
	],
};
