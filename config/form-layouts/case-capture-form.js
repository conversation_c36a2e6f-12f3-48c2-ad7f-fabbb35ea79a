module.exports = {
	name: 'case-capture',
	caption: 'Capture',
	partiallyDynamic: true,
	partiallyDynamicFormOptions: {
		editableInFormBuilder: false,
	},
	entity: {
		base: 'sys',
		name: 'case',
	},
	elements: [
		{ field: 'caseType' },
		{
			type: 'insertionPoint',
			name: 'insertion-point-1',
		},
		{
			type: 'insertionPoint',
			name: 'case-dynamic-tab-contents',
		},
		{
			type: 'section',
			caption: 'case_assignment',
			elements: [
				{
					caption: 'assign_to',
					field: 'owner',
					editRule: 'canAssignCase',
					typeOptions: {
						enableSelfAssign: 'enabled',
					},
				},
			],
		},
	],
};
