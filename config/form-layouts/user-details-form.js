module.exports = {
	name: 'user-details',
	entity: {
		base: 'sys',
		name: 'user',
	},
	caption: 'User Profile',
	captionPlural: 'User Profiles',
	partiallyDynamic: true,
	partiallyDynamicFormOptions: {
		initialStatus: 'active',
	},
	elements: [
		{
			field: 'ssoUser',
			displayRule: 'isSSOEnabled || isHotlineUser',
			editRule: '!isHotlineUser',
		},
		{ field: 'nick' },
		{
			field: 'userRoleId',
			editRule: '!isHotlineUser',
		},
		{
			type: 'insertionPoint',
			name: 'insertion-point-1',
			showOnIntake: true,
			aboveContextHint: 'user_insertion_point_1_above_context_hint',
			belowContextHint: 'user_insertion_point_1_below_context_hint',
		},
		{
			type: 'section',
			elements: [
				{ field: 'firstName' },
				{ field: 'lastName' },
				{
					field: 'email',
					typeOptions: {
						linkWithSystemUser: false,
					},
				},
				{ field: 'phoneNumber' },
				{ field: 'locale' },
				{ field: 'themeId' },
				{ field: 'caseCaptureRedirect' },
				{ field: 'signature' },
			],
		},
	],
};
