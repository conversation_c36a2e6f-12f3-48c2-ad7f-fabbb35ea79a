/**
 *
 *		How to extend this file?
 *
 *		Define a file within your configuration project with the same name under
 *		config_project/config. Only add the values that you want to change, OptUtil does a
 *		_.assign between this configuration and the extended configuration, if defined.
 */

module.exports = {
	extensions: [
		'.3dmlw',
		'.3dxml',
		'.3mf',
		'.aec',
		'.dgn',
		'.drw',
		'.dxf',
		'.dwf',
		'.dwg',
		'.easm',
		'.edrw',
		'.iam',
		'.idw',
		'.ipn',
		'.ipt',
		'.accdb',
		'.accde',
		'.accdr',
		'.accdt',
		'.mdb',
		'.3gp',
		'.afx',
		'.asf',
		'.asx',
		'.au',
		'.avi',
		'.divx',
		'.m3u',
		'.mov',
		'.mpeg',
		'.mpg',
		'.qt',
		'.ram',
		'.viv',
		'.vivo',
		'.vob',
		'.vqf',
		'.wma',
		'.wmv',
		'.shs',
		'.flv',
		'.dvr-ms',
		'.mpe',
		'.m4v',
		'.ogg',
		'.swf',
		'.cdr',
		'.cmx',
		'.csv',
		'.dat',
		'.del',
		'.doc',
		'.docb',
		'.docm',
		'.docx',
		'.dot',
		'.dotm',
		'.dotx',
		'.geojson',
		'.geotiff',
		'.gml',
		'.gpx',
		'.e00',
		'.gdoc',
		'.gdraw',
		'.gslides',
		'.gsheet',
		'.jpg',
		'.jpeg',
		'.jps',
		'.jp2',
		'.bmp',
		'.ico',
		'.dds',
		'.gif',
		'.png',
		'.tga',
		'.gpl',
		'.icns',
		'.pcx',
		'.jfif',
		'.exif',
		'.pns',
		'.raw',
		'.tif',
		'.tiff',
		'.log',
		'.lwp',
		'.m3u',
		'.pls',
		'.md',
		'.markdown',
		'.mid',
		'.midi',
		'.mp3',
		'.wav',
		'.flac',
		'.aiff',
		'.m4a',
		'.wma',
		'.mp4',
		'.m4p',
		'.aac',
		'.spx',
		'.ra',
		'.rm',
		'.swa',
		'.smp',
		'.sid',
		'.wtv',
		'.mp2',
		'.mpp',
		'.myo',
		'.myob',
		'.tax',
		'.numbers',
		'.lwp',
		'.odf',
		'.odm',
		'.odt',
		'.ott',
		'.odp',
		'.otp',
		'.ods',
		'.ots',
		'.pages',
		'.key',
		'.pdf',
		'.pdn',
		'.pot',
		'.potm',
		'.potx',
		'.ppam',
		'.pps',
		'.ppsx',
		'.ppt',
		'.pptm',
		'.pptx',
		'.prf',
		'.msg',
		'.pst',
		'.ost',
		'.sc2',
		'.ps',
		'.psd',
		'.pdd',
		'.psp',
		'.rpt',
		'.rtf',
		'.sldm',
		'.sldx',
		'.stw',
		'.sxm',
		'.sxw',
		'.stc',
		'.sxc',
		'.tab',
		'.tls',
		'.txt',
		'.tsv',
		'.text',
		'.wk1',
		'.wk3',
		'.wk4',
		'.wks',
		'.123',
		'.wlmp',
		'.wpd',
		'.wps',
		'.wpt',
		'.vsd',
		'.vsdx',
		'.xla',
		'.xlam',
		'.xlk',
		'.xll',
		'.xlm',
		'.xls',
		'.xlsb',
		'.xlsm',
		'.xlsx',
		'.xlt',
		'.xltm',
		'.xltx',
		'.xlw',
		'.xml',
		'.xps',
		'.at3',
		'.zip',
		'.eml',
		'.gz',
		'.rar',
		'.7z',
		'.at3',
		'.eml',
		'.webp',
		'.hevc',
		'.mpga',
		'.mpa',
		'.heif',
	],
};
