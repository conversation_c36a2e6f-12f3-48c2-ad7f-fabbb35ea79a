module.exports = {
	code: 'name-detailed',
	caption: 'Name - detailed',
	fieldAttributes: ['person'],
	icon: 'fa-user',
	description: 'Group of fields to record a person\'s full name.',
	layout: {
		layoutType: 'field-group-section',
		layoutOpts: {
			fieldGroup: 'name-detailed',
		},
		children: [
			{
				layoutType: 'field',
				layoutOpts: {
					fieldDef: {
						type: 'textbox',
						caption: 'First name',
						sourceGroupField: 'firstName',
					},
				},
			},
			{
				layoutType: 'field',
				layoutOpts: {
					fieldDef: {
						type: 'textbox',
						caption: 'Last name',
						sourceGroupField: 'lastName',
					},
				},
			},
			{
				layoutType: 'field',
				layoutOpts: {
					fieldDef: {
						type: 'textbox',
						caption: 'Middle initials',
						sourceGroupField: 'middleInitials',
					},
				},
			},
		],
	},
};
