module.exports = {
	code: 'person-information',
	caption: 'Person Information',
	fieldAttributes: ['person'],
	icon: 'fa-user',
	description: 'Group of fields to record a person\'s full name, phone numbers, address, email, and manager.',
	layout: {
		layoutType: 'field-group-section',
		layoutOpts: {
			fieldGroup: 'person-information',
		},
		children: [
			{
				layoutType: 'field',
				layoutOpts: {
					fieldDef: {
						type: 'textbox',
						caption: 'First name',
						sourceGroupField: 'firstName',
					},
				},
			},
			{
				layoutType: 'field',
				layoutOpts: {
					fieldDef: {
						type: 'textbox',
						caption: 'Last name',
						sourceGroupField: 'lastName',
					},
				},
			},
			{
				layoutType: 'field',
				layoutOpts: {
					fieldDef: {
						type: 'textbox',
						caption: 'Employee ID',
						sourceGroupField: 'employeeId',
						fieldAttributes: ['ID'],
					},
				},
			},
			{
				layoutType: 'field',
				layoutOpts: {
					fieldDef: {
						type: 'phone-number',
						caption: 'Work phone number',
						sourceGroupField: 'workPhoneNumber',
					},
				},
			},
			{
				layoutType: 'field',
				layoutOpts: {
					fieldDef: {
						type: 'phone-number',
						caption: 'Home phone number',
						sourceGroupField: 'homePhoneNumber',
					},
				},
			},
			{
				layoutType: 'field',
				layoutOpts: {
					fieldDef: {
						type: 'email',
						caption: 'Email (work)',
						sourceGroupField: 'emailWork',
						typeOptions: {
							linkWithSystemUser: false,
						},
					},
				},
			},
			{
				layoutType: 'field',
				layoutOpts: {
					fieldDef: {
						type: 'email',
						caption: 'Email (personal)',
						sourceGroupField: 'emailPersonal',
						typeOptions: {
							linkWithSystemUser: false,
						},
					},
				},
			},
			{
				layoutType: 'field',
				layoutOpts: {
					fieldDef: {
						type: 'textbox',
						caption: 'Street address',
						sourceGroupField: 'streetAddress',
						fieldAttributes: ['location'],
					},
				},
			},
			{
				layoutType: 'field',
				layoutOpts: {
					fieldDef: {
						type: 'textbox',
						caption: 'City',
						sourceGroupField: 'city',
						fieldAttributes: ['location'],
					},
				},
			},
			{
				layoutType: 'field',
				layoutOpts: {
					fieldDef: {
						type: 'textbox',
						caption: 'State / Province / Territory',
						sourceGroupField: 'stateProvinceTerritory',
						fieldAttributes: ['location'],
					},
				},
			},
			{
				layoutType: 'field',
				layoutOpts: {
					fieldDef: {
						type: 'country',
						caption: 'Country',
						sourceGroupField: 'country',
						fieldAttributes: ['location'],
					},
				},
			},
			{
				layoutType: 'field',
				layoutOpts: {
					fieldDef: {
						type: 'postalCode',
						caption: 'Zip / Postal code',
						sourceGroupField: 'zipPostalCode',
						fieldAttributes: ['location'],
					},
				},
			},
			{
				layoutType: 'field',
				layoutOpts: {
					fieldDef: {
						type: 'textbox',
						caption: 'Name of manager',
						sourceGroupField: 'nameOfManager',
					},
				},
			},
		],
	},
};
