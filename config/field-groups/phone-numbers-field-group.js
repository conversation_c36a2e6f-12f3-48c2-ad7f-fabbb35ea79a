module.exports = {
	code: 'phone-numbers',
	caption: 'Phone number(s)',
	fieldAttributes: ['ID'],
	icon: 'fa-hashtag',
	description: 'Group of fields to record home, work, and mobile phone numbers.',
	layout: {
		layoutType: 'field-group-section',
		layoutOpts: {
			fieldGroup: 'phone-numbers',
		},
		children: [
			{
				layoutType: 'field',
				layoutOpts: {
					fieldDef: {
						type: 'phone-number',
						caption: 'Work phone number',
						sourceGroupField: 'workPhoneNumber',
					},
				},
			},
			{
				layoutType: 'field',
				layoutOpts: {
					fieldDef: {
						type: 'phone-number',
						caption: 'Home phone number',
						sourceGroupField: 'homePhoneNumber',
					},
				},
			},
			{
				layoutType: 'field',
				layoutOpts: {
					fieldDef: {
						type: 'phone-number',
						caption: 'Mobile phone number',
						sourceGroupField: 'mobilePhoneNumber',
					},
				},
			},
		],
	},
};
