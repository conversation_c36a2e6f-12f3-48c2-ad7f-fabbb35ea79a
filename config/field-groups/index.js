const fs = require('fs');
const _ = require('lodash');
const path = require('path');

const fieldGroups = {};

function addFieldGroupsInFolder(folderPath) {
	const files = fs.readdirSync(folderPath);
	_.each(files, (file) => {
		const filePath = path.join(folderPath, file);
		if (/field-group.js$/.test(file)) {
			// eslint-disable-next-line import/no-dynamic-require
			const layout = require(filePath);
			const code = layout.code;
			if (!code) throw new Error('Field Groups must specify a code');
			fieldGroups[code] = layout;
		}
	});
}

addFieldGroupsInFolder(__dirname);
module.exports = fieldGroups;
