module.exports = {
	code: 'location',
	caption: 'Location',
	fieldAttributes: ['location'],
	icon: 'fa-map',
	description: 'Group of fields to record a full address.',
	layout: {
		layoutType: 'field-group-section',
		layoutOpts: {
			fieldGroup: 'location',
		},
		children: [
			{
				layoutType: 'field',
				layoutOpts: {
					fieldDef: {
						type: 'textbox',
						caption: 'Location name',
						sourceGroupField: 'locationName',
					},
				},
			},
			{
				layoutType: 'field',
				layoutOpts: {
					fieldDef: {
						type: 'textbox',
						caption: 'Street address',
						sourceGroupField: 'streetAddress',
					},
				},
			},
			{
				layoutType: 'field',
				layoutOpts: {
					fieldDef: {
						type: 'textbox',
						caption: 'City',
						sourceGroupField: 'city',
					},
				},
			},
			{
				layoutType: 'field',
				layoutOpts: {
					fieldDef: {
						type: 'textbox',
						caption: 'State / Province / Territory',
						sourceGroupField: 'stateProvinceTerritory',
					},
				},
			},
			{
				layoutType: 'field',
				layoutOpts: {
					fieldDef: {
						type: 'country',
						caption: 'Country',
						sourceGroupField: 'country',
					},
				},
			},
			{
				layoutType: 'field',
				layoutOpts: {
					fieldDef: {
						type: 'postalCode',
						caption: 'Zip / Postal code',
						sourceGroupField: 'zipPostalCode',
					},
				},
			},
		],
	},
};
