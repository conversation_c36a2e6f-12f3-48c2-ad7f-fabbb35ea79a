module.exports = {
	code: 'emails',
	caption: 'Email(s)',
	fieldAttributes: ['ID'],
	icon: 'fa-hashtag',
	description: 'Group of fields to record home and work email addresses.',
	layout: {
		layoutType: 'field-group-section',
		layoutOpts: {
			fieldGroup: 'emails',
		},
		children: [
			{
				layoutType: 'field',
				layoutOpts: {
					fieldDef: {
						type: 'email',
						caption: '<PERSON><PERSON> (work)',
						sourceGroupField: 'emailWork',
						typeOptions: {
							linkWithSystemUser: false,
						},
					},
				},
			},
			{
				layoutType: 'field',
				layoutOpts: {
					fieldDef: {
						type: 'email',
						caption: 'Email (personal)',
						sourceGroupField: 'emailPersonal',
						typeOptions: {
							linkWithSystemUser: false,
						},
					},
				},
			},
		],
	},
};
