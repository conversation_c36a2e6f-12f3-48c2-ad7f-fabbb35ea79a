const fs = require('fs');
const path = require('path');
const config = require('./index.js');
const OptUtil = require('../lib/common/OptUtil.js');

const enableStaticSSOOverride = process.env.ENABLE_STATIC_SSO_OVERRIDE === 'true';

module.exports = {
	defaultStrategy: process.env.AUTH_DEFAULT_STRATEGY || 'local',
	passwordResetDisabled: process.env.DISABLE_PASSWORD_RESET === 'true',
	loginPageDisabled: process.env.DISABLE_LOGIN_PAGE === 'true',
	autoUserProvisioningEnabled: process.env.ENABLE_AUTO_USER_PROVISIONING === 'true',
	dynamicAuthRouteName: 'authd',
	uniqueIdentifierUserTypes: {
		internal: ['local', 'saml', 'wsfed', 'ldap', 'hotline'],
		external: ['guest', 'portal'],
	},
	// Static SSO configurations are deprecated as of v8.5.0 and disabled by default.
	// Setting this flag will enable Static SSO configurations
	// and disable the dynamic configuration.
	enableStaticSSOOverride,
	disableSSOOptions: process.env.DISABLE_SSO_OPTIONS === 'true',
	staticSSOStrategies: ['saml', 'wsfed', 'ldap'],
	strategies: {
		local: {
			// Standard
			enabled: process.env.LOCAL_AUTH_ENABLED !== 'false',
			successRedirect: process.env.AUTH_SUCCESS_REDIRECT || '/',
			jsonRedirect: true,
			postUrl: '/auth/local',
			strategyPath: path.join(__dirname, '../lib/auth/passport-local.js'),
			// Others
			maxTries: 3,
			maxTriesExpiry: 60 * 60, // 1 hour
		},
		portal: {
			// Standard
			enabled: true,
			features: ['twoWayPortal'],
			options: [{ name: 'portalSubmissionAccess', value: 'Two-Way' }],
			successRedirect: process.env.PORTAL_SUCCESS_REDIRECT || '/external',
			jsonRedirect: true,
			postUrl: '/auth/portal',
			strategyPath: path.join(__dirname, '../lib/auth/passport-portal.js'),
			// Others
			maxTries: 3,
			maxTriesExpiry: 60 * 60, // 1 hour
			deserializeOptions: {
				includeHidden: true,
				omitFields: ['email', 'nick', 'firstName', 'lastName', 'name'],
			},
		},
		hotline: {
			type: 'hotline',
			enabled: process.env.ENABLE_HOTLINE_SSO === 'true',
			features: ['hotlineAgentIntake'],
			options: [
				{ name: 'enablePortal', value: true },
				{ name: 'portalSubmissionAccess', value: 'Two-Way' },
			],
			strategyPath: path.join(__dirname, '../lib/auth/passport-saml.js'),
			successRedirect: process.env.HOTLINE_SSO_SUCCESS_REDIRECT || '/',
			failureRedirect: process.env.HOTLINE_SSO_FAILURE_REDIRECT || process.env.SSO_FAILURE_REDIRECT || '/login?failure=true',
			identifierMapping: process.env.HOTLINE_SSO_IDENTIFIER_MAPPING || 'email',
			getUrl: process.env.HOTLINE_SSO_URL || '/hotline_auth/saml',
			postUrl: process.env.HOTLINE_SSO_URL || '/hotline_auth/saml',
			dataMapping: process.env.HOTLINE_SSO_DATA_MAPPING_CONFIG
				? {
					identifier: process.env.HOTLINE_SSO_IDENTIFIER_DATA_MAPPING || 'email',
					...OptUtil.parseEnvironmentDataMap(process.env.HOTLINE_SSO_DATA_MAPPING_CONFIG),
				}
				: {
					identifier: process.env.HOTLINE_SSO_IDENTIFIER_DATA_MAPPING || 'http://schemas.xmlsoap.org/ws/2005/05/identity/claims/emailaddress',
					email: process.env.HOTLINE_SSO_EMAIL_DATA_MAPPING || 'http://schemas.xmlsoap.org/ws/2005/05/identity/claims/emailaddress',
					nick: process.env.HOTLINE_SSO_NICK_DATA_MAPPING || 'http://schemas.xmlsoap.org/ws/2005/05/identity/claims/nameidentifier',
					firstName: process.env.HOTLINE_SSO_FIRST_NAME_DATA_MAPPING || 'http://schemas.xmlsoap.org/ws/2005/05/identity/claims/givenname',
					lastName: process.env.HOTLINE_SSO_LAST_NAME_DATA_MAPPING || 'http://schemas.xmlsoap.org/ws/2005/05/identity/claims/surname',
				},
			defaultData: process.env.HOTLINE_SSO_DEFAULT_DATA_CONFIG
				? OptUtil.parseEnvironmentDataMap(process.env.HOTLINE_SSO_DEFAULT_DATA_CONFIG)
				: {
					locale: process.env.HOTLINE_AGENT_DEFAULT_LOCALE || 'en_US',
				},
			passportOptions: {
				cert: process.env.HOTLINE_SSO_CERT,
				issuer: process.env.HOTLINE_SSO_ISSUER || config.baseUrl,
				entryPoint: process.env.HOTLINE_SSO_ENTRY_POINT || 'https://sts.windows.net/1eeca8e4-63eb-4cc9-b105-6cd99aaf5ea1/',
				callbackUrl: process.env.HOTLINE_SSO_CALLBACK_URL || `${config.baseUrl}/hotline_auth/saml`,
				identifierFormat: process.env.HOTLINE_SSO_IDENTIFIER_FORMAT || 'urn:oasis:names:tc:SAML:1.1:nameid-format:emailAddress',
				authnContext: process.env.HOTLINE_SSO_AUTHN_CONTEXT,
				disableRequestedAuthnContext: process.env.ENABLE_HOTLINE_SSO_AUTHN_CONTEXT !== 'true',
				wantAssertionsSigned: process.env.DISABLE_HOTLINE_WANT_ASSERTIONS_SIGNED !== 'true',
				wantAuthnResponseSigned: process.env.ENABLE_HOTLINE_WANT_AUTHN_RESPONSE_SIGNED === 'true',
			},
			autoUserProvisionOptions: {
				matchIdentifierByUserTypeCategory: true,
				forceUserRole: process.env.HOTLINE_SSO_USER_ROLE || 'Hotline Agent',
			},
		},
		// Deprecated since v8.5.0
		saml: {
			// Standard
			enabled: enableStaticSSOOverride && process.env.SAML_AUTH_ENABLED === 'true',
			successRedirect: process.env.SAML_SUCCESS_REDIRECT || process.env.AUTH_SUCCESS_REDIRECT || '/',
			failureRedirect: process.env.SAML_SSO_FAILURE_REDIRECT || process.env.SSO_FAILURE_REDIRECT || '/login?failure=true',
			getUrl: process.env.SAML_URL || process.env.SSO_URL || '/auth/saml',
			postUrl: process.env.SAML_CALLBACK_URL || process.env.SSO_CALLBACK_URL || '/auth/saml',
			strategyPath: path.join(__dirname, '../lib/auth/passport-saml.js'),
			// Others
			issuer: process.env.SAML_ISSUER || '127.0.0.1_no_ssl_8000',
			callbackUrl: process.env.SAML_CALLBACK_URL || process.env.SSO_CALLBACK_URL || '/auth/saml',
			entryPoint: process.env.SAML_ENTRY_POINT,
			cert: process.env.SAML_CERT,
			privateCert: process.env.SAML_PRIVATE_CERT,
			authnContext: process.env.SAML_AUTHN_CONTEXT,
			identifierFormat: process.env.SAML_IDENTIFIER_FORMAT || process.env.SSO_IDENTIFIER_FORMAT,
			identifierMapping: process.env.SAML_IDENTIFIER_MAPPING || process.env.SSO_IDENTIFIER_MAPPING || 'nick',
			dataMapping: {
				email: process.env.SAML_EMAIL_DATA_MAPPING || process.env.SSO_EMAIL_DATA_MAPPING || 'email',
				nick: process.env.SAML_NICK_DATA_MAPPING || process.env.SSO_NICK_DATA_MAPPING || 'http://schemas.xmlsoap.org/ws/2005/05/identity/claims/nameidentifier',
				firstName: process.env.SAML_FIRST_NAME_DATA_MAPPING || process.env.SSO_FIRST_NAME_DATA_MAPPING || 'http://schemas.xmlsoap.org/ws/2005/05/identity/claims/givenname',
				lastName: process.env.SAML_LAST_NAME_DATA_MAPPING || process.env.SSO_LAST_NAME_DATA_MAPPING || 'http://schemas.xmlsoap.org/ws/2005/05/identity/claims/surname',
				identifier: process.env.SAML_IDENTIFIER_DATA_MAPPING || process.env.SSO_IDENTIFIER_DATA_MAPPING || 'http://schemas.xmlsoap.org/ws/2005/05/identity/claims/nameidentifier',
				userRole: process.env.SAML_USER_ROLE_DATA_MAPPING || process.env.SSO_USER_ROLE_DATA_MAPPING,
			},
			defaultData: {
				userRole: process.env.SAML_USER_ROLE_DATA_DEFAULT || process.env.SSO_USER_ROLE_DATA_DEFAULT,
				locale: process.env.SAML_LOCALE_DATA_DEFAULT || process.env.SSO_LOCALE_DATA_DEFAULT || 'en_US',
			},
			passportOptions: {
				wantAssertionsSigned: process.env.DISABLE_SAML_WANT_ASSERTIONS_SIGNED !== 'true',
				wantAuthnResponseSigned: process.env.ENABLE_SAML_WANT_AUTHN_RESPONSE_SIGNED === 'true',
			},
		},
		// Deprecated since v8.5.0
		wsfed: {
			// Standard
			enabled: enableStaticSSOOverride && process.env.WSFED_AUTH_ENABLED === 'true',
			successRedirect: process.env.WSFED_SUCCESS_REDIRECT || process.env.AUTH_SUCCESS_REDIRECT || '/',
			failureRedirect: process.env.WSFED_SSO_FAILURE_REDIRECT || process.env.SSO_FAILURE_REDIRECT || '/login?failure=true',
			getUrl: process.env.WSFED_URL || process.env.SSO_URL || '/auth/wsfed',
			postUrl: process.env.WSFED_PATH || process.env.SSO_CALLBACK_URL || '/auth/wsfed',
			strategyPath: path.join(__dirname, '../lib/auth/passport-wsfed.js'),
			// Others
			identifierFormat: process.env.WSFED_IDENTIFIER_FORMAT || process.env.SSO_IDENTIFIER_FORMAT,
			identifierMapping: process.env.WSFED_IDENTIFIER_MAPPING || process.env.SSO_IDENTIFIER_MAPPING || 'nick',
			dataMapping: {
				email: process.env.WSFED_EMAIL_DATA_MAPPING || process.env.SSO_EMAIL_DATA_MAPPING || 'email',
				nick: process.env.WSFED_NICK_DATA_MAPPING || process.env.SSO_NICK_DATA_MAPPING || 'http://schemas.xmlsoap.org/ws/2005/05/identity/claims/nameidentifier',
				firstName: process.env.WSFED_FIRST_NAME_DATA_MAPPING || process.env.SSO_FIRST_NAME_DATA_MAPPING || 'http://schemas.xmlsoap.org/ws/2005/05/identity/claims/givenname',
				lastName: process.env.WSFED_LAST_NAME_DATA_MAPPING || process.env.SSO_LAST_NAME_DATA_MAPPING || 'http://schemas.xmlsoap.org/ws/2005/05/identity/claims/surname',
				identifier: process.env.WSFED_IDENTIFIER_DATA_MAPPING || process.env.SSO_IDENTIFIER_DATA_MAPPING || 'http://schemas.xmlsoap.org/ws/2005/05/identity/claims/nameidentifier',
				userRole: process.env.WSFED_USER_ROLE_DATA_MAPPING
					|| process.env.SSO_USER_ROLE_DATA_MAPPING,
			},
			defaultData: {
				userRole: process.env.WSFED_USER_ROLE_DATA_DEFAULT
					|| process.env.SSO_USER_ROLE_DATA_DEFAULT,
				locale: process.env.WSFED_LOCALE_DATA_DEFAULT || process.env.SSO_LOCALE_DATA_DEFAULT || 'en_US',
			},
			// This object here is passed raw to passport-wsfed-saml2 plugin.
			passportOptions: {
				protocol: process.env.WSFED_PROTOCOL || 'samlp',
				protocolBinding: process.env.WSFED_PROTOCOL_BINDING || null,
				path: process.env.WSFED_PATH || process.env.SSO_CALLBACK_URL || '/auth/wsfed',
				realm: process.env.WSFED_REALM, // 'urn:node:app',
				homeRealm: process.env.WSFED_HOME_REALM || '',
				identityProviderUrl: process.env.WSFED_IDENTITY_PROVIDER_URL,
				cert: process.env.WSFED_CERT_FILE
					? fs.readFileSync(process.env.WSFED_CERT_FILE, process.env.WSFED_CERT_ENCODING || 'utf8')
					: process.env.WSFED_CERT,
				signingKey: process.env.WSFED_SIGNING_KEY_KEY_FILE
					&& process.env.WSFED_SIGNING_KEY_CERT_FILE
					? {
						key: fs.readFileSync(process.env.WSFED_SIGNING_KEY_KEY_FILE),
						cert: fs.readFileSync(process.env.WSFED_SIGNING_KEY_CERT_FILE),
					}
					: null,
				decryptionKey: process.env.WSFED_DECRYPTION_KEY_FILE
					? fs.readFileSync(process.env.WSFED_DECRYPTION_KEY_FILE)
					: null,
			},
		},
		// Deprecated since v8.5.0
		ldap: {
			// Standard
			enabled: enableStaticSSOOverride && process.env.LDAP_AUTH_ENABLED === 'true',
			successRedirect: process.env.LDAP_SUCCESS_REDIRECT || process.env.AUTH_SUCCESS_REDIRECT || '/',
			failureRedirect: process.env.LDAP_SSO_FAILURE_REDIRECT || process.env.SSO_FAILURE_REDIRECT || '/login?failure=true',
			url: process.env.LDAP_URL || '/auth/ldap',
			strategyPath: path.join(__dirname, '../lib/auth/passport-ldap.js'),
			// Others
			passportOptions: {
			},
		},
	},
};
