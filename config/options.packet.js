
const ensureNumber = (value, varname) => {
	const isValidNumber = typeof value === 'number' && !Number.isNaN(value);

	if (isValidNumber) {
		return;
	}

	throw new Error(`${varname} must be a valid number`);
};


const maxPacketFileSize = Number(process.env.PACKET_MAX_FILE_SIZE || '4718592'); // Bytes
ensureNumber(maxPacketFileSize, 'PACKET_MAX_FILE_SIZE');


const maxPacketFilePages = Number(process.env.PACKET_MAX_FILE_PAGES || '25');
ensureNumber(maxPacketFilePages, 'PACKET_MAX_FILE_PAGES');


const maxPacketNumExcelCells = Number(process.env.PACKET_MAX_EXCEL_CELLS || '250');
ensureNumber(maxPacketNumExcelCells, 'PACKET_MAX_EXCEL_CELLS');


module.exports = {
	maxPacketFileSize,
	maxPacketFilePages,
	maxPacketNumExcelCells,
};
