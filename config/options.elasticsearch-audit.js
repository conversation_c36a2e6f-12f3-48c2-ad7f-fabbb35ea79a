/* global module */
function isEnabled(value) {
	return (value !== '0') && (value !== 'false');
}

// TODO: timestamp should be explicitly indexed as a specific datetime type (cexajones)

module.exports = {
	refreshOnSave: isEnabled(process.env.ES_REFRESH_ON_SAVE),
	fetchEntitiesFromDB: true,
	base: 'audit',
	entities: [{
		base: 'audit',
		name: 'log',
		indexedAttributes: {
			id: {
				type: 'keyword',
			},
			timestamp: true,
			userId: {
				type: 'keyword',
			},
			objectType: {
				type: 'keyword',
			},
			objectId: {
				type: 'keyword',
			},
			parentType: {
				type: 'keyword',
			},
			parentId: {
				type: 'keyword',
			},
			action: {
				type: 'keyword',
			},
		},
	}],
};
