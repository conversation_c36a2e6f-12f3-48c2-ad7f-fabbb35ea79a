const formsDefaultDynamicDataFilters = ['createdDate', 'lastUpdatedDate'];
module.exports = {
	'calendar-grid': {
		sortColumn: 'createdDate',
		sortOrder: 'desc',
		columns: [
			{ field: 'childNumber' },
			{ field: 'typeText' },
			{ field: 'effectiveStartDate' },
			{ field: 'createdDate' },
			{ field: 'details' },
			{ field: 'createdBy' },
		],
	},
	'case-calendar-grid': {
		sortColumn: 'createdDate',
		sortOrder: 'desc',
		columns: [
			{ field: 'childNumber' },
			{ field: 'typeText' },
			{ field: 'effectiveStartDate' },
			{ field: 'createdDate' },
			{ field: 'details' },
			{ field: 'createdBy' },
		],
	},
	'case-all-grid': {
		sortColumn: 'createdDate',
		sortOrder: 'desc',
	},
	'case-portal-shared-records-grid': {
		sortColumn: 'recordName',
		sortOrder: 'asc',
		columns: [
			{ field: 'recordName' },
			{ field: 'details' },
			{ field: 'sharedPartiesCount' },
		],
	},
	'case-forms-all-grid': {
		sortColumn: 'createdDate',
		sortOrder: 'desc',
		customForms: true,
		defaultDynamicDataFilters: formsDefaultDynamicDataFilters,
		columns: [
			{ field: 'childNumber' },
			{ field: 'typeText' },
			{ field: 'createdBy' },
			{ field: 'createdDate' },
			{ field: 'lastUpdatedDate' },
		],
	},
	'case-external-forms-all-grid': {
		sortColumn: 'typeText',
		sortOrder: 'asc',
		customForms: true,
		defaultDynamicDataFilters: formsDefaultDynamicDataFilters,
		columns: [
			{ field: 'typeText'},
			{ field: 'createdDate' },
		],

	},
	'export-data-dictionary-grid': {
		sortColumn: 'caption',
		sortOrder: 'desc',
	},
	'forms-all-grid': {
		sortColumn: 'createdDate',
		sortOrder: 'desc',
		customForms: true,
		defaultDynamicDataFilters: formsDefaultDynamicDataFilters,
		columns: [
			{ field: 'childNumber' },
			{ field: 'typeText'},
			{ field: 'createdBy' },
			{ field: 'createdDate' },
			{ field: 'lastUpdatedDate' },
		],
	},
	'case-history-grid': {
		sortColumn: 'timestamp',
		sortOrder: 'desc',
	},
	'team-members-grid': {
		sortColumn: 'name',
		sortOrder: 'desc',
	},
	'team-history-grid': {
		sortColumn: 'timestamp',
		sortOrder: 'desc',
	},
	'team-activity-grid': {
		sortColumn: 'timestamp',
		sortOrder: 'desc',
		entities: ['audit/log'],
		defaultDynamicDataFilters: ['timestamp'],
	},
	'export-data-grid': {
		sortColumn: 'caption',
		sortOrder: 'desc',
	},
	'forms-all-search-grid': {
		sortColumn: 'createdDate',
		sortOrder: 'desc',
	},
	'inline-actions-grid': {
		sortColumn: 'createdDate',
		sortOrder: 'desc',
		excludeColumns: ['datePurged', 'pendingPurgeDate', 'purgeReason', 'excludeFromSuggestedLinks'],
		columns: [
			{ field: 'actionType' },
			{ field: 'details' },
			{ field: 'contextUsers' },
			{ field: 'systemUsers' },
		],
	},
	'workflow-history-grid': {
		sortColumn: 'timestamp',
		sortOrder: 'desc',
	},
	'rule-history-grid': {
		sortColumn: 'timestamp',
		sortOrder: 'desc',
	},
	'main-flags-grid': {
		sortColumn: 'createdDate',
		sortOrder: 'desc',
		columns: [
			{ field: 'name' },
			{ field: 'description' },
		],
		entities: ['sys/flag'],
		defaultDynamicDataFilters: ['createdDate', 'lastUpdatedDate'],
	},
	'case-dynamic-form-grid': {
		sortColumn: 'createdDate',
		sortOrder: 'desc',
		columns: [
			{ field: 'childNumber' },
			{ field: 'createdBy' },
			{ field: 'createdDate' },
			{ field: 'lastUpdatedDate' },
		],
	},
	'external-case-dynamic-form-grid': {
		sortColumn: 'createdDate',
		sortOrder: 'desc',
		columns: [
			{ field: 'childNumber' },
			{ field: 'createdDate' },
		],
	},
	'dynamic-form-grid': {
		sortColumn: 'createdDate',
		sortOrder: 'desc',
		columns: [
			{ field: 'childNumber' },
			{ field: 'createdBy' },
			{ field: 'createdDate' },
			{ field: 'lastUpdatedDate' },
		],
	},
	'data-form-entries-grid': {
		sortColumn: 'createdDate',
		sortOrder: 'desc',
		defaultDynamicDataFilters: formsDefaultDynamicDataFilters,
		columns: [
			{ field: 'typeText' },
			{ field: 'createdBy' },
			{ field: 'createdDate' },
			{ field: 'lastUpdatedDate' },
		],
	},
	'search-results-data-form-entries': {
		sortColumn: 'createdDate',
		sortOrder: 'desc',
	},
	'custom-form-main-layouts': {
		sortColumn: 'caption',
		sortOrder: 'asc',
		columns: [
			{ field: 'caption' },
			{ field: 'status' },
			{ field: 'entityType' },
			{ field: 'hideOnIntake'},
			{ field: 'showOnPortal'},
			{ field: 'showOnHotline'},
			{ field: 'sequence'},
		],
		defaultDynamicDataFilters: ['status', 'createdDate', 'lastUpdatedDate', 'entityType'],
	},
	'custom-form-main-layouts-hotline-disabled': {
		sortColumn: 'caption',
		sortOrder: 'asc',
		columns: [
			{ field: 'caption' },
			{ field: 'status' },
			{ field: 'entityType' },
			{ field: 'hideOnIntake'},
			{ field: 'showOnPortal'},
			{ field: 'sequence'},
		],
		defaultDynamicDataFilters: ['status', 'createdDate', 'lastUpdatedDate', 'entityType'],
	},
	'link-entities-source-grid': {
		sortColumn: 'createdDate',
		sortOrder: 'desc',
		columns: [
			{ field: 'childNumber' },
			{ field: 'gridDescriptor' },
			{ field: 'createdDate' },
		],
	},
	'link-entities-target-grid': {
		sortColumn: 'createdDate',
		sortOrder: 'desc',
		columns: [
			{ field: 'childNumber' },
			{ field: 'gridDescriptor' },
			{ field: 'createdDate' },
		],
	},
};
