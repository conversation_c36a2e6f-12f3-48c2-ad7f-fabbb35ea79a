/***
 *
 *		How to extend this file?
 *
 *		Define a file within your configuration project with the same name under
 *		config_project/config. Only add the values that you want to change, OptUtil does a
 *		_.assign between this configuration and the extended configuration, if defined.
 *
 */

const _ = require('lodash');
const moment = require('moment');
var OptUtil = require('../lib/common/OptUtil.js');
var myOptions = require('./');
var supportedFileFormatsConfig = OptUtil.getOptionsFile('options.supported-file-formats.js');
var coordinateUtils = require('../field-types/coordinate/utils.js');

const authOptions = OptUtil.getAuthOptions();
const packetOptions = OptUtil.getOptionsFile('options.packet.js');
const monthMs = moment.duration(30, 'days').asMilliseconds();

const dataLookupOptions = OptUtil.getOptionsFile('options.data-lookup.js');

/***
 *
 *		Config that affects all applications, if you want to overwrite or create an app specific
 *		options, use (config_project)/config/options.global.js
 *
 */
module.exports = {
	systemName: 'Case IQ',

	// File Sharing
	sharedLinkAccessMaxTries: 3,

	clientPulseInterval: myOptions.clientPulseInterval,
	sseEventsThrottleMs: 1000, // SSE events will trigger no more than 1 call a second per listener

	// API
	apiRoot: '/api/1.0',
	mockLinkApi: myOptions.mockLinkApi,

	// UI
	buttonClickEventsThrottle: 300, // In ms
	defaultThrottle: 150, // In ms
	defaultNavbarLogoWidth: 131, // px
	defaultNavbarLogoHeight: 34, // px
	quickSearchRecentSearchesLimit: 5,
	quickSearchSavedSearchesLimit: 5,
	similarSearchDebounce: 1000,

	maxSimultaneousCopy: 10,
	// Formats
	defaultNumberFormat: "'0,0.{{scale}}",
	defaultNumberScaleFormat: "'0",
	defaultNumberNullScale: 0,
	defaultCurrencyFormat: "'0,0.00",
	defaultIntegerFormat: '0,0',
	defaultDateFormat: 'DD-MMM-YYYY',
	defaultDateLongFormat: 'MMMM D, YYYY',
	defaultDateTimeFormat: 'DD-MMM-YYYY h:mm A',
	defaultDateTimeFileFormat: 'DD.MM.YYYY_h.mmA', // no spaces or colons
	defaultDateTimeLongFormat: 'MMMM D, YYYY h:mm A',
	defaultMonthAbbreviations: {
		en: [
			'Jan.', 'Feb.', 'Mar.', 'Apr.', 'May', 'Jun.',
			'Jul.', 'Aug.', 'Sep.', 'Oct.', 'Nov.', 'Dec.',
		],
	},
	defaultMoneyFormat: '$0,0.{{scale}}',
	defaultMoneyScaleFormat: '00',
	defaultMoneyNullScale: 2,
	defaultTimeFormat: 'hh:mm A',
	defaultTimezone: 'America/Toronto',
	defaultPostalCodeProfile: 'CANADA',
	defaultColor: '#ffffff',
	//Export
	maxFileNameLength: 220,
	// ACL
	// eslint-disable-next-line max-len
	enableRequisiteAndSufficientRulesDoNotUseUnlessYouFullyUnderstandTheConsequencesOfWhatYouAreDoing: false,
	injectACLBasedOnPermissions: true,
	userRoleFilterLimit: 10,

	// Redirect routes for Authentication
	redirectLoginRoute: '/login',
	redirectLoginSuccessRoute: authOptions.strategies.local.successRedirect,
	redirectLogoutRoute: '/',

	redirectLoginRouteExternal: '/external/login',
	redirectLoginSuccessRouteExternal: authOptions.strategies.portal.successRedirect,
	redirectLogoutRouteExternal: '/portal',

	// Audit
	auditViewActivityGap: 30, // Seconds
	maxAuditPrintableRows: 1000,

	// Case
	caseNumberFormat: ['YYYY', '-', 'MM', '-', 'NNNN'],

	// Cache
	defaultPicklistTtl: 15 * 60 * 1000, // 15 minutes
	userPreferenceTTL: 21 * 24 * 60 * 60 * 1000, // 21 days
	userPreferenceThrottle: 60 * 1000, // 1 minute
	executeCopyPublishProcessingTTL: Number(process.env.EXECUTE_COPY_PUBLISH_PROCESSING_TTL)
		|| 60 * 60 * 1000, // 60 minutes
	workflowOperationUnderwayTTL: Number(process.env.WORKFLOW_OPERATION_UNDERWAY_TTL)
		|| 60 * 60 * 1000, // 60 minutes
	optionCacheRefreshInterval: myOptions.optionCacheRefreshInterval,
	optionRedisKey: 'optionCacheVersion',

	gridDefaultRowCountOption: 10,
	gridDefaultRowCountPrintOption: 100,
	gridMaxFilters: 20,
	gridRowCountOptions: [5, 10, 25, 50, 100],
	dataFormGridInitialColumnLimit: 10,

	// Print
	// Recommend this stays disabled. It is considered unstable.
	showAdvancedPrintOptions: false,

	//Notifier
	defaultNotificationMethod: 'email',

	// Emails
	acceptAllSendersEnabled: false,
	bccEmails: false,
	ccEmails: true,
	emailThreadHistoryLimit: 5,
	caseEmailSenderPrefix: 'replyto',

	// Email rules
	maxOutgoingEmailFilters: 10,

	// Ical Options
	ical: {
		domain: 'caseiq',
		prodId: '//caseiq.com//i-sight//EN',
		organizerEmail: `appointments@${myOptions['mail-server'].domain}`,
	},

	// Attachments
	maxFileUploadCount: 1,
	maxAttachmentSize: 50 * 1024 * 1024, // 50 MB
	maxDocumentLibraryAttachmentSize: 50 * 1024 * 1024, // 50 MB
	maxAttachmentSizeToIndex: 15 * 1024 * 1024, // 15 MB
	maxSearchableFilesPerCase: 1000,
	supportedFileFormats: supportedFileFormatsConfig.extensions,
	defaultESIndexAttachmentStreamAccumulatorSize: 40 * 1024 * 1024, // 40 MB
	pdftronEnabled: process.env.ENABLE_PDFTRON === 'true',
	maxPacketFileSize: packetOptions.maxPacketFileSize,
	maxPacketFilePages: packetOptions.maxPacketFilePages,
	maxPacketNumExcelCells: packetOptions.maxPacketNumExcelCells,
	multiFileUploadMaxCount: myOptions.multiFileUploadMaxCount,
	multiFileProcessingMaxCount: myOptions.multiFileProcessingMaxCount,
	multiFileDownloadMaxCount: myOptions.multiFileDownloadMaxCount,

	// Import
	maxImportAttachmentSize: 10 * 1024 * 1024, // 10 MB
	maxImportImageSize: 5 * 1024 * 1024, // 5 MB
	maxImportImageDimension: 600, // px
	// Ignore errors that occur on processing rows while doing an import via the Configuration Export
	// and Import so that the import process can continue
	ignoreConfigurationImportErrors: true,
	importInProgressRedisKey: 'configuration_import_in_progress',
	importInProgressTTL: Number(process.env.IMPORT_IN_PROGRESS_CACHE_KEY_TTL)
		|| 60 * 60, // 60 minutes


	// Translations
	missingTranslationIndicator: '¿',
	missingTranslationSeparator: '~',

	// Helpers
	flashDuration: 2000,
	successFlashColor: '#dff0d8',

	// Notify
	notificationDefaultDisplayTime: 7000,
	notificationDefaultFadeOutTime: 2000,

	// Ladda
	spinnerDelay: 100,

	// Picklists
	maxNumberOfItemsForBasicDropdown: 50,
	maxPicklistParents: 4, // Recommended limit

	// Radio
	maxNumberOfDynamicRadioOptions: 10,

	// Calendar
	maxNumberOfCalendarEvents: 1000,

	// Links
	linkEnableLocking: true,
	linkSearchSrollSize: 100,
	linkSearchMatchSize: 100,
	linkSuggestTimeout: 10 * 60 * 1000, // 10 minutes
	// Used when a snapshot record is stuck in "In Progress" status
	// to ignore it and consider complete, unlock the UI for the case.
	linkSnapshotInProgressTimeout: 30 * 60 * 1000, // 30 minutes
	linkSuggestionLockTimeout: 30 * 60 * 1000, // 30 minutes
	linkSuggestionFetchLockRetryInterval: 60 * 1000, // 1 minute
	caseLinkColMaxSize: Number(process.env.CASE_LINK_COL_MAX_SIZE) || 100,

	// Logging

	// Determines if we'll allow the client to persist specific log calls to the server.
	logFromClientToServer: true,

	// When persisting log calls from the client to the server, we want to ensure that
	// not too many of them are made too close together timing wise. This will avoid
	// flooding the server with log calls.
	clientToServerBustCallsAtCount: 25,

	// The number of milliseconds allows between client logging calls
	// that persist to server before they are considered burst calls. If the number
	// of client to server log calls exceeds 'clientToServerBustCallsAtCount' (see above)
	// and all of the calls were made within the number below number of milliseconds of
	// eachother then we'll we'll stop persisting the calls to the server until the calls
	// slow down.
	timeoutThreshholdBetweenPersistentLogs: 200,

	// Form Generation

	// Controls the size of fields in a form. Small, Medium, Large and Extra Large
	// are supported. Each size uses a specific number of Bootstrap grid columns
	// defined here. These are generally consumed by the form-layout-helper.
	formGeneration: {
		fieldWidth: {
			small: 'col-sm-2',
			medium: 'col-sm-3',
			large: 'col-sm-5',
			xlarge: 'col-sm-8',
		},
	},

	/**
	 *		Config for the generated signature.
	 *
	 *		Values defined will be compared against data saved for a given user.
	 *
	 *		Arrays will get consumed as one line in the signature, seperated by a whitespace.
	 */
	generatedUserSignature: {
		fields: [['firstName', 'lastName'], 'email'],
	},

	// Allowed number of login attempts
	maxLoginTries: _.get(authOptions, 'strategies.local.maxTries'),

	// Disable loginip
	disableLoginIp: myOptions.disableLoginIp,

	// Config for password expiry, mostly defined by env vars in /config/index.js
	passwordExpiring: {
		enabled: myOptions['password-expiry'].enabled,
		remindBefore: myOptions['password-expiry'].remindBefore,
		units: myOptions['password-expiry'].units,
		remindUnits: myOptions['password-expiry'].remindUnits,
		generatedMaxAge: myOptions['password-expiry'].generatedMaxAge,
		generatedUnits: myOptions['password-expiry'].generatedUnits,
	},
	//number of seconds before the password reset session code will no longer work 86400 is 1 day.
	passwordResetCodeExpiration: myOptions.passwordResetCodeExpiration,

	// Input masking options
	defaultDecimalMask: {
		alias: 'numeric',
		// Digit restictions
		integerDigits: 13,
		digits: 2,
		// Other Options
		autoGroup: true,
		groupSize: 3,
		groupSeparator: ',',
		nullable: true,
		rightAlign: false,
		unmaskAsNumber: true,
	},
	defaultMoneyMask: {
		alias: 'numeric',
		// Digit restictions
		integerDigits: 13,
		digits: 2,
		// Other options
		autoGroup: true,
		groupSize: 3,
		groupSeparator: ',',
		nullable: true,
		prefix: '$ ',
		rightAlign: false,
		unmaskAsNumber: true,
	},
	defaultNumberMask: {
		alias: 'numeric',
		// Digit restrictions
		integerDigits: 15,
		digits: 0,
		// Other options
		integerOptional: true,
		nullable: true,
		placeholder: '',
		radixPoint: '.', // A non-empty, non-null radixPoint is required for nullable values
		rightAlign: false,
		unmaskAsNumber: true,
	},
	purgeDelay: 5, // Days
	labelsOnTop: false, //Determines whether the field's label should be on top of the field
	subscriberUrl: myOptions.nchan.subscriberUrl,
	sseEnabled: myOptions.nchan.enabled,
	selectizeQuickSearchLoadThrottleMs: '1000',

	// Teams
	teamsTTL: (process.env.TEAMS_TTL && parseInt(process.env.TEAMS_TTL, 10)) || 900000, //ms
	maxNumberOfTeamMembers: 100,

	// Person Party Integration
	personPartyMapping: {
		fields: [
			'firstName',
			'lastName',
			'middleInitial',
			'dateOfBirth',
			'address',
			'city',
			'stateProvince',
			'country',
			'zipCodePostalCode',
			'homePhone',
			'workPhone',
			'emailAddress',
		],
	},

	// Toasts
	toastTimeout: 5000, // ms
	toastPosition: 'toast-bottom-right', // See https://codeseven.github.io/toastr/demo.html
	toastErrorWaitPeriod: 4, // seconds

	// Compute function safe limit
	COMPUTE_LIMIT: 5,

	// Usage Dashboard
	//
	// The "usageDashboardVisibility" option can take on the following values:
	//   - "everything": shows users and data storage odometers as well as dollar overage amounts
	//   - "limited": shows users and data storage odometers, hides dollar overage amounts
	//   - "hidden": does not show usage dashboard
	usageDashboardVisibility: myOptions.usageDashboardVisibility,
	usageDashboardStorageUnits: 'gigabytes',
	usedUsageColor: '#3E3096',
	availableUsageColor: '#e6e6e6',
	exceededUsageColor: '#b00020',

	// User Lists
	maxNumberOfListsPerUser: 100,

	// clients for notification
	defaultClientForSMS: 'twilio',
	defaultClientForPhoneCalls: 'twilio',

	// twilio voice and language
	// for voice options, find more details in https://www.twilio.com/docs/voice/twiml/say#voice
	twilioVoice: 'alice',
	twilioLanguage: 'en',

	// true: twilio will leave a voicemail on an answering machine
	// false: twilio will begin playing the recording
	//        once any answer is detected
	enableTwilioVoicemailDetection: false,

	defaultPhoneNumberMask: {
		countryCode: 1,
		mask: '(*************',
	},

	// Automated Todo Action
	automaticTodoCreation: true,

	// Scroll to top button
	scrollToTopTimeout: 5000, // ms
	scrollToTopExtendedTimeout: 2000, // ms

	// This disables protections against security vulnerabilities. Don't change
	// this unless you have absolutely no choice, and please report the issue
	// to platform.
	disableSearchProtection: true,

	// Form-builder
	formBuilderNestedSectionsLimit: 1,
	formBuilderTabLimit: 6,
	fieldRestoreThreshold: 50,
	displayRuleLimit: 10,
	translatableFieldLimit: myOptions.translatableFieldLimit,
	reservedDynamicFieldNames: ['typeText'],
	enableDynamicCaseDetailsTab: true,

	// Workflows
	maxActiveWorkflowsPerEntity: 10,
	maxWorkflowTimelineNodes: 10,

	// UUID namespace to be used by sync scripts (e.g. sync-workflows). This is a randomly generated
	// v4 UUID that can be replaced as desired to affect the outcome of generated v5 UUIDs.
	uuidv5Namespace: '2d512467-ffb0-44dd-9b58-fc2496c57945',

	// Geo Mapping
	enableGeoMapping: myOptions.enableGeoMapping,
	enableGeoMappingProxyMode: myOptions.enableGeoMappingProxyMode,
	disableGeoMapAttributon: myOptions.disableGeoMapAttributon,
	mapboxAccessToken: myOptions.enableGeoMappingProxyMode
		? myOptions.mapboxFrontAccessToken : myOptions.mapboxAccessToken,
	formattedCoordinatePrecision: 5, // 5 decimal places has an accuracy of ~1 meter
	initialMapCenter: coordinateUtils.getGeoJson({
		coordinates: [-11771950.161242133, 7475325.4132665135],
	}), // Centers the map on Canada (EPSG:3857)
	initialMapZoom: 0, // Zoomed out
	defaultMapZoom: 16,
	defaultMapLayer: 'Streets',
	dropMapPinOnSearch: true,
	enableExternalHelp: myOptions.enableExternalHelp,
	helpUrl: myOptions.helpUrl,
	caseNumberHelpUrl: myOptions.caseNumberHelpUrl,
	accessibilityHelpUrl: myOptions.accessibilityHelpUrl,
	manageTeamHelpUrl: myOptions.manageTeamHelpUrl,

	// Expression
	expression: {
		disabled: myOptions.expression.disabled,
	},

	// Case Number
	caseNumber: {
		maxLength: myOptions.caseNumber.maxLength,
	},

	// Grids
	apiBatchExternalTimeout: myOptions.apiBatchExternalTimeout,

	// Write Only fields
	writeOnlyFieldPlaceholder: '********************************',

	// Aggregate Data
	aggregateFilterLimit: 10,

	// Database
	db: {
		defaultListLimit: 1000,
	},

	// Record Limits
	recordLimits: {
		sys_listItem: {
			warning: parseInt(process.env.PICKLIST_ITEM_WARNING_LIMIT, 10) || 3150,
			limit: parseInt(process.env.PICKLIST_ITEM_LIMIT, 10) || 3500,
		},
		sys_form_layout_type: {
			warning: parseInt(process.env.FIELD_WARNING_LIMIT, 10) || 225,
			limit: parseInt(process.env.FIELD_LIMIT, 10) || 250,
		},
		external_sys_party: {
			limit: parseInt(process.env.PORTAL_PARTY_LIMIT, 10) || 50,
		},
		external_sys_attachment: {
			limit: parseInt(process.env.PORTAL_FILE_LIMIT, 10) || 50,
		},
		sys_grid_view: {
			limit: parseInt(process.env.USER_VIEW_LIMIT, 10) || 20,
		},
	},
	// Portal
	privacyPolicyUrl: myOptions.privacyPolicyUrl,

	// Delay
	monthMs,
	yearMs: 12 * monthMs,

	// Auto-Populate
	autoPopulateFormFieldsToShow: 3,
	autoPopulateMaxFieldsToShow: 10,
	autoPopulateMaxValueLength: 70,
	autoPopulateMaxHighlightsPerField: 1,

	// Data Imports
	dataImportEntities: ['sys/person', 'sys/case', 'sys/user', 'sys/party', 'sys/note', 'sys/email', 'sys/todo', 'sys/attachment'],
	replaceEntities: ['sys/person'],
	dataImportStaticUserLookupFields: ['email', 'nick'],
	dataImportRestrictedFields: ['id', 'caseNumber'],
	mappingMaxFields: myOptions.dataImportMappingMaxFields,
	dataImportActionsHelpUrl: myOptions.dataImportActionsHelpUrl,
	dataImportDeleteColumnName: myOptions.dataImportDeleteColumnName,
	dataImportSsoColumnName: myOptions.dataImportSsoColumnName,
	dataImportCacheLimit: parseInt(process.env.DATA_IMPORT_CACHE_LIMIT, 10) || 100,
	dataImportAttachmentCacheLimit: parseInt(process.env.DATA_IMPORT_CACHE_LIMIT, 10) || 100,
	dataImportParentsCacheLimit: parseInt(process.env.DATA_IMPORT_PARENTS_CACHE_LIMIT, 10) || 500,
	dataImportIntegrationLoggingLimit: Number(process.env.INTEGRATION_LOGGING_LIMIT) || 100,

	// Relating Case Information
	maxRecordLinkSelectableGridRecords:
		parseInt(process.env.MAX_RECORD_LINK_SELECTABLE_GRID_RECORDS, 10) || 50,

	// Autosave Window
	dataMappingGridAutosaveInterval:
		parseInt(process.env.DATA_MAPPING_AUTOSAVE_WINDOW, 10) ?? 500,

	// Postgres db Identifiers
	maxPgIdentifierLength: 63,
	maxYfRefCodeLength: 40,

	dataLookupOptions,

	// Date Picker datelocale Defaults
	defaultLocaleTranslations: {
		en: {
			clear: 'Clear',
			days: 'Sunday,Monday,Tuesday,Wednesday,Thursday,Friday,Saturday',
			daysmin: 'Su,Mo,Tu,We,Th,Fr,Sa',
			daysshort: 'Sun,Mon,Tue,Wed,Thu,Fri,Sat',
			months: 'January,February,March,April,May,June,July,August,September,October,November,December',
			monthsshort: 'Jan,Feb,Mar,Apr,May,Jun,Jul,Aug,Sep,Oct,Nov,Dec',
			today: 'Today',
		},
	},
	telemetry: {
		enabled: myOptions.telemetry.frontend.enabled,
		serviceName: myOptions.telemetry.serviceName,
		samplingRatio: myOptions.telemetry.frontend.samplingRatio,
		// Prevent accidental exposure of backend appInsightsConnectionString
		appInsightsConnectionString: _.isEqual(myOptions.telemetry.appInsightsConnectionString,
			myOptions.telemetry.frontend.appInsightsConnectionString)
			? null : myOptions.telemetry.frontend.appInsightsConnectionString,
	},
};
