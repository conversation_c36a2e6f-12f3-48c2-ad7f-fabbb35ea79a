/**
 * Permissions
 *
 * This works with the permissions configured within each entity. Ability to cross reference
 * parents by name.
 *
 * Example translations for acl hierarchy in languages other than base language:
 *
 *	locale: 'fr',
 *	groups: [{
 *		groupName: 'sys/permission',
 *		caption: 'Permission',
 *		subgroups: [{
 *			subgroupName: 'warning_message',
 *			caption: 'Warning message',
 *			translations: {
 *				<permission>: '<warning message translation>',
 *			},
 *		},
 *		{
 *			subgroupName: 'tooltip',
 *			caption: 'Tooltip',
 *			translations: {
 *				<permission>: '<tooltip translation>',
 *			},
 *		},
 *		{
 *			subgroupName: 'captions',
 *			caption: 'Caption',
 *			translations: {
 *				<permission>: '<caption translation>',
 *			},
 *		}],
 *	}],
 *
 */

module.exports = [
	{
		caption: 'Settings',
		permission: 'settings',
		type: 'group',
	},
	{
		caption: 'Access',
		permission: 'view_access_settings',
		parentPermission: 'settings',
		type: 'group',
	},
	{
		permission: 'view_system_audit_log',
		parentPermission: 'view_access_settings',
		caption: 'View Users Activity',
		tooltip: 'View all users activity under Settings -> Access -> User Activity',
		dependencies: ['view_user_activity_log', 'view_user_details'],
	},
	{
		caption: 'Data',
		permission: 'view_data_settings',
		parentPermission: 'settings',
		type: 'group',
	},
	{
		caption: 'System',
		permission: 'view_system_settings',
		parentPermission: 'settings',
		type: 'group',
	},
	{
		caption: 'Forms',
		permission: 'view_forms_settings',
		parentPermission: 'settings',
		type: 'group',
	},
	{
		caption: 'Reporting',
		type: 'group',
		permission: 'reporting',
	},
	{
		caption: 'Reports',
		type: 'group',
		permission: 'reports',
		parentPermission: 'reporting',
		sequence: 1,
	},
	{
		caption: 'View',
		tooltip: 'View private reports',
		permission: 'view_private_reports',
		parentPermission: 'reports',
		sequence: 1,
	},
	{
		caption: 'Edit',
		tooltip: 'Edit private reports',
		permission: 'edit_private_reports',
		parentPermission: 'reports',
		sequence: 2,
		dependencies: ['view_private_reports'],
	},
	{
		caption: 'Remove',
		tooltip: 'Delete private reports',
		permission: 'remove_private_reports',
		parentPermission: 'reports',
		sequence: 3,
		dependencies: ['view_private_reports'],
	},
	{
		caption: 'Create',
		type: 'group',
		permission: 'create_reports',
		parentPermission: 'reports',
		dependencies: ['edit_private_reports', 'view_private_reports'],
	},
	{
		caption: 'Private',
		tooltip: 'Create private reports',
		permission: 'create_private_reports',
		parentPermission: 'create_reports',
		sequence: 1,
		dependencies: ['edit_private_reports', 'view_private_reports'],
	},
	{
		caption: 'Ad Hoc Reports',
		tooltip: 'Build reports for one time use',
		permission: 'ad_hoc_reports',
		parentPermission: 'create_reports',
		sequence: 2,
		dependencies: ['view_private_reports', 'edit_private_reports', 'create_private_reports'],
	},
	{
		caption: 'Related Reports',
		tooltip: 'Set up co-display, drill down and drill through functionality',
		permission: 'related_reports',
		parentPermission: 'create_reports',
		sequence: 3,
		dependencies: ['view_private_reports', 'edit_private_reports', 'create_private_reports'],
	},
	{
		caption: 'Sub Queries',
		tooltip: 'Create sub query reports',
		permission: 'report_sub_queries',
		parentPermission: 'create_reports',
		sequence: 4,
		dependencies: ['view_private_reports', 'edit_private_reports', 'create_private_reports'],
	},
	{
		caption: 'Public Reports',
		type: 'group',
		permission: 'public_reports',
		parentPermission: 'reports',
		dependencies: ['view_private_reports', 'edit_private_reports'],
	},
	{
		caption: 'View',
		tooltip: 'View public reports',
		permission: 'view_public_reports',
		parentPermission: 'public_reports',
		sequence: 2,
		dependencies: ['view_private_reports', 'edit_private_reports'],
	},
	{
		caption: 'Edit',
		tooltip: 'Edit public reports',
		permission: 'edit_public_reports',
		parentPermission: 'public_reports',
		sequence: 3,
		dependencies: ['view_private_reports', 'edit_private_reports', 'view_public_reports'],
	},
	{
		caption: 'Remove',
		tooltip: 'Delete public reports',
		permission: 'remove_public_reports',
		parentPermission: 'public_reports',
		sequence: 4,
		dependencies: ['view_private_reports', 'edit_private_reports', 'view_public_reports'],
	},
	{
		caption: 'Create',
		tooltip: 'Create public reports',
		permission: 'create_public_reports',
		parentPermission: 'public_reports',
		sequence: 1,
		dependencies: ['view_private_reports', 'edit_private_reports', 'view_public_reports', 'edit_public_reports'],
	},
	{
		caption: 'Dashboards',
		type: 'group',
		permission: 'dashboards',
		parentPermission: 'reporting',
		sequence: 2,
		dependencies: ['view_private_reports'],
	},
	{
		caption: 'View',
		tooltip: 'View personal dashboards',
		permission: 'view_personal_report_dashboard',
		parentPermission: 'dashboards',
		sequence: 2,
		dependencies: ['view_private_reports'],
	},
	{
		caption: 'Edit',
		tooltip: 'Edit personal dashboards',
		permission: 'edit_personal_report_dashboard',
		parentPermission: 'dashboards',
		sequence: 3,
		dependencies: ['view_private_reports', 'view_personal_report_dashboard'],
	},
	{
		caption: 'Code Editor',
		tooltip: 'Edit dashboards in code mode',
		permission: 'edit_dashboards_in_code_mode',
		parentPermission: 'dashboards',
		sequence: 4,
		dependencies: ['view_private_reports', 'view_personal_report_dashboard', 'edit_personal_report_dashboard'],
	},
	{
		caption: 'Remove',
		tooltip: 'Delete personal dashboards',
		permission: 'remove_personal_report_dashboard',
		parentPermission: 'dashboards',
		sequence: 5,
		dependencies: ['view_private_reports', 'view_personal_report_dashboard'],
	},
	{
		caption: 'Create',
		tooltip: 'Create personal dashboards',
		permission: 'create_personal_report_dashboard',
		parentPermission: 'dashboards',
		sequence: 1,
		dependencies: ['view_private_reports', 'view_personal_report_dashboard'],
	},
	{
		caption: 'Public dashboard',
		permission: 'public_report_dashboard',
		type: 'group',
		parentPermission: 'dashboards',
		dependencies: ['view_private_reports', 'view_personal_report_dashboard'],
	},
	{
		caption: 'View',
		tooltip: 'View public dashboards',
		permission: 'view_public_report_dashboard',
		parentPermission: 'public_report_dashboard',
		sequence: 2,
		dependencies: ['view_private_reports', 'view_personal_report_dashboard'],
	},
	{
		caption: 'Edit',
		tooltip: 'Edit public dashboards',
		permission: 'edit_public_report_dashboard',
		parentPermission: 'public_report_dashboard',
		sequence: 3,
		dependencies: ['view_private_reports', 'view_personal_report_dashboard', 'view_public_report_dashboard'],
	},
	{
		caption: 'Remove',
		tooltip: 'Delete public dashboards',
		permission: 'remove_public_report_dashboard',
		parentPermission: 'public_report_dashboard',
		sequence: 4,
		dependencies: ['view_private_reports', 'view_personal_report_dashboard', 'view_public_report_dashboard'],
	},
	{
		caption: 'Create',
		tooltip: 'Create public dashboards',
		permission: 'create_public_report_dashboard',
		parentPermission: 'public_report_dashboard',
		sequence: 1,
		dependencies: ['view_private_reports', 'view_personal_report_dashboard', 'view_public_report_dashboard'],
	},
	{
		caption: 'Themes',
		type: 'group',
		permission: 'report_themes',
		parentPermission: 'reporting',
		sequence: 3,
		dependencies: ['view_private_reports'],
	},
	{
		caption: 'View',
		tooltip: 'View report themes',
		permission: 'view_report_themes',
		parentPermission: 'report_themes',
		sequence: 2,
		dependencies: ['view_private_reports'],
	},
	{
		caption: 'Edit',
		tooltip: 'Edit report themes',
		permission: 'edit_report_themes',
		parentPermission: 'report_themes',
		sequence: 3,
		dependencies: ['view_private_reports', 'view_report_themes'],
	},
	{
		caption: 'Remove',
		tooltip: 'Delete report themes',
		permission: 'remove_report_themes',
		parentPermission: 'report_themes',
		sequence: 4,
		dependencies: ['view_private_reports', 'view_report_themes'],
	},
	{
		caption: 'Create',
		tooltip: 'Create report themes',
		permission: 'create_report_themes',
		parentPermission: 'report_themes',
		sequence: 1,
		dependencies: ['view_private_reports', 'view_report_themes'],
	},
	{
		caption: 'Stories',
		type: 'group',
		permission: 'report_stories',
		parentPermission: 'reporting',
		sequence: 4,
		dependencies: ['view_private_reports'],
	},
	{
		caption: 'View',
		tooltip: 'View report stories',
		permission: 'view_report_stories',
		parentPermission: 'report_stories',
		sequence: 2,
		dependencies: ['view_private_reports'],
	},
	{
		caption: 'Edit',
		tooltip: 'Edit report stories',
		permission: 'edit_report_stories',
		parentPermission: 'report_stories',
		sequence: 3,
		dependencies: ['view_private_reports', 'view_report_stories'],
	},
	{
		caption: 'Remove',
		tooltip: 'Delete report stories',
		permission: 'remove_report_stories',
		parentPermission: 'report_stories',
		sequence: 4,
		dependencies: ['view_private_reports', 'view_report_stories'],
	},
	{
		caption: 'Create',
		tooltip: 'Create report stories',
		permission: 'create_report_stories',
		parentPermission: 'report_stories',
		sequence: 1,
		dependencies: ['view_private_reports', 'view_report_stories'],
	},
	{
		caption: 'Data Discovery',
		type: 'group',
		permission: 'data_discovery',
		parentPermission: 'reporting',
		sequence: 5,
		dependencies: ['view_private_reports'],
	},
	{
		caption: 'Assisted Discovery',
		tooltip: 'Analyze data with assisted discovery',
		permission: 'assisted_discovery',
		parentPermission: 'data_discovery',
		sequence: 1,
		dependencies: ['view_private_reports'],
	},
	{
		caption: 'Instant Insight',
		tooltip: 'Analyze data with instant insights',
		permission: 'instant_insight',
		parentPermission: 'data_discovery',
		sequence: 2,
		dependencies: ['view_private_reports'],
	},
	{
		caption: 'Distribution',
		type: 'group',
		permission: 'distribution',
		parentPermission: 'reporting',
		sequence: 6,
		dependencies: ['view_private_reports'],
	},
	{
		caption: 'Broadcast',
		type: 'group',
		permission: 'broadcast',
		parentPermission: 'distribution',
		dependencies: ['view_private_reports'],
	},
	{
		caption: 'Internal',
		tooltip: 'Broadcast reports',
		permission: 'broadcast_report',
		parentPermission: 'broadcast',
		sequence: 2,
		dependencies: ['view_private_reports'],
	},
	{
		caption: 'External',
		tooltip: 'Broadcast reports to non Case IQ users',
		permission: 'multicast_report',
		parentPermission: 'broadcast',
		sequence: 1,
		dependencies: ['view_private_reports'],
	},
	{
		caption: 'Dashboards',
		tooltip: 'Broadcast dashboards',
		permission: 'dashboards_report',
		parentPermission: 'broadcast',
		sequence: 3,
		dependencies: ['view_private_reports'],
	},
	{
		caption: 'Export',
		type: 'group',
		permission: 'export',
		parentPermission: 'distribution',
		dependencies: ['view_private_reports'],
	},
	{
		caption: 'Export to CSV/Text',
		tooltip: 'Export reports to CSV/Text',
		permission: 'export_reports_to_csv',
		parentPermission: 'export',
		dependencies: ['view_private_reports'],
	},
	{
		caption: 'Export to DOC',
		tooltip: 'Export reports to DOC',
		permission: 'export_reports_to_doc',
		parentPermission: 'export',
		dependencies: ['view_private_reports'],
	},
	{
		caption: 'Export to PDF',
		tooltip: 'Export reports to PDF',
		permission: 'export_reports_to_pdf',
		parentPermission: 'export',
		dependencies: ['view_private_reports'],
	},
	{
		caption: 'Export to XLS',
		tooltip: 'Export reports to XLS',
		permission: 'export_reports_to_excel',
		parentPermission: 'export',
		dependencies: ['view_private_reports'],
	},
	{
		caption: 'Administration',
		type: 'group',
		permission: 'administration',
		parentPermission: 'reporting',
		sequence: 7,
		dependencies: ['view_private_reports'],
	},
	{
		caption: 'Folder Access',
		tooltip: 'Create and manage folders',
		permission: 'report_folder_access',
		parentPermission: 'administration',
		sequence: 1,
		dependencies: ['view_private_reports'],
	},
	{
		caption: 'Bookmark',
		tooltip: 'Add a bookmark for a report',
		permission: 'bookmark_reports',
		parentPermission: 'administration',
		sequence: 2,
		dependencies: ['view_private_reports'],
	},
	{
		caption: 'User Groups',
		tooltip: 'Create and manage groups',
		permission: 'report_group_management',
		parentPermission: 'administration',
		sequence: 3,
		dependencies: ['view_private_reports'],
	},
	{
		caption: 'Configuration Export & Import',
		type: 'group',
		parentPermission: 'view_system_settings',
		permission: 'view_configuration_export_import_settings',
	},
	{
		caption: 'Export',
		permission: 'export_configuration',
		tooltip: 'Export data from Case IQ',
		parentPermission: 'view_configuration_export_import_settings',
	},
	{
		caption: 'Import',
		tooltip: 'Import system configuration data',
		permission: 'import_configuration',
		parentPermission: 'view_configuration_export_import_settings',
	},
	{
		caption: 'Export Data',
		tooltip: 'Raw data export that ignores access control filters',
		parentPermission: 'view_system_settings',
		permission: 'export_data',
		warningMessage: 'This feature allows users to export raw data from the system, ignoring permission\'s restrictions and filters. Are you sure you wish to grant this permission?',
	},
	{
		caption: 'Purge Records',
		type: 'group',
		parentPermission: 'view_system_settings',
		permission: 'schedule_or_purge',
		options: [
			{
				caption: 'Schedule for Purge',
				tooltip: 'Schedule selected records to be purged from the system',
				permission: 'mark_purge',
			},
			{
				caption: 'Purge',
				tooltip: 'Purge records from the system',
				permission: 'purge',
			},
			{
				caption: 'Cancel Purge',
				tooltip: 'Cancel a scheduled purge',
				permission: 'restore_purge',
			},
		],
	},
	{
		caption: 'Upload Files to system',
		disabled: true,
		permission: 'upload_file',
	},
	{
		caption: 'External User',
		disabled: true,
		permission: 'external_user',
	},
	{
		caption: 'Hotline Agent',
		disabled: true,
		permission: 'hotline_agent',
	},
	{
		caption: 'Bypass Case Save Inheritance External',
		disabled: true,
		permission: 'bypass_case_save_inheritance_external',
	},
	{
		caption: 'Bypass Note Load Inheritance External',
		disabled: true,
		permission: 'bypass_note_load_inheritance_external',
	},
	{
		caption: 'Bypass Todo Load Inheritance External',
		disabled: true,
		permission: 'bypass_todo_load_inheritance_external',
	},
	{
		caption: 'Validate New Portal User',
		disabled: true,
		permission: 'validate_portal_user',
	},
	{
		caption: 'View Non-Portal Fields',
		disabled: true,
		permission: 'view_non_portal_fields',
	},
	{
		caption: 'Save Non-Hotline Fields',
		disabled: true,
		permission: 'save_non_hotline_fields',
	},
	{
		caption: 'View Static Data Form Entry Fields',
		disabled: true,
		permission: 'view_static_data_form_entry_fields',
	},
	{
		type: 'group',
		caption: 'Auto-Populate',
		permission: 'auto_populate',
		sequence: 4,
	},
	{
		type: 'group',
		caption: 'Calendar',
		permission: 'calendar',
		options: [
			{
				caption: 'Full Calendar',
				tooltip: 'View full calendar',
				permission: 'view_global_calendar',
			},
			{
				caption: 'Case Calendar',
				tooltip: 'View case calendar',
				permission: 'view_case_calendar',
			},
		],
	},
	{
		caption: 'Usage Dashboard',
		type: 'group',
		parentPermission: 'view_system_settings',
		permission: 'view_usage_dashboard_settings',
		options: [
			{
				caption: 'View',
				tooltip: 'View Usage Dashboard',
				permission: 'view_usage_dashboard',
			},
		],
	},
	{
		caption: 'Data Dictionary',
		type: 'group',
		parentPermission: 'view_system_settings',
		permission: 'data_dictionary_settings',
		options: [
			{
				caption: 'View',
				tooltip: 'View Data Dictionary',
				permission: 'view_data_dictionary',
			},
		],
	},
	{
		caption: 'Form Builder',
		tooltip: 'Form Builder',
		permission: 'form_builder',
		parentPermission: 'view_forms_settings',
		sequence: 1,
	},
];
