/* eslint-disable import/no-dynamic-require */
const fs = require('fs');
const _ = require('lodash');
const options = require('./');

let routes = [
	'/portal',
	'/portal/reportonline',
	'/portal/dialing-instructions',
];

let fileSharingRoutes = [
	'/shared-link/:id',
];

const requestFormRoutes = [
	'/request-form/:id',
];

let configAppRoutes = {};

// Combine any routes defined in the config with the routes defined in this file
const routerFilePath = `${options.appConfigPath}/config/options.routes-portal.js`;
if (fs.existsSync(routerFilePath)) {
	configAppRoutes = require(routerFilePath);
	routes = _.union(routes, configAppRoutes.routes);
	fileSharingRoutes = _.union(fileSharingRoutes, configAppRoutes.fileSharing);
}

module.exports = {
	entityMap: _.assign({}, {
		sys_attachment: '/external/file/{id}',
		sys_case: '/external/case/{id}',
		sys_file: '/external/file/{id}',
		sys_shared_link: '/external/case/{caseId}/files/shared',
		sys_note: '/external/note/{id}',
		sys_party: '/external/party/{id}',

		'-/sys/attachment': '/external/file/{id}',
		'-/sys/case': '/external/case/{id}',
		'-/sys/file': '/external/file/{id}',
		'-/sys/shared_link': '/external/case/{caseId}/files/shared',
		'-/sys/note': '/external/note/{id}',
		'-/sys/party': '/external/party/{id}',
	}, configAppRoutes.entityMap),
	routes,
	fileSharingRoutes,
	requestFormRoutes,
};
