var path = require('path');
var _ = require('lodash');

process.env.PLATFORM_PATH = process.env.PLATFORM_PATH || path.resolve(__dirname + '/../');

var CUSTOMER_CODE = process.env.CUSTOMER_CODE;

var DB_HOST = process.env.DB_HOST || '127.0.0.1';
var ES_HOST = process.env.ES_HOST || '127.0.0.1';

var DB_NAME = process.env.DB_NAME || 'isight';
var DB_USER = process.env.DB_USER || 'postgres';
var DB_PASS = process.env.DB_PASS || 'postgres';
var AUDIT_DB_NAME = process.env.AUDIT_DB_NAME || 'isight_audit';

var ES_INDEX = process.env.ES_INDEX || DB_NAME;
var ES_IMPORT_ASYNC_LIMIT = process.env.ES_IMPORT_ASYNC_LIMIT
	? parseInt(process.env.ES_IMPORT_ASYNC_LIMIT, 10) : 2000;
var ES_RETRY_ON_CONFLICT = process.env.ES_RETRY_ON_CONFLICT
	? parseInt(process.env.ES_RETRY_ON_CONFLICT, 10)
	: 3;

//TODO: default to false, leave true for now to prevent breaking envs
var MAIL_DEBUG = process.env.MAIL_DEBUG === 'true' || process.env.MAIL_DEBUG === '1';
var DISABLE_MAIL_IGNORE_TLS = process.env.DISABLE_MAIL_IGNORE_TLS === 'true'
	|| process.env.DISABLE_MAIL_IGNORE_TLS === '1';
var MAIL_STARTTLS = process.env.MAIL_STARTTLS === 'true'
	|| process.env.MAIL_STARTTLS === '1';

var MAIL_DISABLE_DNS_VALID = process.env.MAIL_DISABLE_DNS_VALID ? true : false;

var MAILSRV_PORT = process.env.MAILSRV_PORT || 2525;
var MAILSRV_DOMAIN = process.env.MAILSRV_DOMAIN || 'caseiq.com';
var MAILSRV_TIMEOUT = process.env.MAILSRV_TIMEOUT
	? parseInt(process.env.MAILSRV_TIMEOUT, 10)
	: 3 * 60 * 1000;

var defaultConfigPath = __dirname + '/../test/fixture';
var APP_CONFIG_PATH = process.env.APP_CONFIG_PATH || defaultConfigPath;
var APP_REQ_TIMEOUT = process.env.APP_REQ_TIMEOUT
	? parseInt(process.env.APP_REQ_TIMEOUT, 10)
	: 15 * 60 * 1000;

var PLATFORM_VERSION = require('../package.json').version;
// eslint-disable-next-line import/no-dynamic-require
var CONFIG_VERSION = require(`${APP_CONFIG_PATH}/package.json`).version;

var QUNIT_ENABLED = process.env.QUNIT_ENABLED === 'true' || process.env.QUNIT_ENABLED === '1';

var INDEX_ATTACHMENTS_ENABLED = (process.env.INDEX_FILE_ATTACHMENTS === 'true'
	|| process.env.INDEX_FILE_ATTACHMENTS === '1');

// deprecated, LOGINUSERNOPASSWORD is enforced to prevent managing Yellowfin user password
/*
var YF_LOGIN_NO_PASSWORD = process.env.YF_LOGIN_NO_PASSWORD === 'true' ||
	process.env.YF_LOGIN_NO_PASSWORD === '1';
 */

var PASSWORD_EXPIRY_DISABLED = process.env.PASSWORD_EXPIRY_DISABLED === 'true'
	|| process.env.PASSWORD_EXPIRY_DISABLED === '1';
var PASSWORD_EXPIRY_GENERATED_AGE = process.env.PASSWORD_EXPIRY_GENERATED_AGE
	? parseInt(process.env.PASSWORD_EXPIRY_GENERATED_AGE, 10)
	: 1;
var PASSWORD_EXPIRY_MAX_AGE = process.env.PASSWORD_EXPIRY_MAX_AGE
	? parseInt(process.env.PASSWORD_EXPIRY_MAX_AGE, 10)
	: 90;
var PASSWORD_EXPIRY_UNITS = process.env.PASSWORD_EXPIRY_UNITS || 'days';
var PORTAL_PASSWORD_EXPIRY_MAX_AGE = process.env.PORTAL_PASSWORD_EXPIRY_MAX_AGE
	? parseInt(process.env.PORTAL_PASSWORD_EXPIRY_MAX_AGE, 10)
	: PASSWORD_EXPIRY_MAX_AGE;
var PORTAL_PASSWORD_EXPIRY_UNITS = process.env.PORTAL_PASSWORD_EXPIRY_UNITS
	|| PASSWORD_EXPIRY_UNITS;
var PASSWORD_EXPIRY_REMIND_BEFORE = process.env.PASSWORD_EXPIRY_REMIND_BEFORE
	? parseInt(process.env.PASSWORD_EXPIRY_REMIND_BEFORE, 10)
	: 5;

var ALLOWED_REFERERS = process.env.ALLOWED_REFERERS
	? process.env.ALLOWED_REFERERS.split(/,| /) : [];

const YF_VIEWS_SLOW_TIME_MS = parseInt(process.env.YF_VIEWS_SLOW_TIME_MS, 10) ?? 3000;

var INTEGRATION_PATH_FILES = process.env.INTEGRATION_PATH_FILES || '/usr/local/data/externaldata/sftp/';
var INTEGRATION_PATH_FILES_PROCESSED = process.env.INTEGRATION_PATH_FILES_PROCESSED || '/usr/local/data/externaldata/sftp/processed/';
var DATA_IMPORT_MAX_FILE_SIZE = parseInt(process.env.DATA_IMPORT_MAX_FILE_SIZE, 10)
|| 150 * 1024 * 1024; // 150 MB;
var DATA_IMPORT_MAX_RECORD_COUNT = process.env.DATA_IMPORT_MAX_RECORD_COUNT || '500000';
var SYSTEM_TIME_ZONE = process.env.SYSTEM_TIME_ZONE
	? process.env.SYSTEM_TIME_ZONE
	: Intl.DateTimeFormat().resolvedOptions().timeZone;

var IMPORT_PLUGIN_BATCH_SIZE = process.env.IMPORT_PLUGIN_BATCH_SIZE
	? Number(process.env.IMPORT_PLUGIN_BATCH_SIZE)
	: 200;

var INTEGRATION_FILE_PURGE_DELAY = process.env.INTEGRATION_FILE_PURGE_DELAY || '8 days';

var DATA_MIGRATION_DEFAULT_FILE_SEPARATOR = process.DATA_MIGRATION_DEFAULT_FILE_SEPARATOR || ';';
var DATA_MIGRATION_DEFAULT_MAPPING_SEPARATOR = process.DATA_MIGRATION_DEFAULT_MAPPING_SEPARATOR || ':';
var DATA_MIGRATION_DEFAULT_COLUMN_SEPARATOR = process.DATA_MIGRATION_DEFAULT_COLUMN_SEPARATOR || ',';
var DATA_MIGRATION_DEFAULT_SOURCE_TARGET_SEPARATOR = process.DATA_MIGRATION_DEFAULT_SOURCE_TARGET_SEPARATOR || '=';

// Database connections that are re-used across the config.

var elasticsearch = {
	retryOnConflict: ES_RETRY_ON_CONFLICT,
	connection: {
		host: ES_HOST + ':9200',
		index: ES_INDEX,
		apiVersion: '1.7',
		requestTimeout: (process.env.ES_REQ_TIMEOUT
			? parseInt(process.env.ES_REQ_TIMEOUT, 10)
			: 3 * 60 * 1000),
	},
};

var postgresql = {
	host: DB_HOST,
	port: 5432,
	database: DB_NAME,
	name: DB_NAME,
	username: DB_USER,
	user: DB_USER,
	password: DB_PASS,
};

var nchanConfig = require('./options.nchan.js');

// baseUrl must not be used with a trailing /, so it's removed if present
var BASE_URL = _.trimEnd(process.env.BASE_URL, '/') || 'http://127.0.0.1:8000';

var QUARTZ_HOST = process.env.QUARTZ_HOST || '127.0.0.1';
var QUARTZ_PORT = process.env.QUARTZ_PORT || '8090';
// HACK backwards compat override
var QUARTZ_URL = process.env.QUARTZ_URL || (process.env.QUARTZ_HOST ? 'http://' + QUARTZ_HOST + ':' + QUARTZ_PORT + '/scheduler/api' : 'http://127.0.0.1:8090/scheduler/api');

var TELEMETRY_SAMPLING_RATIO = Number(process.env.TELEMETRY_SAMPLING_RATIO) || 0.3;

function getBulkImportESRefresh() {
	if (process.env.BULK_IMPORT_ES_REFRESH === 'wait_for') {
		return 'wait_for';
	}
	if (process.env.BULK_IMPORT_ES_REFRESH === 'true') {
		return true;
	}
	return false;
}

function parseAsNumber(envVar) {
	const parsedVal = parseInt(envVar, 10);
	if (_.isNaN(parsedVal)) return null;
	return parsedVal;
}

module.exports = {
	customerCode: CUSTOMER_CODE,
	platformVersion: PLATFORM_VERSION,
	configVersion: CONFIG_VERSION,
	db: {
		// Some databases (e.g. Oracle) impose a limit on the number of expressions in a list. That
		// means if a SQL query contains a list with more itmes than the limit, then the query will
		// fail. Therefore, we want our code to be aware of that, and partition queries accordingly.
		maxNumExpressionsInList: Number(process.env.DB_MAX_NUM_EXPRESSIONS_IN_LIST ?? '1000'),
	},
	appRequestTimeout: APP_REQ_TIMEOUT,
	devServerPort: Number(process.env.DEV_SERVER_PORT) || 9010,
	clientPulseInterval: Number(process.env.CLIENT_PULSE_INTERVAL) || 18 * 100000, // 30 minutes
	optionCacheRefreshInterval: Number(process.env.OPTION_CACHE_REFRESH_INTERVAL)
		|| 15 * 60 * 1000, // 15 minutes
	fieldsMemoryCacheTtl: process.env.FIELDS_MEMORY_CACHE_TTL_MS
		? parseInt(process.env.FIELDS_MEMORY_CACHE_TTL_MS, 10)
		: 900000, // 15min
	fieldRedisCacheTtl: process.env.FIELDS_REDIS_CACHE_TTL
		? parseInt(process.env.FIELDS_REDIS_CACHE_TTL, 10)
		: 7200, // 2 hours
	appConfigPath: path.resolve(APP_CONFIG_PATH),
	qUnitEnabled: QUNIT_ENABLED,
	csrfEnabled: process.env.CSRF !== '0' && process.env.CSRF !== 'false',
	indexAttachmentsEnabled: INDEX_ATTACHMENTS_ENABLED,
	samlType: process.env.SAML_TYPE,
	sameSite: process.env.SAME_SITE === 'false' ? false : process.env.SAME_SITE || 'lax',
	disableSecureCookies: process.env.DISABLE_SECURE_COOKIES === 'true',
	disableSsePolling: process.env.DISABLE_SSE_POLLING === 'true',
	baseUrl: BASE_URL,
	formsOnIntakeLimit: Number(process.env.FORMS_ON_INTAKE_LIMIT) || 5,
	formsOnPortalLimit: Number(process.env.FORMS_ON_PORTAL_LIMIT) || 5,
	// Domains that are allowed to be referers of the app
	// NOTE: For iframes & IE, only the first domain will be allowed an iframe.
	allowedRefererDomains: ALLOWED_REFERERS,
	ignoreAllowedReferers: process.env.ALLOWED_REFERERS === 'true'
		|| process.env.ALLOWED_REFERERS === '1',

	mockLinkApi: process.env.MOCK_LINK_API === 'true'
		|| process.env.MOCK_LINK_API === '1',

	eventEmitterMaxListeners: 300,
	// seneca service configurations
	filestore: {
		fileStoreVolumePath: process.env.FILE_STORE_VOLUME_PATH,
		fileStorageDriver: process.env.FILE_STORAGE_DRIVER || 'local',
	},
	'elasticsearch': _.extend({}, elasticsearch, {
		pingTimeout: 5000,
	}),
	'postgresql-store': postgresql,

	audit: {
		host: DB_HOST,
		port: 8125,
		file: 'audit.log',

		'elasticsearch': elasticsearch,
		postgresql: _.extend({}, postgresql, {
			database: AUDIT_DB_NAME,
			name: AUDIT_DB_NAME,
			map: {
				'-/audit/-': '*',
			},
		}),
	},

	'purge': {
		'purgeDelayUnit': process.env.PURGE_DELAY_UNIT || 'd',
		'purgeDelay': process.env.PURGE_DELAY || 5,
		// Common expressions:
		// every5Minutes: '0 0/5 * * * ? *',
		// every15Minutes: '0 0/15 * * * ? *',
		// everyDayAt4AM: '0 0 4 * * ? *'
		'cronExpression': process.env.PURGE_CRON_EXPRESSION || '0 0 4 * * ? *',
	},

	removalOfTemporaryFilesInPdfPlugin: {
		'cronExpression': process.env.PDF_REMOVAL_OF_TEMPORARY_FILES_CRON_EXPRESSION
			|| '0 0/15 * * * ? *',
	},

	/**
	 * Default is at 07:00AM UTC everyday.
	 * Goal is to run when the app is at low activity.
	 * Since default timezone is ETC, the default cron schedule is at 02:00AM/03:00AM
	 */
	calculateUsageStatsCron: process.env.CALCULATE_USAGE_STATS_CRON || '0 0 7 * * ? *',

	/**
	 * Default to 07:00AM UTC everyday.
	 */
	syncCaseAgeCron: process.env.SYNC_CASE_AGE_CRON || '0 0 7 * * ? *',
	/**
	 * Default is at 5 am every sunday morning
	 */
	metadataDeleteCron: process.env.METADATA_DELETE_CRON || '0 0 5 ? * SUN *',

	dbViews: {
		/**
			 * Default is every 30 minutes
			 */
		updateViewsCron: process.env.UPDATE_VIEWS_CRON || '0 */30 * ? * *',
		platformViewsDirectory: path.join(__dirname, '..', 'script', 'database', 'views'),
		configViewsDirectory: path.join(APP_CONFIG_PATH, 'script', 'database', 'views'),
		cacheKey: 'updateDbViews',
		createYellowfinUsernameIndex: process.env.YF_CREATE_USERNAME_INDEX === 'true',
		yfViewsSlowTimeMs: YF_VIEWS_SLOW_TIME_MS,
		updateInProgressCacheKey: 'yf_view_update_in_progress',
		updateInProgressTTL: Number(process.env.IMPORT_IN_PROGRESS_CACHE_KEY_TTL)
			|| 24 * 60 * 60, // 24 hours
	},

	'quartz-scheduler': {
		listen: process.env.QUARTZ_PORT || 8001,
		callbackURL: process.env.QUARTZ_CALLBACK || 'http://docker.for.mac.localhost:8001/api/job',
		quartzURL: QUARTZ_URL,
		quartzHealthStatus: `http://${QUARTZ_HOST}:${QUARTZ_PORT}/status`,
		quartzCheck: `http://${QUARTZ_HOST}:${QUARTZ_PORT}/check`,
	},

	settings: {
		spec: {},
	},
	main: {
		port: process.env.SERVER_PORT || 8000,
		'public': '/public',
	},
	'mail-server': {
		port: MAILSRV_PORT,
		timeout: MAILSRV_TIMEOUT,
		domain: MAILSRV_DOMAIN,
		banner: 'caseiq',
		secure: false,
		debug: MAIL_DEBUG,
		disableDNSValidation: MAIL_DISABLE_DNS_VALID,
		disableSTARTTLS: !MAIL_STARTTLS,
		ignoreTLS: !DISABLE_MAIL_IGNORE_TLS,
		maxAllowedUnauthenticatedCommands: process.env.MAX_ALLOWED_UNAUTHENTICATED_COMMANDS || Infinity,
	},

	'data-import': {
		asyncLimitIndex: ES_IMPORT_ASYNC_LIMIT,
		peoplesoft: {
			ftps: {
				protocol: process.env.PEOPLESOFT_FTP_PROTOCOL || 'sftp',
				host: process.env.PEOPLESOFT_FTP_HOST || '127.0.0.1',
				port: process.env.PEOPLESOFT_FTP_PORT || 22,
				username: process.env.PEOPLESOFT_FTP_USER || 'none',
				password: process.env.PEOPLESOFT_FTP_PASSWORD || 'none',
				cwd: process.env.PEOPLESOFT_FTP_CWD || '/',
				pattern: process.env.PEOPLESOFT_FTP_PATTERN || '*',
			},
			downloadDir: process.env.PEOPLESOFT_DOWNLOAD_DIR || '/tmp/peoplesoft/',
		},
	},

	redis: {
		host: process.env.REDIS_HOST || '127.0.0.1',
		port: process.env.REDIS_PORT ? parseInt(process.env.REDIS_PORT, 10) : 6379,
		username: process.env.REDIS_USER || 'default',
		password: process.env.REDIS_PASS, // default to undefined for backwards compatibility
		db: process.env.REDIS_DATABASE ? parseInt(process.env.REDIS_DATABASE, 10) : 0,
	},

	ftp: {
		host: '127.0.0.1', // required
		username: 'user', // required
		password: 'test', // required
		protocol: 'sftp', // optional, values : 'ftp', 'sftp', 'ftps',... default is 'ftp'
		port: 22, // optional,
		ftpPath: '',
		localPath: '/your/local/path/',
	},

	'password-expiry': {
		enabled: !PASSWORD_EXPIRY_DISABLED,
		generatedMaxAge: PASSWORD_EXPIRY_GENERATED_AGE,
		generatedUnits: process.env.PASSWORD_EXPIRY_GENERATED_UNITS || 'days',
		maxAge: PASSWORD_EXPIRY_MAX_AGE,
		units: PASSWORD_EXPIRY_UNITS,
		portalMaxAge: PORTAL_PASSWORD_EXPIRY_MAX_AGE,
		portalUnits: PORTAL_PASSWORD_EXPIRY_UNITS,
		remindBefore: PASSWORD_EXPIRY_REMIND_BEFORE,
		remindUnits: process.env.PASSWORD_EXPIRY_REMIND_UNITS || 'days',
		emailSubject: {
			passwordExpiring: process.env.PASSWORD_EXPIRY_SUBJ_EXPIRING,
			passwordExpired: process.env.PASSWORD_EXPIRY_SUBJ_EXPIRED,
		},
		mail: {
			baseUrl: BASE_URL,
		},
	},
	// Pub/Sub server
	nchan: nchanConfig,

	passwordResetCodeExpiration: process.env.PASSWORD_RESET_EXPIRY
		? parseInt(process.env.PASSWORD_RESET_EXPIRY, 10)
		: 86400,
	passwordResetLimit: process.env.PASSWORD_RESET_LIMIT
		? parseInt(process.env.PASSWORD_RESET_LIMIT, 10)
		: 5,
	passwordResetLimitWindow: process.env.PASSWORD_RESET_LIMIT_WINDOW
		? parseInt(process.env.PASSWORD_RESET_LIMIT_WINDOW, 10)
		: 10 * 60 * 1000, // 10 minutes (in ms)

	matomo: {
		enabled: ['true', '1'].includes(process.env.MATOMO_ENABLED),
		// Using URL class to normalize the url.
		server: process.env.MATOMO_SERVER ? new URL(process.env.MATOMO_SERVER).href : null,
		trackerPath: process.env.MATOMO_TRACKER_PATH || 'matomo.php',
		siteId: process.env.MATOMO_SITE_ID || '1',
	},

	linkSearchScrollTimeout: process.env.LINK_SEARCH_SCROLL_TIMEOUT || '3m',

	// Redis
	recordLockNamespace: 'lock:',
	// Prevent login ip from being saved, loaded, or listed.
	disableLoginIp: process.env.DISABLE_LOGIN_IP === 'true',
	cacheSearchRequests: process.env.ENABLE_SEARCH_REQUEST_CACHE !== 'false',
	debugNamespaces: process.env.DEBUG || '',

	// Bulk Import
	bulkImportESRefresh: getBulkImportESRefresh(),
	bulkImportESBatchSize: process.env.BULK_IMPORT_ES_BATCH_SIZE
		? parseInt(process.env.BULK_IMPORT_ES_BATCH_SIZE, 10)
		: 1000,

	// Mapping Config
	enableGeoMapping: process.env.ENABLE_GEO_MAPPING === 'true',
	enableGeoMappingProxyMode: process.env.GEO_MAPPING_PROXY_MODE !== 'false',
	disableGeoMapAttributon: process.env.DISABLE_GEO_MAP_ATTRIBUTION === 'true',
	mapTileUrl: process.env.MAP_TILE_URL || 'https://api.mapbox.com/v4/{tileset_id}/{z}/{x}/{y}.{format}',
	mapStyleUrl: process.env.MAP_STYLE_URL || 'https://api.mapbox.com/styles/v1/{username}/{style_id}',
	mapboxAccessToken: process.env.GEO_MAPPING_SECRET_KEY,
	mapboxFrontAccessToken: process.env.GEO_MAPPING_FRONTEND_KEY,
	mapboxAccessTokenHash: process.env.GEO_MAPPING_ACCESS_TOKEN_HASH,

	enableSelectedFieldsApi: process.env.ENABLE_SELECTED_FIELDS_API !== 'false',

	pdftronEnabled: process.env.ENABLE_PDFTRON === 'true',

	enableExternalHelp: process.env.DISABLE_EXTERNAL_HELP !== 'true',
	helpUrl: process.env.EXTERNAL_HELP_URL || 'https://help.caseiq.com/',
	caseNumberHelpUrl: process.env.EXTERNAL_CASE_NUMBER_HELP_URL || 'https://help.caseiq.com/en_US/system/case-number-format',
	accessibilityHelpUrl: process.env.EXTERNAL_ACCESSIBILITY_HELP_URL || 'https://help.caseiq.com/en_US/accessibility-statement',
	manageTeamHelpUrl: process.env.EXTERNAL_MANAGE_TEAM_HELP_URL || 'https://help.caseiq.com/en_US/cases/manage-team-for-a-case',

	dynamicFieldsCaseFilterLimit: Number(process.env.DYNAMIC_FIELDS_CASE_FILTER_LIMIT) || 5,

	apiBatchExternalTimeout: (Number(process.env.API_BATCH_EXTERNAL_TIMEOUT) || 40) * 1000, // 40 secs

	expression: {
		disabled: process.env.DISABLE_EXPRESSION === 'true',
		singleVmMemoryLimit: Number(process.env.EXPRESSION_SINGLE_VM_MEMORY_LIMIT) || 265, //MBs
		vmMemoryLimit: Number(process.env.EXPRESSION_VM_MEMORY_LIMIT) || 64, //MBs
		vmPoolSize: Number(process.env.EXPRESSION_VM_POOL_SIZE) || 3,
		vmMaxQueueLength: Number(process.env.EXPRESSION_VM_MAX_QUEUE_LENGTH),
		vmRuntimeMaxUses: Number(process.EXPRESSION_VM_RUNTIME_MAX_USES) || 20,
		vmTimeout: Number(process.EXPRESSION_VM_TIMEOUT_MS) || 30000, // 30s
		singleVmTimeout: Number(process.EXPRESSION_SINGLE_VM_TIMEOUT_MS) || 30000, // 30s
		// For testing purposes only, should never be disabled
		disableVM: process.env.EXPRESSION_DISABLE_VM === 'true',
		cacheTtl: Number(process.env.EXPRESSION_CACHE_TTL_MS) || 300000, // 5 mins
	},

	caseNumber: {
		legacyCaseNumbering: process.env.LEGACY_CASE_NUMBERING === 'true',
		maxLength: Number(process.env.CASE_NUMBER_MAX_LENGTH) || 60,
	},

	translations: {
		cacheTtl: (Number(process.env.TRANSLATION_CACHE_TTL_MINS) || 60) * 60 * 1000, // 60 mins
	},

	vectorStorage: {
		disabled: process.env.DISABLE_VECTOR_STORAGE === 'true',
		indexer: {
			interval: process.env.AZURE_SEARCH_VECTOR_INDEXER_INTERVAL ?? 'PT5M', // 5 mins
			executionEnvironment: process.env.AZURE_SEARCH_VECTOR_INDEXER_EXECUTION_ENVIRONMENT,
			indexedFileNameExtensions: process.env.AZURE_SEARCH_VECTOR_INDEXED_FILENAME_EXTENSIONS ?? '.pdf, .docx, .txt',
		},
		embedding: {
			chunkSizeBytes: Number(process.env.AZURE_SEARCH_VECTOR_CHUNK_SIZE) || 2000,
			chunkOverlapBytes: Number(process.env.AZURE_SEARCH_VECTOR_CHUNK_OVERLAP) || 500,
		},
	},

	outcomeAssistant: {
		generationTTL: Number(process.env.OUTCOME_ASSISTANT_GENERATION_TTL_MS)
			|| 1000 * 60 * 60 * 1, // 1 hour
	},

	llm: {
		maxConcurrency: 10,
		maxRetries: Number(process.env.OUTCOME_ASSISTANT_MAX_RETRIES) || 2,
	},

	entityDynamicLayout: {
		layoutConfigurationName: process.env.INITIAL_FORM_BUILDER_CONFIG,
	},

	// Portal
	privacyPolicyUrl: process.env.PRIVACY_POLICY_URL || 'https://www.caseiq.com/privacy-policy-portalusers/',
	enablePortalAnonCountryFilter: process.env.ENABLE_PORTAL_ANON_COUNTRY_FILTER === 'true',
	// Legacy Env Vars
	portalEnabled: process.env.ENABLE_PORTAL === 'true',
	twoWayPortalEnabled: process.env.ENABLE_TWO_WAY_PORTAL === 'true',

	// Export configuration
	exportConfigurationTimeout: Number(process.env.EXPORT_CONFIGURATION_TIMEOUT)
		|| 1000 * 60 * 60 * 1, // 1 hour

	usageDashboardVisibility: process.env.USAGE_DASHBOARD_VISIBILITY || 'limited',

	restrictEditFieldLimit: Number(process.env.RESTRICT_EDIT_FIELD_LIMIT) || 20,
	translatableFieldLimit: Number(process.env.TRANSLATABLE_FIELD_LIMIT) || 25,

	multiFileUploadMaxCount: Number(process.env.MULTI_FILE_UPLOAD_MAX_COUNT) || 20,
	multiFileProcessingMaxCount: Number(process.env.MULTI_FILE_PROCESSING_MAX_COUNT) || 100,
	multiFileDownloadMaxCount: Number(process.env.MULTI_FILE_DOWNLOAD_MAX_COUNT) || 20,

	// DATA IMPORTS
	dataImportMappingMaxFields: Number(process.env.DATA_IMPORT_MAPPING_FIELD_LIMIT) || 40,
	integrationPathFiles: INTEGRATION_PATH_FILES,
	integrationPathFilesProcessed: INTEGRATION_PATH_FILES_PROCESSED,
	dataImportMaxFileSize: DATA_IMPORT_MAX_FILE_SIZE,
	dataImportMaxRecordCount: DATA_IMPORT_MAX_RECORD_COUNT,
	staticAllowedEncodings: ['ascii', 'utf8', 'utf-8', 'utf16le', 'utf-16le', 'ucs2', 'ucs-2', 'base64', 'base64url', 'latin1', 'binary', 'hex'],
	systemTimeZone: SYSTEM_TIME_ZONE,
	dataImportActionsHelpUrl: process.env.DATA_IMPORT_ACTIONS_HELP_URL || 'https://help.caseiq.com/en_US/system/add-or-edit-a-data-import#schedule-a-data-import-5',
	integrationFilesPurgeDelay: INTEGRATION_FILE_PURGE_DELAY,
	dataImportDeleteColumnName: process.env.DATA_IMPORT_DELETE_COLUMN_NAME || 'delete',
	dataImportSsoColumnName: process.env.DATA_IMPORT_SSO_COLUMN_NAME || 'sso user',

	// DATA IMPORT PLUGIN CONFIGURATION DATA
	importPluginConfig: {
		batchSize: IMPORT_PLUGIN_BATCH_SIZE,
	},

	telemetry: {
		debug: process.env.TELEMETRY_DEBUG,
		enabled: ['true', '1'].includes(process.env.TELEMETRY_ENABLED),
		samplingRatio: TELEMETRY_SAMPLING_RATIO,
		platformVersion: PLATFORM_VERSION,
		configVersion: CONFIG_VERSION,
		serviceURL: BASE_URL,
		serviceName: CUSTOMER_CODE || 'unknown',
		namespace: process.env.TELEMETRY_NAMESPACE || 'com.caseiq',
		collectorUrl: process.env.TELEMETRY_COLLECTOR_URL || 'http://localhost:4318',
		metricsMeterName: process.env.TELEMETRY_METRICS_METER_NAME || 'CaseIQMetrics',
		metricsExportIntervalMs: Number(process.env.TELEMETRY_METRICS_EXPORT_INTERVAL_MS) || 60000,
		collectorHealthUrl: process.env.TELEMETRY_COLLECTOR_HEALTH_URL || 'http://localhost:13133',
		collectorScraperHealthUrl: process.env.TELEMETRY_COLLECTOR_SCRAPER_HEALTH_URL || 'http://localhost:13133',
		dailyAggregateStatsEnabled: process.env.TELEMETRY_DAILY_AGGREGATE_STATS_ENABLED !== 'false',
		appInsightsConnectionString: process.env.TELEMETRY_APP_INSIGHTS_CONNECTION_STRING,
		frontend: {
			enabled: process.env.TELEMETRY_FRONTEND_ENABLED === 'true',
			samplingRatio: Number(process.env.TELEMETRY_FRONTEND_SAMPLING_RATIO)
				|| TELEMETRY_SAMPLING_RATIO,
			appInsightsConnectionString: process.env.TELEMETRY_FRONTEND_APP_INSIGHTS_CONNECTION_STRING,
		},
	},

	devOps: {
		apiVer: '5.2.2',
	},

	// Data migration separators
	dataMigrationCLIOptions: {
		defaultFileSeparator: DATA_MIGRATION_DEFAULT_FILE_SEPARATOR,
		defaultMappingSeparator: DATA_MIGRATION_DEFAULT_MAPPING_SEPARATOR,
		defaultColumnSeparator: DATA_MIGRATION_DEFAULT_COLUMN_SEPARATOR,
		defaultSourceTargetSeparator: DATA_MIGRATION_DEFAULT_SOURCE_TARGET_SEPARATOR,
	},

	disableWebhookValidation: process.env.DISABLE_WEBHOOK_VALIDATION === 'true',
	reindexPeriod: {
		// can be a boolean or a list of comma separated ents
		disabled: process.env.REINDEX_PERIOD_DISABLED ?? 'sys/attachment',
		defaultPeriodMonths: parseAsNumber(process.env.REINDEX_PERIOD_DEFAULT) ?? 24,
		entities: [
			'audit/log',
			'sys/attachment',
			'sys/transition_history',
			'sys/event',
		],
	},

	dataLookupCacheNameSpaces: {
		accessTokenNamespace: 'data-lookup-access-token::',
		mappedFieldsNamespace: 'data-lookup-mapped-fields::',
		tableConfigNamespace: 'data-lookup-table-config::',
	},
};
