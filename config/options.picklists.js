module.exports = {
	'email_templates': {
		caption: 'Email Templates',
		parents: ['supported_languages'],
		parentAttributes: ['locale'],
		url: '/standard_response',
		text: 'name',
		value: 'name',
	},
	'standard_response': {
		caption: 'Standard Response',
		parents: ['supported_languages'],
		parentAttributes: ['locale'],
		url: '/standard_response',
		text: 'name',
		value: 'name',
	},
	'templates': {
		caption: 'Templates',
		parents: ['supported_languages'],
		parentAttributes: ['locale'],
		url: '/template',
		text: 'name',
		value: 'id',
	},
	'packets': {
		caption: 'Packets',
		url: '/packet',
		text: 'name',
		value: 'id',
	},
	'selected-files-for-packet': {
		caption: 'Selected files',
		url: '/attachment?caseId={packetAttachmentId__caseId}',
		text: 'files[0].name',
		value: 'id',
		filter: 'filterSelectedFilesForPacket',
	},
	// Picklist Module
	'cancel_reasons': {
		caption: 'Cancel Reasons',
		text: 'value',
		value: 'value',
	},
	'notify_methods': {
		caption: 'Notify Methods',
		text: 'value',
		value: 'value',
		locked: true,
	},
	'case_types': {
		caption: 'Case Types',
		text: 'value',
		value: 'value',
		disableNewPicklistItems: true,
	},
	'record_sources': {
		caption: 'Record sources',
		text: 'caption',
		value: 'value',
		locked: true,
	},
	'year_format': {
		caption: 'Year Format',
		text: 'caption',
		value: 'value',
		locked: true,
	},
	'month_format': {
		caption: 'Month Format',
		text: 'caption',
		value: 'value',
		locked: true,
	},
	'sequence_period': {
		caption: 'Sequence Period',
		text: 'caption',
		value: 'value',
		locked: true,
	},
	'portal_updates_options': {
		caption: 'Portal Updates Options',
		text: 'caption',
		value: 'value',
		locked: true,
	},
	'countries': {
		caption: 'Countries',
		text: 'value',
		value: 'value',
		external: true,
	},
	'currencies': {
		caption: 'Currencies',
		text: 'value',
		value: 'value',
	},
	'user_roles': {
		caption: 'User Roles',
		url: '/user_role',
		text: 'name',
		value: 'id',
		subText: 'description',
	},
	'file_kinds': {
		caption: 'File Kinds',
		text: 'value',
		value: 'value',
		locked: true,
		filter: 'filterOutPacketItems',
		external: true,
	},
	'fiscal_year_start_options': {
		caption: 'Fiscal Year Start Month',
		text: 'caption',
		value: 'value',
	},
	'note_types': {
		caption: 'Note Types',
		text: 'value',
		value: 'value',
		external: true,
	},
	'party_types': {
		caption: 'Party Types',
		text: 'value',
		value: 'value',
		external: true,
		locked: false,
		disableNewPicklistItems: true,
	},
	'date_formats': {
		caption: 'Date Formats',
		text: 'value',
		value: 'value',
		filter: 'filterDtmAutomaticOption',
	},
	'date_time_formats': {
		caption: 'Date Time Formats',
		text: 'value',
		value: 'value',
	},
	'primitive_states': {
		caption: 'Primitive States',
		text: 'value',
		value: 'value',
		locked: true,
	},
	'reassign_reasons': {
		caption: 'Case Reassign Reasons',
		text: 'value',
		value: 'value',
	},
	'states_and_provinces': {
		caption: 'States and Provinces',
		text: 'value',
		value: 'value',
		external: true,
	},
	'summary_download_types': {
		caption: 'Summary Download File Types',
		text: 'value',
		value: 'value',
		locked: true,
		features: ['outcomeAssistant'],
	},
	'template_file_types': {
		caption: 'Template File Types',
		text: 'value',
		value: 'value',
		locked: true,
	},
	'attachment_generated_file_types': {
		caption: 'File Types',
		text: 'value',
		value: 'value',
		locked: true,
		filter: 'filterOutPacketItems',
	},
	'packet_generation_methods': {
		caption: 'Document generation method',
		text: 'translation',
		subText: 'relatedData.subText',
		value: 'value',
		locked: true,
	},
	'todo_types': {
		caption: 'To-Do Types',
		text: 'value',
		value: 'value',
	},
	'email_rule_types': {
		caption: 'Email Rule Types',
		text: 'value',
		value: 'value',
		locked: true,
	},
	'todo_statuses': {
		caption: 'To-Do Statuses',
		text: 'value',
		value: 'value',
		locked: true,
	},
	'appointment_statuses': {
		caption: 'Appointment Statuses',
		text: 'value',
		value: 'value',
		locked: true,
	},
	'case_notification_events': {
		caption: 'Case Notification Events',
		text: 'value',
		value: 'value',
		locked: true,
	},
	'email_directions': {
		caption: 'Email Directions',
		text: 'value',
		value: 'value',
		locked: true,
	},
	'export_types': {
		caption: 'Export Types',
		text: 'value',
		value: 'value',
		locked: true,
	},
	'grid_export_query_selection': {
		caption: 'Grid Export Query Selection',
		text: 'value',
		value: 'value',
		locked: true,
	},
	'grid_export_column_selection': {
		caption: 'Grid Export Column Selection',
		text: 'value',
		value: 'value',
		locked: true,
	},
	'picklist_types': {
		caption: 'Picklist Types',
		url: '/picklist_type',
		text: 'caption',
		value: 'name',
		locked: true,
	},
	'layout_element_types': {
		caption: 'Layout Element Types',
		text: 'value',
		value: 'value',
		locked: true,
	},
	'layout_names': {
		caption: 'Layout Names',
		url: '/layout_names',
		parentAttributes: ['entityName'],
		text: 'caption',
		value: 'name',
	},
	'field_kind_flags': {
		caption: 'Field Kind Flags',
		text: 'value',
		value: 'value',
		locked: true,
	},
	'radio_button_orientations': {
		caption: 'Radio Button Orientations',
		text: 'value',
		value: 'value',
		locked: true,
	},
	'link_match_statuses': {
		caption: 'Link Match Statuses',
		text: 'value',
		value: 'value',
		locked: true,
	},
	'link_statuses': {
		caption: 'Link Statuses',
		text: 'value',
		value: 'value',
		locked: true,
	},
	'link_types': {
		caption: 'Link Types',
		text: 'value',
		value: 'value',
		locked: true,
	},
	'event_types': {
		caption: 'Event Types',
		text: 'value',
		value: 'value',
		locked: true,
	},
	'user_status': {
		caption: 'User Status',
		text: 'value',
		value: 'value',
		locked: true,
	},
	'iso_languages': {
		caption: 'ISO Languages',
		text: 'caption',
		value: 'value',
		locked: true,
	},
	'user_types': {
		caption: 'User Types',
		text: 'value',
		value: 'value',
		locked: true,
	},
	'responsible_user_types': {
		caption: 'Responsible User Types',
		text: 'value',
		value: 'value',
		locked: true,
		filter: 'filterResponsibleUserTypes',
	},
	'packet_table_of_contents_template_kinds': {
		caption: 'Template',
		text: 'value',
		value: 'value',
		locked: true,
	},
	'date_types': {
		caption: 'Date Types',
		text: 'value',
		value: 'value',
		locked: true,
	},
	'purge_reasons': {
		caption: 'Purge Reasons',
		text: 'value',
		value: 'value',
	},
	'todo_automation_events': {
		caption: 'To-Do Automation Events',
		text: 'value',
		value: 'value',
		locked: true,
	},
	'themes': {
		caption: 'Themes',
		url: '/theme',
		text: 'name',
		value: 'id',
		subText: 'description',
	},
	'link_generation_options': {
		caption: 'Link Generation Options',
		text: 'value',
		value: 'value',
		locked: true,
	},
	'teams': {
		caption: 'Teams',
		url: '/team',
		text: 'name',
		value: 'id',
	},
	'link_party_with_person_methods': {
		caption: 'Link Party With Person Methods',
		text: 'value',
		value: 'value',
		locked: true,
	},
	'case_capture_redirect_options': {
		caption: 'Case Capture Redirect Options',
		text: 'value',
		value: 'value',
		locked: true,
	},
	'trigger_types': {
		caption: 'Trigger Types',
		text: 'value',
		value: 'value',
		locked: true,
	},
	'graph_types': {
		caption: 'Graph Types',
		text: 'value',
		value: 'value',
		locked: true,
	},
	'date_filters': {
		caption: 'Date Filters',
		text: 'value',
		value: 'value',
		locked: true,
	},
	'usage_stats_categories': {
		caption: 'Usage Statistic Categories',
		url: '/distinct_usage_stats_categories',
		text: 'caption',
		value: 'category',
	},
	'usage_stats_keys': {
		caption: 'Usage Statistic Keys',
		parents: ['usage_stats_categories'],
		parentAttributes: ['category'],
		url: '/distinct_usage_stats_keys',
		text: 'caption',
		value: 'key',
	},
	'usage_stats_datasets': {
		caption: 'Usage Statistic Datasets',
		parents: ['usage_stats_categories', 'usage_stats_keys'],
		parentAttributes: ['category', 'key'],
		url: '/distinct_usage_stats_datasets',
		text: 'caption',
		value: 'dataset',
	},
	'usage_stats_datasets_single_parent': {
		caption: 'Usage Statistic Datasets',
		parents: ['usage_stats_categories'],
		parentAttributes: ['category'],
		url: '/distinct_usage_stats_datasets',
		text: 'caption',
		value: 'dataset',
	},
	'usage_stats_concatenated_sets': {
		caption: 'Usage Statistic Sets',
		parents: ['usage_stats_categories', 'usage_stats_keys'],
		parentAttributes: ['category', 'key'],
		url: '/distinct_usage_stats_sets',
		text: 'caption',
		value: 'set',
	},
	'usage_stats_concatenated_sets_single_parent': {
		caption: 'Usage Statistic Sets',
		parents: ['usage_stats_categories'],
		parentAttributes: ['category'],
		url: '/distinct_usage_stats_sets',
		text: 'caption',
		value: 'set',
	},
	// The picklist in picklist-properties-view for picking parent picklist
	'form_builder_parent_picklist_name': {
		caption: 'Dependent Picklist',
		text: 'caption',
		value: 'id',
		locked: true,
		maxItems: 1,
	},
	'custom_notification_methods': {
		caption: 'Custom Notification Methods',
		text: 'value',
		value: 'value',
		locked: true,
		filter: 'filterCustomNotificationMethod',
	},
	'entity_transitions': {
		caption: 'Entity transitions',
		url: '/transition_entity/{entity.base}_{entity.name}/{id}', //TOD change after ITPL-14286
		text: 'name',
		value: 'id',
		cache: false,
	},
	'transition_reasons': {
		caption: 'Transition Reasons',
		dataField: 'reasons',
		text: 'value',
		value: 'value',
	},
	'restore_reasons': {
		caption: 'Case Restore Reasons',
		text: 'value',
		value: 'value',
	},
	'rule_types': {
		caption: 'Rule Types',
		text: 'value',
		value: 'value',
	},
	'rule_frequency': {
		features: ['dataImport'],
		caption: 'Frequency',
		text: 'value',
		value: 'value',
	},
	'rule_week_days': {
		features: ['dataImport'],
		caption: 'Weekdays',
		text: 'value',
		value: 'value',
	},
	'rule_month_days': {
		features: ['dataImport'],
		caption: 'Days of the Month',
		text: 'value',
		value: 'value',
	},
	'record_events': {
		caption: 'Standard Workflow Events',
		filter: 'ruleRecordEventsFilter',
		text: 'caption',
		value: 'value',
		locked: true,
	},
	'primary_state_types': {
		caption: 'Status Type',
		text: 'value',
		value: 'value',
		locked: true,
	},
	'time_formats': {
		caption: 'Time Formats',
		text: 'caption',
		value: 'value',
		locked: true,
	},
	'postal_code_countries': {
		caption: 'Postal Code Countries',
		text: 'caption',
		value: 'value',
		locked: true,
	},
	'date_constraints': {
		caption: 'Date Constraints',
		text: 'caption',
		value: 'value',
		locked: true,
		filter: 'dateConstraintFilter',
	},
	'shared_link_status': {
		caption: 'Shared Link Status',
		text: 'value',
		value: 'value',
		locked: true,
	},
	'read_receipt_options': {
		caption: 'Read Receipt Options',
		text: 'value',
		value: 'value',
		locked: true,
	},
	'email_sources': {
		caption: 'Email Sources',
		text: 'value',
		value: 'value',
		locked: true,
	},
	'information_box_types': {
		caption: 'Information Box Types',
		text: 'value',
		value: 'value',
		locked: true,
	},
	'data_import': {
		caption: 'Data Imports',
		url: '/data_import',
		text: 'name',
		value: 'id',
		subText: 'importEntity',
		locked: true,
	},
	'sso_protocols': {
		caption: 'SSO Protocols',
		text: 'value',
		value: 'value',
		locked: true,
		features: ['SSO'],
	},
	'aggregation_methods': {
		caption: 'Aggregation Methods',
		value: 'value',
		text: 'translation',
		subText: 'relatedData.subText',
		locked: true,
	},
	'sso_identifier_formats': {
		caption: 'SSO Identifier Formats',
		text: 'value',
		value: 'value',
		features: ['SSO'],
	},
	'sso_authn_context_classes': {
		caption: 'SSO Authentication Context Classes',
		text: 'value',
		value: 'value',
		features: ['SSO'],
	},
	'portal_user_sub_types': {
		caption: 'Portal User Sub Types',
		text: 'value',
		value: 'value',
		locked: true,
	},
	'create_portal_account': {
		caption: 'Create Portal Account',
		text: 'value',
		value: 'value',
		locked: true,
		subText: 'relatedData.subText',
	},
	'portal_user_remain_anonymous': {
		caption: 'Portal User Remain Anonymous',
		text: 'value',
		value: 'value',
		locked: true,
	},
	'portal_user_provide_email': {
		caption: 'Portal User Provide Email',
		text: 'value',
		value: 'value',
		locked: true,
	},
	'portal_submission_access': {
		caption: 'Portal Submission Access',
		text: 'value',
		value: 'value',
		locked: true,
		features: ['twoWayPortal'],
	},
	'entity_types': {
		caption: 'Entity Types',
		text: 'translation',
		value: 'value',
		subText: 'relatedData.subText',
		locked: true,
	},
	'intake_methods': {
		caption: 'Intake Methods',
		text: 'caption',
		value: 'value',
		locked: true,
	},
	'case_file_inclusion_kinds': {
		caption: 'Case File Inclusion Kinds',
		text: 'value',
		value: 'value',
		locked: true,
	},
	'dynamic_kinds': {
		caption: 'Dynamic Kinds',
		text: 'value',
		value: 'value',
		subText: 'relatedData.subText',
		locked: true,
	},
	'intake_translation_statuses': {
		caption: 'Intake Translation Statuses',
		text: 'value',
		value: 'value',
		locked: true,
		features: ['intakeTranslation'],
	},
	'case_states': {
		caption: 'Case States',
		text: 'value',
		value: 'value',
		locked: true,
	},
	'layout_statuses': {
		caption: 'Layout Statuses',
		text: 'value',
		value: 'value',
		locked: true,
	},
	'schedule_occurrence': {
		caption: 'Schedule Ocurrence',
		text: 'value',
		value: 'value',
		locked: true,
	},
	'workflow_statuses': {
		caption: 'Workflow Statuses',
		text: 'value',
		value: 'value',
		locked: true,
	},
	'rule_entity_submission_types': {
		caption: 'Rule Entity Submission Types',
		text: 'value',
		value: 'value',
		locked: true,
	},
	'supported_languages': {
		caption: 'Supported Languages',
		text: 'caption',
		value: 'value',
		locked: true,
		external: true,
	},
	'data_import_methods': {
		caption: 'Methods',
		text: 'methods',
		features: ['dataImport'],
		value: 'value',
	},
	'data_import_types': {
		caption: 'Types',
		text: 'types',
		features: ['dataImport'],
		value: 'value',
	},
	'data_import_file_delimiters': {
		caption: 'File Delimiters',
		text: 'types',
		features: ['dataImport'],
		value: 'value',
	},
	'data_import_update_methods': {
		caption: 'Update Methods',
		text: 'updateMethods',
		features: ['dataImport'],
		value: 'value',
	},
	'data_import_file_formats': {
		caption: 'File Formats',
		text: 'fileFormats',
		features: ['dataImport'],
		value: 'value',
	},
	'data_import_file_encodings': {
		caption: 'File Encodings',
		text: 'value',
		features: ['dataImport'],
		value: 'value',
		locked: true,
	},
	'rules': {
		caption: 'Rules',
		features: ['dataImport'],
		url: '/rule',
		text: 'name',
		value: 'id',
	},
	'phone_number_country_codes': {
		caption: 'Phone Number Country Codes',
		text: 'value',
		value: 'value',
		locked: true,
		external: true,
	},
	'copilot_types': {
		caption: 'Copilot Types',
		text: 'caption',
		value: 'value',
		locked: true,
		features: ['outcomeAssistant'],
	},
	'relationship_types': {
		caption: 'Record Link Types',
		url: '/relationship_type',
		text: 'verb',
		value: 'compositeId',
	},
	'http_methods': {
		caption: 'HTTP Methods',
		text: 'caption',
		value: 'value',
		locked: true,
	},
	'authentication_methods': {
		caption: 'Authentication Methods',
		text: 'caption',
		value: 'value',
		locked: true,
	},
	'lookup_methods': {
		caption: 'Lookup Methods',
		text: 'caption',
		value: 'value',
		locked: true,
	},
	'mapping_methods': {
		caption: 'Mapping method',
		text: 'caption',
		value: 'value',
		locked: true,
	},
	'access_request_statuses': {
		caption: 'Access Request Statuses',
		text: 'value',
		value: 'value',
		locked: true,
		features: ['requestCaseAccess'],
	},
	'external_access_grant_types': {
		caption: 'External Access Grant Types',
		text: 'caption',
		value: 'value',
		locked: true,
		features: ['externalCaseAccess'],
	},
	'grant_types': {
		caption: 'Grant Types',
		text: 'caption',
		value: 'value',
		locked: true,
	},
	'client_authentication_options': {
		caption: 'Client Authentication Option',
		text: 'caption',
		value: 'value',
		locked: true,
	},
	'case_linking_options': {
		caption: 'Case Linking Options',
		features: ['requestCaseAccess'],
		text: 'value',
		value: 'value',
		locked: true,
	},
	'request_statuses': {
		caption: 'Request Statuses',
		features: ['nonUserCollaboration'],
		text: 'value',
		value: 'value',
		locked: true,
	},
};
