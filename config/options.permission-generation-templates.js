module.exports = {
	external: {
		name: 'External',
		permissionCodeFn(code) {
			return `${code}_external`;
		},
		conditions: [{
			attributes: {
				externalRecord: true,
			},
		}],
	},
	external_not_created_by: {
		name: 'External Not Created By',
		permissionCodeFn(code) {
			return `${code}_external_not_created_by`;
		},
		conditions: [{
			attributes: {
				externalRecord: true,
				createdBy: '{!user.id}',
				reportedBy: '{!user.id}',
			},
		}],
	},
	external_case_not_created_by: {
		name: 'External Case Not Created By',
		permissionCodeFn(code) {
			return `${code}_external_case_not_created_by`;
		},
		conditions: [{
			attributes: {
				externalRecord: true,
				caseId__createdBy: '{!user.id}',
				caseId__reportedBy: '{!user.id}',
				caseId__createdBySession: '{!user.authenticatedBySession}',
			},
		}],
	},
};
