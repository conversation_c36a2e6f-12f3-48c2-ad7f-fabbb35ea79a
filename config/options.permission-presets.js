const _ = require('lodash');

/**
 * Filters entity for all fields with flag (flagName) set to true
 *
 * @param {Object} obj The entity object to filter
 * @param {Object} context The context containing all the field definitions
 * @param {Array} joinFieldsWhitelist Name of fields to be joined
 * @param {String} flagName The flag to filter by
 * @param {Boolean} allowSystemFields Select fields of kind 'system' regardless of flag
 * @returns {Object} Entity fields with flag set to true
 */
function filterFieldsByFlag(
	obj, context, joinFieldsWhitelist, flagName, allowSystemFields = false,
) {
	const entDefFields = context?.entDef?.fields();
	const filter = {};
	_.each(entDefFields, (fieldDef) => {
		if (fieldDef[flagName] === true || (allowSystemFields && fieldDef.kind === 'system')) {
			filter[fieldDef.field] = true;
		}
	});
	if (_.isArray(joinFieldsWhitelist) && !_.isEmpty(joinFieldsWhitelist)) {
		_.each(joinFieldsWhitelist, (joinField) => {
			filter[joinField] = true;
		});
	}
	return filter;
}

module.exports = {
	requireCaseLoadInheritance() {
		return {
			name: 'Inherit case load acl',
			roles: ['bypass_inherited_acl'],
			control: 'required',
			actions: ['load', 'list'],
			conditions: [{
				attributes: {
					'!caseId': null,
				},
			}, 'sys/case::{caseId}::load'],
		};
	},
	requireCaseSaveInheritance() {
		return {
			name: 'Inherit case save acl',
			roles: ['bypass_inherited_acl'],
			control: 'required',
			actions: ['save_new', 'save_existing', 'remove'],
			conditions: [{
				attributes: {
					'!caseId': null,
				},
			}, {
				fn(obj, context) {
					const hasBypassRole = _.includes(context?.user?.perm?.roles, 'bypass_case_save_inheritance_external');
					const isNew = !obj?.id;
					const isExternal = obj?.externalRecord === true;
					const bypassCaseInheritance = hasBypassRole && isNew && isExternal;

					// apply inheritance if true
					return { ok: !bypassCaseInheritance };
				},
				selectedFields(opts, callback) {
					const fields = ['id', 'externalRecord'];
					return callback(null, fields);
				},
			}, 'sys/case::{caseId}::save'],
		};
	},
	requireWorkflowLoadInheritance: function () {
		return {
			name: 'Inherit workflow load acl',
			roles: ['bypass_inherited_acl'],
			control: 'required',
			actions: ['load', 'list'],
			conditions: [{
				attributes: {
					'!workflowId': null,
				},
			}, 'sys/workflow::{workflowId}::load'],
		};
	},
	requireWorkflowSaveInheritance: function () {
		return {
			name: 'Inherit workflow save acl',
			roles: ['bypass_inherited_acl'],
			control: 'required',
			actions: ['save_new', 'save_existing', 'remove'],
			conditions: [{
				attributes: {
					'!workflowId': null,
				},
			}, 'sys/workflow::{workflowId}::save'],
		};
	},
	requireRuleLoadInheritance: function () {
		return {
			name: 'Inherit rule load acl',
			roles: ['bypass_inherited_acl'],
			control: 'required',
			actions: ['load', 'list'],
			conditions: [{
				attributes: {
					'!ruleId': null,
				},
			}, 'sys/rule::{ruleId}::load'],
		};
	},
	requireRuleSaveInheritance: function () {
		return {
			name: 'Inherit rule save acl',
			roles: ['bypass_inherited_acl'],
			control: 'required',
			actions: ['save_new', 'save_existing', 'remove'],
			conditions: [{
				attributes: {
					'!ruleId': null,
				},
			}, 'sys/rule::{ruleId}::save'],
		};
	},
	filterMarkForPurge: function() {
		return {
			name: 'mark for purge',
			roles: ['mark_purge'],
			control: 'filter',
			actions: ['save_new', 'save_existing'],
			conditions: [],
			filters: {
				'pendingPurgeDate': false,
			},
		};
	},
	filterPurge: function() {
		return {
			name: 'purge',
			roles: ['purge'],
			control: 'filter',
			actions: ['save_new', 'save_existing'],
			conditions: [],
			filters: {
				'datePurged': false,
			},
		};
	},
	filterPortalFields: function (opts = {}) {
		const { joinFieldsWhitelist } = opts;
		return {
			name: 'View non portal fields',
			roles: ['view_non_portal_fields'],
			control: 'filter',
			actions: ['list', 'load', 'remove'],
			conditions: [],
			filters: {
				fn: (obj, context) => filterFieldsByFlag(obj, context, joinFieldsWhitelist, 'showOnPortal'),
			},
		};
	},
	filterChildPortalFields: function(opts = {}) {
		const joinFieldsWhitelist = [
			'caseId__caseNumber',
			'caseId__sysActive',
			'caseId__sysSubmitted',
			'caseId__createdDate',
			'caseId__externalRecord',
		];
		if (_.isArray(opts.joinFieldsWhitelist) && !_.isEmpty(opts.joinFieldsWhitelist)){
			joinFieldsWhitelist.push(...opts.joinFieldsWhitelist);
		}
		return {
			name: 'View non portal fields',
			roles: ['view_non_portal_fields'],
			control: 'filter',
			actions: ['list', 'load', 'remove'],
			conditions: [],
			filters: {
				fn: (obj, context) => filterFieldsByFlag(obj, context, joinFieldsWhitelist, 'showOnPortal'),
			},
		};
	},
	filterHotlineFields: function (opts = {}) {
		const { joinFieldsWhitelist, allowSystemFields = true } = opts;
		return {
			name: 'Save non hotline fields',
			roles: ['save_non_hotline_fields'],
			control: 'filter',
			actions: ['save_new'],
			conditions: [],
			filters: {
				fn: (obj, context) => filterFieldsByFlag(obj, context, joinFieldsWhitelist, 'showOnHotline', allowSystemFields),
			},
		};
	},
	filterChildHotlineFields: function () {
		const joinFieldsWhitelist = [
			'caseId__caseNumber',
			'caseId__sysActive',
			'caseId__sysSubmitted',
			'caseId__createdDate',
			'caseId__externalRecord',
		];
		return {
			name: 'Save non hotline fields',
			roles: ['save_non_hotline_fields'],
			control: 'filter',
			actions: ['save_new'],
			conditions: [],
			filters: {
				fn: (obj, context) => filterFieldsByFlag(obj, context, joinFieldsWhitelist, 'showOnHotline', true),
			},
		};
	},
	filterRestrictEditFields: function (opts = {}) {
		const {
			roles,
			conditions,
			actions,
			features,
		} = opts;
		return {
			name: 'Save Restricted Fields',
			roles,
			control: 'filter',
			actions,
			conditions,
			features,
			filters: {
				fn: (obj, context) => {
					const entDefFields = context?.entDef?.fields();
					const filter = {};
					_.each(entDefFields, (fieldDef) => {
						if (fieldDef.restrictEdit) {
							filter[fieldDef.field] = false;
						}
					});
					return filter;
				},
			},
		};
	},
	filterViewSourceData: function () {
		return {
			name: 'View Source Data of any Record',
			roles: ['view_source_data'],
			actions: ['list', 'load'],
			filters: {
				sourceTag: false,
				sourceData: false,
				sourceId: false,
			},
			control: 'filter',
		};
	},
	requireRecordLinkingInheritance: function () {
		return {
			name: 'Inherit Linked Records ACL',
			roles: [],
			actions: [],
			conditions: [],
			dynamicAclFn(context) {
				const { entities } = context;
				const baseAclDef = {
					roles: ['bypass_inherited_acl'],
					actions: ['load', 'list', 'save_new', 'save_existing', 'remove'],
					control: 'required',
				};
				const dynamicEntityData1Conditions = [
					{ attributes: { '!entity1Type': 'sys/case' } },
					{ attributes: { '!entity1Type': 'sys/person' } },
				];
				const dynamicEntityData2Conditions = [
					{ attributes: { '!entity2Type': 'sys/case' } },
					{ attributes: { '!entity2Type': 'sys/person' } },
				];
				const linkableEntities = entities.getRecordLinkingEntities();
				const inheritanceAclDefsPairs = _.map(linkableEntities, (linkableEntDef) => {
					const entity1AclDef = _.extend({
						name: `Inherit Linked ${linkableEntDef.caption} 1 ACL`,
						conditions: [{
							attributes: {entity1Type: linkableEntDef.entityCanon},
						}, `${linkableEntDef.entityCanon}::{entity1Id}::load`],
					}, baseAclDef);
					const entity2AclDef = _.extend({
						name: `Inherit Linked ${linkableEntDef.caption} 2 ACL`,
						conditions: [{
							attributes: {entity2Type: linkableEntDef.entityCanon},
						}, `${linkableEntDef.entityCanon}::{entity2Id}::load`],
					}, baseAclDef);

					// add to the dynamic-entity-data conditions
					dynamicEntityData1Conditions.push({ attributes: { '!entity1Type': linkableEntDef.entityCanon } });
					dynamicEntityData2Conditions.push({ attributes: { '!entity2Type': linkableEntDef.entityCanon } });

					return [entity1AclDef, entity2AclDef];
				});
				// create dynamic-entity-data inheritance acl defs
				const dynamicEntityDataAclDefs = [
					_.extend({
						name: 'Inherit Dynamic Entity Data 1 ACL',
						conditions: [...dynamicEntityData1Conditions, 'sys/dynamic_entity_data::{entity1Id}::load'],
					}, baseAclDef),
					_.extend({
						name: 'Inherit Dynamic Entity Data 2 ACL',
						conditions: [...dynamicEntityData2Conditions, 'sys/dynamic_entity_data::{entity2Id}::load'],
					}, baseAclDef),
				];
				return _.flatten([...inheritanceAclDefsPairs, ...dynamicEntityDataAclDefs]);
			},
		};
	},
	requireParentRecordLinkingInheritance: function () {
		return {
			name: 'Inherit Linked Records Parent ACL',
			roles: [],
			actions: [],
			conditions: [],
			dynamicAclFn(context) {
				const { entities } = context;
				const baseAclDef = {
					roles: ['bypass_inherited_acl'],
					actions: ['load', 'list', 'save_new', 'save_existing', 'remove'],
					control: 'required',
					// only want these acl definitions to apply to elasticsearch because it cannot handle more
					// than one level of inheritance
					skipDbAcl: true,
					skipSenecaPerm: true,
				};
				const linkableEntities = entities.getRecordLinkingEntities();
				let linkableEntitiesParents = [];
				_.forEach(linkableEntities, (linkableEntDef) => {
					if (linkableEntDef.parents.length > 0) {
						const parentEntCanons = _.map(linkableEntDef.parents, ({ entity }) => `${entity.base}/${entity.name}`);
						linkableEntitiesParents.push(...parentEntCanons);
					}
				});
				linkableEntitiesParents = _.uniq(linkableEntitiesParents);
				const inheritanceAclDefsPairs = _.map(linkableEntitiesParents, (parentEnt) => {
					const parentEntDef = entities.get(parentEnt);
					const entity1AclDef = _.extend({
						name: `Inherit Linked ${parentEntDef.caption} Parent 1 ACL`,
						conditions: [{
							attributes: {entity1ParentType: parentEntDef.entityCanon},
						}, `${parentEntDef.entityCanon}::{entity1ParentId}::load`],
					}, baseAclDef);
					const entity2AclDef = _.extend({
						name: `Inherit Linked ${parentEntDef.caption} Parent 2 ACL`,
						conditions: [{
							attributes: {entity2ParentType: parentEntDef.entityCanon},
						}, `${parentEntDef.entityCanon}::{entity2ParentId}::load`],
					}, baseAclDef);

					return [entity1AclDef, entity2AclDef];
				});
				return _.flatten(inheritanceAclDefsPairs);
			},
		};
	},
	requireRootRecordLinkingInheritance: function () {
		return {
			name: 'Inherit Linked Records Root ACL',
			roles: [],
			actions: [],
			conditions: [],
			dynamicAclFn(context) {
				const { entities } = context;
				const baseAclDef = {
					roles: ['bypass_inherited_acl'],
					actions: ['load', 'list', 'save_new', 'save_existing', 'remove'],
					control: 'required',
					// only want these acl definitions to apply to elasticsearch because it cannot handle more
					// than one level of inheritance
					skipDbAcl: true,
					skipSenecaPerm: true,
				};
				const linkableEntities = entities.getRecordLinkingEntities();
				const linkableEntitiesParents = [];
				_.forEach(linkableEntities, (linkableEntDef) => {
					if (linkableEntDef.parents.length > 0) {
						const parentEntCanons = _.map(linkableEntDef.parents, ({ entity }) => `${entity.base}/${entity.name}`);
						linkableEntitiesParents.push(...parentEntCanons);
					}
				});
				let linkableEntitiesRoots = [];
				_.forEach(_.uniq(linkableEntitiesParents), (parentEnt) => {
					const entDef = entities.get(parentEnt);
					if (entDef.parents?.length > 0) {
						const parentEntCanons = _.map(entDef.parents, ({ entity }) => `${entity.base}/${entity.name}`);
						linkableEntitiesRoots.push(...parentEntCanons);
					}
				});
				linkableEntitiesRoots = _.uniq(linkableEntitiesRoots);
				const inheritanceAclDefsPairs = _.map(linkableEntitiesRoots, (parentEnt) => {
					const parentEntDef = entities.get(parentEnt);
					const entity1AclDef = _.extend({
						name: `Inherit Linked ${parentEntDef.caption} Root 1 ACL`,
						conditions: [{
							attributes: {entity1RootType: parentEntDef.entityCanon},
						}, `${parentEntDef.entityCanon}::{entity1RootId}::load`],
					}, baseAclDef);
					const entity2AclDef = _.extend({
						name: `Inherit Linked ${parentEntDef.caption} Root 2 ACL`,
						conditions: [{
							attributes: {entity2RootType: parentEntDef.entityCanon},
						}, `${parentEntDef.entityCanon}::{entity2RootId}::load`],
					}, baseAclDef);

					return [entity1AclDef, entity2AclDef];
				});
				return _.flatten(inheritanceAclDefsPairs);
			},
		};
	},
};
