const utils = require('../utils.js');
const basePropertiesForm = require('../base-properties-form.js');

module.exports = utils.formExtend(basePropertiesForm, {
	name: 'number-properties-form',
	elements: [
		{
			type: 'section',
			defaultFieldsLayout: {
				labelOnTop: false,
			},
			elements: [
				{
					field: 'max',
					labelWidth: 'col-sm-12 col-sm-8',
					width: 'col-xs-12 col-sm-4',
				},
				{
					field: 'min',
					labelWidth: 'col-sm-12 col-sm-8',
					width: 'col-xs-12 col-sm-4',
				},
			],
		},
	],
});
