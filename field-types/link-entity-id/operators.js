const {extend} = require('lodash');
const idType = require('../id');
const codeOperators = require('../code').getOperators('es');
const {es: commonEsOperators} = require('../common-operators.js');

module.exports = {
	es: extend({}, idType.getOperators('es'), {
		contains: codeOperators.contains,
		contains_partial: commonEsOperators.containsPartialFormatted,
		does_not_contain: codeOperators.does_not_contain,
		contains_exact_words: codeOperators.contains_exact_words,
		does_not_contain_exact_words: codeOperators.does_not_contain_exact_words,
	}),
	obj: idType.getOperators('obj'),
	db: idType.getOperators('db'),
};
