/* global $appData */

/**
*
*		Party Selection Multiple Component
*
*		Component for creating a multi party search input
*
*/
require('selectize');
const $ = require('jquery');
const _ = require('lodash');
const utils = require('../../public/lib/utils.js');
const BaseComponent = require('../base-component.js');
const entities = require('../../entities');
const multiFieldFunctions = require('../../public/config/options.multi-field-functions.js');
const PartySearchModalView = require('../party/search-modal-view.js');

module.exports = BaseComponent.extend({
	field: null,
	caseIdField: null,
	selectize: null,
	searchCaption: null,
	template: require('./component-tmpl.dust'),
	className: 'party-multiple-component-tmpl',
	events: {
		'mousedown .view-all button': 'onViewAllClick',
	},
	/**
	*       On Init
	*
	*       Initialize a party multiple search component instance.
	*/
	onInit(opts) {
		this.field = opts.field;
		const { fieldDef, fieldDef: { typeOptions = {}} } = this;
		this.searchCaption = utils.translateKey({
			groupName: 'placeholders',
			key: typeOptions.searchCaption || 'search_party',
		});

		this.labelField = 'partyName';
		this.secondaryLabelField = typeOptions.secondaryLabelField || 'emailAddress';

		this.selectizeAttribute = `${this.field}__${this.labelField}`;
		this.selectizeSecondaryAttribute = `${this.field}__${this.secondaryLabelField}`;
		this.deletedDateAttribute = `${this.field}__deletedDate`;

		this.isModelCase = this.model.getEntityCanon() === 'sys/case';
		this.caseIdField = this.isModelCase ? 'id' : _.get(typeOptions, 'caseIdField', 'caseId');

		this.maxItems = fieldDef?.maxItems ?? null;
		this.dependentMaxItemField = typeOptions?.dependentMaxItemField ?? null;
		this.maxItemFunction = typeOptions?.maxItemFunction ?? null;
		this.maxOptions = 10;
	},
	/**
	*		Locals
	*
	*		Variables to pass to the dust template.
	*/
	locals() {
		return {
			field: this.field,
			ariaLabel: _.get(this, 'fieldDef.ariaLabel') || _.get(this, 'fieldDef.caption', this.field),
		};
	},
	haveValuesChanged(newValues) {
		const currentValues = this.model.get(this.field);
		return !_.isEqual(newValues, currentValues);
	},
	updateModelValues(newValues) {
		const partiesData = this.selectize.options;
		// Include the model selected from the search view if it's missing from the options
		if (this.selectedSearchModel && !partiesData[this.selectedSearchModel.id]) {
			partiesData[this.selectedSearchModel.id] = this.selectedSearchModel.attributes;
		}
		const set = {};
		set[this.field] = newValues;
		set[this.selectizeAttribute] = _.map(newValues, (val) => {
			return _.get(partiesData[val], this.labelField);
		});
		set[this.selectizeSecondaryAttribute] = _.map(newValues, (val) => {
			return _.get(partiesData[val], this.secondaryLabelField);
		});
		this.model.set(set);
	},
	/**
	*		On Selectize Value Change
	*
	*		Called when the selected value changed in selectize.
	*/
	onSelectizeItemsChange(...args) {
		let newValues = _.clone(this.selectize.items);
		if (_.isEmpty(newValues)) newValues = null;
		// Avoid creating unnecessary events when the value didn't really change
		if (this.haveValuesChanged(newValues)) {
			this.updateModelValues(newValues);
			BaseComponent.prototype.onEditableValueChange.apply(this, args);
		}
	},
	/**
	*		On Model Change
	*
	*		Function triggered whenever the model changes, either from within this component
	*		or external functions.
	*/
	onModelChange() {
		const newValues = this.model.get(this.field);
		// Skip if values are the same
		if (_.isEqual(newValues, this.selectize.items)) return;
		// Extra information
		const mainLabelValues = this.model.get(this.selectizeAttribute) || [];
		const secondaryLabelValues = this.model.get(this.selectizeSecondaryAttribute) || [];
		// Set selectize
		const missingOptions = _.difference(newValues, _.keys(this.selectize.options));
		_.each(missingOptions, (id) => {
			const idx = _.indexOf(newValues, id);
			const option = {
				id,
				[this.secondaryLabelField]: secondaryLabelValues[idx] || '',
			};
			option[this.labelField] = mainLabelValues[idx] || utils.translateKey('missing_party');
			this.selectize.addOption(option);
		});
		this.selectize.setValue(newValues, true);
	},
	/**
	 * Get Selectize Options
	 *
	 * Function to get options for selectize
	 */
	getSelectizeOptions() {
		const self = this;
		const fieldName = this.field;
		return {
			valueField: 'id',
			labelField: self.labelField,
			searchField: [self.labelField, self.secondaryLabelField],
			preload: 'focus',
			// Do not open on focus since we manually open the dropdown as needed
			openOnFocus: false,
			create: false,
			persist: false,
			plugins: ['remove_button'],
			placeholder: _.get(self, 'fieldDef.typeOptions.placeholder', self.searchCaption),
			sortField: self.labelField,
			load: self.apiSearchEnabled() ? self.loadFn.bind(self) : null,
			maxItems: self.getMaxItems(),
			maxOptions: this.maxOptions,
			mode: 'multi',
			onInitialize() {
				// Once added, removing an item does not show it back in the options. Rather than
				// refetch the entire options list, we'll maintain a cache of added options
				this._addedItems = {};
				this.originalRefreshOptions = this.refreshOptions;
				this.refreshOptions = function updatedRefreshOptions() {
					// Set `triggerDropdown: false` to prevent the dropdown from closing when there
					// are no options since we want the "No matches found" text to show
					this.originalRefreshOptions.call(this, false);
					// Exit early if no searches have been performed yet to prevent the
					// "No matches found" from showing before the results have been received
					if (_.isEmpty(this.loadedSearches)) return;
					self.updateNoMatchesPlaceholder(this.hasOptions);
					if (self.preventNextDropdownOpen) {
						self.preventNextDropdownOpen = false;
						return;
					}
					this.open();
				};
			},
			onLoad(data) {
				// Since `refreshOptions` does not get triggered if no options are returned from
				// `load`, we need to manually open the dropdown here
				if (_.isEmpty(data)) this.open();
			},
			onItemAdd(value) {
				this._addedItems[value] = this.options[value];
			},
			onItemRemove(value) {
				if (!this.options[value]) this.options[value] = this._addedItems[value];
				delete this._addedItems[value];
			},
			render: {
				option(item, escape) {
					let secondaryLabel = '';
					if (item[self.secondaryLabelField]) {
						secondaryLabel = `<span class="highlight-sub-search-block">${escape(item[self.secondaryLabelField])}</span>`;
					}
					return `<div id="${fieldName}:${escape(item.id)}" role="option" class="option">`
						+ `<span>${escape(item[self.labelField]) ?? ''}</span>${secondaryLabel}</div>`;
				},
			},
		};
	},
	/*
	*		Build Typeahead
	*
	*		Function to register typeahead plugins to proper textbox.
	*/
	buildTypeahead() {
		const selectizeOptions = this.getSelectizeOptions();
		const $select = this.$element.selectize(selectizeOptions);

		this.selectize = $select[0].selectize;
		// in some cases, we re-create the autocomplete form with different filters.
		// The dropdown content cache is cleared everytime on init so that we avoid
		// stale cached data.
		this.selectize.clearOptions();
		// Set selected value if any
		const values = this.model.get(this.field) || [];
		const mainLabelValues = this.model.get(this.selectizeAttribute) || [];
		const secondaryLabelValues = this.model.get(this.selectizeSecondaryAttribute) || [];
		const deletedDateValues = this.model.get(this.deletedDateAttribute) || [];
		if (values.length > 0) {
			_.each(values, (id, idx) => {
				const isDeleted = !_.isEmpty(deletedDateValues[idx]);
				const option = {
					id,
					[this.secondaryLabelField]: (!isDeleted && secondaryLabelValues[idx]) || '',
				};
				option[this.labelField] = (!isDeleted && mainLabelValues[idx]) || utils.translateKey('missing_party');

				// Don't add null/undefined option
				if (!option[this.labelField]) return;
				this.selectize.addOption(option);
			});
			this.selectize.setValue(values, true);
		}
		const noMatchesHtml = '<div class="no-matches">'
			+ `${utils.escapedTranslateKey('no_matches_found')}`
			+ '</div>';
		const selectAllHtml = '<div class="view-all">'
			+ `<button class="btn-link btn">${utils.escapedTranslateKey('view_all')}</button>`
			+ '</div>';
		this.selectize.$dropdown.append(noMatchesHtml);
		this.selectize.$dropdown.append(selectAllHtml);
	},
	clearSelectize() {
		this.model.set(this.field, null);
		this.selectize.clearOptions();
	},
	/**
	*		On Rendered
	*
	*		Function called when view is rendered to DOM and need to capture specific elements,
	*		register special events.
	*/
	onRendered(callback) {
		// Set needed elements
		this.$element = this.$(`input[name="${this.field}"]`);
		// Apply typeahead plugin
		this.buildTypeahead();
		// Set custom events
		this.model.on(`change:${this.caseIdField}`, this.clearSelectize, this);
		this.model.on(`change:${this.field}`, this.onModelChange, this);
		this.selectize.on('change', this.onSelectizeItemsChange.bind(this));
		if (this.dependentMaxItemField && this.maxItemFunction) {
			this.model.on(`change:${this.dependentMaxItemField}`, this.updateMaxItems, this);
		}
		// Callback
		callback();
	},
	/**
	*		On Remove
	*
	*		Unregister events to avoid memory leak
	*/
	onRemove() {
		if (this.selectize) {
			this.selectize.destroy();
		}
		if (this.dependentMaxItemField && this.maxItemFunction) {
			this.model.off(`change:${this.dependentMaxItemField}`, this.updateMaxItems, this);
		}
		this.model.off(`change:${this.field}`, this.onModelChange, this);
		this.model.off(`change:${this.caseIdField}`, this.clearSelectize, this);
	},
	onViewAllClick: _.throttle(function throttled() {
		this.renderSearchView();
	}, $appData.globalConfig.buttonClickEventsThrottle, { trailing: false }),
	loadFn(strQuery, callback) {
		$.ajax(`${$appData.globalConfig.apiRoot}/grid_search/sys_party`, {
			data: JSON.stringify(this.buildQuery(strQuery)),
			contentType: 'application/json',
			type: 'POST',
		}).done((data) => {
			// data will now be an ElasticSearc result set that needs to
			// be drilled down into to get the data which will be in
			// the hits.hits node.
			const hits = _.sortBy(data.hits.hits, '_score').reverse();
			const results = [];
			_.each(hits, (hit) => {
				if (hit && hit._source && !_.isEmpty(hit._source)) results.push(hit._source);
			});
			callback(results);
		}).fail((jqXHR) => {
			const message = jqXHR.responseJSON?.message || 'Could not search entities';
			callback(new Error(message));
		});
	},
	buildQuery(searchQuery) {
		const entDef = entities.get({base: 'sys', name: 'party'});
		let query = entDef.query();
		let querySize = this.maxOptions;
		const { fieldDef: { typeOptions = {} } } = this;

		// our basic unfiltered search
		const queryFilters = [];
		if (!_.isEmpty(searchQuery)) {
			queryFilters.push(
				query.or([
					query.contains_partial(this.labelField, searchQuery),
					query.and([
						query.is_not_empty(this.labelField),
						query.contains_partial(this.secondaryLabelField, searchQuery),
					]),
				]),
			);
		}

		// Check for caseId
		if (!_.isEmpty(this.model.get(this.caseIdField))) {
			queryFilters.push(query.is('caseId', this.model.get(this.caseIdField)));
		} else {
			querySize = 0;
		}

		// Merge typeOptions Query
		if (typeOptions.query) {
			queryFilters.push(typeOptions.query);
		}

		// Merge typeOptions Filters
		if (typeOptions.filter) {
			queryFilters.push(typeOptions.filter);
		}

		query = query.and(queryFilters).toQuery();

		const showInactiveRecords = this.isModelCase
			? !this.model.get('sysActive')
			: !this.model.get(`${this.caseIdField}__sysActive`);
		return {
			filters: query,
			size: querySize,
			from: 0,
			// all context filters should be applied on the backend
			showInactiveRecords,
			lang: $appData.intakeTranslationLanguage,
		};
	},
	getMaxItems() {
		const maxItemFunction = multiFieldFunctions[this.maxItemFunction];
		return _.isFunction(maxItemFunction)
			? maxItemFunction.call(this.model) : this.maxItems;
	},
	updateMaxItems() {
		const maxItems = this.getMaxItems();
		const { selectize } = this.$element[0];
		selectize.settings.maxItems = maxItems;
		if (maxItems && selectize.items.length > maxItems) {
			selectize.clear();
		}
	},
	updateNoMatchesPlaceholder(hide) {
		const $noMatches = this.selectize.$dropdown.find('.no-matches');
		if (hide) return $noMatches.hide();
		return $noMatches.show();
	},
	renderSearchView() {
		const self = this;
		const partySearchModalView = new PartySearchModalView({
			model: this.model,
			searchText: this.selectize.lastValue,
			onRowClicked(model) {
				self.preventNextDropdownOpen = true;
				const currentValue = self.model.get(self.field);
				const newValues = _.isArray(currentValue)
					? _.uniq([...currentValue, model.id])
					: [model.id];
				if (self.haveValuesChanged(newValues)) {
					if (!self.selectize.options[model.id]) self.selectedSearchModel = model;
					self.updateModelValues(newValues);
				}
			},
		});
		this.addSubView(partySearchModalView).renderAsModal();
	},
});
