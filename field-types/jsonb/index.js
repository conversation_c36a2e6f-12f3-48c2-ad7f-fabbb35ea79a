/**
 *	Represents a jsonb column, designed for PG and dynamic data
 */
const _ = require('lodash');
const utils = require('../utils.js');
const baseType = require('../_primitive/base');

module.exports = utils.extend(baseType, {
	name: 'jsonb',
	caption: 'JSONB',
	db: {
		joiType: 'object',
		type(table, fieldDef) {
			const column = _.snakeCase(fieldDef.field);
			return table.jsonb(column);
		},
		beforeSave(client, value) {
			if (_.isNil(value)) {
				return {};
			}
			return value;
		},
		afterRead(client, value) {
			if (value === '') return null;
			if (_.isString(value)) {
				return JSON.parse(value);
			}
			return value;
		},
	},
	es: {
		disableSorting: true,
		mapping: false,
	},
	format(val, context) {
		if (!val) return '';
		return JSON.stringify(val);
	},
});
