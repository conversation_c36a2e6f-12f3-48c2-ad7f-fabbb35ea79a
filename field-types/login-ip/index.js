/**
*
*		LoginIP
*
*		Used for login ip fields
*
*/

const _ = require('lodash');
const utils = require('../utils.js');
const textboxType = require('../textbox/index.js');
const OptUtil = require('../../lib/common/OptUtil.js');

module.exports = utils.extend(textboxType, {
	name: 'loginIp',
	caption: 'Login IP',
	includeInFormBuilder: false,
	db: {
		beforeSave(client, value) {
			if (_.isString(value)) {
				const trimmed = _.trim(value);
				if (trimmed !== '' && !OptUtil.$opt.disableLoginIp) {
					return trimmed;
				}
				return null;
			}
			return null;
		},
		afterRead(client, value) {
			if (value === '' || OptUtil.$opt.disableLoginIp) {
				return null;
			}
			return value;
		},
	},
});
