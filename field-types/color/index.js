/**
 *
 *        Color
 *
 *        Used to select a color and have it store the hex color value (e.g. "#000000")
 *
 */

const _ = require('lodash');
const utils = require('../utils.js');
const textType = require('../_primitive/text.js');

module.exports = utils.extend(textType, {
	name: 'color',
	caption: 'Color',
	description: 'Select and display a color input.',
	includeInFormBuilder: true,
	ui: {
		component() {
			return require('./component.js');
		},
		formComponent() {
			return require('./form-component.js');
		},
		searchComponent() {
			return require('./component.js');
		},
		dragComponent() {
			return require('./drag-component.js');
		},
		configurationTabs() {
			return require('../base-configuration-tabs.js');
		},
		cellTemplate() {
			return require('./color-cell-tmpl.dust');
		},
		gridConfiguration: false,
		createForm: 'base-properties-form',
		propertiesForm: 'base-properties-form',
		icon: 'fa-paint-brush',
	},
	escape(val) {
		return _.escape(val);
	},
	formatHtml(val) {
		if (!val) return '';
		return `<span class="color-cell-tmpl" style="background-color:${val};"></span>`;
	},
	rules: null,
});
