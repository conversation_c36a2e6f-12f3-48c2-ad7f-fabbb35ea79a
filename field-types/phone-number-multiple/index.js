/** *
*		Phone Number Multiple
*/

const _ = require('lodash');
const { parsePhoneNumberFromString } = require('libphonenumber-js');
const utils = require('../utils.js');
const codeMultipleType = require('../code-multiple');
const logger = require('../../plugins/log').file('field-types/phone-number-multiple/index.js');

const phoneNumberMultipleFieldType = utils.extend(codeMultipleType, {
	name: 'phone-number[]',
	caption: 'Phone Number Multiple',
	description: 'Store one or more phone numbers.',
	fieldAttributes: ['ID'],
	includeInFormBuilder: true,
	db: {
		beforeSave(client, value) {
			if (!value) return value;
			const _value = value;
			const numbers = _.map(_value, (number) => {
				// remove special characters
				const newValue = utils.removeSpecialCharsFromPhoneNumber(number);
				if (utils.isValidPhoneNumber(newValue)) {
					// format number
					const parsedPhoneNumber = parsePhoneNumberFromString(newValue);
					const cleanValue = parsedPhoneNumber.format('E.164');
					return cleanValue;
				}
				if (!_.isNil(number)) {
					logger?.warn('Invalid phone-number value. Set to null.');
				}
			});
			return utils.prepareJSONForDb(client, _.compact(numbers));
		},
		joiType(Joi) {
			return Joi.array().items(Joi.phoneNumber()).sparse();
		},
	},
	ui: {
		component() {
			return require('./component.js');
		},
		formComponent() {
			return require('./form-component.js');
		},
		searchComponent(operator) {
			const fieldTypes = require('../');
			switch (operator) {
				case 'is_empty':
				case 'is_not_empty':
					return null;
				case 'contains':
				case 'does_not_contain':
					return fieldTypes.textbox.ui.searchComponent();
				default:
					return require('./component.js');
			}
		},
		cellTemplate() {
			return require('./phone-number-multiple-cell-tmpl.dust');
		},
		image(){
			return require('./img.png');
		},
		dragComponent() {
			return require('./drag-component.js');
		},
		configurationTabs() {
			return require('../base-configuration-tabs.js');
		},
		defaultValueOnPropertiesForm: false,
		createForm: 'phone-number[]-properties-form',
		propertiesForm: 'phone-number[]-properties-form',
		icon: 'fa-phone',
		sizes: {
			medium: {
				width: 'col-xs-12 col-sm-6 col-lg-3',
			},
		},
	},
	escape(val) {
		return _.escape(val);
	},
	es: {
		mapping: {
			type: 'keyword',
			fields: {
				_english: {
					type: 'text',
					analyzer: 'english',
				},
				_exact: {
					type: 'keyword',
				},
				_partial: {
					type: 'text',
					analyzer: 'phone_number_analyzer',
					// Don't want search text analyzed as n-gram
					search_analyzer: 'standard',
				},
			},
		},
		formatSearchText(searchText) {
			return utils.removeSpecialCharsFromPhoneNumber(searchText);
		},
	},
	operators: require('./operators.js'),
	coerce(values) {
		if (_.isArray(values)) {
			values = _.flattenDeep(values);
		} else if (_.isObject(values)) {
			values = _.flattenDeep(Object.values(values));
		}
		if (!Array.isArray(values)) return [values];
		return _.map(values, value => utils.removeSpecialCharsFromPhoneNumber(value));
	},
	format(values, context) {
		if (!values) return '';
		const formatInternational = _.get(context, 'fieldDef.typeOptions.formatInternational');
		const countryMaskMapping = _.get(context, 'fieldDef.typeOptions.countryMasks');
		const formattedNumbers = _.map(values, (val) => {
			if (!val) {
				return val;
			}
			const {
				phoneNumberwithE164,
				internationalNumber,
				nationalNumber,
				countryCode,
			} = utils.parsePhoneNumber(val);

			const countryCodeIsDefault = context.globalOptions.defaultPhoneNumberMask.countryCode
	=== _.parseInt(countryCode);
			const countryCodeInTypeOptions = countryMaskMapping && countryMaskMapping[countryCode];

			if (formatInternational) {
				return internationalNumber;
			}

			// apply mask by default or mask specified in typeOptions (higher priority)
			if (countryCodeIsDefault || countryCodeInTypeOptions) {
				const mask = _.get(countryMaskMapping, countryCode)
		|| context.globalOptions.defaultPhoneNumberMask.mask;
				const nationalNumberWithMask = utils.applyMask(nationalNumber, mask);
				return `+${countryCode} ${nationalNumberWithMask}`;
			}

			// no mask to apply
			return phoneNumberwithE164;
		});

		return formattedNumbers.join(', ');
	},
	export: {
		format(data, context) {
			data = phoneNumberMultipleFieldType.format(data, context);
			if (!data) return '';
			return `="${data}"`;
		},
	},
});

module.exports = phoneNumberMultipleFieldType;
