/**
*
*		Field Type
*
*		Used to identify field type components
*
*/
const utils = require('../utils.js');
const codeType = require('../code');

module.exports = utils.extend(codeType, {
	name: 'fieldType',
	caption: 'Field Type',
	ui: {
		component() {
			return require('./component.js');
		},
		formComponent() {
			return require('./form-component.js');
		},
		searchComponent() {
			return require('./component.js');
		},
		sizes: {
			medium: {
				width: 'col-xs-12 col-sm-10',
			},
		},
	},
	format(val, context) {
		if (!val) return '';
		return context.translateKey({
			groupName: 'field_types',
			subgroupName: val,
			key: 'name',
		});
	},
});
