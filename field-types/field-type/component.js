/**
*
*		Field List Component
*
*		Component for creating a field list component
*
*/
require('selectize');
const _ = require('lodash');
const utils = require('../../public/lib/utils.js');
const fieldTypes = require('../../field-types');
const BaseComponent = require('../base-component.js');
require('./component.less');

module.exports = BaseComponent.extend({
	field: null,
	selectize: null,
	searchCaption: 'Search',
	template: require('./component-tmpl.dust'),
	className: 'field-type-component-tmpl',
	/**
	*		Locals
	*
	*		Variables to pass to the dust template.
	*/
	locals() {
		return {
			field: this.field,
			ariaLabel: this.fieldDef.ariaLabel,
		};
	},
	/**
	*		On Selectize Value Change
	*
	*		Called when the selected value changed in selectize.
	*/
	onSelectizeValueChange(newValue) {
		if (_.isEmpty(newValue)) newValue = null;
		const currentValue = this.model.get(this.field);
		// Avoid creating unecessary events whent he value didn't really change
		if (newValue !== currentValue) {
			this.model.set(this.field, newValue);
			BaseComponent.prototype.onEditableValueChange.call(this, newValue);
		}
	},
	/**
	*		On Model Change
	*
	*		Function triggered whenever the model changes, either from within this component
	*		or external functions.
	*/
	onModelChange() {
		const val = this.model.get(this.field);
		// Set selectize
		const option = this.selectize.getOption(val);
		if (!option) {
			this.selectize.addOption(this.getSelectizeItem(option));
		}
		this.selectize.setValue(val, true);
	},
	/**
	*		Build Typeahead
	*
	*		Function to register typeahead plugins to proper textbox.
	*/
	buildTypeahead() {
		const $select = this.$element.selectize(this.getSelectizeOptions());
		this.selectize = $select[0].selectize;
		// Set selected value if any
		const val = this.model.get(this.field);
		if (val) {
			this.selectize.addOption(this.getSelectizeItem(this.field));
			this.selectize.setValue(val, true);
		}
	},
	/**
	*		On Rendered
	*
	*		Function called when view is rendered to DOM and need to capture specific elements,
	*		register special events.
	*/
	onRendered(callback) {
		// Set needed elements
		this.$element = this.$(`select[name="${this.field}"]`);
		// Apply typeahead plugin
		this.buildTypeahead();
		// Set custom events
		this.model.on(`change:${this.field}`, this.onModelChange, this);
		this.selectize.on('change', this.onSelectizeValueChange.bind(this));
		// Callback
		callback();
	},
	onRemove() {
		this.selectize.destroy();
		this.model.off(`change:${this.field}`, this.onModelChange, this);
	},
	getSelectizeOptions() {
		return {
			options: this.getSelectizeData(),
			placeholder: utils.translateKey('search'),
			labelField: 'text',
			valueField: 'value',
			sortField: [
				{
					field: 'text',
					direction: 'asc',
				},
			],
			optgroups: [
				{value: 'standard', label: 'Standard', label_scientific: 'Standard'},
				{value: 'advanced', label: 'Advanced', label_scientific: 'Advanced'},
			],
			optgroupField: 'group',
			render: {
				option: this.renderOption.bind(this),
			},
		};
	},
	getSelectizeData() {
		const data = fieldTypes;
		return _.map(_.keys(data), this.getSelectizeItem);
	},
	getSelectizeItem(fieldName) {
		const hasImage = !!_.get(fieldTypes[fieldName], 'ui.image');
		return {
			group: hasImage ? 'standard' : 'advanced',
			value: fieldName,
			text: utils.translateKey({
				groupName: 'field_types',
				subgroupName: fieldName,
				key: 'name',
			}),
		};
	},
	renderOption(item, escape){
		const imgFn = _.get(fieldTypes[item.value], 'ui.image');
		return `<div id="${this.field}:${escape(item.id)}" role="option" class="option">${imgFn
			? `<img alt="" class="field-type-img" src="${imgFn()}"/>`
			: '<div class="field-type-no-img" >&nbsp;</div>'}`
			+ `<div class="field-type-txt"><p>${escape(item.text)}</p></div>`
			+ '<div>';
	},

});
