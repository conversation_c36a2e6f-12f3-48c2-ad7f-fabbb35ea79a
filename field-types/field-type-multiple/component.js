const _ = require('lodash');
const BaseComponent = require('../base-component');
const FieldTypeComponent = require('../field-type/component.js');

module.exports = FieldTypeComponent.extend({
	template: require('./component-tmpl.dust'),
	onSelectizeValueChange(newValues) {
		if (!newValues) newValues = [];
		const currentValues = this.model.get(this.field) || [];
		// Avoid creating unecessary events whent he value didn't really change
		if (!_.isEqual(newValues, currentValues)) {
			this.model.set(this.field, newValues);
			BaseComponent.prototype.onEditableValueChange.call(this, newValues);
		}
	},
	onModelChange() {
		const newValues = this.model.get(this.field);
		// Skip if values are the same
		if (_.isEqual(newValues, this.selectize.items)) return;
		// Set selectize
		_.each(newValues, (newVal) => {
			const option = this.selectize.getOption(newVal);
			if (!option) {
				this.selectize.addOption(this.getSelectizeItem(option));
			}
		});
		this.selectize.setValue(newValues, true);
	},
	buildTypeahead() {
		const $select = this.$element.selectize(this.getSelectizeOptions());
		this.selectize = $select[0].selectize;
		// Set selected value if any
		const values = this.model.get(this.field);
		if (values) {
			_.each(values, (val) => {
				this.selectize.addOption(this.getSelectizeItem(this.field));
			});
			this.selectize.setValue(values, true);
		}
	},
});
