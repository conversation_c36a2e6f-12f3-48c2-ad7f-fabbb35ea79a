/* eslint-disable no-undef */
/**
* Represents a file record that is added to the uploader. Handles uploading in background if it
* can.
*
*/

const $ = require('jquery');
const _ = require('lodash');
const { v4: uuidv4 } = require('uuid');
const utils = require('../../../public/lib/utils.js');
const BaseComponent = require('../../base-component.js');
const viewMan = require('../../../public/lib/view-manager.js');
const fileUtils = require('../../../public/lib/file-group.js');
const FilePreviewView = require('../../../public/views/file/preview/file-preview-view.js');

module.exports = BaseComponent.extend({
	className: 'item-tmpl',
	template: require('./item-tmpl.dust'),
	file: null,
	domObj: null,
	isUploading: false,
	isUploaded: false,
	serverObj: null,
	uploadCanceled: false,
	xhrUploadRequest: null,
	canDelete: true,
	onInit(opts) {
		const name = opts.domObj ? opts.domObj.name : opts.fileName;
		const fileSize = opts.domObj ? opts.domObj.fileSize : opts.fileSize;
		this.canDelete = opts.canDelete !== false;
		this.model = opts.model || null;
		this.field = opts.field || null;
		this.mode = opts.mode;
		this.file = {
			name,
			fileSize,
			description: opts.fileDescription,
		};
		this.domObj = opts.domObj;
		this.descriptionId = `attachment-description-${uuidv4()}`;
		if (opts.serverObj) {
			this.isUploaded = true;
			this.serverObj = opts.serverObj;
		}
		this.disableDownload = opts.disableDownload;
		this.disablePreview = opts.disablePreview;

		if (opts.downloadRule) {
			this.disableDownload = this.disableDownload
				|| !this.model.rules[opts.downloadRule].call(this.model);
		}
		if (opts.downloadUrl) {
			const downloadUrl = this.model[opts.downloadUrl];
			this.downloadUrl = _.isFunction(downloadUrl)
				? downloadUrl.call(this.model, this.serverObj)
				: downloadUrl;
		}
		if (opts.truncateFilename) {
			this.truncateFilename = opts.truncateFilename;
		}
	},
	/**
	 * @description Use this to remove the file from file uploader, it will trigger the proper event
	 * and remove itself from the DOM automatically.
	 */
	removeView() {
		viewMan.remove(this);
	},
	/**
	 * @description return translated current file type
	 * @returns translated file type.
	 */
	getFileType(){
		const {file: {name}} = this;
		return utils.translateKey({
			groupName: 'fileTypes',
			subgroupName: 'all',
			key: fileUtils.getFileType(name),
		});
	},
	/**
	 * @returns data needed for template.
	 */
	locals() {
		const fileSize = this.file.fileSize ? utils.formatSizeUnits(this.file.fileSize) : null;
		return {
			name: this.file.name,
			fileSize,
			fileType: this.getFileType(),
			descriptionId: this.descriptionId,
			isUploaded: this.isUploaded,
			description: this.file.description,
			icon: fileUtils.getIconClass(this.file.name),
			allowDownload: this.canDownload(),
			allowPreview: this.canPreview(),
		};
	},
	events: {
		'click .download': 'onDownloadClick',
		'click .preview': `onPreviewClick:${$appData.globalConfig.buttonClickEventsThrottle}--leading`,
		'click button[name=remove]': 'onCancelClick',
		'change .file-description': 'updateServerObjDescription',
		'input .file-description': 'warnDescriptionLength',
		'blur .file-description': 'clearDescriptionWarning',
	},
	hasModelIDAndEnt() {
		return !!this.model.id && !!this.model.get('entity$');
	},
	canDownload() {
		return this.hasModelIDAndEnt() && !this.disableDownload && !!this.serverObj;
	},
	canPreview() {
		return this.hasModelIDAndEnt() && !this.disablePreview;
	},
	/**
	 * @description event triggered to download file
	 */
	onDownloadClick() {
		if (this.canDownload()){
			const url = this.downloadUrl
			|| `${$appData.globalConfig.apiRoot}/${this.model.getEntity().name}`
				+ `/${this.model.id}/attachment?fieldName=${this.field}&fsObjectId=${this.serverObj.objectId}`;

			utils.downloadFile(url);
		}
	},
	onPreviewClick() {
		this.renderFilePreview();
	},
	/**
	 * @description Event triggered when cancel is clicked
	 */
	onCancelClick() {
		if (this.isUploading) {
			this.uploadCanceled = true;
			this.xhrUploadRequest.abort();
			this.isUploading = false;
		}
		this.removeView();
		this.trigger('removed', this.serverObj);
	},
	/**
	 * @description Event triggered when view is rendered.
	 */
	onRendered(callback = _.noop) {
		if (this.truncateFilename) {
			this.setShortFilename();
		}
		this.model.on('saved', this.onModelSaved, this);
		this.$description = this.$('.file-description');
		// start upload is already not uploaded.
		if (!this.isUploaded) {
			this.startUpload(callback);
		} else {
			this.uploadComplete();
			callback();
		}
		this.renderDescriptionTooltip();
	},
	onRemove() {
		this.model.off('saved', this.onModelSaved, this);
	},
	onModelSaved() {
		this.render();
	},
	/**
	 * @description starts uploading the file in the background
	 */
	startUpload(callback = _.noop) {
		const self = this;
		self.isUploading = true;
		const formData = new FormData();
		formData.append('file', self.domObj, self.file.name);
		self.xhrUploadRequest = $.ajax({
			url: `${$appData.globalConfig.apiRoot}/file/upload`,
			data: formData,
			cache: false,
			contentType: false,
			processData: false,
			type: 'POST',
			xhr() {
				const xhr = new window.XMLHttpRequest();
				xhr.upload.addEventListener('progress', self.onUploadProgress.bind(self));
				return xhr;
			},
		}).done((result) => {
			self.onUploadSuccess.call(self, result, callback);
		}).fail((err) => {
			self.onUploadError.call(self, err, callback);
		});
		// Disable save button on modal while uploading
		if (this.el.closest('.modal')) {
			$('#save').attr('disabled', 'disabled');
		}
		// Alter progress UI
		self.$('.progress').show();
		self.$('.file-progress').show();
	},
	/**
	 * @description updates the file upload progress
	 */
	onUploadProgress: _.throttle(function onUploadProgress(e) {
		if (e.lengthComputable && this.isUploading) {
			const percentComplete = (e.loaded / e.total * 100).toFixed(2);
			this.$('.file-progress').html(`${percentComplete}%&nbsp;${utils.translateKey('complete')}`);
			this.$('.progress-bar').css('width', `${percentComplete}%`);
		}
	}, 250),
	/**
	 * @description Function called when upload successfully finished.
	 */
	onUploadSuccess({uploadedFiles: [uploadedFile]}, callback = _.noop) {
		this.xhrUploadRequest = null;
		this.isUploading = false;
		this.isUploaded = true;
		this.serverObj = uploadedFile;
		this.$('.well').addClass('success');
		this.updateServerObjDescription();
		this.uploadComplete();
		$('#save').removeAttr('disabled');
		callback();
	},
	/**
	 * Function called when an error occurred during the file upload process.
	 */
	onUploadError(err, callback = _.noop) {
		const self = this;
		// eslint-disable-next-line no-param-reassign
		err.weAlreadyPreviouslyHandledThis = true;
		// Cancel causes error..
		if (this.uploadCanceled) return;
		this.isUploading = false;
		this.$('.well').addClass('error');
		utils.setupValidationTooltip(this, this.$('.well'), {
			container: self.$el,
			title: utils.translateKey({
				groupName: 'errors',
				subgroupName: 'general',
				key: 'an_error_has_occurred',
			}),
		});
		this.$('.well').tooltip('show');
		this.$('.progress').removeClass('active');
		this.$('.progress-bar').removeClass('progress-bar-success');
		this.$('.progress-bar').addClass('progress-bar-danger');
		$('#save').removeAttr('disabled');
		callback(err);
	},
	/**
	 * Update description of the file due to a change made in the DOM or upload complete and we
	 * have a server object.
	 */
	updateServerObjDescription() {
		const description = this.$description.val();
		this.file.description = description;
		if (this.serverObj) {
			this.serverObj.description = description;
			this.trigger('updateFile', this.serverObj.objectId, this.serverObj);
		}
	},
	/**
	 * Function called when upload is complete, either due to file already existing in the backend
	 * or the file upload process just finished.
	 */
	uploadComplete() {
		// Incase not done already, update stats
		this.$('.progress').removeClass('progress-striped active');
		this.$('.file-progress').html(`100%&nbsp;${utils.translateKey('complete')}`);
		this.$('.progress-bar').css('width', '100%');
		if (this.canDelete === false) {
			this.$('.fa.fa-times').hide();
		}
		this.trigger('upload', this.serverObj);
	},
	warnDescriptionLength: _.debounce(function warnDescriptionLength() {
		const {length} = this.$description.val();
		const atMaxLength = (length >= 10000);

		this.showDescriptionWarning(atMaxLength);
	}, 100),
	showDescriptionWarning: _.debounce(function showDescriptionWarning(show = true) {
		this.$description.tooltip(show ? 'show' : 'hide');
	}),
	clearDescriptionWarning() {
		this.showDescriptionWarning(false);
	},
	renderDescriptionTooltip() {
		const title = utils.translateKey({
			groupName: 'validation',
			subgroupName: 'tooltip',
			key: 'maximum_characters',
		}, {
			context: {
				characterLimit: 10000,
			},
		});

		this
			.$description
			.tooltip({
				title,
				placement: 'right',
				trigger: 'manual',
			});
	},
	renderFilePreview() {
		this.filePreviewView = new FilePreviewView({
			model: this.model,
			fileName: this.file.name,
			showViewRecordLink: false,
			downloadUrl: this.downloadUrl,
			disableDownload: !this.canDownload(),
		});
		this.addSubView(this.filePreviewView).renderAsModal();
	},
	setShortFilename() {
		const shortFileName = fileUtils.getShortFileName(this.file.name);
		const $fileName = this.$('.file-name');
		if (shortFileName !== this.file.name) {
			$fileName.text(shortFileName);
			$fileName.attr('title', this.file.name);
		}
	},
});
