<div class="row well well-sm">
	<div class="col-sm-6 file-name-container">
		<div>
			<span class="file-name">
				{?allowPreview}<a href="#" class="preview" data-toggle="tooltip" data-placement="bottom" title="{@resource key="preview" /}">{/allowPreview}
				<i class="file-type-icon fa {icon}"></i> {name}
				{?allowPreview}</a>{/allowPreview}
			</span>
			{#fileSize}
				<span class="file-size no-wrap">{.}</span>
			{/fileSize}
			{?allowDownload}
				<a href="#" class="download" data-toggle="tooltip" data-placement="bottom" aria-label="{@resource key="download" /} {name}" title="{@resource key="download" /}">
					<i class="fa fa-icon fa-download download-icon"></i>
				</a>
			{/allowDownload}
			<div class="file-progress" data-readonly="false" style="display: none;">0%&nbsp;{@resource key="complete" /}</div>
		</div>
		<div class="progress progress-striped active" data-readonly="false" style="display: none;">
			<div class="progress-bar progress-bar-success" role="progressbar" style="width: 0%;" aria-label="{@resource key="progress_bar" /}"></div>
		</div>
	</div>
	<div class="col-sm-5 description-container">
		<textarea id="{descriptionId}" aria-label="{@resource key="description_" /}" data-readonly="false" maxlength="10000" rows="3" class="form-control file-description" placeholder="{@resource key="description_" /}">
			{description}
		</textarea>
		<p class="form-control-static" data-readonly="true">{description}</p>
	</div>
	<div class="icon-container" data-readonly="false">
		<button type="button" class="remove" name="remove" aria-label="{@resource key="remove" /} {@resource key="file" /}">
			<i class="fa-solid fa-trash-can"></i>
		</button>
	</div>
</div>
