const utils = require('../utils.js');
const basePropertiesForm = require('../base-properties-form.js');

module.exports = utils.formExtend(basePropertiesForm, {
	name: 'money-properties-form',
	elements: [
		{
			type: 'section',
			defaultFieldsLayout: {
				labelOnTop: false,
			},
			elements: [
				{
					field: 'scale',
					labelWidth: 'col-sm-12 col-sm-8',
					width: 'col-xs-12 col-sm-4',
					typeOptions: {
						min: 0,
						nullValue: 2,
					},
					displayRule: null,
				},
				{
					field: 'max',
					labelWidth: 'col-sm-12 col-sm-8',
					width: 'col-xs-12 col-sm-4',
				},
				{
					field: 'min',
					labelWidth: 'col-sm-12 col-sm-8',
					width: 'col-xs-12 col-sm-4',
				},
			],
		},
	],
});
