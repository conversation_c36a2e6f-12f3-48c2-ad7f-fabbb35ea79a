/**
*
*		Money
*
*		Used to identify money components
*
*/

const _ = require('lodash');
const numeral = require('numeral');
const utils = require('../utils.js');
const decimalType = require('../_primitive/decimal.js');

const moneyFieldType = utils.extend(decimalType, {
	name: 'money',
	caption: 'Money',
	description: 'Store a money amount.',
	includeInFormBuilder: true,
	format(val, context) {
		if (_.isString(val)) val = Number(val);
		if (!_.isNumber(val) || _.isNaN(val)) return '';
		const fieldDef = context?.fieldDef;
		let format = fieldDef?.format;
		if (!format) {
			format = utils.compileNumeralFormat({
				fieldDef,
				defaultFormat: context.globalOptions.defaultMoneyFormat,
				defaultScaleFormat: context.globalOptions.defaultMoneyScaleFormat,
			});
		}
		if (utils.checkNumericalOverflow({ val, fieldDef })) {
			val = Math.trunc(val);
		}
		return numeral(val).format(format);
	},
	db: {
		dbViewCastType: 'numeric(15,2)',
	},
	ui: {
		component() {
			return require('./component.js');
		},
		formComponent() {
			return require('./form-component.js');
		},
		searchComponent(operator) {
			const fieldTypes = require('../');
			switch (operator) {
				case 'contains':
				case 'does_not_contain':
					return fieldTypes.textbox.ui.searchComponent();
				default:
					return require('./component.js');
			}
		},
		image() {
			return require('./img.png');
		},
		dragComponent() {
			return require('./drag-component.js');
		},
		configurationTabs() {
			return require('../base-configuration-tabs.js');
		},
		defaultValueOnPropertiesForm: false,
		createForm: 'money-properties-form',
		propertiesForm: 'money-properties-form',
		icon: 'fa-usd',
	},
	export: {
		numFmt(context) {
			const { fieldDef, globalOptions } = context;
			return utils.compileNumeralFormat({
				fieldDef,
				defaultFormat: globalOptions.defaultMoneyFormat,
				defaultScaleFormat: globalOptions.defaultMoneyScaleFormat,
			});
		},
		format(val, context) {
			if (context.exportType === '.xlsx') {
				if (val == null) return val;
				if (typeof val === 'number') return val;
				return parseFloat(val);
			}
			return moneyFieldType.format(val, context);
		},
	},
});

module.exports = moneyFieldType;
