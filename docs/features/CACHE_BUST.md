# Cache Bust

Application is bootstrapped with system information on user sign-in or when the application is refreshed. This cached information is used throughout the UI for grid configurations, form layouts, translations, permissions, and much more.

The cache bust mechanism enables developers to dynamically update the `$appData` without page refresh when there are data changes on an entity.

For example, syncing dynamic permission translations with cached translations on the UI. Whenever a workflow is created/updated, the system subsequently generates/updates dynamic permissions for the workflow. Translation updates for these permissions are updated through cacheBustGroups configuration defined for translation entity.

## Implementation

`cacheBustGroups` is an array configuration defined at the entity level and multiple groups can be added per entity. Cache bust works hand in hand with SSE and falls back to polling if SSE doesn't succeed.
Each group comprises of the following information:

- `cacheKey`
- `condition`
- `cacheBustHandler`
- `query`

On app startup, SSE events are registered for each group with the cacheBustHandler defined. These handlers are executed when SSE events are dynamically emitted from the backend on meeting the condition defined for the group.

If the system falls back to polling, all handlers will be executed at an interval of 60s.

For every single group, there is a Redis and front-end identifier which is used to determine if the system needs to fetch new data with the query defined and pass it to the handler that can be used to update the `$appData`.

The only time data will be fetched for a group is when the front-end and Redis identifier are not in sync. On meeting the condition, the backend Redis identifier is updated and after the data fetch front end identifier is also updated to the latest Redis identifier value so that they are in sync.
