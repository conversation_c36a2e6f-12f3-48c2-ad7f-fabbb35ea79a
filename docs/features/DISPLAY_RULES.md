# Display Rules

Display rules is a Form Builder feature that allows users to specify visibily logic for showing/hiding layout components that they add to a custom form.

## Filters

The `sys/filter` entity describes a specify individual display rule that, much like a filter query, defines a entity, field, operator and value to evaluate.

## Filter Groups

Filter Groups define a combination of a filters and a boolean operator (`and` or `or`). This is used to provide flexible logic.

## Usage

Create or edit display rules by clicking the field or section for which you want to add visibility criteria, then clicking “Rules” in the sidebar. You can then select another field and its value on which the field or section is dependent.

## Groups

You can apply “and” or “or” to a group of criteria statements in a display rule, rather than all criteria statements. A group of criteria statements must use the same operator but you can use separate operators between the different groups.

For example, you could set up the following criteria for a field’s display rule:
a
One group where the field must meet any of the criteria statements to be displayed (i.e. using “or” between statements).

Another group where the field must meet all of the criteria statements (i.e. placing “and” between them).

Then select “or” between the two different groups.

This would create two distinct set of criteria in which the field would be displayed.

### Sections

If you add display rules to a section, the rules will apply to all fields in the section. Additional visibility rules may be applied to the section’s fields, but the section’s rules will take priority.
