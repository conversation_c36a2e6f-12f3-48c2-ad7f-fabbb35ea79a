# Service Accounts and OAuth2

Case IQ supports Service Accounts and OAuth2 authentication for third-party integrations. This allows integrations to authenticate and authorize requests to Case IQ APIs from outside of the web interface.

**Relevant files:**
- `script/data/static-service-accounts.js`: Static Service Account configuration file.
- `script/sync-static-service-accounts.js`: <PERSON>ript to sync Static Service Accounts from the configuration file. Can be run with the `make sync-static-service-accounts` command.
- `script/create-sample-static-service-accounts.js`: Script to create a sample Static Service Accounts for local testing. Can be run with the `make create-sample-static-service-accounts` command.
- `plugins/user-provision/src/service-accounts/`: This directory contains code for creating and updating Service Accounts, as well as scheduling API key expiry related jobs.
- `lib/services/business-logic/user/workflow/api-key-expiry.js`: This file contains the seneca actions which automatically rotate or send API key expiry notifications. These actions are scheduled by the `ServiceAccountApiKeyExpiryScheduler` in the user provision plugin.
- `lib/auth/oauth/`: This directory contains code relevant to the OAuth2 authentication flow, including the token endpoint and the Client Credentials flow.
- `plugins/json-web-token/JsonWebToken.js`: Plugin for signing / verifying JSON web tokens.
- `lib/middleware/api-bearer-token-auth.js`: Middleware for authenticating requests using Bearer tokens.
- `lib/api/dev-ops.js`: Dev-ops API endpoints for initializing and rotating API keys for Static Service Accounts.
- `lib/api/api-rotate-key.js`: External API endpoint for rotating API keys for Service Accounts. This endpoint requires providing one of the account's API keys to authenticate the request.

# Service Accounts
Service Accounts are more or less a special type of user account that is not tied to a specific person. They are used for server-to-server communication, where a third-party service needs to authenticate with Case IQ without user interaction. The main differences between Service Accounts and regular user accounts are:
- Service Accounts are not tied to a specific person.
- Service accounts use two system-generated API keys for credentials instead of passwords. Either API key can be used to authenticate as the client.
- Service Accounts cannot be used to log in to the Case IQ web interface.
- Service Accounts must use Bearer tokens (JSON web tokens) for request authentication.

At the time of writing, there is no user interface for creating or managing Service Accounts. When we move forwards with a full public API offering the user interface, permission structure, and API key expiry notifications for Service Accounts will need to be implemented.

### Service Account Permissions
What Service Accounts have permission to do is determined by the account's user role, just like regular user profils. When configuring Static Service accounts, developers should be careful to ensure that the permissions granted to the Service Account are appropriate for the intended use case, and should not be granted any permissions beyond what is required for the integration.

### Static Service Accounts
Static Service Accounts are a sub-category of Service Account intended for use by Case IQ (the organization) to implement integrations with third-party services. For example, if Case IQ wanted the Lextegrity application to retrieve resources from CIQ applications, we would configure a Static Service Account for that purpose. Static Service Accounts are created and managed by Case IQ, and are not intended for use by customers. Static Service Accounts are not visible in the web interface, and cannot be created or managed by users.

The only awareness internal users of the application would have of Static Service Accounts would be records submitted by the account, (which would be attributed to that Service Account in fields like `createdBy`), or any audit trail left by the account's actions.

Static Service Accounts can be defined in `/script/data/static-service-accounts.js`, which will be read to sync accounts when running the make command `make sync-static-service-accounts`. This command will update existing Static Service Accounts to match the current configuration file (lookup based on account `nick`), or will create new accounts if no existing profile with that `nick` exists in the system.

Static Service Accounts are not initialized with a set of API keys. Their credentials can be initialized from the dev-ops API endpoint `/static-service-account/initialize-api-keys`, and rotated using the dev-ops API endpoint `/static-service-account/rotate-api-key`. The rotation endpoint will generate a new API key for the specified field, invalidating the old key.

### API Key Expiry
The API keys for Service Accounts will expire 180 days after they are generated. This is to enforce credential rotation for integrations. Seven days before an API key expires, a notification email will be sent to the email address associated with the Service Account if one is configured.

Rotating an API key will reset the API key expiry date and the expiry notification job.

# OAuth2
Case IQ leverages OAuth2 for third-party integrations, allowing integrations to interact with the Case IQ application APIs from external applications. We accomplish this with the OAuth2 Client Credentials flow, which is a server-to-server authentication.

The OAuth2 Client Credentials flow is used to obtain an access token (JWT) that can be used to authenticate requests to Case IQ APIs. The access token is a short-lived token that is issued by the authorization server and can be used to access protected resources on behalf of the client application.

At this time the Client Credentials grant type is the only supported OAuth2 flow.Note that our OAuth2 implementation is not a full OAuth2 server, but rather a simplified version that focuses on the Client Credentials flow. The Client Credentials flow is a more straightforward approach for server-to-server communication, where a client application needs to access protected resources on behalf of itself rather than on behalf of a user.

Please read the full OAuth2 specification for more information on the OAuth2 protocol: https://datatracker.ietf.org/doc/html/rfc6749

### Client Credentials Flow
The Client Credentials flow is a server-to-server authentication method that allows a client application to authenticate with the authorization server and obtain an access token without user interaction. This flow is typically used for machine-to-machine communication, where a client application needs to access protected resources on behalf of itself rather than on behalf of a user.

Service Accounts are the only supported profiles which can leverage the Client Credentials flow. The Client Credentials flow is not available for regular user accounts.

The Client Credentials flow is simple, consisting of a single request to the authorization server (the CIQ server) to obtain an access token. The request must include a `client_id` and `client_secret`, which map to a Service Account's `nick` and one of its API keys. The request must also include a `grant_type` parameter set to `client_credentials`. The authorization server will respond with an access token, which can then be used to authenticate requests to protected resources on behalf of the client application.

When requesting an access token the client credentials can be provided in two ways:
- As a Basic Authorization header (Base64 encoded `client_id:client_secret`)
	- Example: `Authorization: Basic eW91clNlcnZpY2VBY2NvdW50Tmljazp5b3VyU2VydmljZUFjY291bnRBUElLZXk=`
	- This decodes to `yourServiceAccountNick:yourServiceAccountAPIKey`
- `client_id` and `client_secret` in the body of the request
	- Note that while the OAuth2 standard only specifies that `x-www-form-urlencoded` be supported for the request body, `application/json` (or any other request body format supported by the server) may also be used.

The token returned from the endpoint will, by default, last for 10 minutes. A longer token lifespan, up to a maximum of 1 hour, can be requested by setting `ttl` in the request body to the desired lifespan (in seconds).

#### Example Requests
```
POST /auth/oauth2/token
Content-Type: application/x-www-form-urlencoded
Authorization: Basic eW91clNlcnZpY2VBY2NvdW50Tmljazp5b3VyU2VydmljZUFjY291bnRBUElLZXk=

grant_type=client_credentials
```

**OR**

```
POST /auth/oauth2/token
Content-Type: application/json

{
  "grant_type": "client_credentials",
  "client_id": "yourServiceAccountNick",
  "client_secret": "yourServiceAccountAPIKey",
  "ttl": 3600
}
```

These request will return a JSON response containing the access token, token type, and expiration time. The access token can then be used to authenticate requests to protected resources by including it in the `Authorization` header of the request. The token type should be set to `Bearer`, followed by the access token.

#### Example Token Endpoint Response
```
{
  "access_token": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJyZWFsbHkiOiJZb3UgcmVhbGx5IGNoZWNrZWQgd2hhdCBJIHB1dCBpbiB0aGlzIHRva2VuPyJ9.oQ6qrHdSa0F-3e-wk89zZ0KzzaqccyFO1EBiMzpuvik",
  "token_type": "Bearer",
  "expires_in": 3600
}
```


#### Example Bearer Authorization Header
```
Authorization: Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJyZWFsbHkiOiJZb3UgcmVhbGx5IGNoZWNrZWQgd2hhdCBJIHB1dCBpbiB0aGlzIHRva2VuPyJ9.oQ6qrHdSa0F-3e-wk89zZ0KzzaqccyFO1EBiMzpuvik
```

Note that Bearer Tokens can be used to access any internal API endpoint, as long as the Service Account has the appropriate permissions for said endpoint.