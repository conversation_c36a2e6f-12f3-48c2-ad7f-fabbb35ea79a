# Advanced Search

Each field type determines how it can be searched as well as how the mapping is configured within ElasticSearch.

Advanced Search and Case Linking Search uses the sources of truth to build accurate queries (datetime exclude seconds & milliseconds, etc).

> :information_source: **NOTE**: It is highly recommended to not build queries based on sub fields within ElasticSearch (ex: _search, raw) unless needed. These will be removed / changed in a future release.

## Supported Operators

* Is
* Is Empty
* Is Not
* Is Not Empty
* Between
* Contains
* Less Than or Equals
* Greater Than or Equals
* Does Not Contain

> :information_source: **NOTE**: Availability of operators changes based on whether the field type supports it or not. See specific field type documentation to see which operators it supports.

## Searchable Entities

The list of searchable entities comes from the picklist "searchable_entity_types".

The "value" is now what is displayed in the dropdown.

The "relatedData" contains the entity it represents.
