# Hotline SSO

Hotline SSO allows us to specify a separate identity provider for authenticating Hotline agents.

Autouser provisioning is always enabled for this login method, and users who sign in here will be automatically assigned the Hotline user role. When enabled, a "Login as a hotline agent" button will appear on the login page (not on the external login page).

Hotline SSO currently only supports SAML

# Configuring Hotline SSO

A full list of the options & their corresponding environment variables can be found in `options.auth.js` under `strategies -> hotline`. The corresponding Dynamic SSO option will also be specified here for those familiar with configuring it.

### Enabling Hotline SSO

 - Hotline Agent Intake must already be enabled
 	- Following feature flags must be toggled on: Portal, Two Way Portal & Hotline Agent Intake.
 - Hotline SSO is then enabled with `ENABLE_HOTLINE_SSO=true`

### Identity Provider Options

- `HOTLINE_SSO_CERT`
	- The private certificate from the Identity Provider.
	- Dynamic SSO Option: Identity Provider Certificate
- `HOTLINE_SSO_ENTRY_POINT`
	- The Identity Provider's login URL - where users will be redirected when they click "Login as a hotline agent"
	- Dynamic SSO Option: Identity Provider Endpoint
- `HOTLINE_SSO_ISSUER`
	- Default value: `config.baseUrl`
	- The Service Provider's (Case IQ's) Entity ID. This is expected to be a URI, and defaults to the baseUrl of the application. This should not need to be configured.
	- Dynamic SSO Option: Service Provider Entity ID
- `HOTLINE_SSO_CALLBACK_URL`
	- Default value: `{config.baseUrl}/hotline_auth/saml`
	- Where the Identity Provider directs the request after a user has successfully authenticated with them. This is usually provided to the Identity Provider ahead of time, and should not need to be configured.
	- Dynamic SSO Option: Service Provider Consumer URL
- `ENABLE_HOTLINE_SSO_AUTHN_CONTEXT`
	- Default value: `false`
	- Enables additional authentication context when set to `true`
	- Dynamic SSO Option: Request Authentication Context
- `HOTLINE_SSO_AUTHN_CONTEXT`
	- Additional authentication context required by the Service Provider (Case IQ). Only required when authn context is enabled.
	- Dynamic SSO Option: Authentication Context

### Identifier

- `HOTLINE_SSO_IDENTIFIER_MAPPING`
	- Default value: `email`
	- The property of Case IQ profiles which will be used to identify users. Afters users have authenticated with the Identity Provider we will look for an existing Case IQ user profile with a matching identifier on field specified by this variable (see `HOTLINE_SSO_IDENTIFIER_DATA_MAPPING`). If no Case IQ profile is found a new one will be created.
	- Dynamic SSO Option: Identifier Data Mapping
- `HOTLINE_SSO_IDENTIFIER_DATA_MAPPING`
	- Default value: `email`
	- The property name of our identifier on the Identity Provider user profiles. Example: If we want to use 'email' as our identifier, but the Identity Provider returns profiles with an email property named `emailAddress`, we would set `HOTLINE_SSO_IDENTIFIER_MAPPING=email` and `HOTLINE_SSO_IDENTIFIER_DATA_MAPPING=emailAddress`. Then, when a user logs in via Hotline SSO we will look for an existing Case IQ user profile with an `email` value that matches the `emailAddress` value from the Identity Provider's profile.
	- Dynamic SSO Option: Identifier Data Mapping
- `HOTLINE_SSO_IDENTIFIER_FORMAT`
	- Default value: `urn:oasis:names:tc:SAML:1.1:nameid-format:emailAddress`
	- The format of the identifier data. Defaults to email address. This should not need to be configured unless you are using an identifier that is not an email address. See `https://docs.oasis-open.org/security/saml/v2.0/saml-core-2.0-os.pdf` (page 78) for further reading.
	- Dynamic SSO Option: Identifier Format

### Data Mapping

`HOTLINE_SSO_DATA_MAPPING_CONFIG`

Data mapping is used to populate the fields of Case IQ user profiles based on the profile provided by the Identity Provider. Data mapping can be defined with an environment variable using the following format:

`'{caseIQField1}::{IdPField1};;{caseIQField2}::{IdPField2}'`

Example: The Identity Provider profiles have the following structure, which does not match the fields Case IQ uses for our user profiles.

```
{
	emailAddress,
	givenName,
	surname,
}
```

We can map these fields to a Case IQ profile by setting `HOTLINE_SSO_DATA_MAPPING_CONFIG='email::emailAddress;;firstName::givenName;;lastName::surname'`


This will produce the following mapping:

```
email <- emailAddress
firstName <- givenName
lastName <- surname
```

Note that the Case IQ property comes first, then the Identity Provider property.

In this example, when a user logs in via Hotline SSO, the values of the Case IQ fields `email`, `firstName`, and `lastName` will be populated using the values of the `emailAddress`, `givenName`, and `surname` fields from the Identity Provider's user profile.

### Default Values

`HOTLINE_SSO_DEFAULT_DATA_CONFIG`

A similar mapping can be used to define the default values for user profiles logging in via Hotline SSO. We can specify the default values for a profile using an environment variable.

This follows the same `'{field1}::{defaultValue1};;{field2}::{defaultValue2}'` format as the data mapping.

Example: We want to default value to be the following
```
locale: 'en_US'
name: 'None'
```

We can set `HOTLINE_SSO_DEFAULT_DATA_CONFIG='locale::en_US;;name::None'`

If there was no mapping defined for these properties, or if the Identity Provider profile did not map a value to them, they will be defaulted to `'en_US'` and `'None'` respectively.
