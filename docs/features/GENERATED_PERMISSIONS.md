# Generated Permissions

## ACL Rules

An attribute `generatePermissions` is available to be added to any ACL rule. Once added, an equivalent ACL rule(s) will be generated for that specific rule.
- New rule(s) will be generated with same conditions as the original as well as the conditions defined in `options.permission-generation-templates.js`
  - Original condition can be omitted from generated rule with `doNotAddToGeneratedRule` flag
- New rule(s) will use the original rule's roles and add the postfix defined in the corresponding permission generation template's `permissionCodeFn`
- This generated rules will include the `roles` of the Original Rule in the `roles` of the Generated Rule
  - This means that granting access using a one of the`roles` from the Original Rule will also grant access to the Generated Rule

Example:
```js
// Original Rule
.required({
    name: 'View Case',
    roles: ['view_case'],
    actions: ['load', 'list', 'save_existing', 'remove'],
    conditions: [permHelper.conditionForInternalRecord, conditionForOther, conditionForNonCanceledCase, {
        attributes: {
            sysSubmitted: true,
        },
    }],
    generatePermissions: ['external', 'external_not_created_by'],
})
// Generated 2 new rules
.required({
    name: 'View Case External',
    roles: ['view_case', 'view_case_external'],
    actions: ['load', 'list', 'save_existing', 'remove'],
    conditions: [conditionForOther, conditionForNonCanceledCase, {
        attributes: {
            sysSubmitted: true,
        },
    }, {
        attributes: {
            externalRecord: true,
        },
    }],
})
.required({
    name: 'View Case External Not Created By',
    roles: ['view_case', 'view_case_external_not_created_by'],
    actions: ['load', 'list', 'save_existing', 'remove'],
    conditions: [conditionForOther, conditionForNonCanceledCase, {
        attributes: {
            sysSubmitted: true,
        },
    }, {
        attributes: {
            externalRecord: true,
            createdBy: '{!user.id}',
        },
    }],
})
```
