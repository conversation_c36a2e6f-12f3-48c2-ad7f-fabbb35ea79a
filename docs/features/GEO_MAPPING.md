# Geo Mapping
This feature allows the usage of geo encoding, map tile APIs and map field types / components (both static and dynamic).

This feature is designed to work with MapBox APIs out of the box, however it may be possible to replace these with any Open Street Layers compatible APIs.

## Usage

Use the geo mapping feature in your environment by setting the enabling the feature toggle environment variable (see below) and setting the keys and access token hash.
End users will gain access to the relevant dynamic field types within the Form Builder and developers will be able to leverage the `coordinate` field type
### Options
See `options.geomapping.js` for additional configuration parameters including layers.

### Environment Variables

    ENABLE_GEO_MAPPING (boolean): *required* Set this true to enable mapping in Case IQ. Defaults to false.
    GEO_MAPPING_PROXY_MODE (boolean): Set this to false to disable proxying mapbox request to the backend. Defaults to true.
    DISABLE_GEO_MAP_ATTRIBUTION (boolean): Set this to true disable setting attributions on the map. Defaults to false.
    MAP_TILE_URL (string):  Set this string to change the URL for proxied tile requests. Will template values in {value} and defaults to mapbox api endpoint.
    MAP_STYLE_URL (string): Set this string to change the URL for proxied style requests. Will template values in {value} and defaults to mapbox api endpoint.
    GEO_MAPPING_SECRET_KEY (string): *required* The access token provided by Mapbox or other service to perform tile and search requests. This will exclusively be used in tbe backend for proxied requests. *Warning* If GEO_MAPPING_PROXY_MODE is set false, then this acces token will be used in the front end for every request. 
    GEO_MAPPING_FRONTEND_KEY (string): *required when proxying* An access token with no access to any requests. Is used to validate and load the mapbox search API when GEO_MAPPING_PROXY_MODE is set true (default).
    GEO_MAPPING_ACCESS_TOKEN_HASH (string): *required* A unique key tied to the associated GEO_MAPPING_SECRET_KEY. Used in tandem to validate requests to mapbox API.
