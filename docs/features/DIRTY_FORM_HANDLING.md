# Dirty Form Handling

Dirty Form Handling warns users of unsaved changes on a form, when they are about to navigate away from it, and provides them with the option to remain on the current page.

This is triggered by navigations within the app, page refresh, browser back/forward button, and exiting browser or browser tab.

A form is considered dirty if a user has modified a field on the form.

## Design

As Case IQ is a single page application, there are few ways for a user to navigate away from a page:

Clicking an HTML element inside the browser

Clicking “back” or “forward” button on the browser

Clicking a browser button or using browser keyboard shortcuts to do a complete page refresh.  Examples are pressing ENTER in URL bar, or clicking “x” to exit the browser.

For the first two, the method is not a full page refresh. Thus, we utilize view-manager and Backbone.view to determine if any forms are dirty before removing the view.

The third method on the other hand causes a full termination of the page. As the view manager cannot be utilized in this situation, the view listens to a `beforeunloadhandler` event which is triggered before a full page refresh. Details on event can be found here: https://developer.mozilla.org/en-US/docs/Web/API/Window/beforeunload_event.  We utilize the event to determine whether to show a Dirty notification popup. Beyond that, developers have little power over it as most modern browsers limit its configurability.

Regardless of how and when we listen to navigation changes, the logic for determining whether a view is considered dirty or not is wrapped in a function `isDirtied`.

For now, an unsaved change is defined as any user modified input. This means that the user has to actively change the value of the field for the field to be considered dirty. Any secondary changes (for example, a picklist causing another picklist to change value or a change in a number field causing a recalculation of another field) do not constitute the secondary field as being dirty.

To enable a Dirty Form Handling on a view, set `dirtyFormHandling` to `true` in the view’s features. All `backbone-form` views have it already enabled, so it shouldn’t be necessary in most cases.

## Administration

Under Settings > System > Options, Dirty Form Handling can be enabled or disabled. By default, it is enabled.

## Dirty Inline Forms

Inline forms have, by default, Dirty Form Handling enabled. As long as it’s enabled, it will trigger a dirty check just like any other Dirty Form would.

However, there may be scenarios in which we do not want the parent form to perform a dirty check but we still want the inline form to perform it. An example of this is when the parent form’s Save button is clicked. Since a save on the parent form may navigate away from the view, it is important to make sure the Inline Forms are not dirty first. In such scenarios the function `isSafeToLeaveDirtyInlines()` can be utilized to perform a dirty check on Inline forms alone.
