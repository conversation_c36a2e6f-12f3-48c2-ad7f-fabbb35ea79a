# Similar Parties

The Similar Parties feature notifies and assists users when creating a party. By finding matching values over a determined set of fields, it notifies users of parties that have similar data to the one they are filling out.

Clicking on any of the similar parties will open up a modal which will give the user more detailed information on the party. In addition, it can also autofill the rest of the form for the user by copying all the information from the similar party onto the party that is being created. This does not include any information stored in a child of the party. Also, 

> :information_source: **NOTE**: If there are any fields that should never be autofilled by the similar search feature, simply set `excludeFromAutofill` to `true` on the field’s configuration (see [entities/README](../../entities/README.md) for more information).

## Usage

By default, the Party Name (First Name + Last Name), Address, Home and Work #, and Email Address are mapped for matching to find similar parties. The mappings for determining match results is configured the same way as the mappings for Suggested Links. However, though the configuration is the same, the query generation is slightly different and should offer a wider range of field types that can be configured.

Moreover, the Similar Search feature is designed to be configurable for other entities as well. If you want another entity, say <PERSON>, to find similar records of the same entity, here are the steps to add it:

Add the desired configuration to the entity’s attribute `similarSearchConfig` (see party for example). The three subattributes that can be configured are the mappings, `titleField` which is the field to show as a title on the modal, and `formConfigName` which is the name of the form to display on the modal.

Under the entity’s `rowTemplates`, create a `similarSearch` template (see party for example).

In the desired view, enable the feature `similarSearch`.

Add translation for `groupName: ${entity}`, `subgroupName: 'general'`, and `key: 'similar'`.
