# Date/time formats

We support various date/time formats for displaying dates and times. The following can be used to customize how dates and times are shown throughout the app.

NOTE: Whenever possible, use the date/time formats defined in $appData (e.g. $appData.dateTimeFormat) so that any customizations made by admins on the front-end take precedence over the defaults.

## options.global.js

These are the default formats that can be configured by projects and are accessed via OptUtil.js.

### Date/time formats that accept Moment.js formatting. See https://momentjs.com/docs/#/displaying/format.

- defaultDateFormat: The default format for displaying a compact date with no time (e.g. 'DD-MMM-YYYY' which gives '31-Dec-2023')
- defaultDateLongFormat: The default format for displaying an expanded date with no time (e.g. 'MMMM D, YYYY' which gives 'December 31, 2023')
- defaultDateTimeFormat: The default format for displaying a compact date with a time (e.g. 'DD-MMM-YYYY h:mm A' which gives '31-Dec-2023 12:00 AM')
- defaultDateTimeFileFormat: The default format for displaying a compact date with a time without any spaces or colons for use in a filename (e.g. 'DD.MM.YYYY_h.mmA' which gives '31.12.2023_12.00AM')
- defaultDateTimeLongFormat: The default format for displaying an expanded date with a time (e.g. 'MMMM D, YYYY h:mm A' which gives 'December 31, 2023 12:00 AM')
- defaultTimeFormat: The default format for displaying a time (e.g. 'hh:mm A' which gives '12:00 AM')
- defaultTimezone: The default timezone to apply for dates and times (e.g. 'America/Toronto')

## system-config.json

These are the formats that can be configured by admins using the options service and are accessed on the front-end via Settings > System > Options. Admins can select from a picklist of date/time formats. By default, no values are selected.

- dateFormat: The date format for displaying a date with no time (e.g. 'YYYY-MM-DD' which gives '2023-12-31')
- dateTimeFormat: The date format for displaying a date with a time (e.g. 'YYYY-MM-DD 12h' which gives '2023-12-31 12:00 AM')
- timeFormat: The time format for displaying a time (e.g. '12h' which gives '12:00 AM')

## $appData

These are the formats available specifically on the front-end. They use the date/time formats configured by admins using the options service if a value is available, otherwise they fall back on the defaults set in options.global.js.

- dateFormat: The date format for displaying a date with no time (e.g. 'YYYY-MM-DD' which gives '2023-12-31')
- dateTimeFormat: The date format for displaying a date with a time (e.g. 'YYYY-MM-DD 12h' which gives '2023-12-31 12:00 AM')
- timeFormat: The time format for displaying a time (e.g. '12h' which gives '12:00 AM')
