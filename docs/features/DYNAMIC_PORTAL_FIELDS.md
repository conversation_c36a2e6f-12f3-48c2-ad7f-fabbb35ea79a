# Dynamic Fields on Portal

Dynamic fields can be added to `partiallyDynamic` forms and used on the external Portal. Case and Party entities are supported by default.

When external dynamic fields have been enabled for an entity, the "Show on Portal" option will be available when adding fields through the form builder.

## Requirements

External portal is enabled:
- The `Portal` feature flag should be set to `true`.

External dynamic fields can be externally writable:
- Set `allowDynamicFieldsOnPortal: true` on entity definition.

External API routes are set up for entity:
```
api: {
    useGenericApiExternal: true,
},
allowExternalSearch: true,
```

External form exists for entity with insertion points:
- See `config/form-layouts/README.md`
