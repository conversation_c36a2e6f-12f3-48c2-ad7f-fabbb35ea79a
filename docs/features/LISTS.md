# Lists

Users can to create their lists and group/associate different cases with lists created. Maximum number of lists per user is controlled by a static JSON option `maxNumberOfListsPerUser` in `options.global.js`.

## Usage

### Creating Lists

Lists can be created by clicking the Lists option under settings gear icon on top right or selecting ellipses option next to the case record in the grid.

### Adding to Lists

Users have the ability to add cases to lists that they have created. They can add single case to multiple lists(through lists option from ellipses) or through 'Add to List' bulk action on the grid

To add the case, user will need to select the desired list(s) and click `Apply`

### Viewing a list

Users can filter cases by selecting the desired list filter or navigating to the individual list.

### Remove from list

Case can be removed from the list(s) the exact way case is added to a list by clicking ellipses icon and selecting lists option.

Multiple cases can be removed from list by navigating to a specific list and doing a batch action 'Remove from List'

### Editing/Deleting Lists

Existing lists in the system can be edited or deleted through the list management view.

### Bookmarks

Users will be able to bookmark particular records through the grid or record view and it will work the same way as other lists.

## Extending

Extends `standard-config`.

> :warning: **WARNING**: This is a standardized platform entity that drives out of box functionality and it is not recommended to extend this entity.
