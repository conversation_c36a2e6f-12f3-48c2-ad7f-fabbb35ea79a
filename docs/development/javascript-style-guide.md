# JavaScript Style Guide
# [Official Airbnb Javascript style guide](https://github.com/airbnb/javascript)

# Table of contents

- [JavaScript Style Guide](#javascript-style-guide)
- [Official Airbnb Javascript style guide](#official-airbnb-javascript-style-guide)
- [Table of contents](#table-of-contents)
	- [Tabs for indention](#tabs-for-indention)
	- [Newlines](#newlines)
	- [No trailing whitespace](#no-trailing-whitespace)
	- [Use Semicolons](#use-semicolons)
	- [100 characters per line](#100-characters-per-line)
	- [Use single quotes](#use-single-quotes)
	- [Opening braces go on the same line](#opening-braces-go-on-the-same-line)
	- [Method chaining](#method-chaining)
	- [Declare one variable per const/let statement](#declare-one-variable-per-constlet-statement)
	- [Use lowerCamelCase for variables, properties and function names](#use-lowercamelcase-for-variables-properties-and-function-names)
	- [Use UpperCamelCase for class names](#use-uppercamelcase-for-class-names)
	- [Use UPPERCASE for Constants](#use-uppercase-for-constants)
	- [Object / Array creation](#object--array-creation)
	- [Use the === operator](#use-the--operator)
	- [Use multi-line ternary operator](#use-multi-line-ternary-operator)
	- [Do not extend built-in prototypes](#do-not-extend-built-in-prototypes)
	- [Use descriptive conditions](#use-descriptive-conditions)
	- [Write small functions](#write-small-functions)
	- [Return early from functions](#return-early-from-functions)
	- [Name your closures](#name-your-closures)
	- [No nested closures](#no-nested-closures)
	- [Object.freeze, Object.preventExtensions, Object.seal, with, eval](#objectfreeze-objectpreventextensions-objectseal-with-eval)
	- [Getters and setters](#getters-and-setters)
	- [Private and Public](#private-and-public)

## Tabs for indention

Use tabs for indenting your code and swear an oath to never mix tabs and spaces - a special kind of hell is awaiting you otherwise.
Using tabs allows the developer to switch between 4 space or 2 space tabs if they wish, depending on how they like to work.

## Newlines

Use UNIX-style newlines (`\n`), and a newline character as the last character
of a file. Windows-style newlines (`\r\n`) are forbidden inside any repository.

## No trailing whitespace

Just like you brush your teeth after every meal, you clean up any trailing
whitespace in your JS files before committing. Otherwise the rotten smell of
careless neglect will eventually drive away contributors and/or co-workers.

## Use Semicolons

According to [scientific research][hnsemicolons], the usage of semicolons is a core value of our community. Consider the points of [the opposition][], but be a traditionalist when it comes to abusing error correction mechanisms for
cheap syntactic pleasures.

[the opposition]: http://blog.izs.me/post/2353458699/an-open-letter-to-javascript-leaders-regarding
[hnsemicolons]: http://news.ycombinator.com/item?id=1547647

## 100 characters per line

Limit your lines to 100 characters. Yes, screens have gotten much bigger over the last few years, but your brain has not. Use the additional room for split screen, your editor supports that, right?  
In Sublime, go to View -> Ruler -> 100

## Use single quotes

Use single quotes, unless you are writing JSON.

**Right:**

```js
const foo = 'bar';
```

**Wrong:**

```js
const foo = "bar";
```

## Opening braces go on the same line

Your opening braces go on the same line as the statement.

**Right:**

```js
if (true) {
  console.log('winning');
}
```

**Wrong:**

```js
if (true)
{
  console.log('losing');
}
```

Also, notice the use of whitespace before and after the condition statement.

## Method chaining

One method per line should be used if you want to chain methods.

You should also indent these methods so it's easier to tell they are part of the same chain.

**Right:**

```js
User
  .findOne({ name: 'foo' })
  .populate('bar')
  .exec((err, user)=>{
    return true;
  });
```

**Wrong:**

```js
User
.findOne({ name: 'foo' })
.populate('bar')
.exec((err, user) =>{
  return true;
});

User.findOne({ name: 'foo' })
  .populate('bar')
  .exec((err, user)=>{
    return true;
  });

User.findOne({ name: 'foo' }).populate('bar')
.exec((err, user)=>{
  return true;
});

User.findOne({ name: 'foo' }).populate('bar')
  .exec((err, user)=>{
    return true;
  });
```

## Declare one variable per const/let statement

Declare one variable per const/let statement, it makes it easier to re-order the
lines. However, ignore [Crockford][crockfordconvention] when it comes to
declaring variables deeper inside a function, just put the declarations wherever
they make sense.

**Right:**

```js
const keys   = ['foo', 'bar'];
const values = [23, 42];

let object = {};
while (keys.length) {
  const key = keys.pop();
  object[key] = values.pop();
}
```

**Wrong:**

```js
const keys = ['foo', 'bar'],
    values = [23, 42],
    key;
let object = {},

while (keys.length) {
  const key = keys.pop();
  object[key] = values.pop();
}
```

[crockfordconvention]: http://javascript.crockford.com/code.html

## Use lowerCamelCase for variables, properties and function names

Variables, properties and function names should use `lowerCamelCase`.  They
should also be descriptive. Single character variables and uncommon
abbreviations should generally be avoided.

**Right:**

```js
const adminUser = db.query('SELECT * FROM users ...');
```

**Wrong:**

```js
const admin_user = db.query('SELECT * FROM users ...');
```

## Use UpperCamelCase for class names

Class names should be capitalized using `UpperCamelCase`.

**Right:**

```js
function BankAccount() {
}
```

**Wrong:**

```js
function bank_Account() {
}
```

## Use UPPERCASE for Constants

Constants should be declared as regular variables or static class properties,
using all uppercase letters.

Node.js / V8 actually supports mozilla's [const][const] extension, but
unfortunately that cannot be applied to class members.

**Right:**

```js
const SECOND = 1 * 1000;

function File() {
}
File.FULL_PERMISSIONS = 0777;
```

**Wrong:**

```js
const SECOND = 1 * 1000;

function File() {
}
File.fullPermissions = 0777;
```

[const]: https://developer.mozilla.org/en/JavaScript/Reference/Statements/const

## Object / Array creation

Use trailing commas and put *short* declarations on a single line. Only quote
keys when your interpreter complains:

**Right:**

```js
const a = ['hello', 'world'];
const b = {
  good: 'code',
  'is generally': 'pretty',
};
```

**Wrong:**

```js
const a = [
  'hello', 'world'
];
const b = {"good": 'code'
        , is generally: 'pretty'
        };
```

## Use the === operator

Programming is not about remembering [stupid rules][comparisonoperators]. Use
the triple equality operator as it will work just as expected.

***Right:***

```js
const a = 0;
if (a !== '') {
  console.log('winning');
}

```

***Wrong:***

```js
const a = 0;
if (a == '') {
  console.log('losing');
}
```

[comparisonoperators]: https://developer.mozilla.org/en/JavaScript/Reference/Operators/Comparison_Operators

## Use multi-line ternary operator

The ternary operator should not be used on a single line. Split it up into multiple lines instead.

**Right:**

```js
const foo = (a === b)
  ? 1
  : 2;
```

**Wrong:**

```js
const foo = (a === b) ? 1 : 2;
```

## Do not extend built-in prototypes

Do not extend the prototype of native JavaScript objects. Your future self will
be forever grateful.

**Right:**

```js
const a = [];
if (!a.length) {
  console.log('winning');
}
```

**Wrong:**

```js
Array.prototype.empty = function() {
  return !this.length;
}

const a = [];
if (a.empty()) {
  console.log('losing');
}
```

## Use descriptive conditions

Any non-trivial conditions should be assigned to a descriptively named variable or function:

**Right:**

```js
const isValidPassword = password.length >= 4 && /^(?=.*\d).{4,}$/.test(password);

if (isValidPassword) {
  console.log('winning');
}
```

**Wrong:**

```js
if (password.length >= 4 && /^(?=.*\d).{4,}$/.test(password)) {
  console.log('losing');
}
```

## Write small functions

Keep your functions short. A good function fits on a slide that the people in
the last row of a big room can comfortably read. So don't count on them having
perfect vision and limit yourself to ~15 lines of code per function.

## Return early from functions

To avoid deep nesting of if-statements, always return a function's value as early
as possible.

**Right:**

```js
function isPercentage(val) {
  if (val < 0) {
    return false;
  }

  if (val > 100) {
    return false;
  }

  return true;
}
```

**Wrong:**

```js
function isPercentage(val) {
  if (val >= 0) {
    if (val < 100) {
      return true;
    } else {
      return false;
    }
  } else {
    return false;
  }
}
```

Or for this particular example it may also be fine to shorten things even
further:

```js
function isPercentage(val) {
  const isInRange = (val >= 0 && val <= 100);
  return isInRange;
}
```

## Name your closures

Feel free to give your closures a name. It shows that you care about them, and
will produce better stack traces, heap and cpu profiles.

**Right:**

```js
req.on('end', function onEnd() {
  console.log('winning');
});
```

**Wrong:**

```js
req.on('end', ()=>{
  console.log('losing');
});
```

## No nested closures

Use closures, but don't nest them. Otherwise your code will become a mess.

**Right:**

```js
setTimeout(()=>{
  client.connect(afterConnect);
}, 1000);

function afterConnect() {
  console.log('winning');
}
```

**Wrong:**

```js
setTimeout(()=>{
  client.connect(()=>{
    console.log('losing');
  });
}, 1000);
```


## Object.freeze, Object.preventExtensions, Object.seal, with, eval

Crazy shit that you will probably never need. Stay away from it.

## Getters and setters

Do not use setters, they cause more problems for people who try to use your
software than they can solve.

Feel free to use getters that are free from [side effects][sideeffect], like
providing a length property for a collection class.

[sideeffect]: http://en.wikipedia.org/wiki/Side_effect_(computer_science)

## Private and Public

Private methods should be prefixed with '_' and placed at the bottom of 'class' objects.


[Original Source](https://github.com/felixge/node-style-guide)
