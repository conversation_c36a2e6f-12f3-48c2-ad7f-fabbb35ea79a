# Table of contents
- [Table of contents](#table-of-contents)
- [No<PERSON><PERSON> (Auto App Restart)](#nodemon-auto-app-restart)
	- [Official Documentation](#official-documentation)
- [Setup](#setup)
	- [Install nodemon globally](#install-nodemon-globally)
	- [Add to package.json](#add-to-packagejson)
	- [Setup ignore folders or files](#setup-ignore-folders-or-files)
- [Basic usage](#basic-usage)
	- [Run the app](#run-the-app)
	- [Alias in bash profile](#alias-in-bash-profile)

# Nodemon (Auto App Restart)
Automatically restarting the app when a back-end file changes is done with the node package nodemon.

## [Official Documentation](https://www.npmjs.com/package/nodemon)

# Setup

## Install nodemon globally
```bash
$ npm install -g nodemon
```

## Add to package.json
```JSON
{
	"name": "example",
	"homepage": "http://example.com",
	"...": "... other standard package.json values",
	"nodemonConfig": {
		"delay": "2500"
	}
}
```

## Setup ignore folders or files
The nodemon module will pickup the `nodemonConfig` specifies which directories shouldn't make the app restart when a file changes and delay.

```JSON
"nodemonConfig": {
	"ignore": ["test/*", "docs/*"],
	"delay": "2500"
}
```


# Basic usage

## Run the app
In general instead of starting your app with `node`, you start your app with `nodemon`.
```bash
$ nodemon server.js
```

## Alias in bash profile

You can make this easier by updating your bash_profile. The following should be added:


```bash
$ alias run='nodemon --watch $PLATFORM_PATH --watch .. server.js'
```
Modifying your start app alias with something like the following should do it.

**Note:** this is specific to nodemon commands, there may be more globals or arguments in the command,
but below is an example:
