# Table of contents
- [Table of contents](#table-of-contents)
- [Prerequisites](#prerequisites)
	- [Install XCode](#install-xcode)
	- [Install Homebrew](#install-homebrew)
- [Install Software Dependencies](#install-software-dependencies)
	- [Postgres](#postgres)
	- [Docker for Mac](#docker-for-mac)
	- [NVM, and Node](#nvm-and-node)
	- [Yarn](#yarn)
		- [Install Yarn](#install-yarn)
		- [Update bash Profile](#update-bash-profile)
		- [Upgrade Yarn](#upgrade-yarn)
		- [Test that Yarn is installed by running:](#test-that-yarn-is-installed-by-running)
- [Setup Github](#setup-github)
- [ENV Setup](#env-setup)
- [Platform Installation](#platform-installation)
	- [Prepare platform dependencies](#prepare-platform-dependencies)
- [Config installation](#config-installation)
	- [Clone base](#clone-base)
	- [Set origin to new repository](#set-origin-to-new-repository)
	- [Change Node version](#change-node-version)
	- [Prepare Config dependencies](#prepare-config-dependencies)
	- [New config setup](#new-config-setup)
	- [Run Application](#run-application)
	- [Run watch](#run-watch)
# Prerequisites

## Install XCode
XCode is a bundle for software for development. It is required to compile C/C++ source files, among other things.
The installation from the store is quite massive and will probably take over an hour to download. It comes with a lot
of bloat; software we do not need. Thankfully, we can install just the command line tools.

```bash
$ xcode-select --install
```

## Install Homebrew 
The system depends upon a variety of external software. Our application server, data stores, text editor, and package
manager all need to be installed. Fear not, however, as we will be using [Homebrew](https://brew.sh) to download and install our software
dependencies. **Homebrew** is a command-line utility which allows you to quickly and easily manage installed software in
OSX. If you have ever used **apt-get**, **nu-get**, or **chocolatey** you should feel right at home.
 ```bash
$ /usr/bin/ruby -e "$(curl -fsSL https://raw.githubusercontent.com/Homebrew/install/master/install)"
$ brew tap caskroom/cask
 ```

# Install Software Dependencies
Now that we have access to the brew utility at the command line, we can get some serious dependency installation going.

## Postgres
command line tools for postgres

```bash
$ brew install postgres
```

## Docker for Mac 
Our application relies on a number of services (postgres, redis, elasticsearch, quartz, etc.) We use docker to easily manage different versions,
and instances of these services depending on the project
```bash
$ brew update
$ brew cask install docker
```

## NVM, and Node  
[Node](https://nodejs.org/en/) is a platform built on [Chrome's JavaScript runtime](https://v8.dev/). Since, We use multiple versions of node depending on the project,
we use a tool called [nvm](https://github.com/creationix/nvm) to quickly switch between these versions.
```bash
$ touch ~/.bash_profile
$ curl -o- https://raw.githubusercontent.com/creationix/nvm/v0.34.0/install.sh | bash
```

## Yarn
[Yarn](https://yarnpkg.com/lang/en/) is a node dependency Manager.

### Install Yarn
```bash
$ brew install yarn
```

### Update bash Profile
Add the following to `.bash_profile` and restart the terminal
```bash
export PATH="$PATH:`yarn global bin`"
```

### Upgrade Yarn
```bash
$ brew upgrade yarn
```

### Test that Yarn is installed by running:
```bash
$ yarn --version
```

# Setup Github
Ensure that you have a valid GitHub account in the Case IQ org 

Setup your SSH keys for git to use to pull private dependencies
See GitHub documentation for help (https://help.github.com/articles/generating-a-new-ssh-key-and-adding-it-to-the-ssh-agent/)

# ENV Setup
Environment variables are used to change the way Case IQ application will behave on a without modifying the code.

Setup your environment variables for Case IQ to run in a development mode
Here is an example of a local set of values (see feature documentation for specifics)

```bash
export NODE_ENV=development
export DISABLE_DB_BACKUP=true
export DISABLE_ES_SNAPSHOT=true
export SLEEP_TIME=0
export ALLOWED_REFERERS=true
export DISABLE_SECURE_COOKIES=true
export DISABLE_PROXY=true
export SAME_SITE=false
export YF_ENABLED=false
export DEV_SERVER=true
export SOURCE_MAP=1
export MAIL_IGNORE_TLS=true
export MAIL_DISABLE_DNS_VALID=true
export MAIL_OVERRIDE_RECIP=$EMAIL_ADDRESS
export MAIL_TRANSPORT=direct
export MAILSRV_DOMAIN=localhost.local
## elasticsearch 
export ES_USER=elastic
export ES_PASS=changeme
```

# Platform Installation
Start up your services (if using docker)
```bash
$ cd ~/git/isight_main_v5_beta
$ make docker-up
```

## Prepare platform dependencies
```bash
$ yarn install
$ yarn link
```
# Config installation

## Clone base
Clone the base application from git in a new folder:
```bash
$ cd ~/git
$ <NAME_EMAIL>:i-Sight/config_base_v5.git $PROJECT_DIRECTORY
```

## Set origin to new repository
Inside of the new folder Set the new git repository as the main repository:
```bash
$ cd $PROJECT_DIRECTORY
$ git remote set-<NAME_EMAIL>:i-Sight/$PROJECT_DIRECTORY.git
```
Push the local files to the new git repository:
```bash
$ git push origin develop
```

## Change Node version
Use the correct version of node for the project (install it if you do not have it)

```bash
$ nvm use
```

## Prepare Config dependencies

```bash
$ yarn install
$ yarn link isight
```

## New config setup
Remove any previous installation, setup the application and add test users
```bash
$ make breakdown
$ make setup
$ make create-sample-users
```

## Run Application

Run the application and open [localhost:8000](http://localhost:8000)
```bash
$ node server.js
```

## Run watch
Run webpack-dev-server in a new terminal window [localhost:9010](http://localhost:9010)
```bash
$ make watch
```
