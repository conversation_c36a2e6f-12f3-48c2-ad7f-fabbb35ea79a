
# Oracle Step-by-Step Setup

These instructions will guide you through the process of setting up Case IQ to run on an Oracle database. It includes installing a VM with Oracle pre-installed, installing SQLPlus so you can access the Oracle database from the local CLI, install a GUI Oracle client for the Mac, setup your local environment variables.





## Oracle Database Setup in a Virtual Machine

* Download and install [VirtualBox](http://download.virtualbox.org/virtualbox/5.2.0/VirtualBox-5.2.0-118431-OSX.dmg), the VM client that will host the Oracle Database.
* Download the [VM with the Oracle Database](http://www.oracle.com/technetwork/database/enterprise-edition/databaseappdev-vm-161299.html) preinstalled. The link on the page is `Oracle DB Developer VM`. Note that you'll have to create an Oracle account before downloading. This VM consists of:
    * _Oracle Linux 7_
    * _Oracle Database 12c Release 2 Enterprise Edition (******** Linux x86-64)_
    * _Oracle XML DB_
    * _Oracle SQL Developer 4.2_
    * _Oracle Application Express 5.1_
    * _Oracle REST Data Services 3.0.9_
    * _Hands-On-Labs (accessed via the Toolbar Menu in Firefox)_
* If needed, you can find other Oracle VMs [here](http://www.oracle.com/technetwork/community/developer-vm/index.html).
* From the VirtualBox app go to the `File->Import Appliance…` menu and select the downloaded VM .ova file that should be called something like `DeveloperDaysVM2017-06-13_01.ova`.
* Once the VM has been imported you'll see it in the left sidebar of VirtualBox. Right click it and select `Settings` then click on the `Network` icon. The `Enable Network Adapter` checkbox should be checked. From the `Attached to` dropdown select `Bridged Adapter`. This will setup the VM as a regular machine with its own IP address on the network.
* To determine the VMs IP address, you can get it in various ways:
    * From within the VM click on the down arrow in the top right corner of the Linux screen and click on the `Wired` entry and then `Wired Settings`. 
    * Or, in the terminal enter the command `ifconfig -a`. The IP address for the VM will be seen in the `eth0` entry beside `inet`.
* Ensure you can see the VM from your Mac by getting a response from a `ping <VM IP address>` from the local CLI.
* Here are details of the pre-installed Oracle Database in the VM:
    * __Oracle SID__: orcl12c
    * __Database__: orcl
    * __Pluggable DB__: ords create if required by running:  'newpdbords' in the terminal - takes a few minutes.
    * __ALL PASSWORDS ARE (for ‘HR’, ‘sys’ and ‘system’)__ : oracle
    * Login from sqlplus with `system/oracle` (example: `$ sqlplus system/oracle@//*************`)
    * __Folder with oracle data__: ‘/u01/app/oracle/oradata/orcl12c’
    * __Once you create the databases, they should be in this location__: ‘/u01/app/oracle/oradata/orcl12c/dbs/isight’





## Installing SQLPlus for the Local CLI

These instructions are all to be made locally on your Mac.
* Download the following files [here](http://www.oracle.com/technetwork/topics/intel-macsoft-096467.html).
    * instantclient-basiclite-macos.x64-********.0.zip
    * instantclient-sqlplus-macos.x64-********.0.zip
    * instantclient-sdk-macos.x64-********.0.zip
* Create the folder `~/instantclient_12_2`.
* Unzip each of the 3 downloaded zip files into the folder created above. Note that you want the contents of each zip in this folder all mashed together, not a folder representing each zip so you should have around 21 files and one folder called `sdk`.
* If you don’t already have it, create the folder `~/lib`.
* Create the necessary links as follows:
    *   `ln -s ~/instantclient_12_2/libclntsh.dylib ~/lib/`
    *   `ln -s ~/instantclient_12_2/libclntsh.dylib.12.1 ~/lib/`
* Update your path variable as follows: `export PATH=~/instantclient_12_2:$PATH`
* Set the following environment variables:
    * `export OCI_HOME=~/instantclient_12_2`
    * `export OCI_VERSION=12`
    * `export OCI_LIB_DIR=~/instantclient_12_2`
    * `export OCI_INC_DIR=~/instantclient_12_2/sdk/include`
* Ensure you're in a fresh CLI environment locally on your Mac, you can just fire up a new terminal to be sure. You should now be able to run `sqlplus` from the CLI and it'll ask you for a user-name. You can terminate this, we'll get back to it later.





## Setup Oracle GUI Client

* Download, install and run [SQL Developer](http://www.oracle.com/technetwork/developer-tools/sql-developer/downloads/index.html).
    * Right click on `Connections` in the left sidebar and click `New Connection`
    * Fill out the New / Select Database Connection form. Unless specified below, leave defaults.
        * __Connection Name__: Linux VM _(or whatever you want)_
        * __Username__: system
        * __Password__: oracle
        * __Hostname__: ************* _(or whatever your VM is assigned)_
        * __Service Name__: orcl





## Setup a new Admin user in Oracle

\# Connect to Oracle as `sysdba` (replace IP with your Oracle location)  
```
$ sqlplus sys/oracle@//*************/orcl as sysdba
SQL> create user isightadmin identified by 123456; 
SQL> grant dba to isightadmin;
```





## Environment Setup

Setup your bash script to include the required environment variables.
I created a new file called `.isight_oracle_aliases` in my home folder (`~`) with the contents below and include them into my environment by adding the following into my `.bash_profile` in the same folder:
```
if [ -f ~/.isight_oracle_aliases ]; then
    . ~/.isight_oracle_aliases
fi
```

Following are the variables to put into your bash (or as an option noted above, into a `.isight_oracle_aliases` file. Ensure you update the IP address to reflect your Linux Oracle VM (below it's *************):

```
echo Executing ~/.isight_oracle_aliases...

# The Container Database that our tablespaces will be in
export ORACLE_CONTAINER_DB_NAME="orcl"
export ORACLE_CONTAINER_DB_USER="system"
export ORACLE_CONTAINER_DB_PASS="oracle"

export ORACLE_ADMIN_USER=sys
export ORACLE_ADMIN_PASS=oracle

# Case IQ (base dataset)
export DB_CLIENT="oracledb"
export DB_NAME="orcl"
export DB_HOST=*************
export DB_TABLESPACE_NAME="isight"
export DB_USER="isight_root_user"
export ORACLE_DB_USER=isight_root_user
export DB_PASS="oracle"
# Case IQ (base dataset) Test
export TEST_DB_CLIENT="oracledb"
export TEST_DB_NAME="orcl"
export TEST_DB_HOST=*************
export TEST_DB_TABLESPACE_NAME="test_isight"
export TEST_ORACLE_PASS="oracle"
export TEST_ORACLE_DB_USER="test_isight_root_user"
export TEST_ORACLE_DB_PASS="oracle"

# Audit
export AUDIT_DB_CLIENT="oracledb"
export AUDIT_DB_NAME="orcl"
export AUDIT_DB_HOST=*************
export AUDIT_DB_TABLESPACE_NAME="isight_audit"
export ORACLE_AUDIT_DB_USER="isight_audit_user"
export AUDIT_DB_USER="isight_audit_user"
export AUDIT_DB_PASS="oracle"
# Audit Test
export TEST_AUDIT_DB_CLIENT="oracledb"
export TEST_AUDIT_DB_NAME="orcl"
export TEST_AUDIT_DB_HOST=*************
export TEST_AUDIT_DB_TABLESPACE_NAME="test_isight_audit"
export TEST_AUDIT_DB_USER="test_isight_audit_user"
export TEST_AUDIT_DB_PASS="oracle"

# Quartz
export QUARTZ_DB_CLIENT="oracledb"
export QUARTZ_DB_NAME="orcl"
export QUARTZ_DB_HOST=*************
export QUARTZ_DB_TABLESPACE_NAME="isight_quartz"
export ORACLE_QUARTZ_DB_USER="isight_quartz_user"
export QUARTZ_DB_USER="isight_quartz_user"
export QUARTZ_DB_PASS="oracle"
# Quartz Test
export TEST_QUARTZ_DB_CLIENT="oracledb"
export TEST_QUARTZ_DB_NAME="orcl"
export TEST_QUARTZ_DB_HOST=*************
export TEST_QUARTZ_DB_TABLESPACE_NAME="test_isight_quartz"
export TEST_QUARTZ_DB_USER="test_isight_quartz_user"
export TEST_QUARTZ_DB_PASS="oracle"

# Filestore
export FILESTORE_DB_CLIENT="oracledb"
export FILESTORE_DB_NAME="orcl"
export FILESTORE_DB_HOST=*************
export FILESTORE_DB_TABLESPACE_NAME="isight_filestore"
export ORACLE_FILESTORE_DB_USER="isight_filestore_user"
export FILESTORE_DB_USER="isight_filestore_user"
export FILESTORE_DB_PASS="oracle"
# Filestore Test
export TEST_FILESTORE_DB_CLIENT="oracledb"
export TEST_FILESTORE_DB_NAME="orcl"
export TEST_FILESTORE_DB_HOST=*************
export TEST_FILESTORE_DB_TABLESPACE_NAME="test_isight_filestore"
export TEST_FILESTORE_DB_USER="test_isight_filestore_user"
export TEST_FILESTORE_DB_PASS="oracle"
```





## Backups

We’ll be using RMAN (Recovery MANager) which is supplied by Oracle to perform our backups and recovery.

Note that there are two processes that can be involved in a restoring a database. One is the actual `restore`. This involves copying the backup. This will reset the database to what it looked like at the time of the backup. From there, you can also run a `recover` after the restore which will apply all of the archivelog and redo log files to bring the database back to what it looked like at the point of failure. By doing the recover you don't lose the changes to the database between the time of the backup and the time of failure.

So in short...  
**Restore:** Copy files from the backup, overwriting the existing database files.

**Recovery:** Applies the changes to the database until the point of failure. These changes are recorded in online redolog and archivelog (which are the backups of redolog) files.





### Preparation

To use RMAN effectively we’ll need to ensure we have `ARCHIVELOG` mode turned on which is off by default. The following instructions will show you have to flip it on. Note that your command responses may not be identical to what you see below but they should be relatively close.





### Create SSH Keypair

Now we want to be able to execute RMA scripts on the Oracle VM via our projects local makefile. To do this we’ll need to create an SSH Keypair so we can execute the scripts via a CLI command and not enter the password. If you need to enter a password, it will simply fail when being executed from inside a makefile.

Let's start in our .ssh folder. If it doesn't exist yet, create it with `mkdir ~/.ssh`.
```
$ cd ~/.ssh
```

We'll generate our key here. You can rename the id_rsa file to what you like. Below I called it `id_rsa_ora`. For the passphrase prompts, just press enter.

```
$ ssh-keygen -t rsa
Generating public/private rsa key pair.
Enter file in which to save the key (/Users/<USER>/.ssh/id_rsa): /Users/<USER>/.ssh/id_rsa_ora
Enter passphrase (empty for no passphrase): [enter]
Enter same passphrase again: [enter]
Your identification has been saved in /Users/<USER>/.ssh/id_rsa_ora.
Your public key has been saved in /Users/<USER>/.ssh/id_rsa_ora.pub.
The key fingerprint is:
SHA256:37bD5xKxqmemBySNA3kaH75mfsEBVDsJlf7ExHi+EgA tgaylord@CEC000218
The key's randomart image is:
+---[RSA 2048]----+
|    E+ooo+       |
|    + =.oo+      |
|     B B+=       |
|    . * *.+ .    |
|       *S= . o   |
|      + =.o.o    |
|     +   +.oo.   |
|      . . *.+..  |
|       .oB  .=.  |
+----[SHA256]-----+
```
Now create a `.ssh` folder on the VM. If it already exists, that's fine as this will have no effect.
```
$ ssh -i ~/.ssh/id_rsa_ora oracle@************* mkdir -p .ssh
oracle@*************'s password: [default is "oracle"]
```
Here we'll add identity to the local ssh authorizer.
```
$ ssh-add id_rsa_ora
Identity added: id_rsa_ora (id_rsa_ora)
```
Once that's done, copy the public key to the Oracle VM.
```
$ scp id_rsa_ora.pub oracle@*************:~/.ssh/
oracle@*************'s password: [default is "oracle"]
id_rsa_ora.pub     100%  400   238.9KB/s   00:00  
```
Finally, add the public key to the `authorized_keys` on the remote host.
```
$ ssh-copy-id -i ~/.ssh/id_rsa_ora.pub oracle@*************
/usr/bin/ssh-copy-id: INFO: Source of key(s) to be installed: "/Users/<USER>/.ssh/id_rsa_ora.pub"
/usr/bin/ssh-copy-id: INFO: attempting to log in with the new key(s), to filter out any that are already installed
/usr/bin/ssh-copy-id: INFO: 1 key(s) remain to be installed -- if you are prompted now it is to install the new keys
oracle@*************'s password: [default is "oracle"]

Number of key(s) added:        1

Now try logging into the machine, with:   "ssh 'oracle@*************'"
and check to make sure that only the key(s) you wanted were added.
```
Sweet! You should now be able to login to the VM without a password by running the following on your local machine.
```
$ ssh oracle@*************
Last login: <last login date and time> from <your computer name>
[oracle@localhost ~]$ 
```





### SQLPlus Commands to Prepare RMAN

These commands will prepare the Oracle database for using RMAN.

The following should be entered in your local CLI. The username should be `sys` and the password `oracle`. Also ensure you use your own VMs IP address (below it's *************). Any line below prefixed with `#` is a comment.

```
$ sqlplus /nolog
SQL> conn sys/oracle@//************* as sysdba
Connected.

# Check to see that we're in "No Archive Mode"
SQL> archive log list
Database log mode	       No Archive Mode
Automatic archival	       Disabled
Archive destination	       USE_DB_RECOVERY_FILE_DEST
Oldest online log sequence     13
Current log sequence	       15
SQL> select log_mode from v$database;
LOG_MODE
------------
NOARCHIVELOG
Database is in NOARCHIVELOG mode.

# Being in NOARCHIVELOG mode is what we would expect by default. If 
# this isn’t the case for you, you can skip to the RMAN Commands 
# section below.
# Note: If the response from the "startup mount" command  tells you that
# oracle is already running then run "shutdown immediate" and re-run
# the `startup mount` command. 

# Now lets turn ARCHIVELOG on.
SQL> startup mount;
ORACLE instance started.

Total System Global Area  838860800 bytes
Fixed Size		    8798312 bytes
Variable Size		  339742616 bytes
Database Buffers	  486539264 bytes
Redo Buffers		    3780608 bytes
Database mounted.

SQL> alter database archivelog;

Database altered.

SQL> alter database open;

Database altered.

SQL> select open_mode from v$database;

OPEN_MODE
------------------------------------------------------------
READ WRITE

# Optional. To change the location of the archive log you can use the 
# the following command.
SQL> alter system set log_archive_dest_1='location=/home/<USER>/arch' scope=both;
System altered.

SQL> archive log list
Database log mode	       Archive Mode
Automatic archival	       Enabled
Archive destination	       USE_DB_RECOVERY_FILE_DEST
Oldest online log sequence     13
Next log sequence to archive   15
Current log sequence	       15

# Check that the recovery location is what you expect.
SQL> show parameter db_recovery_file_dest

NAME			TYPE           VALUE
----------------------------------------------------------------------

db_recovery_file_dest		string	 /u01/app/oracle/fast_recovery_area/orcl12c

db_recovery_file_dest_size	big integer	5G
```

We’re done with the sqlplus commands for now. Lets move on to RMAN.





## RMAN Commands

RMAN doesn’t have a remote client as does sqlplus so you’ll have to be on the VM that Oracle is installed on. If you setup Oracle as above on a Linux machine then go into the VM, fire up the terminal and get started with the following.

FYI, although the RMAN commands we'll be talking about will be setup in scripts and are able to be executed remotely you can test out commands and play with RMAN in the RMAN command prompt. To access the prompt, log into the Oracle VM, fire up the terminal and enter the following (assuming you're using the default username `sys` and default password of `oracle`.
```
$ rman target sys/oracle
```





### Create RMAN Scripts to Backup/Restore Database

On the Linux VM, run the following from the CLI.

```
# Move into Oracle’s data folder
$ cd /u01/app/oracle/oradata/orcl12c

# Ensure you have a `backups` folder
$ mkdir backups
```





#### RMAN Script: backup-database.rman

```
# Now create a file called “backup-database.rman”
$ vi backup-database.rman
```
Once in the VI editor, press the “I” key to enter the “Insert” mode and type in or paste the following (the sys/oracle is the username and password, these are defaults in the VM):
```
connect target "sys/oracle AS SYSDBA"
run {
    BACKUP DATABASE TAG='&1' FORMAT = '/u01/app/oracle/oradata/orcl12c/backups/&2';
}
```
Now save the file by pressing the ESC key and then type “:WQ” which stands for Write Quit.





#### RMAN Script: restore-database.rman

We'll create the restore file now the same as above for the restore script as follows.

```
$ vi restore-database.rman
```
Paste the following into VI.
```
connect target "sys/oracle AS SYSDBA"
run {
    RESTORE DATABASE FROM TAG '&1';
}
```





#### RMAN Script: recover-database.rman

We'll create the restore file now the same as above for the restore script as follows.

```
$ vi recover-database.rman
```
Paste the following into VI.
```
connect target "sys/oracle AS SYSDBA"
run {
    RECOVER DATABASE FROM TAG '&1';
}
```





#### RMAN Script: restore-database-latest.rman

Now we'll create some slightly different versions of the two files created above to handle the 'latest' backups.
```
$ vi restore-database-latest.rman 
```
Paste the following into VI.
```
connect target "sys/oracle AS SYSDBA"
run {
    RESTORE DATABASE;
}
```





#### RMAN Script: recover-database-latest.rman

Finally, we'll setup the recovery for the latest.
```
$ vi recover-database-latest.rman 
```
Paste the the following into VI.
```
connect target "sys/oracle AS SYSDBA"
run {
    RECOVER DATABASE;
}
```


You can now test that the backup procedure works by running the "rman" line below on the VM. It will prompt you with "Enter value for 1:" The value you provide here is the name of the backup that will be created in the "backups" folder. Later we’ll be calling this remotely from our makefile on the Mac.
```
$ rman @backup-database.rman

[oracle@localhost orcl12c]$ rman @backup-database.rman

Recovery Manager: Release ********.0 - Production on Tue Feb 27 11:41:31 2018

Copyright (c) 1982, 2017, Oracle and/or its affiliates.  All rights reserved.

RMAN> connect target *
2> run {
3>     BACKUP DATABASE FORMAT = '/u01/app/oracle/oradata/orcl12c/backups/
Enter value for 1: backuptest2
backuptest2';
4> }
5> 
connected to target database: ORCL12C:ORCL (DBID=4079644691)

Starting backup at 27-FEB-18
using target database control file instead of recovery catalog
allocated channel: ORA_DISK_1
channel ORA_DISK_1: SID=57 device type=DISK
channel ORA_DISK_1: starting full datafile backup set
channel ORA_DISK_1: specifying datafile(s) in backup set
input datafile file number=00010 name=/u01/app/oracle/oradata/orcl12c/orcl/sysaux01.dbf
input datafile file number=00011 name=/u01/app/oracle/oradata/orcl12c/orcl/undotbs01.dbf
input datafile file number=00009 name=/u01/app/oracle/oradata/orcl12c/orcl/system01.dbf
input datafile file number=00012 name=/u01/app/oracle/oradata/orcl12c/orcl/users01.dbf
input datafile file number=00013 name=/u01/app/oracle/oradata/orcl12c/orcl/APEX_1941389856444596.dbf
channel ORA_DISK_1: starting piece 1 at 27-FEB-18
channel ORA_DISK_1: finished piece 1 at 27-FEB-18
piece handle=/u01/app/oracle/oradata/orcl12c/backups/backuptest2 tag=TAG20180227T114138 comment=NONE
channel ORA_DISK_1: backup set complete, elapsed time: 00:00:15
Finished backup at 27-FEB-18

Recovery Manager complete.
[oracle@localhost orcl12c]$ 
```
The above output is from a successful backup.
