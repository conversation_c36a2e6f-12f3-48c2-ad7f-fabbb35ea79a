Overview
========

This document outlines the operational architecture for the Case IQ system.

Logical Operational Architecture
================================

The system is logically seperated into a number of separate tiers. Deployment of
software resources is controlled by a container management host. All software
artefacts will be managed and deployed as docker containers unless explictly
stated otherwise.

This tiering of services is configurable. A standard 3-tier architecture is outlined below.

Frontend tier
-------------

The frontend tier will run consist of a web server proxy using Nginx. Nginx will be used to cache and serve up static content and also provides a standard TLS security specification and standard web server proxy configuration. In smaller environments the Frontend tier and Services tier may be merged into a single tier.

Services tier
-------------------

The services tier hosts business logic services, incoming mail services and worker nodes all leveraging Node.js. In addition, a message queue, using RabbitMQ. In some cases business logic and worker nodes may be broken down into smaller micro services.

Data tier
---------

The data tier will house the SQL-based DBMS (default of which is PostgreSQL) and caching and index (search) services using Redis and Elastic Search containers respectively. Some environments may specify that the database exists on a seperate host or tier altogether.