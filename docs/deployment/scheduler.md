Scheduler
=========

### Scheduling a job:

1. Browser: User selects (for example) that they want a reminder sent to them 5
days before a To-Do is due and saves the To-Do.
2. Server: The To-Do item is saved and the schedule command is executed.
  1. The sys_schedule table is checked to see if an existing scheduled reminder
  has been set. This obviously only applies if the todo is being updated.
  2. At this point one of 5 things can happen:
    1. The new to-do does not have due or emailReminder set so no scheduled job
    is created.
    2. The new to-do has both due and emailReminder set so a new scheduled job
    is created in the quartz-scheduler and a record is created in the sys_schedule table.
    3. An existing to-do is updated that had a previously scheduled task and
    there are values for both due and emailReminder. The sys_scheduler record
    and quartz-scheduler are both updated.
    4. An existing to-do is updated that had a previously scheduled task and one
    of due or emailReminder is missing. The sys_scheduler record is deleted and
    quartz-scheduler is sent a cancel message.
    5. An existing to-do is updated that did not have a previously scheduled
    task (because one of due or emailReminder had been missing) and both due and
    emailReminder are present. A new scheduled job is created in the
    quartz-scheduler and a record is created in the sys_schedule table.
    6. An existing to-do is closed that had a previously scheduled task.
    The sys_scheduler record is deleted and quartz-scheduler is sent a
    cancel message.
3. Browser: User receives a success response

### Canceling or Updating a job:

The To-Do examples under Scheduling a job show how canceling or updating a job
should work.

### Executing a job:

1. The Quartz-Scheduler will make an HTTP POST to the api it was given in the
original request with the payload it was given which includes the id from the
sys_schedule table as a property called parentId.
2. The entity from the sys_schedule table is retrieved and based on the values
in the fields the appropriate action is executed.
3. The entity is removed from the sys_schedule table.
4. Appropriate error messages are logged if this fails.
