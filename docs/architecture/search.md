

Search Engine
=============

The search engine used is Elasticsearch.

Indexing
========

Whenever entities are created/updated or removed, they are indexed in
elasticsearch.

Each entity has its own index where its data resides.

The name of the index is generated from the name of the entity, but can be controlled by
specifying a "esIndex" attribute on the entity definition.

See the plugin ``elasticsearch`` (``plugins/elasticsearch``) for more details.

Mappings
--------

Mappings for entities are generated through an entity's fields. The field types have the
mapping information and the analyzers, this information is used to create the index for
the respective entity.

Custom mappings that are not necessarily tied to an entity can be defined under
``config/elasticsearch-entities``

All available analyzers and global elasticsearch settings are defined in
``config/options.elasticsearch.js``.

Restrictions
============

Augmented queries
-----------------

Because our ACL layer (perm) removes records from the result set if the user doesn't
have access to them, a search query against elasticsearch may not return only objects
that a user has access. That makes the pagination display a wrong number of result.

To prevent that from happening, queries are augmented based on users' access
roles and filters are added to users query.

These filters that are added are automatically generated based on ACL defined in the
options.permission layer.

When writing ACL rules using functions (``fn``) acl filters for elasticsearch will have
to be written for that rule (``esFilter``).

This generation of ACL filters for elasticsearch is done in ``elasticsearch-filters.js`` 

Fetching results from DB
----

Once a query is executed by elasticsearch, the result set is replaced with the
database content with another query. The entities are matched by id and type.

That ensures that the ACLs can filter out any result/data that a user does not
have access to because the ACLs always run on top of the database and not
elasticsearch.

See the plugin ``elasticsearch`` (``plugins/elasticsearch/seneca.js``) 
