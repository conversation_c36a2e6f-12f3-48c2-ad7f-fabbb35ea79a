
Authentication
==============

The authentication is based on [passport](https://github.com/jared<PERSON>son/passport).


There are 2 ways to authenticate on the system. The first one is through regular
login and password. The second one is through SAML.

Authentication for all the strategies is implemented in `lib/api/auth.js` and configuration of
strategies is in `config/options.auth.js` and can be extended with project's config file,
if defined with same name

Login/Password
--------------

Strategy `passport-local` is the authentication mechanism with login and password.

* `lib/auth/passport.js` registers all the authentication strategies.
* `lib/core/password.js` does password verification and implements hashing using PBKDF2.

PBKDF2's behaviour can be configured by modifying
~~[`config/options.pbkdf2.js`](/config/options.pbkdf2)~~ [`config/options.pbkdf2user.js`](/config/options.pbkdf2user):

```
{
	keyLength: 128,
	saltLength: 128,
	iterations: 5000,
	digest: 'sha256',
	generator: {
		length: 12,
		memorable: false,
		pattern: /[a-zA-Z0-9]/,
	},
}
```

SAML
----

SAML authentication is configured as a strategy in `config/options.auth.js` and implemented in `lib/api/auth.js`.

The SAML configuration can be configured as follows:
```
{
	// Standard
	enabled: process.env.SAML_AUTH_ENABLED === 'true',
	successRedirect: '/',
	method: 'get',
	url: process.env.SAML_URL || '/auth/saml',
	strategyPath: path.join(__dirname, '../lib/auth/passport-saml.js'),
}
```

Please check `config/options.auth.js` for more details

Cookies
-------

Express's connect.sid
================
 Application is using `connect.sid` cookies for express sessions

 Documentation on available options -> [Express cookie options](https://expressjs.com/en/api.html#res.cookie)

 Options for the app are set in `config/options.sessions.js`

* **httpOnly**: true
* **maxAge**: Set through LOGIN_SESSION_EXPIRY, defaults to 45 minutes
* **path**: Path for the cookie, defaults to “/”.
* **secure**: Set through DISABLE_SECURE_COOKIES.
* **signed**: Indicates if the cookie should be signed.
* **sameSite**: Set through SAME_SITE, defaults to 'lax'

CSRF Protection
===============

First, check out [what CSRF is](https://www.owasp.org/index.php/Cross-Site_Request_Forgery_%28CSRF%29).

The application validate any HTTP request that can modify data by checking

1. if there is a CSRF token
2. if that token is valid

CSRF tokens are generated by the
[csurf](https://github.com/expressjs/csurf) module.

These CSRF token expire quickly (configurable) and are session-less. It means:

- Tokens are HMAC signed with an expiration
- Each CSRF is tied to the current user's session ID
- A secret needs to be shared between all fron-end node processes

In the UI, there is a loop that regularly poll a new CSRF token. This is to make
sure that users can keep windows open and resume their session. Their session
will expire before they see a CSRF token.

Data in transition
==================

Communication between components
--------------------------------

Communication between components needs to be secure against on the wire
listening. Some of the components may already communicate through a secure
protocol. Because all components communications happen over TCP, we can pipe the
un-encrypted communications through [stunnel](https://www.stunnel.org/).

![System Components Diagram](architecture/assets/secure_comms_stunnel.jpg)

For this to be effective, operations need to look out for 2 things:

1. services should only bind to the local loop interface
2. firewalls should only expose the secure ports and shut all ports by default

Data at rest
============

The data at rest needs to be secure. To do this, we recommend disk encryption
for all hardware, including those that don't necessarily permanently store data.

The reason is that sometime processes crash and dump data to the disk.
