
Introduction
============

This application is running node.js on the back-end and is organized with
Backbone.js on the UI.

System Components
=================

This system is not homogeneous. It is composed from a minimum set (to reduce
administrative overhead) of specialized services. The key technologies are:

- a relational dbms (PostgreSQL / MySQL / OracleDB)
- an application server (Node.js)
- a message queue (RabbitMQ)
- a worker node to perform background tasks (Node.js)
- a search engine (ElasticSearch)
- an external scheduling engine (Quartz)
- a memory cache server caching and record numbering (Redis)

These services are exposed by a message interface. The messages are acted on by
a set of Node.js processes. This means that the system is insulated from
technology choices, and that changes can be made to meet future customer
requirements.

The system is designed to run independently of the underlying hardware. This
ensures that it can be deployed on cloud environments, traditional co-location,
or in-house infrastructure. Services are not run directly. Rather they are
“containerized” so that deployment can be predictable, managed and automated. In
particular, containerization enables partial and multi-version deployments. It
also enables fast rollback of services if adverse affects are observed with new
deployments.
