

# Introduction

This document describes the organisation of the server side code. In an attempt
to create documentation that stays up to date for a long time, and because code
evolves, this document will not describe in detail how each piece work.

Rather, it is a description of the adopted architecture.

> Pre-requisites:
>
>   - good understanding of asynchronous javascript development
>   - good understanding of seneca pattern matching

The server code base has a multi-layer approach.

The application is organized in workflows with one workflow per entity type. A
workflow is a list of actions that are run when an entity is saved or fetched.

> A workflow is really just a name given to the set of logic that runs when an
> action is performed on an entity.

Seneca allows to plug into a workflow:

```
  // pluging into the sys/case entity save workflow
  seneca.add({role: 'entity', cmd: 'save', base: 'sys', name: 'case'})
```

As much as possible, when some logic applies to multiple entities, we tried to
implement that logic in a generic way. The main 2 benefits of the reduction of
the amount of code in the application are (1) better maintainability (2) less
potential for bugs.

Hence there are a few workflow microservices that are common across a number of
entities:

## Diff

Many actions need to know what fields the user has updated on a given entity. To
facilitate that, a diff microservice (`diff.js`) compares the saved entity with
the one on the database and generates a `diff` object.

Here is an example checking if the owner of a case was updated and setting the
`ownerSince` date.


```
seneca.add({role: 'entity', cmd: 'save', base: 'sys', name: 'case' }, function(args, callback) {

  if(args.diff && args.diff.owner) {

    if(args.diff.owner.updatedValue) {

      // there is a new owner
      args.ent.ownerSince = new Date();

    } else {

      // there is no owner anymore
      args.ent.ownerSince = null;

    }

    // do not forget to tell other microservices that `ownerSince` has a new
    //   value
    args.diff.ownerSince = {
      type: args.ent.ownerSince ? 'E' : 'N',
      originalValue: args.ent.ownerSince,
      updatedValue: entity.createdDate
    }
  }

  // we're done with this microservice, hand over to the next one.
  this.prior(args, callback);
});
```


If a microservice modifies the entity, it MUST also update the diff object. The
reason for this is that other microservices may want to know that which
attributes have changed and the logic may depend on the attribute that you are
modifying.


```
seneca.add({role: 'entity', cmd: 'save', base: 'sys', name: 'case' }, function(args, callback) {

  // Both update and create share the same 'workflow' {role: 'entity', cmd: 'save'}.
  // We can differentiate create from update by checking if the entity already
  //   has an id or not.
  if(!args.ent.id) {

    entity.createdDate = new Date();
    args.diff.createdDate = {
      type: 'N', // N means New, E -> Edit, D -> Delete. (legacy stuff)
      originalValue: undefined,
      updatedValue: entity.createdDate
    }
  }

  // we're done with this microservice, hand over to the next one.
  this.prior(args, callback);
});
```

## Denormalize

Some data needs to be denormalized. This is because when fetching objects, we
want to make as little joins as possible to limit the load on the system.
Sometime it makes sense to denormalize data, sometime it is better to use joins.

The fields denormalized are defined in the configuration files at
[config/entities](/config/entities).

## Joins

Joins' base behaviour is configured in
[config/options.joins.js](/config/options.joins.js).

Additional to that, a query parameter gives fine grain control over which
attributes are joined. Details in
[lib/services/business-logic/joins/joins.js](/lib/services/business-logic/joins/joins.js).

By default, retrieving an entity will also retrieve the linked entities and
populate the fields specified in the /config/options.joins.js file.

To prevent a join (or expansion) from happening on an API request you should
pass a query parameter called 'expand' with a value set to false:

* \<no query parameter\> - full join done on all fields in options.joins.js file
* expand=* - same as \<no query parameter\>
* expand=caseNumber,createdBy - only join the two fields caseNumber,createdBy
* expand=false - don't do any joins

eg.

`/api/1.0/party/<partyId>?expand=false` returns the following object:

```
{
  "entity$": "-/sys/party",
  "id": "0248b2ef-0025-6b4b-3afc-ba4690ac16db",
  "caseId": "0125fd5d-187b-a537-5297-b3b9190d8798",
  "primaryParty": "No",
  "partyType": "Subject",
  "associationToWalmart": "External",
  "title": "Mr",
  "firstName": "John",
  "lastName": "Smith",
  "createdBy": "02cb0165-ef50-576c-3791-5d188f620c89",
  "createdDate": "2014-08-01T13:52:10.278Z"
}
```

The following call `/api/1.0/party/<partyId>` will
populate the appropriate attributes:

```
{
  "entity$": "-/sys/party",
  "id": "0248b2ef-0025-6b4b-3afc-ba4690ac16db",
  "caseId": "0125fd5d-187b-a537-5297-b3b9190d8798",
  "caseNumber": "2014-08-00004393",
  "primaryParty": "No",
  "partyType": "Subject",
  "associationToWalmart": "External",
  "title": "Mr",
  "firstName": "John",
  "lastName": "Smith",
  "createdBy": "02cb0165-ef50-576c-3791-5d188f620c89",
  "createdByName": "James Smith",
  "createdDate": "2014-08-01T13:52:10.278Z"
}
```

And the following call `/api/1.0/party/<partyId>?expand=caseNumber` will
populate the appropriate attributes:

```
{
  "entity$": "-/sys/party",
  "id": "0248b2ef-0025-6b4b-3afc-ba4690ac16db",
  "caseId": "0125fd5d-187b-a537-5297-b3b9190d8798",
  "caseNumber": "2014-08-00004393",
  "primaryParty": "No",
  "partyType": "Subject",
  "associationToWalmart": "External",
  "title": "Mr",
  "firstName": "John",
  "lastName": "Smith",
  "createdBy": "02cb0165-ef50-576c-3791-5d188f620c89",
  "createdDate": "2014-08-01T13:52:10.278Z"
}
```

Note that on the last call the `createdByName` has not been included.


### Search/indexing

Every time an entity is saved, it is indexed in ElasticSearch (ES) This allows to
use complex indexing on entities fields.

How entities are indexed is defined in ```/config/options.elasticsearch.js```.
The module doing the indexing is [seneca-elasticsearch](https://github.com/nherment/seneca-elasticsearch)

> Note: The ES plugin replaces the entities fetched from ES with the entities
> from the database. There are 2 reasons for this:
> - ES is not the reference store for information, the DB is. ES is an index.
> - We want to run entities through the permissions module before handing them
>   out.

## Permissions

The permissions module is [seneca-perm](https://github.com/rjrodger/seneca-perm).
Any entity saved or loaded from the DB has to go through the permission module.

The list of permissions are defined in `/config/options.permissions.js`.

> Note: access controls will only be executed if `args.perm$` is present.
> The `args.perm$` variable contains the current user's roles.

> Note: Some permissions are run within the business logic.

## Database

The database used is PostgreSQL. The seneca driver is [seneca-postgresql-store](https://github.com/nherment/seneca-postgresql-store).

## Locks

To prevent users from overwriting each other's changes on a given entity,
entities are 'locked'. These lock are activated when a user enters into edit
mode for an entity.

There are 3 parts to the lock mechanism:

- a client side library to activate, release and check locks
- an API to activate, release and check locks
- a workflow microservice which prevents saving an entity when it is locked by
another user

Locks are purely transient and stored in redis.

## Ad-hoc business logic

The business logic can plug into the workflows. For example, let's say we want to
set the created date on case creation:

```
seneca.add({role: 'entity', cmd: 'save', base: 'sys', name: 'case' }, function(args, callback) {

  // Both update and create share the same 'workflow' {role: 'entity', cmd: 'save'}.
  // We can differentiate create from update by checking if the entity already
  //   has an id or not.
  if(!args.ent.id) {

    entity.createdDate = new Date();

  }

  // we're done with this microservice, hand over to the next one.
  this.prior(args, callback);
});
```

> **Note 1**: the order in which these microservices are declared with
> seneca.use(...) is extremely important.

> **Note 2**: It's good practice to split business logic into small pieces, each
> of them in a separate file.
