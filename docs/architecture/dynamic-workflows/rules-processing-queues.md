<!-- TODO: update rules processing queues documentation before releasing 6.0.0 release-->

# Rules processing queues TBD

There could be some transitions or static events on all the entities which would trigger events processing queue as follows

## Rule Event processing queue

use event service to register events

### Input: Expected Input

- Event type: transitions or static events
- Record Data: record data on which the event got triggered
- Record id: record id on which the event got triggered
- Record type: record type on which the event got triggered

### Processing: load all rules with the following

- event type // something jas is going to do
- match conditions // something muj is going to do

### Outcomes

- list of rules matched

### Processing

- load all the actions and send it to ```Actions Processing queue```
- or create quartz jobs if it actions require to be triggered in a later date or time
- use event service to emit actions execution

## Action execution queue

use event service to register actions execution queue

### Input

- actionId:
- actionType:
- recordId:
- recordType:

### Processing: execute  the action specified

example action config:
```js
{
	role: 'rule-action',
	cmd: 'execute',
	actionType: 'sys/assignment_action',
}
```
