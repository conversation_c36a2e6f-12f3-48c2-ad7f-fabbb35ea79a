# Configuration for probot-stale - https://github.com/probot/stale

# Number of days of inactivity before an Issue or Pull Request becomes stale
daysUntilStale: 60

# Number of days of inactivity before an Issue or Pull Request with the stale label is closed.
# Set to false to disable. If disabled, issues still need to be closed manually, but will remain marked as stale.
daysUntilClose: 7

# Issues or Pull Requests with these labels will never be considered stale
exemptLabels:
  - pinned
  - security
  - wip

# Ignore issues in a project
exemptProjects: true

# Ignore issues in a milestone
exemptMilestones: true

# Label to use when marking as stale
staleLabel: wontfix

# Comment to post when marking as stale
markComment: >
  This issue has been automatically marked as stale because it has not had
  recent activity. It will be closed if no further activity occurs. Thank you
  for your contributions.

# Comment to post when closing a stale Issue or Pull Request
closeComment: >
  This issue has been automatically closed because it has not had
  any activity for an extended period of time.

# Number of actions per hour
limitPerRun: 30
