version: 2
updates:
- package-ecosystem: npm
  directory: "/"
  schedule:
    interval: daily
  open-pull-requests-limit: 10
  ignore:
  - dependency-name: "*"
    update-types: ["version-update:semver-major"]
  commit-message:
    prefix: "feature"
    include: "scope"
- package-ecosystem: gitsubmodule
  directory: "/"
  schedule:
    interval: weekly
  open-pull-requests-limit: 5
  commit-message:
    prefix: "chore"
    include: "scope"
- package-ecosystem: docker
  directory: "/"
  schedule:
    interval: weekly
  open-pull-requests-limit: 5
  commit-message:
    prefix: "feature"
    include: "scope"
- package-ecosystem: "github-actions"
  directory: "/"
  schedule:
    interval: weekly
  open-pull-requests-limit: 5
  commit-message:
    prefix: "chore"
    include: "scope"