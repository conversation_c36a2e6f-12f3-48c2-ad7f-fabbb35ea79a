# GitHub Labeler Action configuration

dependencies:
- package.json
- yarn.lock
- npm-shrinkwrap.json
- package-lock.json
- pom.xml

changelog:
- CHANGELOG.md
- CHANGELOG.markdown

docker:
- "Dockerfile*"
- "docker-compose.yml"
- "docker-compose.*.yml"

documentation: "*.+(md|markdown)"

javascript: "*.js"

java: "*.java"

style:
- "*.less"
- "*.css"
- "*.sass"

submodules:
- "quartz-http/*"
- "v5_es/*"
- "v5_redis/*"
- "v5_nginx/*"

tests:
- "test/**/*"

core:
- "Makefile"
- "layout-elements/**/*"
- "entities/*"
- "field-types/*"
- "plugins/**/*"
- "services/**/*"
