# Contributing to Case IQ 
Did you write a patch to fix a bug or do you intend to add a new feature?
* Open an issue in JIRA under project [Case IQ Template(ITPL)](https://i-sight.atlassian.net/secure/CreateIssue!default.jspa). If its an existing projct issue you can clone, move and link the issue.
* Choose the correct branch that you would like the PR to go against. For example, if you would like to patch a bug in v2.6.0 you should choose branch `release/v2.6.x`.
* Create a branch under feature folder that has the issue number as the name. Additional small details can optionally be added to the name, for example: `feature/ITPL-123_auditFix`.
* Add a changelog describe the new changes. For example: add a file named `feature.ITPL-12345.md` with the content `- updated something` in the `changes` directory under the project root. The file name should be prefixed with `feature`/`fix`/`chore` dependent on the type of the change. So the file name could be either `feature.ITPL-12345.md`, `fix.ITPL-12345.md` or `chore.ITPL-12345.md`. If the changes you made are not for the latest version, the changelog needs to be added to file [CHANGELOG.md](../CHANGELOG.md) directly.
* Add a summarizing title to the PR prepended by the issue number, for example: `ITPL-123: Added cool feature to do cool stuff`. This will automatically connet your PR to the issue in JIRA. Try to do this for commit messages as well.
* Add appropriate labels. For example: If you are still working on your PR add the `wip` (Work in Progress) label. This lets us know the status of the PR.
* Most importantly of all, add a description of the PR that details (or summarizes) the changes proposed. Large or confusing pull requests without an adequate description are more likely to be put onto the back burner.

After your PR has been submitted
* Feel free to seek a peer review from any developer.
* PR will be assigned to a developer in the platform team.
* The assignee reviews your PR by suggesting changes or approving it. An existing review and adequate comments can speed this process up.
* If changes are requested, please make sure to discuss or make the changes in a reasonable amount of time. A PR may be requested to be moved to another target branch or milestone. If you have a large PR planned its best to have some discussion up front to avoid wasted re-work or miscommunication.
* If approved, the PR will be merged into the requested branch and changes will be available in the next release. It may be merged at a later date depending on what else is going on for that target branch. But an accepted PR will have the reviwed checkmark and a target milestone.

Some things to keep in mind
* Ensure that the process is followed and that you have an issue link and the correct target branch.
* If your PR is dependant on another PR add a link to that PR.
* Ensure you have an appropriately detailed comment. Sometimes it can be confusing about what the objective of a PR is.
* Make small commits and small PRs. If you have multiple issues separate those into separate PRs. It is much easier to push through smaller, more deliberate changes.
* Keep in mind that the platform must maintain viability for all of our supported client systems.
* If your PR proposes visual changes embed a screenshot or gif demonstrating the change.
