name: Test Coverage
on:
  release:
    types: [released]
permissions:
  contents: write
env:
  DISABLE_NOTIFIER: true
  ENCRYPTION_KEYS_PATH: ./test/fixture/keys/
  SECRET_ENCRYPTION_KEY_FILE: secret-encryption-key.key
  SSE_DISABLED: true
  ES_REFRESH_INTERVAL: 1s
  MAIL_DISABLE_DNS_VALID: true
  SLEEP_TIME: 0
  YF_ENABLED: false
  HUSKY: 0
  NODE_ENV: production
  ENV: test
  ENV_BOX: ci
  DISABLE_DB_BACKUP: true
  ENABLE_PDFTRON: true
  LEGACY_CASE_NUMBERING: true
jobs:
  generate-report:
    name: Generate coverage report
    if: ${{ !startsWith(github.ref, 'refs/tags/fork') }}
    runs-on: ubuntu-latest
    steps:
      - name: Install PostgreSQL client
        run: |
          sudo apt-get update
          sudo apt-get install --yes postgresql-client
      - name: Checkout repository code
        uses: actions/checkout@v3
      - name: Setup Node
        uses: actions/setup-node@v3
        with:
          node-version-file: '.nvmrc'
      - name: Setup SSH keys
        uses: webfactory/ssh-agent@v0.8.0
        with:
          ssh-private-key: ${{ secrets.PULL_PRIVATE_KEY }}
      - name: Install Yarn
        run: npm install --global yarn@1
      - name: Install dependencies
        run: yarn --frozen-lockfile --production=false
      - name: Link Platform to Fixture
        run: |
          yarn link
          pushd test/fixture
          yarn --frozen-lockfile --production=false
          yarn link isight
          popd
      - name: Setup services
        run: make docker-up
      - name: Health check Elasticsearch
        uses: nick-fields/retry@v2
        with:
          timeout_minutes: 1
          max_attempts: 5
          command: curl --silent -XGET --fail http://localhost:9200 || exit 1
      - name: Setup and run tests
        run: make run-tests-coverage
      - name: Zip coverage report
        run: zip -r coverage-report.zip coverage
      - name: Upload coverage report
        uses: softprops/action-gh-release@v1
        with:
          files: coverage-report.zip

