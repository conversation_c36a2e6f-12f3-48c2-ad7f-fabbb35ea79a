# Add "approved" label to PRs with at least 1 approving reviewer
on: pull_request_review

name: Label approved pull requests

permissions:
  contents: read

jobs:
  labelWhenApproved:
    name: Label when approved
    runs-on: ubuntu-latest
    permissions:
      contents: read
      pull-requests: write
    steps:
    - name: Label when approved
      uses: abinoda/label-when-approved-action@v1.0.7
      env:
        APPROVALS: "1"
        GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
        LABEL_NAME: "approved"