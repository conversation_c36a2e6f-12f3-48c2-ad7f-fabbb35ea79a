# Apply various labels based on PR contents
# See /.github/labeler.yml for configuration details
name: Labeler

on:
  pull_request_target:
    types: [opened, synchronize]

permissions:
  contents: read

jobs:
  label:
    name: Labeler
    runs-on: ubuntu-latest
    permissions:
      contents: read
      pull-requests: write
    steps:
      - name: Files changed
        uses: actions/labeler@v4
        with:
          repo-token: "${{ secrets.GITHUB_TOKEN }}"
          sync-labels: true
      - name: Diff size
        uses: pascalgn/size-label-action@v0.5.4
        env:
          GITHUB_TOKEN: "${{ secrets.GITHUB_TOKEN }}"
          IGNORED: ".*\n!.gitignore\nyarn.lock\npackage-lock.json"
