# Lint Docker configuration files for bad practices / errors
name: Docker Lint PR

on:
  pull_request_target:
    types: [opened, synchronize]
    paths: ['Dockerfile*', 'docker-compose.yml', 'docker-compose.*.yml', '.github/workflows/docker-*.yml']

permissions:
  contents: read

jobs:
  docker_lint:

    runs-on: ubuntu-latest

    steps:
    - name: Checkout
      uses: actions/checkout@v4
    - name: Docker Compose config lint
      run: docker stack config -c docker-compose.local.yml
    - name: Hadolint
      uses: docker://cdssnc/docker-lint-github-action
      with:
        args: --ignore SC1091 --ignore SC2086 --ignore DL3005 --ignore DL3008 --ignore DL3009 --ignore DL3015 --trusted-registry docker.io
