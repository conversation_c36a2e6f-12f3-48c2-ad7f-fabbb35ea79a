name: Call Platform Build Jenkins Jobs on New Release

on:
  release:
    types:
      - published
permissions:
  contents: read

jobs:
  call-jenkins-jobs:
    if: github.event.release.prerelease == false
    runs-on: self-hosted

    steps:
      - name: Build on Orch02
        run: |
          curl -X POST "${{ env.ORCH02_DOMAIN }}/job/${{ env.JENKINS_PATH }}/buildWithParameters?TOKEN=${{secrets.JENKINS_API_TOKEN}}" \
          --user "${{ secrets.JENKINS_USER }}:${{ secrets.USER_TOKEN_ORCH02 }}" \
          --data BYPASS_TAG=true \
          --data GIT_TAG=${{github.event.release.tag_name}} \
          --data NOTIFICATION_EMAIL=${{secrets.NOTIFICATION_EMAIL}}

      - name: Build on Orch03
        run: |
          curl -X POST "${{ env.ORCH03_DOMAIN }}/job/${{ env.<PERSON><PERSON>KIN<PERSON>_PATH }}/buildWithParameters?TOKEN=${{secrets.JENKINS_API_TOKEN}}" \
          --user "${{ secrets.JENKINS_USER }}:${{ secrets.USER_TOKEN_ORCH03 }}" \
          --data BYPASS_TAG=true \
          --data GIT_TAG=${{github.event.release.tag_name}} \
          --data NOTIFICATION_EMAIL=${{secrets.NOTIFICATION_EMAIL}}

      - name: Build on Orch04
        run: |
          curl -X POST "${{ env.ORCH04_DOMAIN }}/job/${{ env.JENKINS_PATH }}/buildWithParameters?TOKEN=${{secrets.JENKINS_API_TOKEN}}" \
          --user "${{ secrets.JENKINS_USER }}:${{ secrets.USER_TOKEN_ORCH04 }}" \
          --data BYPASS_TAG=true \
          --data GIT_TAG=${{github.event.release.tag_name}} \
          --data NOTIFICATION_EMAIL=${{secrets.NOTIFICATION_EMAIL}}

      - name: Build on Orch05
        run: |
          curl -X POST "${{ env.ORCH05_DOMAIN }}/job/${{ env.JENKINS_PATH }}/buildWithParameters?TOKEN=${{secrets.JENKINS_API_TOKEN}}" \
          --user "${{ secrets.JENKINS_USER_ID }}:${{ secrets.USER_TOKEN_ORCH05 }}" \
          --data BYPASS_TAG=true \
          --data GIT_TAG=${{github.event.release.tag_name}} \
          --data NOTIFICATION_EMAIL=${{secrets.NOTIFICATION_EMAIL}}
