# Run actions on PR opened such as branch based labelling
name: PR Opened

on:
  pull_request_target:
    types: [opened]

permissions:
  contents: read

jobs:
  pr-opened:
    name: PR Opened
    permissions:
      contents: read # for TimonVS/pr-labeler-action to read config file
      pull-requests: write # for TimonVS/pr-labeler-action to add labels in PR
    runs-on: ubuntu-latest
    steps:
    - name: Label branch name
      uses: TimonVS/pr-labeler-action@v5.0.0  #only works on opened
      env:
        GITHUB_TOKEN: "${{ secrets.GITHUB_TOKEN }}"
