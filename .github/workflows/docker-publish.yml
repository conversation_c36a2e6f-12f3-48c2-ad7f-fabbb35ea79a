name: Build and Upload Release

on:
  release:
    types: [created]

permissions:
  contents: read

jobs:
  build:

    if: "!github.event.release.prerelease"
    runs-on: ubuntu-latest
    permissions:
      contents: read
      packages: write

    steps:
      - name: Checkout repository
        uses: actions/checkout@v3

      # Login against a Docker registry
      - name: Log into registry ${{ env.REGISTRY }}
        uses: docker/login-action@343f7c4344506bcbf9b4de18042ae17996df046d
        with:
          registry: ${{ env.REGISTRY }}
          username: ${{ vars.DOCKERHUB_SA_USER }}
          password: ${{ secrets.DOCKERHUB_SA_PASS }}

      - name: Add SSH key
        run: mkdir -p keys && touch keys/id_rsa && echo "${{ secrets.PULL_PRIVATE_KEY }}" > ./keys/id_rsa && chmod 600 ./keys/id_rsa
        shell: bash

      # Extract metadata (tags, labels) for Docker
      - name: Extract Docker metadata
        id: meta
        uses: docker/metadata-action@818d4b7b91585d195f67373fd9cb0332e31a7175
        with:
          images: ${{ env.REGISTRY }}/${{ env.IMAGE_NAME }}

      # Build and push Docker image with Buildx
      - name: Build and push Docker image
        uses: docker/build-push-action@2eb1c1961a95fc15694676618e422e8ba1d63825
        with:
          context: .
          push: true
          build-args: PLATFORM_VERSION=git+ssh://**************/i-Sight/isight_main_v5_beta#${{ steps.meta.outputs.tags }}
          tags: ${{ steps.meta.outputs.tags }}
          labels: ${{ steps.meta.outputs.labels }}
